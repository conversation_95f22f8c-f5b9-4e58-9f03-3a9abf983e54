## 1.1.0

* upgrade Gradle plugin and dependencies

## 1.0.1+4

* fix bug

## 1.0.1+3

* add `kotlin-android` to android

## 1.0.1+2

* fix bug

## 1.0.1+1

* reduce dart version to `2.12.0`

## 1.0.1

* analyze leaked node type, `Widget`, `Element`
* add `LeakNavigatorObserver`, Automatically check for memory leaks by `NavigatorObserver`
* fix some bugs.

## 1.0.0+1

* Change project configuration

## 1.0.0

* Support `macos` platform

## 0.2.0

* Migrate package to null-safety

## 0.1.2+1-beta

* change `FlatButton` to `TextButton`

## 0.1.2-beta

* Update README.md

## 0.1.1-beta

* Add document description

## 0.1.0-beta

* Increase the basic memory leak detection function, provide `State`, `Element` memory leak detection, provide preview page, leak record storage function.

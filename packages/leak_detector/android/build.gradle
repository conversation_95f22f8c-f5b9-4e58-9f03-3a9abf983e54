group 'com.ljk.leak_detector'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.1'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

allprojects {
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
        }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
// apply plugin: 'dev.flutter.flutter-gradle-plugin'
// plugins {
//     id "com.android.library"
//     id "kotlin-android"
//     // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
//     id "dev.flutter.flutter-gradle-plugin"
// }

android {
    namespace = "com.ljk.leak_detector"
    compileSdk = 35
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }
    
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        multiDexEnabled true
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a"
        }
    }
    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
        abortOnError false
    }
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
}
# playlet

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## 版本管理

Flutter v3.27.1

[fvm](https://fvm.app/)

## 安装启动图标

```sh
flutter pub run flutter_launcher_icons -f flutter_launcher_icons.yaml
```

## 生成启动页配置

```sh
flutter pub run flutter_native_splash:create
flutter pub run flutter_native_splash:create --all-flavors
```

## 目录结构

```yaml
lib
├── api                   # 接口
├── components            # 组件(页面/公共)
├── config                # 配置
├── model                 # 模型
├── modules               # 模块
├── common                # 公共
│   ├── player            # 视频播放
├── routers               # 路由
├── service               # 服务
├── theme                 # 主题
├── utils                 # 工具
├── main.dart             # 应用入口
├── firebase_options.dart # Firebase配置
```

## 参考

[json 转 model](https://javiercbk.github.io/json_to_dart/)
[GETX 中文文档](https://github.com/jonataslaw/getx/blob/master/README.zh-cn.md)

## 资源代码生成

1. 安装插件

```sh
dart pub global activate flutter_gen
```

2. 生成代码(项目根目录下执行)

```sh
fluttergen -c pubspec.yaml
```

## 使用示例

### FFLog

```
// trace用于输出详细日志内容（原verbose），仅在debug下输出
FFLog.trace("Trace log", tag: "模块1");
// debug用于输出调试日志内容，仅在debug下输出
FFLog.debug("Debug log", tag: "模块2");
// info用于输出信息级别的日志内容
FFLog.info(123, tag: "模块3");
// warning用于输出警告级别的日志内容
FFLog.warning({"key": "value", "abc": ["123", "345"]}, tag: "模块4");
// error用于输出错误级别的日志内容
FFLog.error(["1", "2"], tag: "模块5");
// fatal用于输出严重错误级别的日志内容，Debug下中断程序并抛出异常
FFLog.fatal(true, tag: "模块6");
```

## 埋点上报方法

```dart
import 'package:playlet/utils/track_event.dart';

useTrackEvent(
  'event_name',
  extra: {
    'param1': 'value1',
    'param2': 'value2',
  },
);
```
## 广告接入方法

一、获取广告视图

```
// 展示原生信息流广告
// scene：场景
Future<Widget?> getNativeAdView(AdScene scene) async {}

// 示例
final widget = await AdManager().getNativeAdView(AdScene.homeFeed)
```

二、全屏展示广告（插页、激励）

```
// 展示全屏广告(插页、激励)
// scene：场景
// 返回值：关闭时是否领取了奖励
Future<bool> showFullScreenAd(AdScene scene) async {}

// 示例：
final isReceived = await AdManager().showFullScreenAd(AdScene.adsUnlock);
```

三、预缓存广告

```
  // 预加载广告
  void prefetchAds() {}
  
  
  示例：
  AdManager().prefetchAds()
```


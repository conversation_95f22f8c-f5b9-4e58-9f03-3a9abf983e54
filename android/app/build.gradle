plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"  
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

android {
    // TODO 正式包须更换
    namespace = "com.top.drama.test"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    defaultConfig {
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled true
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a"
        }
    }

    signingConfigs {
        release {
            keyAlias KEY_ALIAS
            keyPassword RELEASE_KEY_PASSWORD
            storeFile file(RELEASE_STORE_FILE)
            storePassword RELEASE_STORE_PASSWORD
        }
        debug {
            keyAlias KEY_ALIAS
            keyPassword DEBUG_KEY_PASSWORD
            storeFile file(DEBUG_STORE_FILE)
            storePassword DEBUG_STORE_PASSWORD
        }
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    buildTypes {
        debug {
            minifyEnabled true
            shrinkResources false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled true
            shrinkResources false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    flavorDimensions "env"
    productFlavors {
        dev {
            // TODO 正式包须更换
            applicationId "com.example.playlet"
            buildConfigField("boolean", "DEV_MODE", "true")
            signingConfig signingConfigs.debug
        }

        prod {
            // TODO 正式包须更换
            applicationId "com.example.playlet"
            buildConfigField("boolean", "DEV_MODE", "false")
            signingConfig signingConfigs.release
        }
    }

    buildFeatures {
        buildConfig = true
    }
}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
    implementation 'com.google.android.gms:play-services-auth:21.2.0'

    implementation 'com.google.firebase:firebase-bom:33.11.0'

    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-auth'

    implementation "com.android.billingclient:billing-ktx:7.0.0"

    implementation('com.bytedanceapi:ttsdk-player_premium:1.42.3.103') {
        exclude group: 'com.bytedance.applog', module: 'RangersAppLog-Lite-cn'
    }
    implementation 'com.bytedance.applog:RangersAppLog-Lite-global:6.15.4'

//    deeplink 保活库
    implementation 'dev.deeplink:guard:2.1.9'



}

# Flutter混淆默认规则
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.embedding.** { *; }

# MethodChannel 相关类保持不混淆，以确保通道正常工作
-keepclassmembers class io.flutter.plugin.common.MethodChannel$MethodCallHandler {
    void onMethodCall(io.flutter.plugin.common.MethodCall, io.flutter.plugin.common.MethodChannel$Result);
}
-keepclassmembers class * implements io.flutter.plugin.common.MethodChannel$MethodCallHandler {
    void onMethodCall(io.flutter.plugin.common.MethodCall, io.flutter.plugin.common.MethodChannel$Result);
}


# 保证归因 sdk 不被混淆
-keep class dev.deeplink.sdk.** { *; }

# 保持库依赖不被混淆
-keep class com.google.android.gms.** { *; }
-keep class com.google.firebase.** { *; }
-keep class com.android.billingclient.** { *; }
-keep class com.bytedanceapi.** { *; }
-keep class com.bytedance.applog.** { *; }

# 保持 Kotlin 相关类
-keep class kotlin.** { *; }
-keep class kotlinx.** { *; }

# 避免混淆 Parcelable 类
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# 保持 native 方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保持枚举类不被混淆
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保持 R 资源类中的静态字段不被混淆
-keepclassmembers class **.R$* {
    public static <fields>;
}

# Retrofit 相关
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

# OkHttp 相关
-dontwarn okhttp3.**
-dontwarn okio.**
-keep class okhttp3.** { *; }
-keep class okio.** { *; }

# Gson 相关
-keep class com.google.gson.** { *; }
-keepattributes Signature
-keepattributes *Annotation*
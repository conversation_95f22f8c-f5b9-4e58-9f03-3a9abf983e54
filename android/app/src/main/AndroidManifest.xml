<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools">


    <uses-permission android:name="android.permission.INTERNET"/>
    <!-- 全屏 intent 限制-->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <!-- 如果你使用的是 Android 12.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <application
        android:label="FlareFlow"
        android:name=".FlareApplication"
        tools:replace="android:label"
        android:icon="@mipmap/launcher_icon">

        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>
        <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>
        <meta-data android:name="com.google.android.gms.ads.APPLICATION_ID" android:value="@string/admob_app_id"/>
        <meta-data android:name="io.flutter.embedding.android.NormalTheme" android:resource="@style/NormalTheme" />
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- DeepLink Activity -->
        <activity
            android:name=".NavActivity"
            android:configChanges="orientation|screenSize"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="deeplink.navigator"
            android:theme="@style/TransparentTheme">

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:host="flareflow.deeplink.dev"
                    android:scheme="https"
                    tools:ignore="IntentFilterUniqueDataAttributes" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- flareflow deep links -->
                <data
                    android:host="deeplink.flareflow.tv"
                    android:pathPrefix="/dl/TT"
                    android:scheme="flareflow"
                    tools:ignore="IntentFilterUniqueDataAttributes" />
                <data
                    android:host="deeplink.flareflow.tv"
                    android:pathPrefix="/dl/tt"
                    android:scheme="flareflow"
                    tools:ignore="IntentFilterUniqueDataAttributes" />
                <data
                    android:host="deeplink.flareflow.tv"
                    android:pathPrefix="/dl/FB"
                    android:scheme="flareflow"
                    tools:ignore="IntentFilterUniqueDataAttributes" />
                <data
                    android:host="deeplink.flareflow.tv"
                    android:pathPrefix="/dl/fb"
                    android:scheme="flareflow"
                    tools:ignore="IntentFilterUniqueDataAttributes" />
                <data
                    android:host="deeplink.flareflow.tv"
                    android:pathPrefix="/dl/GG"
                    android:scheme="flareflow"
                    tools:ignore="IntentFilterUniqueDataAttributes" />
                <data
                    android:host="deeplink.flareflow.tv"
                    android:pathPrefix="/dl/gg"
                    android:scheme="flareflow"
                    tools:ignore="IntentFilterUniqueDataAttributes" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="true" />

        <activity android:name=".FullScreenActivity"
            android:exported="false"
            android:theme="@style/FullScreenTheme"
            android:excludeFromRecents="true"
            android:taskAffinity="notification.task"
            />

        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>

    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>
        <intent>
            <action android:name="android.intent.action.SENDTO" />
            <data android:scheme="mailto" />
        </intent>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
    </queries>
</manifest>

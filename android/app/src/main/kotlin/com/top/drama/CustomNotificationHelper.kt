//TODO 正式包须更换
package com.top.drama.test

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.util.Log
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import android.content.Intent
import android.app.PendingIntent
import android.os.Build
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.graphics.Bitmap


class CustomNotificationHelper(private val context: Context) {

    companion object {
        const val TAG = "CustomNotificationHelper"
        const val CHANNEL_ID: String = "flareflow"
        const val SELECT_NOTIFICATION: String = "SELECT_NOTIFICATION"
        const val NOTIFICATION_ID: String = "notificationId"
        const val PAYLOAD: String = "payload"
    }

    // 默认通知栏
    fun showDefaultNotification(title: String, content: String, imagePath: String, payload: String, imageRes: String, canFlod: String, watchNowLabel: String) {
        // 创建折叠视图
        val collapsedView =
            RemoteViews(context.packageName, R.layout.custom_fold_notification_layout)
        collapsedView.setTextViewText(R.id.title_tv, title)
        collapsedView.setTextViewText(
            R.id.description_tv,
            content
        )
        var roundedBitmap: Bitmap? = null
        if (imagePath.isNotEmpty()) {
            roundedBitmap = ImageUtil.getRoundedCornerBitmapByPath(
                context,
                imagePath,
                R.drawable.ic_placeholder,
                Util.dp2px(context, 8f).toFloat() // 圆角半径
            )
            collapsedView.setBitmap(R.id.cover_img, "setImageBitmap", roundedBitmap)
        } else if (imageRes.isNotEmpty()) {
            val resId = Util.getResourceId(context, imageRes, "drawable")
            if (resId != 0) {
                roundedBitmap = ImageUtil.getRoundedCornerBitmap(
                    context,
                    resId,
                    Util.dp2px(context, 8f).toFloat() // 圆角半径
                )
                collapsedView.setBitmap(R.id.cover_img, "setImageBitmap", roundedBitmap)
            }
        }
        var expandedView: RemoteViews? = null
        if (canFlod == "true") {
            // 创建展开视图
            expandedView =
                RemoteViews(context.packageName, R.layout.custom_expand_notification_layout)
            expandedView.setTextViewText(R.id.title_tv, title)
            expandedView.setTextViewText(
                R.id.description_tv,
                content
            )
            expandedView.setBitmap(R.id.cover_img, "setImageBitmap", roundedBitmap)
            expandedView.setTextViewText(R.id.watch_now_tv, watchNowLabel)
        }

        val notificationId = System.currentTimeMillis().hashCode()
        val intent: Intent? = getLaunchIntent(context)?.apply {
            setAction(SELECT_NOTIFICATION)
            putExtra(NOTIFICATION_ID, notificationId)
            putExtra(PAYLOAD, payload)
        }
        var flags = PendingIntent.FLAG_UPDATE_CURRENT
        if (VERSION.SDK_INT >= VERSION_CODES.M) {
            flags = flags or PendingIntent.FLAG_IMMUTABLE
        }
        val pendingIntent = PendingIntent.getActivity(context, notificationId, intent, flags)

        // 构建通知
        val builder: NotificationCompat.Builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification) // 请确保有这个图标资源
            .setCustomContentView(collapsedView)
            .setStyle(NotificationCompat.DecoratedCustomViewStyle())
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
        if (canFlod == "true") {
            builder.setCustomBigContentView(expandedView)
        }

        // 显示通知
        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(notificationId, builder.build())
    }

    // 常驻通知栏
    fun showPermanentNotification(title: String, content: String, imagePath: String, payload: String) {
        // 创建折叠视图
        val collapsedView =
            RemoteViews(context.packageName, R.layout.custom_permanent_notification_layout)
        collapsedView.setTextViewText(R.id.title_tv, title)
        collapsedView.setTextViewText(
            R.id.description_tv,
            content
        )
        collapsedView.setImageViewResource(R.id.play_img, R.drawable.ic_play)
        val roundedBitmap = ImageUtil.getRoundedCornerBitmapByPath(
            context,
            imagePath,
            R.drawable.ic_placeholder,
            Util.dp2px(context, 2f).toFloat() // 圆角半径
        )
        collapsedView.setBitmap(R.id.cover_img, "setImageBitmap", roundedBitmap)

        val notificationId = 1001
        val intent: Intent? = getLaunchIntent(context)?.apply {
            setAction(SELECT_NOTIFICATION)
            putExtra(NOTIFICATION_ID, notificationId)
            putExtra(PAYLOAD, payload)
        }
        var flags = PendingIntent.FLAG_UPDATE_CURRENT
        if (VERSION.SDK_INT >= VERSION_CODES.M) {
            flags = flags or PendingIntent.FLAG_IMMUTABLE
        }
        val pendingIntent = PendingIntent.getActivity(context, notificationId, intent, flags)

        // 构建通知
        val builder: NotificationCompat.Builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification) // 请确保有这个图标资源
            .setCustomContentView(collapsedView)
            .setStyle(NotificationCompat.DecoratedCustomViewStyle())
            .setPriority(NotificationCompat.PRIORITY_MIN)
            .setContentIntent(pendingIntent)
            .setSilent(true)
            .setAutoCancel(false)
            .setOngoing(true)
        // 显示通知
        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(notificationId, builder.build())
    }

    // 全屏通知栏
    fun showFsiNotification(title: String, content: String, imagePath: String, payload: String, watchNowLabel: String, closeLabel: String) {
        try {
            // 创建折叠视图
            val collapsedView =
                RemoteViews(context.packageName, R.layout.custom_fold_notification_layout)
            collapsedView.setTextViewText(R.id.title_tv, title)
            collapsedView.setTextViewText(
                R.id.description_tv,
                content
            )
            var roundedBitmap = ImageUtil.getRoundedCornerBitmapByPath(
                context,
                imagePath,
                R.drawable.ic_placeholder,
                Util.dp2px(context, 8f).toFloat() // 圆角半径
            )
            collapsedView.setBitmap(R.id.cover_img, "setImageBitmap", roundedBitmap)
            // 创建展开视图
            var expandedView =
                RemoteViews(context.packageName, R.layout.custom_expand_notification_layout)
            expandedView.setTextViewText(R.id.title_tv, title)
            expandedView.setTextViewText(
                R.id.description_tv,
                content
            )
            expandedView.setBitmap(R.id.cover_img, "setImageBitmap", roundedBitmap)
            expandedView.setTextViewText(R.id.watch_now_tv, watchNowLabel)

            val notificationId = System.currentTimeMillis().hashCode()
            // 创建全屏Intent
            val fullScreenIntent =
                android.content.Intent(context, FullScreenActivity::class.java).apply {
                    putExtra(NOTIFICATION_ID, notificationId)
                    putExtra(PAYLOAD, payload)
                    putExtra("title", title)
                    putExtra("content", content)
                    putExtra("imagePath", imagePath)
                    putExtra("watchNowLabel", watchNowLabel)
                    putExtra("closeLabel", closeLabel)
                }

            val fullScreenPendingIntent = android.app.PendingIntent.getActivity(
                context,
                0,
                fullScreenIntent,
                android.app.PendingIntent.FLAG_IMMUTABLE or android.app.PendingIntent.FLAG_UPDATE_CURRENT
            )

            val intent: Intent? = getLaunchIntent(context)?.apply {
                setAction(SELECT_NOTIFICATION)
                putExtra(NOTIFICATION_ID, notificationId)
                putExtra(PAYLOAD, payload)
            }
            var flags = PendingIntent.FLAG_UPDATE_CURRENT
            if (VERSION.SDK_INT >= VERSION_CODES.M) {
                flags = flags or PendingIntent.FLAG_IMMUTABLE
            }
            val pendingIntent = PendingIntent.getActivity(context, notificationId, intent, flags)

            // 构建通知
            val builder: NotificationCompat.Builder =
                NotificationCompat.Builder(context, CHANNEL_ID)
                    .setSmallIcon(R.drawable.ic_notification) // 请确保有这个图标资源
                    .setCustomContentView(collapsedView)
                    .setCustomBigContentView(expandedView)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setCategory(NotificationCompat.CATEGORY_CALL) // 使用MESSAGE类别以提高优先级,展示activity触发应用回到前台
                    .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                    .setContentIntent(pendingIntent)
                    .setAutoCancel(true)
            if (Util.isKeyguardLocked(context)) {
                if (Util.isSamsung()) {
                    if (Util.isInteractive(context)) {
                        builder.setCategory(NotificationCompat.CATEGORY_CALL)
                        builder.setFullScreenIntent(createEmptyPendingIntent(context), true)
                    } else {
                        builder.setFullScreenIntent(fullScreenPendingIntent, true)
                    }
                } else {
                    builder.setFullScreenIntent(fullScreenPendingIntent, true)
                }
            } else {
                //没设置锁屏就只显示悬浮通知，避免展示activity触发应用回到前台
                builder.setCategory(NotificationCompat.CATEGORY_CALL)
                builder.setFullScreenIntent(createEmptyPendingIntent(context), true)
            }
            // 显示通知
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(notificationId, builder.build())
        } catch (error: Exception) {
            Log.e(TAG, error.message ?: "")
        }

    }

    private fun createEmptyPendingIntent(context: Context): PendingIntent {
        return PendingIntent.getActivity(
            context, System.currentTimeMillis().toInt(), Intent(), PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    private fun getLaunchIntent(context: Context): Intent? {
        val packageName = context.packageName
        val packageManager = context.packageManager
        return packageManager.getLaunchIntentForPackage(packageName)
    }
}
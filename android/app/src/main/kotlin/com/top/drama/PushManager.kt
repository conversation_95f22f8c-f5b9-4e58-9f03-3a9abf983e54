//TODO 正式包须更换
package com.top.drama.test

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log

class PushManager(private val context: Context, val action: (String?) -> Unit) {

    companion object {
        private const val TAG = "PushManager"
        private const val DEFAULT_PUSH_ALERT_INTERVAL = 5 * 60 * 1000L //ms
    }

    private var mRegisterBroadcastReceiver = false
    private var pushAction = ""
    private var pushAlertIntent: PendingIntent? = null
    private val mBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            Log.i(TAG, "onReceive -> action(${intent.action})")
            action(intent.action)
            if (pushAlertIntent != null) {
                AlarmManagerUtil.cancel(pushAlertIntent, context)
            }
            pushAlertIntent =
                AlarmManagerUtil.start(getPushAction(), DEFAULT_PUSH_ALERT_INTERVAL, context)
        }
    }

    private fun getPushAction(): String {
        if (pushAction.isEmpty()) {
            pushAction = context.packageName.plus(".pushAlert")
        }
        return pushAction
    }


    fun startAlert() {
        stopAlert()
        registerBroadcastReceiver()
        pushAlertIntent =
            AlarmManagerUtil.start(getPushAction(), DEFAULT_PUSH_ALERT_INTERVAL, context)
    }

    fun stopAlert() {
        unregisterBroadcastReceiver()
        if (pushAlertIntent != null) {
            AlarmManagerUtil.cancel(pushAlertIntent, context)
        }
        pushAlertIntent = null
    }

    private fun registerBroadcastReceiver() {
        if (!mRegisterBroadcastReceiver) {
            Log.i(TAG, "registerBroadcastReceiver")
            mRegisterBroadcastReceiver = true
            val intentFilter = IntentFilter().apply {
                this.addAction(getPushAction())
                this.addAction(Intent.ACTION_SCREEN_ON)
                this.addAction(Intent.ACTION_TIME_CHANGED)
            }
            try {
                if (Util.isBelow13()) {
                    context.registerReceiver(mBroadcastReceiver, intentFilter)
                } else {
                    context.registerReceiver(
                        mBroadcastReceiver, intentFilter, Context.RECEIVER_EXPORTED
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "registerBroadcastReceiver failed -> ${e.message}")
                mRegisterBroadcastReceiver = false
            }
        }
    }

    private fun unregisterBroadcastReceiver() {
        if(mRegisterBroadcastReceiver){
            mRegisterBroadcastReceiver = false
            Log.i(TAG, "unregisterBroadcastReceiver")
            try {
                context.unregisterReceiver(mBroadcastReceiver)
            } catch (e: Exception) {
                Log.e(TAG, "unregisterBroadcastReceiver failed -> ${e.message}")
            }
        }
    }

}
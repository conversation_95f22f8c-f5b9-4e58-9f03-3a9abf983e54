//TODO 正式包须更换
package com.top.drama.test

import android.util.Log
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import androidx.core.graphics.createBitmap

object ImageUtil {

    fun getRoundedCornerBitmap(context: Context, resourceId: Int, cornerRadius: Float): Bitmap {
        val original = BitmapFactory.decodeResource(context.resources, resourceId)
        return getRoundedCornerBitmap(original, cornerRadius)
    }

    fun  getRoundedCornerBitmapByPath(context: Context, localImagePath: String, defaultRes:Int, cornerRadius: Float): Bitmap {
        // 从本地路径加载图片或使用默认图片
        val bitmap = if (localImagePath.isNotEmpty()) {
            try {
                BitmapFactory.decodeFile(localImagePath)
            } catch (e: Exception) {
                Log.e("ImageUtil", "加载本地图片失败: ${e.message}")
                BitmapFactory.decodeResource(context.resources, defaultRes)
            }
        } else {
            BitmapFactory.decodeResource(context.resources, defaultRes)
        }
        return getRoundedCornerBitmap(bitmap, cornerRadius)
    }

    fun getRoundedCornerBitmap(bitmap: Bitmap, cornerRadius: Float): Bitmap {
        val output = createBitmap(bitmap.width, bitmap.height)
        val canvas = Canvas(output)

        val paint = Paint().apply {
            isAntiAlias = true
            color = Color.BLACK
        }

        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)

        canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, paint)

        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
        canvas.drawBitmap(bitmap, rect, rect, paint)

        return output
    }

}
//TODO 正式包须更换
package com.top.drama.test

import android.content.Context
import android.app.Activity
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import android.app.PendingIntent
import android.content.Intent
import android.app.NotificationManager
import android.util.Log

class FullScreenActivity : Activity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置全屏显示和锁屏上显示
        window.addFlags(
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
        )
        // 设置窗口为透明
        window.setBackgroundDrawableResource(android.R.color.transparent)
        setContentView(R.layout.activity_full_screen)
        // 在视图加载完成后设置沉浸式模式
        window.decorView.post {
            setImmersiveMode()
        }

        // 获取传递的参数
        val notificationId = intent.getIntExtra(CustomNotificationHelper.NOTIFICATION_ID, -1)
        val payload = intent.getStringExtra(CustomNotificationHelper.PAYLOAD) ?: ""
        val title = intent.getStringExtra("title") ?: ""
        val content = intent.getStringExtra("content") ?: ""
        val imagePath = intent.getStringExtra("imagePath") ?: ""
        val watchNowLabel = intent.getStringExtra("watchNowLabel") ?: ""
        val closeLabel = intent.getStringExtra("closeLabel") ?: ""

        val coverImage = findViewById<ImageView>(R.id.cover_iv)
        val roundedBitmap = ImageUtil.getRoundedCornerBitmapByPath(
            this,
            imagePath,
            R.drawable.ic_placeholder,
            Util.dp2px(this, 2f).toFloat() // 圆角半径
        )
        coverImage.setImageBitmap(roundedBitmap)

        findViewById<TextView>(R.id.app_name_tv).text = getAppName(this)
        findViewById<TextView>(R.id.title_tv).text = title
        findViewById<TextView>(R.id.dec_tv).text = content
        findViewById<TextView>(R.id.watch_now_tv).text = watchNowLabel
        findViewById<TextView>(R.id.close_tv).text = closeLabel

        findViewById<TextView>(R.id.watch_now_tv).setOnClickListener {
            Util.cancelNotification(this,notificationId)
            val intent: Intent? = getLaunchIntent(this)?.apply {
                setAction(CustomNotificationHelper.SELECT_NOTIFICATION)
                putExtra(CustomNotificationHelper.NOTIFICATION_ID, notificationId)
                putExtra(CustomNotificationHelper.PAYLOAD, payload)
            }
            startActivity(intent)
            finish()
        }
        // 关闭按钮
        findViewById<TextView>(R.id.close_tv).setOnClickListener {
            Util.cancelNotification(this,notificationId)
            finish()
        }
        findViewById<ImageView>(R.id.close_iv).setOnClickListener {
            Util.cancelNotification(this,notificationId)
            finish()
        }
    }

    fun getAppName(context: Context): String {
        val packageManager = context.packageManager
        val applicationInfo = packageManager.getApplicationInfo(context.packageName, 0)
        return packageManager.getApplicationLabel(applicationInfo).toString()
    }

    private fun getLaunchIntent(context: Context): Intent? {
        val packageName = context.packageName
        val packageManager = context.packageManager
        return packageManager.getLaunchIntentForPackage(packageName)
    }

    private fun setImmersiveMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11及以上使用WindowInsetsController
            window.setDecorFitsSystemWindows(false)
            val controller = window.insetsController
            if (controller != null) {
                controller.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                controller.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        } else {
            // Android 11以下使用传统方法
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN)

            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
        }
    }

}
//TODO 正式包须更换
package com.top.drama.test
import android.content.Context
import android.util.TypedValue
import android.os.Build
import java.util.Locale
import android.app.KeyguardManager
import android.os.PowerManager
import android.app.NotificationManager
import android.telephony.TelephonyManager
import android.util.Log

object Util {

    /**
     * 将dp值转换为px值
     *
     * @param context 上下文
     * @param dpValue 需要转换的dp值
     * @return 转换后的px值
     */
    fun dp2px(context: Context, dpValue: Float): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dpValue,
            context.resources.displayMetrics
        ).toInt()
    }

    /**
     * 将资源名称字符串转换为资源ID
     * @param context 上下文
     * @param resourceName 资源名称，例如 "ic_launcher"
     * @param resourceType 资源类型，例如 "drawable", "string", "layout" 等
     * @param packageName 包名，默认为应用包名
     * @return 资源ID，如果找不到则返回0
     */
    fun getResourceId(
        context: Context,
        resourceName: String,
        resourceType: String,
        packageName: String? = context.packageName
    ): Int {
        return try {
            context.resources.getIdentifier(resourceName, resourceType, packageName)
        } catch (e: Exception) {
            e.printStackTrace()
            0
        }
    }

    fun isSamsung(): Boolean {
        return getManufacturer() == "samsung"
    }

    /**
     * 手机厂商
     */
    fun getManufacturer(): String {
        return Build.MANUFACTURER.lowercase(Locale.ENGLISH).trim()
    }

    /**
     * 是否锁屏
     */
    fun isKeyguardLocked(context: Context) : Boolean{
        val km = context.getSystemService(Context.KEYGUARD_SERVICE) as? KeyguardManager?
        return km?.isKeyguardLocked?:false
    }

    /**
     * 是否亮屏
     */
    fun isInteractive(context: Context) : Boolean{
        val pm = context.getSystemService(Context.POWER_SERVICE) as PowerManager?
        return pm?.isInteractive()?:false
    }

    /**
    * 关闭通知
    */
    fun cancelNotification(context: Context,id:Int){
        val notificationManager: NotificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancel(id)
    }

    fun isBelow13(): Boolean {
        return Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU
    }

    fun fetchNetworkOperator(context: Context) : String {
        var networkOperator = try {
            val tm =
                context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            val operator: String = tm.getSimOperator() // 获取SIM卡运营商代码

            tm.networkOperator
        } catch (ignore: Exception) {
            ""
        }
        Log.i("Util", "fetchNetworkOperator: ${networkOperator}")
        return networkOperator
    }

    fun fetchSimOperator(context: Context) : String {
        var simOperator = try {
            val tm =
                context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            val operator: String = tm.getSimOperator() // 获取SIM卡运营商代码
            tm.simOperator
        } catch (ignore: Exception) {
            ""
        }
        Log.i("Util", "fetchSimOperator: ${simOperator}")
        return simOperator
    }
}
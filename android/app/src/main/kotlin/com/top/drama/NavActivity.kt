// TODO 正式包须更换
package com.top.drama.test
// TODO 正式包须更换
import com.top.drama.test.MainActivity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.app.Activity

/**
 * 深度链接导航Activity - 负责接收深度链接并传递给MainActivity
 */
class NavActivity : Activity() {
    companion object {
        private const val TAG = "NavActivity"
        private const val KEY_DEEPLINK_URL = "deep_link_url"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "NavActivity启动")
        
        // 获取深度链接URL
        val deeplinkUrl = intent.data?.toString()
        
        if (deeplinkUrl.isNullOrEmpty()) {
            Log.e(TAG, "未获取到深度链接URL，关闭Activity")
            finish()
            return
        }
        
        Log.d(TAG, "获取到深度链接URL: $deeplinkUrl")
        
        // 启动MainActivity并传递深度链接URL
        startMainActivity(deeplinkUrl)
        
        // 结束NavActivity
        finish()
    }
    
    private fun startMainActivity(deeplinkUrl: String) {
        try {
            val intent = Intent(this, MainActivity::class.java)
            intent.putExtra(KEY_DEEPLINK_URL, deeplinkUrl)
            startActivity(intent)
            Log.d(TAG, "已启动MainActivity并传递深度链接URL")
        } catch (e: Exception) {
            Log.e(TAG, "启动MainActivity失败: ${e.message}")
        }
    }
} 
//TODO 正式包须更换
package com.top.drama.test

import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.database.ContentObserver
import android.telephony.TelephonyManager
import android.util.Log
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.EventChannel.EventSink
import io.flutter.plugin.common.EventChannel.StreamHandler
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.googlemobileads.GoogleMobileAdsPlugin
import java.lang.Exception
import java.util.regex.Pattern

class MainActivity : FlutterActivity() {
    companion object {
        private const val TAG = "MainActivity"
        private const val PUSH_ALERT_CHANNEL = "com.flareflow.android/pushAlert"
        private const val CHANNEL = "com.flareflow.android/deeplink"
        private const val LOG_CHANNEL = "com.flareflow.android/log"
        private const val DEVICE_CHANNEL = "com.flareflow.android/device"
        private const val NOTIFICATION_CHANNEL = "com.flareflow.android/notification"
        private const val BACKGROUND_CHANNEL = "com.flareflow.android/background"
        private const val ENGINE_ID = "app_engine"
        private const val KEY_SHORT_ID = "shortid"
        private const val KEY_DEEPLINK_DATA = "deeplink_data"
        private const val KEY_DEEPLINK_URL = "deep_link_url"
        private const val SYSTEM_CHANNEL = "system_settings"
        private const val ROTATION_CHANNEL = "system_rotation_channel"
    }

    private var rotationChannel : EventChannel? = null
    private var systemChannel: MethodChannel? = null
    private var pushAlertChannel : EventChannel? = null
    private var methodChannel: MethodChannel? = null
    private var logMethodChannel: MethodChannel? = null
    private var deviceMethodChannel: MethodChannel? = null
    private var notificationMethodChannel: MethodChannel? = null
    private var backgroundMethodChannel: MethodChannel? = null
    private var flutterChannelReady = false
    private var retryCount = 0
    private val MAX_RETRY_COUNT = 4
    private val RETRY_DELAY_MS = 500L
    private var customNotificationHelper: CustomNotificationHelper? = null
    private val ADMODEL_FACTORY_ID = "CustomTemplateView"

    private var pushManager: PushManager? = null
    private var eventSink: EventSink? = null
    private var handler: Handler? = null
    private var contentObserver: ContentObserver? = null

    /**
     * 检查Flutter引擎是否就绪
     * @return Boolean 返回引擎是否就绪
     */
    private fun isFlutterEngineReady(): Boolean {
        return rotationChannel != null && systemChannel != null && pushAlertChannel !=null && notificationMethodChannel != null && logMethodChannel != null && methodChannel != null && deviceMethodChannel != null && backgroundMethodChannel != null && flutterChannelReady
    }


    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        flutterEngine.getPlugins().add(GoogleMobileAdsPlugin())
        super.configureFlutterEngine(flutterEngine)

        // 注册原生广告
        GoogleMobileAdsPlugin.registerNativeAdFactory(
            flutterEngine,
            ADMODEL_FACTORY_ID, FlareNativeAdFactory(layoutInflater)
        );

        // 缓存引擎供NavActivity使用
        FlutterEngineCache.getInstance().put(ENGINE_ID, flutterEngine)
        Log.d(TAG, "引擎已缓存，ID: $ENGINE_ID")

        pushAlertChannel = EventChannel(flutterEngine.dartExecutor.binaryMessenger, PUSH_ALERT_CHANNEL)
        pushAlertChannel?.setStreamHandler(
            object : StreamHandler {
                override fun onListen(arguments: Any?, events: EventSink) {
                    eventSink = events
                    Log.d(TAG, "pushAlertChannel onListen called")
                }
                override fun onCancel(arguments: Any?) {
                    Log.w(TAG, "pushAlertChannel onCancel called")
                    eventSink = null
                }
            })

        // 设置方法通道
        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)

        // 设置方法通道处理程序
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "flutterChannelReady" -> {
                    Log.d(TAG, "收到Flutter通道就绪通知")
                    flutterChannelReady = true
                    result.success(true)

                    // 处理Intent中的深度链接
                    processIntent(intent)
                }

                else -> result.notImplemented()
            }
        }

        // 设置日志方法通道
        logMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, LOG_CHANNEL)

        // 设置方法通道处理程序
        logMethodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "logToLogcat" -> {
                    val tag = call.argument<String>("tag") ?: "Flutter"
                    val message = call.argument<String>("message") ?: ""
                    val level = call.argument<String>("level") ?: ""
                    if (level == "info") {
                        Log.i(tag, message)
                    } else if (level == "warn") {
                        Log.w(tag, message)
                    } else if (level == "error") {
                        Log.e(tag, message)
                    } else {
                        Log.d(tag, message)
                    }
                    result.success(null)
                }

                else -> result.notImplemented()
            }
        }

        customNotificationHelper = CustomNotificationHelper(this)
        // 设置通知方法通道
        notificationMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, NOTIFICATION_CHANNEL)

        // 设置通知通道处理程序
        notificationMethodChannel?.setMethodCallHandler { call, result ->
            try {
                when (call.method) {
                    "notification" -> {
                        val type = call.argument<String>("type") ?: ""
                        val title = call.argument<String>("title") ?: ""
                        val body = call.argument<String>("body") ?: ""
                        val imagePath = call.argument<String>("imagePath") ?: ""
                        val payload = call.argument<String>("payload") ?: ""
                        val imageRes = call.argument<String>("imageRes") ?: ""
                        val canFlod = call.argument<String>("canFlod") ?: ""
                        val watchNowLabel = call.argument<String>("watchNowLabel") ?: ""
                        val closeLabel = call.argument<String>("closeLabel") ?: ""
                        Log.e(TAG, "notification")
                        // 默认通知栏
                        if (type == "default") {
                            //常驻通知栏
                            customNotificationHelper?.showDefaultNotification(title, body, imagePath, payload, imageRes, canFlod, watchNowLabel)
                        } else if (type == "permanent") {
                            customNotificationHelper?.showPermanentNotification(title, body, imagePath, payload)
                            //全屏通知栏
                        } else if (type == "fsi") {
                            customNotificationHelper?.showFsiNotification(title, body, imagePath, payload, watchNowLabel, closeLabel)
                        }
                        result.success(null)
                    }

                    "isInteractive" -> {
                        result.success(Util.isInteractive(this))
                    }

                    "isKeyguardLocked" -> {
                        result.success(Util.isKeyguardLocked(this))
                    }

                    "canUseFullScreen" -> {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                            val notificationManager: NotificationManager =
                                applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                            val permissionGranted: Boolean = notificationManager.canUseFullScreenIntent()
                            result.success(permissionGranted)
                        } else {
                            result.success(true);
                        }
                    }

                    "startAlert" -> {
                        pushManager?.startAlert()
                    }

                    "stopAlert" -> {
                        pushManager?.stopAlert()
                    }

                    else -> result.notImplemented()
                }
            } catch (error: Exception) {
                // 捕获异常并记录，防止应用崩溃
                Log.e(TAG, "Error in notificationMethodChannel: ${error.message}")
                error.printStackTrace()
            }
        }
        // 设置设备方法通道
        deviceMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, DEVICE_CHANNEL)

        // 设置方法通道处理程序
        deviceMethodChannel?.setMethodCallHandler { call, result ->
            try {
                when (call.method) {
                    "mcc" -> {
                        var operator = Util.fetchSimOperator(this)
                        if(operator.isNullOrEmpty()){
                            operator = Util.fetchNetworkOperator(this)
                        }
                        var mcc: String = operator.toString()
                        if (mcc.length > 3) {
                            // 取前三位，就是mcc
                            mcc = mcc.substring(0, 3)
                        } else {
                            mcc = ""
                        }
                        result.success(mcc)
                    }

                    else -> result.notImplemented()
                }
            } catch (e: Exception) {
                Log.e(TAG, "设备方法通道: ${e.message}")
            }

        }


        // 设置返回键方法通道
        backgroundMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, BACKGROUND_CHANNEL)

        // 设置返回键通道处理程序
        backgroundMethodChannel?.setMethodCallHandler { call, result ->
            try {
               when (call.method) {
                "moveToBackground" -> {
                    moveTaskToBack(true)
                    result.success(true)
                }
                else -> result.notImplemented()
            }

            } catch (e: Exception) {
                Log.e(TAG, "返回键方法通道: ${e.message}")
            }
          }

        // 设置系统设置方法通道
        systemChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SYSTEM_CHANNEL)

        // 设置系统设置通道处理程序
        systemChannel?.setMethodCallHandler { call, result ->
            try {
                when (call.method) {
                    "getRotationStatus" -> result.success(getRotationStatus())
                    else -> result.notImplemented()
                }

            } catch (e: Exception) {
                Log.e(TAG, "系统设置方法通道: ${e.message}")
            }
        }

        rotationChannel = EventChannel(flutterEngine.dartExecutor.binaryMessenger, ROTATION_CHANNEL)
        rotationChannel?.setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(args: Any?, events: EventChannel.EventSink) {
                    contentObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
                        override fun onChange(selfChange: Boolean) {
                            events.success(getRotationStatus())
                        }
                    }
                    contentResolver.registerContentObserver(
                        Settings.System.getUriFor(Settings.System.ACCELEROMETER_ROTATION),
                        true,
                        contentObserver!!
                    )
                }

                override fun onCancel(args: Any?) {
                    contentResolver.unregisterContentObserver(contentObserver!!)
                    contentObserver = null
                }
            }
        )

    }

    private fun getRotationStatus(): Boolean {
        return Settings.System.getInt(
            contentResolver,
            Settings.System.ACCELEROMETER_ROTATION, 0
        ) == 1
    }

    override fun cleanUpFlutterEngine(flutterEngine: FlutterEngine) {
        GoogleMobileAdsPlugin.unregisterNativeAdFactory(flutterEngine, ADMODEL_FACTORY_ID)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "MainActivity.onCreate()")
        handler = Handler(mainLooper)
        pushManager = PushManager(this) { action ->
            // 获取push
            try{
                handler?.post{
                    Log.i(TAG, "eventSink 发送通知 checkPush")
                    eventSink?.success("checkPush")
                }
            }catch (e: Exception) {
                Log.e(TAG, "eventSink 发送通知: ${e.message}")
            }
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "MainActivity.onDestroy()")
        try {
            // 先清除MethodCallHandler
            methodChannel?.setMethodCallHandler(null)
            logMethodChannel?.setMethodCallHandler(null)
            deviceMethodChannel?.setMethodCallHandler(null)
            notificationMethodChannel?.setMethodCallHandler(null)
            pushAlertChannel?.setStreamHandler(null)
            backgroundMethodChannel?.setMethodCallHandler(null)
            systemChannel?.setMethodCallHandler(null)
            rotationChannel?.setStreamHandler(null)
            // 再设置为null
            methodChannel = null
            logMethodChannel = null
            deviceMethodChannel = null
            notificationMethodChannel = null
            pushAlertChannel = null
            backgroundMethodChannel = null
            systemChannel = null
            rotationChannel = null
            flutterChannelReady = false

            // 最后调用父类的onDestroy
            super.onDestroy()
        } catch (e: Exception) {
            // 捕获异常并记录，防止应用崩溃
            Log.e(TAG, "Error in onDestroy: ${e.message}")
            e.printStackTrace()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(TAG, "MainActivity.onNewIntent()")
        setIntent(intent)
        processIntent(intent)
    }

    private fun processIntent(intent: Intent?) {
        if (intent == null) return

        if (!isFlutterEngineReady()) {
            if (retryCount < MAX_RETRY_COUNT) {
                retryCount++
                Log.d(TAG, "Flutter引擎未就绪，等待重试 (第 $retryCount 次)")
                Handler(android.os.Looper.getMainLooper()).postDelayed({
                    processIntent(intent)
                }, RETRY_DELAY_MS)
                return
            } else {
                Log.e(TAG, "Flutter引擎未就绪，已达到最大重试次数")
                retryCount = 0
                return
            }
        }

        retryCount = 0 // 重置重试计数

        // 1. 检查是否有从NavActivity传递的深度链接URL
        val deeplinkUrl = intent.getStringExtra(KEY_DEEPLINK_URL)
        if (!deeplinkUrl.isNullOrEmpty()) {
            Log.d(TAG, "收到从NavActivity传递的深度链接URL: $deeplinkUrl")
            parseDeepLink(deeplinkUrl)
            return
        }

        // 检查Intent Extra中的shortId和deeplink_data
        val shortId = intent.getStringExtra(KEY_SHORT_ID)
        val deeplinkData = intent.getStringExtra(KEY_DEEPLINK_DATA)

        if (!shortId.isNullOrEmpty() && isFlutterEngineReady()) {
            Log.d(TAG, "从Intent Extra获取到shortId: $shortId")
            sendShortIdToFlutter(shortId)
        }

        if (!deeplinkData.isNullOrEmpty() && isFlutterEngineReady()) {
            Log.d(TAG, "从Intent Extra获取到deeplink_data")
            sendDeeplinkDataToFlutter(deeplinkData)
        }
    }

    // 解析深度链接URL
    private fun parseDeepLink(url: String) {
        Log.d(TAG, "解析深度链接: $url")

        try {
            // 将字符串转换为android.net.Uri对象
            val uri = android.net.Uri.parse(url)

            // 提取shortId
            var extractedShortId = extractShortId(uri, url)

            // 发送解析后的数据到Flutter
            if (!extractedShortId.isNullOrEmpty() && isFlutterEngineReady()) {
                sendShortIdToFlutter(extractedShortId)
            }

            // 直接使用整个URL作为deeplink_data
            sendDeeplinkDataToFlutter(url)

        } catch (e: Exception) {
            Log.e(TAG, "解析深度链接失败: ${e.message}")
        }
    }

    // 提取shortId的方法
    private fun extractShortId(uri: android.net.Uri, originalUrl: String): String? {
        // 1. 从查询参数中获取shortId
        var shortId = uri.getQueryParameter(KEY_SHORT_ID)
        if (!shortId.isNullOrEmpty()) {
            Log.d(TAG, "从查询参数中获取到shortId: $shortId")
            return shortId
        }

        // 2. 尝试从campaign_name参数中提取shortId
        val campaignName = uri.getQueryParameter("campaign_name")
        if (!campaignName.isNullOrEmpty()) {
            val pattern = Pattern.compile("shortid-(\\d+)")
            val matcher = pattern.matcher(campaignName)
            if (matcher.find()) {
                shortId = matcher.group(1)
                Log.d(TAG, "从campaign_name参数中提取到shortId: $shortId")
                return shortId
            }
        }

        // 3. 如果URL中包含shortid字样，尝试提取
        val urlPattern = Pattern.compile("[?&]shortid[=-](\\d+)")
        val urlMatcher = urlPattern.matcher(originalUrl)
        if (urlMatcher.find()) {
            shortId = urlMatcher.group(1)
            Log.d(TAG, "从URL中提取到shortId: $shortId")
            return shortId
        }

        return null
    }

    // 发送deeplink_data到Flutter
    private fun sendDeeplinkDataToFlutter(data: String) {
        try {
            Log.d(TAG, "发送deeplink_data到Flutter")
            methodChannel?.invokeMethod("setDeeplinkData", data, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "deeplink_data发送成功")
                    // 发送成功后清除Intent中的deeplink_data
                    intent?.removeExtra(KEY_DEEPLINK_DATA)
                    intent?.removeExtra(KEY_DEEPLINK_URL)
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "deeplink_data发送失败: $errorCode - $errorMessage")
                }

                override fun notImplemented() {
                    Log.e(TAG, "Flutter端未实现setDeeplinkData方法")
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "发送deeplink_data到Flutter时出错: ${e.message}")
        }
    }

    private fun sendShortIdToFlutter(shortId: String) {
        try {
            Log.d(TAG, "发送shortId到Flutter: $shortId")
            methodChannel?.invokeMethod("setDeepLinkShortPlayId", shortId, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "shortId发送成功")
                    // 发送成功后清除Intent中的shortId
                    intent?.removeExtra(KEY_SHORT_ID)
                    intent?.removeExtra(KEY_DEEPLINK_URL)
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "shortId发送失败: $errorCode - $errorMessage")
                }

                override fun notImplemented() {
                    Log.e(TAG, "Flutter端未实现setDeepLinkShortPlayId方法")
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "发送shortId到Flutter时出错: ${e.message}")
        }
    }
}


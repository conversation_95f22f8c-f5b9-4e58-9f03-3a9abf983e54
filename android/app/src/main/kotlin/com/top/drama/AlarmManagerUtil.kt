//TODO 正式包须更换
package com.top.drama.test

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.SystemClock
import android.util.Log

object AlarmManagerUtil {

    private const val TAG = "AlarmManagerUtil"

    fun start(action: String, interval: Long, context:Context): PendingIntent? {
        val intent = Intent(action).apply {
            this.`package` = context.packageName
        }
        val pendingIntent = PendingIntent.getBroadcast(
            context, 0, intent, PendingIntent.FLAG_IMMUTABLE
        )
        val triggerTime = SystemClock.elapsedRealtime() + interval
        try {
            if (isSetExactAndAllowWhileIdleEnable(context)) {
                Log.i(TAG, "start -> isSetExactAndAllowWhileIdleEnable")
                getAlarmManager(context)?.setExactAndAllowWhileIdle(
                    AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    triggerTime, pendingIntent
                )
            } else {
                Log.i(TAG, "start -> setAndAllowWhileIdle")
                getAlarmManager(context)?.setAndAllowWhileIdle(
                    AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    triggerTime, pendingIntent
                )
            }
            Log.i(TAG, "start -> action($action) interval(${interval}ms)")
        } catch (e: Exception) {
            Log.e(TAG, "start exception -> ${e.message}")
            return null
        }
        return pendingIntent
    }

    fun cancel(intent: PendingIntent?,context: Context) {
        try {
            if (intent != null) {
                getAlarmManager(context)?.cancel(intent)
                Log.i(TAG, "cancel -> $intent")
            }
        } catch (e: Exception) {
            Log.e(TAG, "cancel exception -> ${e.message}")
        }
    }

    private fun isSetExactAndAllowWhileIdleEnable(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            getAlarmManager(context)?.canScheduleExactAlarms() ?: false
        } else {
            true
        }
    }

    private fun getAlarmManager(context: Context): AlarmManager? {
        return context.getSystemService(Context.ALARM_SERVICE) as? AlarmManager
    }
}
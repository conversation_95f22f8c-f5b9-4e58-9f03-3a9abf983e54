<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:padding="16dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/full_screen_notification_bg"
            android:paddingHorizontal="14dp"
            android:paddingVertical="10dp">

            <ImageView
                android:id="@+id/icon_iv"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:background="@drawable/cover_bg"
                android:clipToOutline="true"
                android:src="@drawable/ic_notification" />

            <TextView
                android:id="@+id/app_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/icon_iv"
                android:layout_alignBottom="@id/icon_iv"
                android:layout_marginStart="6dp"
                android:layout_toEndOf="@id/icon_iv"
                android:gravity="center_vertical"
                android:text="Flareflow"
                android:textColor="#000000"
                android:textSize="13sp" />

            <ImageView
                android:id="@+id/close_iv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignTop="@id/icon_iv"
                android:layout_alignBottom="@id/icon_iv"
                android:layout_alignParentEnd="true"
                android:src="@drawable/ic_close" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cover_cv"
                android:layout_width="91dp"
                android:layout_height="120dp"
                android:layout_below="@id/icon_iv"
                android:layout_marginTop="12dp"
                app:cardCornerRadius="4dp">

                <ImageView
                    android:id="@+id/cover_iv"
                    android:layout_width="91dp"
                    android:layout_height="120dp" />
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:id="@+id/short_content_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/cover_cv"
                android:layout_alignBottom="@id/cover_cv"
                android:layout_marginStart="12dp"
                android:layout_toEndOf="@id/cover_cv"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/title_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="#121212"
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/dec_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="4"
                    android:textColor="#121212"
                    android:textSize="13sp" />
            </LinearLayout>


        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center"
            android:background="@drawable/full_screen_notification_watch_now_bg"
            android:layout_marginTop="12dp">

            <TextView
                android:id="@+id/watch_now_tv"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:drawablePadding="7dp"
                android:drawableStart="@drawable/ic_small_play"
                android:gravity="center"
                android:textColor="#FFFFFF"
                android:textSize="16sp" />

        </LinearLayout>

        <TextView
            android:id="@+id/close_tv"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/full_screen_notification_close_bg"
            android:gravity="center"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

    </LinearLayout>
</RelativeLayout>
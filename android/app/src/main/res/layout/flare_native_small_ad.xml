<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/small_ad_bg"
        android:minHeight="108dp"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/ad_app_icon"
                android:layout_width="86dp"
                android:layout_height="86dp"
                android:layout_marginEnd="10dp"
                android:adjustViewBounds="true" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#ffffff"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:id="@+id/ad_iv"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/ic_ad" />

                    <TextView
                        android:id="@+id/ad_store"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:textColor="#ffffff"
                        android:textSize="12sp" />
                </LinearLayout>

                <Button
                    android:id="@+id/ad_call_to_action"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/small_ad_action_bg"
                    android:gravity="center"
                    android:textColor="#ffffff"
                    android:textSize="13sp" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</com.google.android.gms.ads.nativead.NativeAdView>
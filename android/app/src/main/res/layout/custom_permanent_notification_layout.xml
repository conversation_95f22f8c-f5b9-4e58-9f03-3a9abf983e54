<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/cover_img"
        android:layout_width="50dp"
        android:layout_height="67dp"
        android:scaleType="centerCrop" />

    <ImageView
        android:id="@+id/play_img"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_play" />

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="12dp"
        android:layout_toStartOf="@id/play_img"
        android:layout_toEndOf="@id/cover_img"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#000000"
        android:textSize="14sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/description_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_tv"
        android:layout_marginStart="12dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="12dp"
        android:layout_toStartOf="@id/play_img"
        android:layout_toEndOf="@id/cover_img"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="#020202"
        android:textSize="12sp" />
</RelativeLayout>

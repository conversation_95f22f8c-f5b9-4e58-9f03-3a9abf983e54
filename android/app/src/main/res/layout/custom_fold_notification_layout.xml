<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/cover_img"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_alignParentEnd="true"
        android:background="@drawable/cover_bg"
        android:clipToOutline="true"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="12dp"
        android:layout_toStartOf="@id/cover_img"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#000000"
        android:textSize="14sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/description_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_tv"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="12dp"
        android:layout_toStartOf="@id/cover_img"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#020202"
        android:textSize="12sp" />
</RelativeLayout>

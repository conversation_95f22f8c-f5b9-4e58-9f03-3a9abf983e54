<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal">

    <androidx.cardview.widget.CardView
        android:id="@+id/cover_cv"
        android:layout_width="91dp"
        android:layout_height="120dp"
        app:cardCornerRadius="4dp">
        <ImageView
            android:id="@+id/cover_img"
            android:layout_width="91dp"
            android:layout_height="120dp"
            android:scaleType="centerCrop" />
    </androidx.cardview.widget.CardView>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/cover_cv"
        android:layout_alignBottom="@id/cover_cv"
        android:layout_marginStart="12dp"
        android:layout_toEndOf="@id/cover_cv"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="#000000"
            android:textSize="15sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/description_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="4"
            android:textColor="#121212"
            android:textSize="13sp" />
    </LinearLayout>


</RelativeLayout>

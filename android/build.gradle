buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.1'
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9' // 推荐使用最新稳定版
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.deeplink.dev/repository/maven-releases/' }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

project('::isar_flutter_libs') {
    afterEvaluate {
        android {
            namespace 'dev.isar.isar_flutter_libs'
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

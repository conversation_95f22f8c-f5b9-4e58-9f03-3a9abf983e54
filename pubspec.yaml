name: playlet
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.0+12

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any

  get: ^4.6.6 # 架构
  get_storage: ^2.1.1 # 本地存储
  dio: ^5.7.0 # 网络请求
  encrypt: ^5.0.3 # 加密
  device_info_plus: ^11.0.0 # 设备信息
  webview_flutter: ^4.8.0 # 网页
  cached_network_image: ^3.4.1 # 网络图片
  flutter_native_splash: ^2.4.4 # 启动屏
  carousel_slider: ^5.0.0 # 轮播图
  flutter_screenutil: ^5.9.3 # 屏幕适配
  infinite_carousel: ^1.1.1 # 无限轮播
  sliding_up_panel: ^2.0.0+1 # 上拉加载
  path_provider: ^2.1.4 # 路径
  wakelock_plus: ^1.2.8 # 屏幕常亮
  easy_refresh: ^3.4.0 # 下拉刷新
  fluttertoast: ^8.2.8 # 消息提示
  in_app_purchase: ^3.2.0 # 内购
  package_info_plus: ^8.1.0 # 包信息
  share_plus: ^10.1.3 # 分享
  toastification: ^2.3.0 # 消息提示
  slide_countdown: ^2.0.2 # 倒计时
  mobile_device_identifier: ^0.0.3 # 设备唯一标识
  event_bus: ^2.0.1 # 事件总线
  debounce_throttle: ^2.0.0 # 防抖节流
  screen_capture_restrictions: ^1.1.0 # 禁止截图
  url_launcher: ^6.3.1 # 打开链接
  google_sign_in: ^6.2.2 # 谷歌登录
  flutter_facebook_auth: ^7.1.1 # facebook登录
  firebase_core: ^3.11.0 # firebase
  firebase_auth: ^5.4.2 # firebase
  firebase_messaging: ^15.2.5 # firebase
  firebase_analytics: ^11.4.5 # firebase
  firebase_crashlytics: ^4.3.5 # firebase
  flutter_smart_dialog: ^4.9.8+6 # 弹窗
  lottie: ^3.3.1 # 动画
  lexo_ttplayer_decryption: ^1.0.8 # 解密播放器
  deeplink_dev: ^2.0.9 #归因
  dlink_fingerprint: ^3.0.4 # 指纹
  dlink_analytics: ^1.0.0 # 埋点
  isar: ^3.1.0+1 # 数据库
  isar_flutter_libs: ^3.1.0+1 # 数据库
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  logger: 2.5.0
  flutter_staggered_grid_view: 0.7.0
  palette_generator: 0.3.3+6 # 获取图片主色
  advertising_id: ^2.7.1 # 广告id
  connectivity_plus: ^6.1.3 # 网络
  vpn_connection_detector: ^1.0.9 # vpn
  safe_device: ^1.2.1 #设备状态
  uuid: ^4.5.1 # uuid
  permission_handler: 11.4.0 # 权限
  flutter_local_notifications: 19.0.0 #通知栏
  google_mobile_ads: 5.3.1
  gma_mediation_meta: 1.1.0
  pretty_dio_logger: ^1.4.0 #网络请求日志打印
  appsflyer_sdk: 6.12.2
  flutter_timezone: ^4.1.0
  app_tracking_transparency: ^2.0.6+1
  mmkv: 2.1.1
  audio_session: 0.2.1
  expandable: ^5.0.1 # 展开收起
  super_tooltip: ^2.0.9
  in_app_review: 2.0.10 #评分
  vector_math: 2.1.4
  visibility_detector: 0.4.0+2
  leak_detector:
    path: packages/leak_detector
  pin_code_fields: ^8.0.1 #验证码输入
  flag: ^7.0.0 #国家冠码映射
  flutter_inappwebview: 6.1.5
  device_orientation: 1.0.0
  live_activities: 2.3.2
  
dev_dependencies:
  flutter_test:
    sdk: flutter

  isar_generator: ^3.1.0+1
  flutter_gen: ^5.9.0
  build_runner: any
  flutter_launcher_icons: ^0.13.1 # 启动图标
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  lint: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_gen:
  output: lib/gen/

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/back_icon/
    - assets/empty/
    - assets/loading/
    - assets/tabbar/
    - assets/home/
    - assets/mylist/
    - assets/notification/
    - assets/profile/
    - assets/login/
    - assets/upgrade/
    - assets/video/
    - assets/store/
    - assets/rewards/
    - assets/language/
    - assets/details/
    - assets/details/advance_watch_ad/
    - assets/recommend/
    - assets/rewards_bind/
    - assets/good_review/
    - assets/fullscreen/
    - assets/resourcebit/
    - assets/live_activity/
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/poppins/Poppins-Black-900.ttf
        - asset: assets/fonts/poppins/Poppins-Bold-700.ttf
        - asset: assets/fonts/poppins/Poppins-ExtraBold-800.ttf
        - asset: assets/fonts/poppins/Poppins-ExtraBoldItalic-Italic800.ttf
        - asset: assets/fonts/poppins/Poppins-Light-300.ttf
        - asset: assets/fonts/poppins/Poppins-Medium-500.ttf
        - asset: assets/fonts/poppins/Poppins-Regular-400.ttf
        - asset: assets/fonts/poppins/Poppins-SemiBold-600.ttf
        - asset: assets/fonts/poppins/Poppins-MediumItalic-Italic500.ttf

  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cba95714fccd2c2f52fae865a00295a4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RangersAppLog/RangersAppLog-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RangersAppLog/RangersAppLog-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/RangersAppLog/RangersAppLog.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "RangersAppLog", "PRODUCT_NAME": "RangersAppLog", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986796dc861abc73d782611e2d52fe5098", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f32a9fcef766dbe1f22ccff6aafe692", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RangersAppLog/RangersAppLog-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RangersAppLog/RangersAppLog-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/RangersAppLog/RangersAppLog.modulemap", "PRODUCT_MODULE_NAME": "RangersAppLog", "PRODUCT_NAME": "RangersAppLog", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fb0e71ccb83f7cd0545ba6ac4c1ea55a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f32a9fcef766dbe1f22ccff6aafe692", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RangersAppLog/RangersAppLog-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RangersAppLog/RangersAppLog-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/RangersAppLog/RangersAppLog.modulemap", "PRODUCT_MODULE_NAME": "RangersAppLog", "PRODUCT_NAME": "RangersAppLog", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983a66ca1d2ce6fae3af7a163d627e3c95", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981c6902ccbf789feb497135383dfc6ef8", "guid": "bfdfe7dc352907fc980b868725387e985422c976a24606213d9a3b764892cedc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983881e541c6175ab24ad65585a18b2781", "guid": "bfdfe7dc352907fc980b868725387e98896c3260571a8c44457ed9e08f4aee9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a3f01e05f1e35398fb3f84ef6864217", "guid": "bfdfe7dc352907fc980b868725387e98e20923bc731cc88b2eb78a727689823c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8767d58238647182528bb381869b96f", "guid": "bfdfe7dc352907fc980b868725387e9846973521252fbe49a1e18d2eaf717f99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bbc74b5c1aea5bfd5f90bf8d6d88498", "guid": "bfdfe7dc352907fc980b868725387e98bfed17e5bd75d4f2b61c6fa665b4a059", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f32f308f4e1c599b0768fd863e60d0b", "guid": "bfdfe7dc352907fc980b868725387e98d1599ed244d2bfa1beff1c2eeebdb412", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afca0267d5698caae07a60587b7d7992", "guid": "bfdfe7dc352907fc980b868725387e982fee4bd29c73f1917004fa43dc8e507a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb34f8f082dad479d6e946e7447c03fb", "guid": "bfdfe7dc352907fc980b868725387e98e38ef613ed0a0588acd6dbc417e7d809", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842afe37197a0a740798dfc2812294606", "guid": "bfdfe7dc352907fc980b868725387e984427a479c1680e9af388b0e1629397c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866300ec319155ad1cf0feac953bb89e9", "guid": "bfdfe7dc352907fc980b868725387e988e10ca2dd25e0f2209b606480c612777", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98583a6b8efe6764551a0419cf937adcf1", "guid": "bfdfe7dc352907fc980b868725387e98bc3c6914adb5968065704e1799b3d3be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cabb66e127a9f242662ee7a8c23f85f", "guid": "bfdfe7dc352907fc980b868725387e9832fff80c2823698e96946b8c50a0deaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecf6be250a0713b598dfe208c3b4ee9f", "guid": "bfdfe7dc352907fc980b868725387e982c9546223c9058978ccadad58f9544d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981802885d8a8b2e490ffd16beccc15829", "guid": "bfdfe7dc352907fc980b868725387e980dc1056dc8c71fce0d59d6e62f11b805", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854905649cc606a501aa79bbb5f383da4", "guid": "bfdfe7dc352907fc980b868725387e987672b79acce13dc7b1463f86e150b402", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c6b3dd921b6b553fd8206bdc587fa4", "guid": "bfdfe7dc352907fc980b868725387e9820e233bc4c23cea03c1cab10c36fb712", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abd45aaf47606171ee246ed179bd5fcb", "guid": "bfdfe7dc352907fc980b868725387e98fc06daada60eed0d81717eaa669b054b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e0b6eb6307871d611c116fb73624054", "guid": "bfdfe7dc352907fc980b868725387e98930a686c316e960b11fb43408281d313", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863b2eb6add9391c1dfe27321c18845eb", "guid": "bfdfe7dc352907fc980b868725387e985ec66b192660687a80868a6a041f56ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e288c836afaed92ed0075d16d9c7aa2b", "guid": "bfdfe7dc352907fc980b868725387e986230e58aa60442dca3fe449f9a0eb8e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f670bf2a1a452c4cc9d4e7361365126f", "guid": "bfdfe7dc352907fc980b868725387e98535a537eae47cad18c4a70d875408b98", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9822eac5422738457fa7ad366af7e893a8", "type": "com.apple.buildphase.headers"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "bfdfe7dc352907fc980b868725387e98d95217674f80b958880dbf508e59ed14", "inputFileListPaths": ["${PODS_ROOT}/Target Support Files/RangersAppLog/RangersAppLog-xcframeworks-input-files.xcfilelist"], "inputFilePaths": [], "name": "[CP] Copy XCFrameworks", "originalObjectID": "CFCC186E3D39329CD3546EA113C31B3C", "outputFileListPaths": ["${PODS_ROOT}/Target Support Files/RangersAppLog/RangersAppLog-xcframeworks-output-files.xcfilelist"], "outputFilePaths": [], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "\"${PODS_ROOT}/Target Support Files/RangersAppLog/RangersAppLog-xcframeworks.sh\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980a8bd0f227f9473e253611069c650af3", "guid": "bfdfe7dc352907fc980b868725387e9873f378c2d048f83926d032a84c841764"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fc9ebe0f28eb7c6870108e77ad92218", "guid": "bfdfe7dc352907fc980b868725387e9844feac2280f1ad73b246b515f5ed0966"}], "guid": "bfdfe7dc352907fc980b868725387e984fa5a505e2acdd0296815355d1261a7a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e98632778b756525c317f48961e236126e0"}], "guid": "bfdfe7dc352907fc980b868725387e98f662b9e428e8493e11a8838a8e55e398", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985ccee64944c83d5834a0061f90051577", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9866bb0bf8a942424d735ba67f943e0a0c", "name": "RangersAppLog-RangersAppLog"}], "guid": "bfdfe7dc352907fc980b868725387e98c7ca20a38b0bedd2369c4d192fa96553", "name": "RangersAppLog", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e984e4930d9084ad892d1708cbaaf718af8", "name": "RangersAppLog.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
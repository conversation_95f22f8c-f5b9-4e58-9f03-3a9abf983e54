{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980e9554411cce276f5fa37db232f01b4c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9813835cc26d4ab9091be4cdba26737b6f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987373872dfacffdf229890e42b147882f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98493beceaa6ffec930a27142c1d160d61", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987373872dfacffdf229890e42b147882f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986e90a260ca8f3f591b023850f42b8225", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ec85360a6dc61378ec3fab9ea00a16e7", "guid": "bfdfe7dc352907fc980b868725387e98e6d6f26e7dbfb380137d2c4da3a2fb03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98615c3b717e69be7d7f253f8125eafeae", "guid": "bfdfe7dc352907fc980b868725387e986004e0193456027fc5166d2e59f35207", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983988f94acfb9884d68ec55315a468918", "guid": "bfdfe7dc352907fc980b868725387e98511a5e1ea64acdf1ac5a123305591322"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98668a1fa78c28b302a27ad06e0d227e27", "guid": "bfdfe7dc352907fc980b868725387e989ec66c790ae0887ee35d64a452b90da3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850d603a79bdab2c23bf0f4516d429bed", "guid": "bfdfe7dc352907fc980b868725387e9841a90b4e9c55534ef0b46ed8c530d75d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e76049e6832321e730783ac11353d19", "guid": "bfdfe7dc352907fc980b868725387e9844f98c16027d08c083b98758de760e3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a1563c0fb3c950b8fd5480ef7c03a26", "guid": "bfdfe7dc352907fc980b868725387e986e3ed10ba04d2c8fc6edf9a39322cfc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca1ca837eed88ae69915b1fb5e3d0ed", "guid": "bfdfe7dc352907fc980b868725387e98d5ba2b2049f8d08defa6282dfbad6b9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831ba06be8c3eab400550708bedee95ab", "guid": "bfdfe7dc352907fc980b868725387e9824cfc23dbf543554d41e0e9bdb602cae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98732f5524d5758a2b460487792d56747a", "guid": "bfdfe7dc352907fc980b868725387e986cb0037e871c5126283cffd8d822a833", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814254af00c8b2fda0f2e976a05d4ed92", "guid": "bfdfe7dc352907fc980b868725387e9892af8191170b9ab9ddc77850d139af53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb6b7398032e7c803633da5b9cba76ff", "guid": "bfdfe7dc352907fc980b868725387e9804d744d4409c54246de6e2eca3eaa58e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863837e5b74cbc1b5133177a93f8155b1", "guid": "bfdfe7dc352907fc980b868725387e983016e2130404530bd097328aba3e73db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981439bb31b04d86eadd0c1bf3b709ede8", "guid": "bfdfe7dc352907fc980b868725387e9886937816b1e92554e1416c9bfc9cb530", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e242e39d62ecb1859e00d1e02dd1f2", "guid": "bfdfe7dc352907fc980b868725387e9831ff0e0b17669a1cbadcd3207873e5d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98023a5e23bc1c292cd7b117fb1011c3cc", "guid": "bfdfe7dc352907fc980b868725387e981a637615b47740c54fd176423bdc7e02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a116dd00f2fb8165e3506eb8d4683c", "guid": "bfdfe7dc352907fc980b868725387e98b2a78b9d8577db751e2251611177e384", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a71613be903f066798ea3fa15d454fe8", "guid": "bfdfe7dc352907fc980b868725387e9891a10c82cef69efcec3bc9309f3fdfa4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e344d2ad11db8c36a6f0fe6b3d9b15fa", "guid": "bfdfe7dc352907fc980b868725387e987f2a5cf93b83cbb862c5eb84b1cee99a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d0327cae5a2066395d7b51e34effe43", "guid": "bfdfe7dc352907fc980b868725387e987a034f0273a030bb2c1339e6da338bb5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dfc017ea9b3aa924ffafcb5df3dadc3", "guid": "bfdfe7dc352907fc980b868725387e98ef1603f66c3e51188c2e996857bafce2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c1b29b315e486fc4ab51b540693b541", "guid": "bfdfe7dc352907fc980b868725387e98fdf5a476a9d90a6bd9813eff1026799a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea7eca8ace6736bebc671d1f037142ae", "guid": "bfdfe7dc352907fc980b868725387e980697ad6777a668fc8b22e08e2f95cc57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def615499d41c88a7caabe6b2961327a", "guid": "bfdfe7dc352907fc980b868725387e98eef793fdfec4458c692e4b27c957f7f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d7d6a702f7dcb7dac951db7bf4da42d", "guid": "bfdfe7dc352907fc980b868725387e983590076251465d395e4f6baa54262b5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b42152452734adfd900d05ca31421535", "guid": "bfdfe7dc352907fc980b868725387e9815bc50d1e3008c58bbc6ecab2d02fa97"}], "guid": "bfdfe7dc352907fc980b868725387e9821b0b23a57ebc8519b4a6635fafd1930", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986bb5579950d5203c865c12fba78a1218", "guid": "bfdfe7dc352907fc980b868725387e982fb6e8c5390edf4f80cdc717816ee885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c98e43ce2be89c5e47a5616a58717993", "guid": "bfdfe7dc352907fc980b868725387e9859f863040f915f06c8f997c6f2c21f0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890c9a7724bccec132755ec73bceb7b4f", "guid": "bfdfe7dc352907fc980b868725387e98caf171d1f928f85c873e762a8afd7a5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c22af75259d58f574d5811dc9bde6187", "guid": "bfdfe7dc352907fc980b868725387e988525dab56b1ee8ab2e25090f61e5829f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1b428569d356fbd6ddedfdbc6de805", "guid": "bfdfe7dc352907fc980b868725387e98a23dcc6777f1f7ee1e73108f6d0c4fc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eaa1e2cc59de24bafd99370acb3aaed", "guid": "bfdfe7dc352907fc980b868725387e9840f1dda8f9dd1202a70c4521f62ee467"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e9e5532b2b07583130a9337ce205cd8", "guid": "bfdfe7dc352907fc980b868725387e98e6514d278da646690787382296e1c76e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984896de2b0b517589ca6f7d6907892ce6", "guid": "bfdfe7dc352907fc980b868725387e988414ed53a97ace2f27d32f89ed3cbd3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd373b29832ee910d9b6e4aee8031552", "guid": "bfdfe7dc352907fc980b868725387e98a244e9ffd93f6db96c911ecc42f66e9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987918a0f03eb87bc439e044028a4e3db8", "guid": "bfdfe7dc352907fc980b868725387e980d4bfb329ddd25552f4b9c895b5914d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6a672b6018d9a9955691b4a3d777215", "guid": "bfdfe7dc352907fc980b868725387e98b9b1d4faa160963b340ad4e900bf60f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98256dabd97dc207c2b60850a0b78bf1d9", "guid": "bfdfe7dc352907fc980b868725387e985304fe03fd4bd959cb7879ff9fe815ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d849bda3e009199a563b2be1201769", "guid": "bfdfe7dc352907fc980b868725387e98e02bab21b9c7b7b6578919e1a22b56c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fce030eb994a9025bc0cbe2c0483037", "guid": "bfdfe7dc352907fc980b868725387e98dfde547d421cbc2ec6654ee5f080b6ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b67ae2b6e31f3970d4b2dfc0fc595ac6", "guid": "bfdfe7dc352907fc980b868725387e986abf8ff0fc50ede627028844af8e856b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cedc9472cc117d4892d732596b621350", "guid": "bfdfe7dc352907fc980b868725387e98177a374139d95accf0e4e680d86ef5d3"}], "guid": "bfdfe7dc352907fc980b868725387e986f93a724df969fbc2f764bcdf6477951", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e9813ae8a6468c28ad17ddfddfab61d342c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98226c4235dc94da08f85a05de7f32e4d8", "guid": "bfdfe7dc352907fc980b868725387e98ea9aba4de4e708c86d35a461fe159c2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e9a79c282100b570a1aa398235350a1", "guid": "bfdfe7dc352907fc980b868725387e98c0cce0d1883ddae60985815c5666051e"}], "guid": "bfdfe7dc352907fc980b868725387e9806f32493383f160e57b3b04d3d97214c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a46e8ec8444bb9b20b0641fa51742fe9", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98c77b31ac5e6f68a85417f3269d15901f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
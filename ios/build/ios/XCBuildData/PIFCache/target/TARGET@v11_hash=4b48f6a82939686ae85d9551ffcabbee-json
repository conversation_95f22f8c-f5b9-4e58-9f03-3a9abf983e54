{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807e20ee9ec493c2b45a88f5956250d18", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f0ca8638bc1da094306a56916163c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c386c017672df3476388c3fe4c788b75", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f77ec353b0de6b7737a53f7904822dac", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c386c017672df3476388c3fe4c788b75", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a01b1e4fc4a7d2a03796d880c714d99b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aaa06d0ad40dc389e88daa7c51037ed8", "guid": "bfdfe7dc352907fc980b868725387e984a815573dfc7ae5f64b725130624df2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa6d590d347593d14f97f061425fb6d", "guid": "bfdfe7dc352907fc980b868725387e982ed090946034067d1e51e113fcde1363"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d910526629f41933e02ee49ea84162e3", "guid": "bfdfe7dc352907fc980b868725387e9870b4b5fab465ca8fe4a40d6af0e8967c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d04c63d57c9a9cffb3a2670833a06c", "guid": "bfdfe7dc352907fc980b868725387e988f3c79822dc8458b15ed61520b0b8718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feed3bdd5146e06fdb5d4b65594da294", "guid": "bfdfe7dc352907fc980b868725387e988bd9b0531905b2f3e166ec0500900455"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c2affa67a4177b84de2ca107e46c974", "guid": "bfdfe7dc352907fc980b868725387e98c5bdda268b2d8a0cc3723cf6530b4a09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872cdd00dcec753043d75eee82fda497f", "guid": "bfdfe7dc352907fc980b868725387e9859cf8783d337bcd6d4844c72ca4a57a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dd8e1a2f75c1bcbfe74e5efe9e39a11", "guid": "bfdfe7dc352907fc980b868725387e98bf79b80aa130c0ed8944e913e3f61b67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfacc5e09c829f2ce6ff45676d1d904a", "guid": "bfdfe7dc352907fc980b868725387e9810f050bb8d346c22f03b7a02bb970092"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817f62ee1e0457db8409dd4d02a6e34c4", "guid": "bfdfe7dc352907fc980b868725387e98e77beee835c1da193e4df688dbe86132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3168b7dc7f4f0bb1187a7f62d2e6644", "guid": "bfdfe7dc352907fc980b868725387e98934d85ea4a34f6941c237e7887875076", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ce6bbb90338e8ca9d9d7bceb051c795", "guid": "bfdfe7dc352907fc980b868725387e9808c59fd33759bef8c7059f31de4d3eb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822329068b0effdff1b96732d376e51b3", "guid": "bfdfe7dc352907fc980b868725387e981227820501656d19abc28767193f7f6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980157e754ec128819c8e58401b3bcbb18", "guid": "bfdfe7dc352907fc980b868725387e983bbbeea18c48f842a6b7c9577872a365"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884f1992896f5a1909e6d21296154db61", "guid": "bfdfe7dc352907fc980b868725387e988cc28965e7491ec135a2291143346ea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb34f01d2e9e54f11b26c0c87f87bdb8", "guid": "bfdfe7dc352907fc980b868725387e98026eea99d593b2cccccd9a1a6ca30b0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3dfaef3b88b0650d12ff5fae4840ba0", "guid": "bfdfe7dc352907fc980b868725387e98101e71aa2b1a06d305a43f642535f6f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fdc52fe7bc273f1e5a5e152e3b13526", "guid": "bfdfe7dc352907fc980b868725387e98ce1692bbf68cefcbbf5e2fa0710adeeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dc2365cecd748e233a3b33dc8ff29d7", "guid": "bfdfe7dc352907fc980b868725387e9870723e6b9015a2b12d0a8bd6496e3e5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9b25fe6450f9c2375b6b287a96fb517", "guid": "bfdfe7dc352907fc980b868725387e984eaf335d92118c04c280a655d6440a17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fb25d513a3c2e1bca6a3070738401f7", "guid": "bfdfe7dc352907fc980b868725387e98ec8cc1e8e6749e8d630d8e3c733a3bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3c556ae4c02885870f8e0796edc30ad", "guid": "bfdfe7dc352907fc980b868725387e981ec328f2e2ca7a06d6f4359084613bce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f834ca5888fc116f0f9fe9ad985fa15e", "guid": "bfdfe7dc352907fc980b868725387e9815d0caab953bfaba1ed47bf31b0d4442"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823aed042230ab10c700e5d712faf8a98", "guid": "bfdfe7dc352907fc980b868725387e981a0f941053841513dfc5e3e5d09079ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891c67a9dc8dc01360f04c39c16effd80", "guid": "bfdfe7dc352907fc980b868725387e988a966ae5aec5f7fa85931795d5d83b7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd916279b6eeb3e7c3a1203c0a09553b", "guid": "bfdfe7dc352907fc980b868725387e98be68cabccc642d51126691b1896f73bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985faa63713369e80312adbccf882937db", "guid": "bfdfe7dc352907fc980b868725387e98db95d3023aaaf0bd542c661fc3a78852"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcda42c996db5fd3703ceb3fb8ca7308", "guid": "bfdfe7dc352907fc980b868725387e982372e7d7853656fc0577fb213a5ae12c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800052d412349cf4f3f2d1254a3d42e3f", "guid": "bfdfe7dc352907fc980b868725387e9880803ff6d383f4663c656c88f9613d58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50c574115418107933ad6f29032d43f", "guid": "bfdfe7dc352907fc980b868725387e985e16b3cbfe13fd2a3731ce7e7e9f721b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988075366ab0f0c5654f79a45c4edc0ad4", "guid": "bfdfe7dc352907fc980b868725387e988cf894cb001d158ac99005540ed82c3e"}], "guid": "bfdfe7dc352907fc980b868725387e98bb5663050afc5d3371683127efcc8427", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804113fe16e059835f57926d7a77d881a", "guid": "bfdfe7dc352907fc980b868725387e98b42ac8babd3a3f213c3d941b45128229"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c8bc52cd72131cb04f6a34a3f2b8375", "guid": "bfdfe7dc352907fc980b868725387e98c82086a8f1357df89c2ab56ea7a7ce4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a371c92904c9441b1236f07ffd4381", "guid": "bfdfe7dc352907fc980b868725387e980a4f68bff84b1a79e4d1f7e42f824778"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6332fd81c452d75e0d7943d924c2ba3", "guid": "bfdfe7dc352907fc980b868725387e984e2c85cf39444a712e836e8cc3fa3190"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2abffadb7faa2c27ea31dbd9300430c", "guid": "bfdfe7dc352907fc980b868725387e9868bbc1cccc6b3b3c6b91fc82cf963489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983187a52a58d74cb7d7f809497b964397", "guid": "bfdfe7dc352907fc980b868725387e9808b8d66c627a17250a40da91b647160c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989faa3db681edb8d265803aa27cdf56c4", "guid": "bfdfe7dc352907fc980b868725387e980b38a6d600bdec4cdf5ee56d6f7da81a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828077907f47bd81f22a1fc663686c616", "guid": "bfdfe7dc352907fc980b868725387e98072fdcfb0430864b768aadb96cf6c5cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ba8a790fe33047b24d333b0ff670b26", "guid": "bfdfe7dc352907fc980b868725387e985d4bd2734ec6720beddeab8205fa094b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad32e66160a5d143088ea53777eef2cb", "guid": "bfdfe7dc352907fc980b868725387e9838f5eda2535fe05ff30abcb154d3f031"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986842b2a2624f7c8d2bebe69862a19e75", "guid": "bfdfe7dc352907fc980b868725387e98ff74c1e4f8947b11928991a47ef82090"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980410ff47615146633cd5df6a0202af1d", "guid": "bfdfe7dc352907fc980b868725387e98eb2317b1cb9f517d129bdc37a7247baa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d096fe730ff3d4ac099258693f37f3a1", "guid": "bfdfe7dc352907fc980b868725387e98d50e36854441f5d9c42e867ec4da8d55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98444e54be911a2908b1576dd9b7fac81a", "guid": "bfdfe7dc352907fc980b868725387e988fa418bed44b1ba2c7536d6b1d1106e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986513d354df79ab25a8f13e373ee386bc", "guid": "bfdfe7dc352907fc980b868725387e9842eb77af42a0a49fb03e72f2f745eb07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f9b896ba46254345cfad9731e884cc3", "guid": "bfdfe7dc352907fc980b868725387e984a4c5f73614eee69c3e6701883337d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878778234fe6ae43b6035beb6be4d22b5", "guid": "bfdfe7dc352907fc980b868725387e98c1994a3dbd2ff0d75fca612e9bb3ba2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6c7f072c6df93b5521c54a2eb4df0c5", "guid": "bfdfe7dc352907fc980b868725387e9870563cd2eee7057ddb147602ab7f9cd4"}], "guid": "bfdfe7dc352907fc980b868725387e987a1a6c7205425aa14c79f614fa6febeb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e98a314f60da3238fa122f15b6582c5a1db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98226c4235dc94da08f85a05de7f32e4d8", "guid": "bfdfe7dc352907fc980b868725387e9898244d1304fffce89b60267f7dd0490d"}], "guid": "bfdfe7dc352907fc980b868725387e9884740943361f6a2dba775b68fd693d17", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983e5263943d6b9da4a376671753f82f31", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98c2cf5176e8a854092bcfc88891e0f837", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
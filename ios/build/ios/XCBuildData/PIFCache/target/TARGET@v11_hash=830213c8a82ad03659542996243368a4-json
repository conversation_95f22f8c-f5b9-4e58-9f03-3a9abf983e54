{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f8f51212a0cfce0b2f42dbb74c45e91", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/CocoaAsyncSocket/CocoaAsyncSocket-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/CocoaAsyncSocket/CocoaAsyncSocket-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/CocoaAsyncSocket/CocoaAsyncSocket.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "CocoaAsyncSocket", "PRODUCT_NAME": "CocoaAsyncSocket", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988eb22aac6b597ffc64ff4850d92387ff", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98090076cb04db7d5ef0d72c98fe766e9a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/CocoaAsyncSocket/CocoaAsyncSocket-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/CocoaAsyncSocket/CocoaAsyncSocket-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/CocoaAsyncSocket/CocoaAsyncSocket.modulemap", "PRODUCT_MODULE_NAME": "CocoaAsyncSocket", "PRODUCT_NAME": "CocoaAsyncSocket", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98427712fdccd2304d793e3842c633e041", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98090076cb04db7d5ef0d72c98fe766e9a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/CocoaAsyncSocket/CocoaAsyncSocket-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/CocoaAsyncSocket/CocoaAsyncSocket-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/CocoaAsyncSocket/CocoaAsyncSocket.modulemap", "PRODUCT_MODULE_NAME": "CocoaAsyncSocket", "PRODUCT_NAME": "CocoaAsyncSocket", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e082dcea428f17fd4696c9c4f8449ba0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e378e5cbcd0465c1068348006c7993f9", "guid": "bfdfe7dc352907fc980b868725387e987d366c0dae923481598eb339b958df88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a855862be53c8fe2fbd359e1e4a14cf", "guid": "bfdfe7dc352907fc980b868725387e981f8f142a119bd696a3409ab497248627", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874efb2d4f975d0e2313db311c5ffc728", "guid": "bfdfe7dc352907fc980b868725387e98175f90b0cc3d63df370e47154013c8cd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984d324e0ce9bc33fe70c602cf1b327fd2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dc61145b66151640fb5c2bd0d2ae02ea", "guid": "bfdfe7dc352907fc980b868725387e98c3efc9b1b72470aae1e0b841dbe7f9ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a133fc706ab9c97e72d788614062b474", "guid": "bfdfe7dc352907fc980b868725387e9893f63313fe8d9ba68591fef7343cbc69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827d0ae3724fe71d9f3aef108ce1171a6", "guid": "bfdfe7dc352907fc980b868725387e98af7d92242fca13fc5e3ec9337334a3e2"}], "guid": "bfdfe7dc352907fc980b868725387e98c2e6eff19d70ee50add5e8aa9e9b026a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9874e2cba7029b7d9f3a96b63fcb85f5ae", "guid": "bfdfe7dc352907fc980b868725387e9831a4ff8d1b6593d225034a32fcff0f80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e9849d3c76ce02d388db3c8ed56a78137e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98226c4235dc94da08f85a05de7f32e4d8", "guid": "bfdfe7dc352907fc980b868725387e98aa743e60d2a4bb638b3a6257818d58aa"}], "guid": "bfdfe7dc352907fc980b868725387e98279d83b48c51123ae60fd050cb59e4c3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985c8958f87f06194ce89a24232b69b1bd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98674427677ed50870bbe9310a50295dc1", "name": "CocoaAsyncSocket", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e989f1fa5acd06660508cdfde6f3b47373d", "name": "CocoaAsyncSocket.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
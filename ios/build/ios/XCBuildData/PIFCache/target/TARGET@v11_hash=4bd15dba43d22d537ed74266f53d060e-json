{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984bb4d91e3f47d2b77fe337b5772f640b", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "ONLY_ACTIVE_ARCH": "NO", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e988f7c95cfe7c05e7c498c4effb83b8ca9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988a3152368cfe15f001f229dc90246515", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e982781b45ebc68bec548f6cde3050d45db", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988a3152368cfe15f001f229dc90246515", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e985b28985a46abb62214b40b026e01f674", "name": "Release"}], "buildPhases": [{"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "bfdfe7dc352907fc980b868725387e984e2176ef4539053c6517ce3088ac9698", "inputFileListPaths": ["${PODS_ROOT}/Target Support Files/GoogleMobileAdsMediationFacebook/GoogleMobileAdsMediationFacebook-xcframeworks-input-files.xcfilelist"], "inputFilePaths": [], "name": "[CP] Copy XCFrameworks", "originalObjectID": "9AD31E84B8BE0888085C7057A8395CFF", "outputFileListPaths": ["${PODS_ROOT}/Target Support Files/GoogleMobileAdsMediationFacebook/GoogleMobileAdsMediationFacebook-xcframeworks-output-files.xcfilelist"], "outputFilePaths": [], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "\"${PODS_ROOT}/Target Support Files/GoogleMobileAdsMediationFacebook/GoogleMobileAdsMediationFacebook-xcframeworks.sh\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c16926158ef45fa2ac81bd3203fcd49e", "name": "FBAudienceNetwork"}, {"guid": "bfdfe7dc352907fc980b868725387e98cd53d937e824fad9f4527eeeb684cb40", "name": "Google-Mobile-Ads-SDK"}], "guid": "bfdfe7dc352907fc980b868725387e98bbd9e0ed4cdc41b93625c3661dbcaa81", "name": "GoogleMobileAdsMediationFacebook", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 0}], "type": "aggregate"}
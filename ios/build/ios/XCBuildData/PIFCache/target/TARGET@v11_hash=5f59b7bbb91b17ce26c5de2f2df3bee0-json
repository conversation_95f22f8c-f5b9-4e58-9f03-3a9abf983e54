{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e76c9b0dfb65183e484723d7f88595ab", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/MMKVCore/MMKVCore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MMKVCore/MMKVCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MMKVCore/MMKVCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "MMKVCore", "PRODUCT_NAME": "MMKVCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982143698b4191e4ddca26acaa7e7fdd6b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808f67a3f6445d46e7fcbae03d889d086", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/MMKVCore/MMKVCore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MMKVCore/MMKVCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MMKVCore/MMKVCore.modulemap", "PRODUCT_MODULE_NAME": "MMKVCore", "PRODUCT_NAME": "MMKVCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f07f584d8e712112d366cb01c07a1943", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808f67a3f6445d46e7fcbae03d889d086", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/MMKVCore/MMKVCore-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MMKVCore/MMKVCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MMKVCore/MMKVCore.modulemap", "PRODUCT_MODULE_NAME": "MMKVCore", "PRODUCT_NAME": "MMKVCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c133c0d2f8bf700fed83852327e7c2c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98040bd9d3651af41318dec72ac7362e86", "guid": "bfdfe7dc352907fc980b868725387e98d248f8a909bb4c43e550fae981eb582b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b9c66ded80a870b6a4d90899b278aca", "guid": "bfdfe7dc352907fc980b868725387e98de7ad4390a58a05373707887ed3117d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bb50d53974589ab722e6916ce53b779", "guid": "bfdfe7dc352907fc980b868725387e987354abdcc8b04dc2cb38f503d413a7b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5d881099f5c76f1efe1610d43603175", "guid": "bfdfe7dc352907fc980b868725387e98ac97b5b12e525da6f720b8b060f540b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824f8418c9256f984cb5772d467931932", "guid": "bfdfe7dc352907fc980b868725387e98bb63ed948a260127d2145b56891fd926"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f8c04d0d6f80c56e547f388d919b1c2", "guid": "bfdfe7dc352907fc980b868725387e9844417a190babbd6efc449550ced21514"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbe715e4a78ee59d727bba7014a171de", "guid": "bfdfe7dc352907fc980b868725387e98e0c35f830cee8cb16237f6dfb0f66c68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98164ac3989360744577c657c9c1d979e1", "guid": "bfdfe7dc352907fc980b868725387e9815520715e2b70b0429da64fbd3ac83c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9a9de5691eab20bd802d5d05c766986", "guid": "bfdfe7dc352907fc980b868725387e98f43996a961a2c7da0f2aaf9b83c70112", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981600ba551e4c19da15977c3b1a41dc66", "guid": "bfdfe7dc352907fc980b868725387e984caf08ec9bda05c84a2e419573d11a57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a249b90511484ad1f31dee88c16392b3", "guid": "bfdfe7dc352907fc980b868725387e986037bc10bd527b55d3154096618a8719", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983051b29885cf7b54e8db7c560f900001", "guid": "bfdfe7dc352907fc980b868725387e989fd1053f38a5be02a417a193a273b9b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845ee3159ff90bdcf808aa4924cf1a3db", "guid": "bfdfe7dc352907fc980b868725387e9833dd88b9ba7843b9e6de8cdca2898259"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7109be492ec25078246d94d9e2c2604", "guid": "bfdfe7dc352907fc980b868725387e98620a716047f85be60f88d484b5db66fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c177bc6c3397f55235c74d2c0c0541", "guid": "bfdfe7dc352907fc980b868725387e98ba93b492ccc64beb67053efbbbbe6c23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c77053d023e20e6a598431d662dd62", "guid": "bfdfe7dc352907fc980b868725387e982eb1ceb00807bd9bdf1c9cd114dd4721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a7cac888384db3e494207e4ad48465", "guid": "bfdfe7dc352907fc980b868725387e98875e7210db63760759e24fd73c0ce0a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891123d6d64fda7c2fcf1c3c2eec812e6", "guid": "bfdfe7dc352907fc980b868725387e98ca709e3c5f19180ebb64d8b7855443d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb560b937d5dedabdde9ae921cacd637", "guid": "bfdfe7dc352907fc980b868725387e989c2510329af85d77acc572410691451d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a9e7707543b032f2dca49da4e824d4b", "guid": "bfdfe7dc352907fc980b868725387e98afade623ab263afd23c9752756141abb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b753f0b58075b35e408e9d9c6279c9e0", "guid": "bfdfe7dc352907fc980b868725387e98aad4ba15b4cf024ba7ce9de4b663382c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c177ffe243308330d754ded40247261", "guid": "bfdfe7dc352907fc980b868725387e985164becd7bca25e28e4737ddc7a108af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852d3dd7239913e71313dbc84495a6306", "guid": "bfdfe7dc352907fc980b868725387e98106b788f48d6678f10e3567075053d96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee1dcfb4611b3faf721352abb9ad325", "guid": "bfdfe7dc352907fc980b868725387e985005911d661faed0e00f4c6a2a83527d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da8771d8d23687b1bffedd94462811c", "guid": "bfdfe7dc352907fc980b868725387e985d769fd83bf8800078a2213b781da606"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b32ed352aeedb7e53718d2e2761dbef5", "guid": "bfdfe7dc352907fc980b868725387e984f6cd703f6e75c2804f302d61b094e7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b0a3d8524c0a21d4e38cf17c4110b26", "guid": "bfdfe7dc352907fc980b868725387e98fbac0cfd8918442a5d2d9da7b4d2663b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2b1747d93017c4d45938e10571f93d5", "guid": "bfdfe7dc352907fc980b868725387e9805e1df792882d403c43b03c70fb1b9c4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9837702781e938745711e747a90ae2bf4e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9817089bbc39ab71cd4e71e93d45a34e82", "guid": "bfdfe7dc352907fc980b868725387e9890b6622e561b03752898e5b20c1976e3"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a15c73b22b682f2b1b53725db9c22d56", "guid": "bfdfe7dc352907fc980b868725387e98f2fc2e6d249dc266e3f0f7eac9252ffc"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98814ff3ccbe0ecbff961798b9579782b9", "guid": "bfdfe7dc352907fc980b868725387e98f2cd9b97a39876e38c8420e0ba01465d"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b7ffc1b37d7a025f60735be75b24fccb", "guid": "bfdfe7dc352907fc980b868725387e984c134b09838f56c24f204dc7b6e4b1d0"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9843f9131192514fc7bc4ff95b9f112019", "guid": "bfdfe7dc352907fc980b868725387e98a179d1a5772ea44b0cd4c6d3959181ef"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c050656caffc2c4020f72a962817f2e8", "guid": "bfdfe7dc352907fc980b868725387e9817f19e7d5717cf3cf6a91f1e788f16aa"}, {"additionalCompilerOptions": "-x objective-c++", "fileReference": "bfdfe7dc352907fc980b868725387e98c09760d6f7f72dd3e2bb5c705457fb66", "guid": "bfdfe7dc352907fc980b868725387e9894bfd8fa9597f44459803f656c166d59"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983ea45fc6916e9a113dc80479f74720d3", "guid": "bfdfe7dc352907fc980b868725387e9855cc03c53f22cc93e3c6fee11d46fb13"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98afba977c750a0f91f7b25f2d69655d8c", "guid": "bfdfe7dc352907fc980b868725387e98200fd02c1bdc7646e5c96fd424f55bf1"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989cee9e3f5b0901d3cc1ec44b5db75e95", "guid": "bfdfe7dc352907fc980b868725387e9873e8a4825cdf34312835e20a668fd62c"}, {"additionalCompilerOptions": "-x objective-c++", "fileReference": "bfdfe7dc352907fc980b868725387e988ad37ba03e4a9c286010a4ce5a26664a", "guid": "bfdfe7dc352907fc980b868725387e982b1e68a22f53b870375bfd5efddcdbbc"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f3dec826b688bc5b189470944de5912b", "guid": "bfdfe7dc352907fc980b868725387e985109eed64bec2a3bc411569fa3cf5fdd"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9829cedda57843e99ccd33901523b2a9af", "guid": "bfdfe7dc352907fc980b868725387e984e2d6e19cf9e6c1bba864e4a25420ac9"}, {"additionalCompilerOptions": "-x objective-c++", "fileReference": "bfdfe7dc352907fc980b868725387e98dc69ded0c4a035d71b49f07626a3201a", "guid": "bfdfe7dc352907fc980b868725387e98a438ce3b8484ce01c538f16aaaca29e5"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fb98bcddc4e0e3b394521b82aedd2aaa", "guid": "bfdfe7dc352907fc980b868725387e98196542be7621f149e30635ea59d45131"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989a4b78f64dcb7be7c273dc8bfc62c114", "guid": "bfdfe7dc352907fc980b868725387e985101c7baa5f248cf17e3f9bfafe7f587"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f1f8477d0871c839cf6fdeb76f252a22", "guid": "bfdfe7dc352907fc980b868725387e98717494488ef37f9fa431ad98a4d5e592"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985e97d148e025ef95c00fd1fc2a7de067", "guid": "bfdfe7dc352907fc980b868725387e982611244e0a2b3c2ba2c06af5b0eac92c"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9832a3774ccae07adaeaf4544b0a5ee856", "guid": "bfdfe7dc352907fc980b868725387e988fff182a784f3bd535ae6db2a4109df2"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9877b9d4430e809c5d1b950ce9e09ad08b", "guid": "bfdfe7dc352907fc980b868725387e98f995e139265234660b46a4b5e46ae72b"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983cf4e21f5e846774186f014e2150aec9", "guid": "bfdfe7dc352907fc980b868725387e9820105eb5c0b39b4d2f3973d593bc763d"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98cd2ae1b419b66c429b01d754e4faef6d", "guid": "bfdfe7dc352907fc980b868725387e98e6b920839aac63af450031dd4bdc0e40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886336e7b405e3b75ccb81082cd452807", "guid": "bfdfe7dc352907fc980b868725387e985c68814c71b0b1934e60699060bc04ee"}, {"additionalCompilerOptions": "-x objective-c++", "fileReference": "bfdfe7dc352907fc980b868725387e98377d262899c1e1e400f55c91f7f3d4f9", "guid": "bfdfe7dc352907fc980b868725387e9822e6bd6cc70e6365244a85ec65638f13"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98279aa8fba9f3faa6b61f9b006e99700d", "guid": "bfdfe7dc352907fc980b868725387e987e1d0a24991046f92c46eeeea4b58a8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989459b8f62af7c80b63e78f793394c34b", "guid": "bfdfe7dc352907fc980b868725387e98fb49cc2585ace4fe95ad9103ed0dc7c1"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9813540a7a978285f9064ba3f77b974c20", "guid": "bfdfe7dc352907fc980b868725387e98e9eb465a4caffda732d52eb6ca325f9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e097fb367b61516f7837be2a35876a5b", "guid": "bfdfe7dc352907fc980b868725387e98523834428e30c5ce8ffd031537cc3771"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9880a7c207351a3ec17322727545944bff", "guid": "bfdfe7dc352907fc980b868725387e9878944dc7a10ebd18b4e0ce24373eca48"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9803ae6eecd426efd28404d674a0e28760", "guid": "bfdfe7dc352907fc980b868725387e983624083393e1ccbf94b739b711a066b8"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98bf9515e2e1a8c4eb0671b3da950c9380", "guid": "bfdfe7dc352907fc980b868725387e98861498053368a4f041305891ba26bfa8"}, {"additionalCompilerOptions": "-x objective-c++", "fileReference": "bfdfe7dc352907fc980b868725387e981283189bc9eecbe4f77437836af5867b", "guid": "bfdfe7dc352907fc980b868725387e98581a7a50325d3e3153d7c5b3d140fefd"}, {"additionalCompilerOptions": "-x objective-c++", "fileReference": "bfdfe7dc352907fc980b868725387e984cd85cbc920ab2e8cf13de801f5d6c5d", "guid": "bfdfe7dc352907fc980b868725387e985bc8d6906109d4e4377d640182ab5590"}, {"additionalCompilerOptions": "-x objective-c++ -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ce12e0b8be74a85e329953366ec236b1", "guid": "bfdfe7dc352907fc980b868725387e9884dcba549f2fe47835625b31581effb7"}], "guid": "bfdfe7dc352907fc980b868725387e987c32224576d7d72d972675e6315430cb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eee7ed068ff654615e78f35e2b0afe25", "guid": "bfdfe7dc352907fc980b868725387e983b8dabf454bdb0bd54afc1910a59f8e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e980e23dd5652ab2f7023871165214ba20c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bf9f81912b8a94a8584885abd980044", "guid": "bfdfe7dc352907fc980b868725387e987bf571bff49f2f9eff2fb45dc723babb"}], "guid": "bfdfe7dc352907fc980b868725387e9893af095897ae2a15e40ee7ac598dca85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9880585c2d6e022c15c66ae8fb83bf9bad", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e988cc9286addbac7e4dfb2bcfefa2147d6", "name": "MMKVCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9805a83dd5af44798d50403480ee1d5559", "name": "MMKVCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
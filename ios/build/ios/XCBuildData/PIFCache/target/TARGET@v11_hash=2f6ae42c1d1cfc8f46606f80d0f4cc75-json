{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981522101a93b89f27892b380651fadc06", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98144af90f2870b005cac5a87e42436367", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a29fdb66126f32ac01247cca3580efe", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981b3378f36da6b95a47588c8ae770071e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a29fdb66126f32ac01247cca3580efe", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986080106077a4e0f18b509b6534971820", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9878009f146800a112ea3a3267db9a978b", "guid": "bfdfe7dc352907fc980b868725387e98121f8065c18b03e931cfbef233acbb5d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fbea6fd47889c10e09f6e9d59fd7870c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982c10ad53c174780934beae33c67a39a0", "guid": "bfdfe7dc352907fc980b868725387e98123ae582475e10df86fa45669e732671"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98992b1f2f16e4703bc9de5ca53139390b", "guid": "bfdfe7dc352907fc980b868725387e987943358ffdd88fc53c572c0ed01751d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98466fecba31b7cf7281261fbfad27af9d", "guid": "bfdfe7dc352907fc980b868725387e989c2098024ae958db9546b58cea208c82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98427efa891a62065a97d105a85bb35cce", "guid": "bfdfe7dc352907fc980b868725387e98f50c4c102f31e72774955754ff847306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed733ddfd71d19eff457f3dcc6534bf8", "guid": "bfdfe7dc352907fc980b868725387e98867e4f55684bf436532e3880985c6424"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c810b29784041e5d21bbdc6377675922", "guid": "bfdfe7dc352907fc980b868725387e9838661176a68a477595fe70d2882cf512"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989143e3f400d67d2bf05587969ab61e49", "guid": "bfdfe7dc352907fc980b868725387e986e5e70d4680a5243ab1ffaf133042b3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e6f960080b59f3ae2b097a36f3c41f1", "guid": "bfdfe7dc352907fc980b868725387e9848947a2568b4fc3ef281963a4fa867cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a991849de335d39e50ad4a79bad247c1", "guid": "bfdfe7dc352907fc980b868725387e9833e92ab0dccf58fde1abfcb669368915"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827c8060417c8205fb2097c7207828077", "guid": "bfdfe7dc352907fc980b868725387e98d48af40de41db022ef50ae882738d7be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5570a4f03454e8c70a130aa95a9e3a9", "guid": "bfdfe7dc352907fc980b868725387e981e5bb0feee770098a97b7c0b81aed8cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d55d6594f0918e7e1eaffedcb1d1b88f", "guid": "bfdfe7dc352907fc980b868725387e98fde2badd60839ad06942227c94ffe9e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837a0d68d1d97e3fa7494f6fba6d97269", "guid": "bfdfe7dc352907fc980b868725387e987dadbb94fb8390eaa357bd592b79ecc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e045e0a946311242b56d94c5ac593a1", "guid": "bfdfe7dc352907fc980b868725387e98a101e9656e1e5d8b54425e93bf70dd3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfa80bc1acb897d181a7cd0ed24b611b", "guid": "bfdfe7dc352907fc980b868725387e982776d64ae5b3568fc0f13d72a2e424ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860f342d5ec9b18ee99624fde3233e9d8", "guid": "bfdfe7dc352907fc980b868725387e9846e1b5290cc0be5f318f3d9779a2852d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cff56ca21658d06ef12a316e62a281c", "guid": "bfdfe7dc352907fc980b868725387e9809291e5e2d93d73d4f1429201a0a089d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed2b5b22c3ba8905c5fd8dd6c1cf3636", "guid": "bfdfe7dc352907fc980b868725387e9863a68a706cb29b3152f2889056add175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbc8f620483e70d48eb2d21ab867ba47", "guid": "bfdfe7dc352907fc980b868725387e98928293baf42715062dafa251c2cc64a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acfd2ee631c639cd114f362dba67b2a0", "guid": "bfdfe7dc352907fc980b868725387e98077932560555501c3801eae3178df51c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988619d08649d673c166d09f131166c66b", "guid": "bfdfe7dc352907fc980b868725387e98557f9d87eed2111a7715b5497f558e6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986472dc4bdd76593714b32bd21b43542a", "guid": "bfdfe7dc352907fc980b868725387e983fce42e5ec9dc5c5909ba697ec7eb121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854c4894b892f635f02d59508841b99e0", "guid": "bfdfe7dc352907fc980b868725387e9841b7f9ccd2ca0fbb93a6762aae034ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bad3142cae3c2188a2496a34bfc303b", "guid": "bfdfe7dc352907fc980b868725387e98940615c501e089512980331438ea27a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981976c794a6714700806bf1d7ea7d7351", "guid": "bfdfe7dc352907fc980b868725387e98ffc5c745c93ae37ca80b90744b03d73d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865a44aa47194e933aaca365b002218ee", "guid": "bfdfe7dc352907fc980b868725387e9895a63be583e1148a7f5bef97106f5901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853de7492e257a69c594a192c061e51d8", "guid": "bfdfe7dc352907fc980b868725387e9814327c9fd1eaaed4fdaf53f201d0d904"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897576f2f68adf40aaa7906cf2e0439cc", "guid": "bfdfe7dc352907fc980b868725387e98911ff052d8e8b0ac8d2c8ef55386d528"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7541d81cf7893a39dd2869bb2daf7ba", "guid": "bfdfe7dc352907fc980b868725387e985eed860bda8b89b5012c63df116e078d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98658ed3711bb1ca49a63ab6af6684406e", "guid": "bfdfe7dc352907fc980b868725387e98cd70efaca094c286a220852d0a103b90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806465ec2fae5b9a58ad5cc603e944b29", "guid": "bfdfe7dc352907fc980b868725387e9844fecd108ac647432223de64833f6c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f3c4aed70778e66e6614f919ce668a9", "guid": "bfdfe7dc352907fc980b868725387e98ba3a3836d6bf7afad417f92f70d23741"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b30ee7e6168e0d41b4aecb7af36b0ed8", "guid": "bfdfe7dc352907fc980b868725387e982649e8a30b8e02e8af4bee4fd65b19b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836836b1aafba382bc769f27db73e6d70", "guid": "bfdfe7dc352907fc980b868725387e98155fb71604fabcb8918d07f53b455d46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98225fe96d097d515e197ca6956565d1da", "guid": "bfdfe7dc352907fc980b868725387e987f64395e25dd5d96f96925569cba8039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d6c2248af018df0e38f9b8fa8d2bba0", "guid": "bfdfe7dc352907fc980b868725387e9814cbb22dd0a6b829117b665c14920cca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98012762e750013873fe8d8e7c9a14d013", "guid": "bfdfe7dc352907fc980b868725387e98b17333863614996197a7c6b763b306bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bcf5860225220110bf4c767340d37f3", "guid": "bfdfe7dc352907fc980b868725387e985de531532abf7120e188f5f409605ed0"}], "guid": "bfdfe7dc352907fc980b868725387e98401d02b2d4a327739bc9b5c1d7ad73d4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e98533d1f0765f98fa4fb2f6bc6f8ee60a8"}], "guid": "bfdfe7dc352907fc980b868725387e9842ef1730a2c77e43b643c17765b47a4f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e7dcf02bc96b2d05dd0125970742eff8", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e9886e0346fb69338927ea023191bcd3e47", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ab5ed5a78ae1d4aa9f2c34f2e359b96", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98121b0755a8b62b0b8ed71a6e8b8f6672", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9878f281f2d1204a03b6a135a96be4a64c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c6a68d54485c4154207d181c238992a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9878f281f2d1204a03b6a135a96be4a64c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98482ad45bb9aadff03128cfb2d6392b79", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982c31503bbb5884b0415496d65507d3d0", "guid": "bfdfe7dc352907fc980b868725387e98eabf857b26ef976742b01ee3c376981f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982714de58869b100548f19e60899359f1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f110769301ca33491e238b7d28e66773", "guid": "bfdfe7dc352907fc980b868725387e985b686ed4d6dac509af5b9b94214a96c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee6c5e2825554c8400d0be24b7bf2b7d", "guid": "bfdfe7dc352907fc980b868725387e9801f26390bfdb9c87ac541e1d55423a0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845a9a5161952c5eb3b2d0ef4f3317a6b", "guid": "bfdfe7dc352907fc980b868725387e983d28b9f0db0234b5e5700a485e273710"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a05f00ede9268460503bb1ecbc3fea3a", "guid": "bfdfe7dc352907fc980b868725387e983350a3f0f85300fe64b83e7c4a0878cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb190d2837ac7b0fa73beb339f18bcfc", "guid": "bfdfe7dc352907fc980b868725387e98941ec65b65566af2b941c9816e9f0a6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b70154318c075f6ed9850bda9666ea9c", "guid": "bfdfe7dc352907fc980b868725387e98a1b2c8266eb21831ea28e0c4e67cf9a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f002fd99d94f3779563a7a55ec6d3a1", "guid": "bfdfe7dc352907fc980b868725387e9854370bcdde1de474af1824e6edcc877f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98156b5b0a0aa4516b524ce4935b4b62d0", "guid": "bfdfe7dc352907fc980b868725387e9810bf31f402db2e307a62693acd3fff11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f06c8b226a058f870c6238c3dc8f1fe2", "guid": "bfdfe7dc352907fc980b868725387e984cb35c76a0b102f6cbfd03415421f2cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f47910c3f64449bb7477abe04f835705", "guid": "bfdfe7dc352907fc980b868725387e98c0eae51003d7ce609068908811952f58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d75559ac78be762b1e6b6d2944f0c54", "guid": "bfdfe7dc352907fc980b868725387e98f5a8cf3fce24ca5890f537a6bf478285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9c1506f3d884e00cf4c3fcd42bb8ddc", "guid": "bfdfe7dc352907fc980b868725387e98969a05a163becc67e67559abeba1609d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981788d1e35a3222c5f8258732a689e2f5", "guid": "bfdfe7dc352907fc980b868725387e98a940a52b6c0a1ad081bf34d8906cb5cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e0f0a89d209ecee6924779bd853a25e", "guid": "bfdfe7dc352907fc980b868725387e98312294a959af53815804f6b3a2ec9883"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822474948467cbbaecb733a6407e936bd", "guid": "bfdfe7dc352907fc980b868725387e98b21f1a4a0262108a71848bb6abe105bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da01ab0957c2ea08dc8de33433856ae1", "guid": "bfdfe7dc352907fc980b868725387e98bd465a96267ec176021587b8c203c8c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849faa1a4ca15d60cb2d3c8f2107fc5c4", "guid": "bfdfe7dc352907fc980b868725387e987fa28c047f5ba9909e474571038f4ec9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98034e84e297f64288c0018fa4e8f34ba6", "guid": "bfdfe7dc352907fc980b868725387e98653be64ec168091a3ac8937767467cdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e10e040ec20dca72af2eda1515a9d58b", "guid": "bfdfe7dc352907fc980b868725387e98e84a9a2d9e9fafc59517ae0c35d89337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beb64b80ab9ea572a615bf6c351fe826", "guid": "bfdfe7dc352907fc980b868725387e987c70b9566b553fea2f0f8d462915120b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea008d91b24d889c8320035ee5d4dd70", "guid": "bfdfe7dc352907fc980b868725387e98069d62851ac1903c5642745f9716be81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df188e9f4f58dc333aefd2d9b33cfb58", "guid": "bfdfe7dc352907fc980b868725387e987283aa78c0203ea06bc90dd657f15b64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847ed6dd3f2c4c94ae83bd6c0e64e3acb", "guid": "bfdfe7dc352907fc980b868725387e98e7dfe2777851053df0dbf2479dcd7b43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50a05d91885e747887dfbfa6aa640f2", "guid": "bfdfe7dc352907fc980b868725387e98481a9a0d780377efa6643e6d0229b180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2df035b23d5380f3b23ba9712c227e7", "guid": "bfdfe7dc352907fc980b868725387e988cf474d1416f27b2ab76d9c5b3a10890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f89772405820f684b83841dbf753d7c", "guid": "bfdfe7dc352907fc980b868725387e98d909467045a5ef04dc3d31660ecb880b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b181be4826dca60ad0fd56c42b663da6", "guid": "bfdfe7dc352907fc980b868725387e983bc577800c5c2611e27b456e3aee93c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989701672ff0bd33a1fefdd2d1b7257090", "guid": "bfdfe7dc352907fc980b868725387e989881738c86a0e2dccbaad476742c01bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbc7bf91262d3055424c4a5189c52f47", "guid": "bfdfe7dc352907fc980b868725387e98391529c8998c51bfd0606eeaca1a6a46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888cadc3558391dee1c4f1747eded37a7", "guid": "bfdfe7dc352907fc980b868725387e9868163c93bc26b33b80f86d32b0c3ec1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98510602fe1cfd48a466ef64663b7ae749", "guid": "bfdfe7dc352907fc980b868725387e98f5bf4df0961ca66dbfd21b1af236c0fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b807ea3e616df215db1d4bfbb9081e", "guid": "bfdfe7dc352907fc980b868725387e980e7fab2bcbe3387d38d63313656d1a69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c3d5680ecafef0246f1def0416f470d", "guid": "bfdfe7dc352907fc980b868725387e98590d8e35aa1fd2b1bbefecb8b9cc2ef5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808cdb1bfbfc66a2f205dcec2a8e79cc0", "guid": "bfdfe7dc352907fc980b868725387e98d3a6767af2e0439af8de06ce7c82795c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf5534a8bd35b61c70c0f91c753a5a38", "guid": "bfdfe7dc352907fc980b868725387e9818e980cafd758392e5a0f2798d510740"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98437c2c9feea0ad6e923c469f49f25c22", "guid": "bfdfe7dc352907fc980b868725387e98108fdf4e9ee307e4ad6605c8feb30cc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab3e79d44a35a2a41002ea1a6d91b33", "guid": "bfdfe7dc352907fc980b868725387e9804a4a476ecf801ffbc03ae8d680d5847"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabfb165a9d287281662498104830be4", "guid": "bfdfe7dc352907fc980b868725387e98288bdd8aba74c16339dfd13bb05474bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb6fbd7c77456c77068f8e6de14670ce", "guid": "bfdfe7dc352907fc980b868725387e9883fc5317a05af86fd8924cf1bfc2a682"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d19f5f4ae5ae4b8b4c47b40252f5522", "guid": "bfdfe7dc352907fc980b868725387e983a03a99a0f7588db0594eab2fe66191d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871f47e2580f810cb391408985dd1b0c0", "guid": "bfdfe7dc352907fc980b868725387e98829d47dbe33611e157c204cd4f362ca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875824a7155591203c96f72b5672be631", "guid": "bfdfe7dc352907fc980b868725387e98b27640b9bb5b87601cfd38838681e8e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae4ced2bd19d4f5e2c4b0b157c121aa3", "guid": "bfdfe7dc352907fc980b868725387e982abde40c6fa8606ad0c823b6cecb8105"}], "guid": "bfdfe7dc352907fc980b868725387e986738216bf3432740b8b6261eb86e6a7f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9874e2cba7029b7d9f3a96b63fcb85f5ae", "guid": "bfdfe7dc352907fc980b868725387e982f12b8fd0303269b0a973eebc22ae634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e98b64abcfa4502efbeed9941b9f08dbc31"}], "guid": "bfdfe7dc352907fc980b868725387e9828ef15012b09ad33035a1c4da4bd8f72", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc76c8907ab85411f2ad32e447c1fb58", "targetReference": "bfdfe7dc352907fc980b868725387e98c0cf7d8ee0c03c9d9476c5a72bca59ee"}], "guid": "bfdfe7dc352907fc980b868725387e983ab21375c7ea070327155cd87e1cf0fa", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c0cf7d8ee0c03c9d9476c5a72bca59ee", "name": "Alamofire-Alamofire"}], "guid": "bfdfe7dc352907fc980b868725387e98ba3679b4c4428e6a6d83c5308d3af99b", "name": "Alamofire", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9878a7912f930d33f69a5abbeb079e5b03", "name": "Alamofire.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
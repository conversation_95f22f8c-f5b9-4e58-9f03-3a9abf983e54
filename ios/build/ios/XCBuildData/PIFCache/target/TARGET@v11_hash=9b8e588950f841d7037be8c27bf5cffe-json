{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989f2bbed5c2f3ba412489698a471e8f90", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892005d2921633428b21c2808d64855e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98de9ac32112dae159d438443bdf61b460", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98997e66c0e98e1db88e18272b5fafa165", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98de9ac32112dae159d438443bdf61b460", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8402fb7425dde778441c192bedf25e0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c2d991e445d498eee7e2a70c206df6be", "guid": "bfdfe7dc352907fc980b868725387e9885f570a1fec8afde570b6011a9e01975", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252764a96eac86d5a05bb42c3b2dd248", "guid": "bfdfe7dc352907fc980b868725387e98082854ee4a41b3fd489314e873b937f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850fd8990ca340c6941811a0d60db25ba", "guid": "bfdfe7dc352907fc980b868725387e98f3b275689294f5da0d7646561606561d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb0789b05e55b95a263dfb104dcde900", "guid": "bfdfe7dc352907fc980b868725387e98a9b7e91d301d772c730f15807247a083", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b6f12ba5d80f9dd5016fa610a268deb", "guid": "bfdfe7dc352907fc980b868725387e98462f46004f19f4a2fda6d1dffe38501f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810af0207361aeb5885add56faac6e10e", "guid": "bfdfe7dc352907fc980b868725387e986575a6de7d67f4425a2ada3e509af23f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab42442809d1e5ce1abd57ca1d54e37a", "guid": "bfdfe7dc352907fc980b868725387e989be957624290feefc0177aa6bca85fb5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98045c38d6b60880df6eb879ef649a8aab", "guid": "bfdfe7dc352907fc980b868725387e982c1c18398d97485b9a952207ba8a2281", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861bfc0e909d09e65b4cd60e2e5db209d", "guid": "bfdfe7dc352907fc980b868725387e9802af5c838625970b4872195ef6a5fde5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d057117684200689de3b1e61404514c", "guid": "bfdfe7dc352907fc980b868725387e98d2a891aaf35a15669bb6405e475b1b5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5b3c7316701a13db6dea5237340ef0a", "guid": "bfdfe7dc352907fc980b868725387e98f284c603505c8a52851038f4c1cb1d45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888c78b3b70c6a0a7c0b2d169f5eb853e", "guid": "bfdfe7dc352907fc980b868725387e9842b2dd80710e75de786ce9dee85ff9ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980792d93f24471be0b8b551b6df8de3ed", "guid": "bfdfe7dc352907fc980b868725387e98410534e5af45d620a747f5cfbd78ddcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9e3a8deaab68f093a2fe78bb75fb610", "guid": "bfdfe7dc352907fc980b868725387e98c6b30541c48a81179af51ae8671eb35c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98709a4ba4d11b5df355eded857df06e02", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986ad8d84e13ad7c1a6a19e0af66c2cc5c", "guid": "bfdfe7dc352907fc980b868725387e987273c06ce13df10f6888be466d8c45e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc2031340724ee5440069b9e5c9ace65", "guid": "bfdfe7dc352907fc980b868725387e981d3ab80bd99acfd869b2c06667c9982a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7525b6a2c28d1341c581b6d07c4b5ea", "guid": "bfdfe7dc352907fc980b868725387e98f927cba9ee7a8ebc39bf57f25850045f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98017993ac9d526ed6b4799b6dc395000b", "guid": "bfdfe7dc352907fc980b868725387e9886c2a0c8a1d0069ef0c7145c65c30c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a42b3223f7771ac4d0612eb6d762062d", "guid": "bfdfe7dc352907fc980b868725387e98108bc5852255ccc67714e84eb526c6bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847483fe8c85ab276c7c92d5e7108aac0", "guid": "bfdfe7dc352907fc980b868725387e98c9eeb6ba93e695358b4d75836330bcb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe42b38cb32ef6550ebeeb70c4966288", "guid": "bfdfe7dc352907fc980b868725387e9810ca6f305582506da89d432333c0611b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a88381cfb8e8d2c6e586a4ff1372783c", "guid": "bfdfe7dc352907fc980b868725387e98a370bc12ce58307ef81145e14dafa61a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98608641eb3204238235a30f10805ac9b0", "guid": "bfdfe7dc352907fc980b868725387e98cd148bea990d6a3fb870c61646e64186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee07b58740a60d9f80121a2e6008183b", "guid": "bfdfe7dc352907fc980b868725387e98f3cc5bc7015333adea4a9e0c953e1c2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821900e3e801287f475390f303ef6a280", "guid": "bfdfe7dc352907fc980b868725387e986b7c2f8760ffaf440ea375915ddbb9ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f2230a3fb886a55c9ab2e54efd3f7d4", "guid": "bfdfe7dc352907fc980b868725387e98369086b790aa6b2b4cc9757d633bf8db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981062e29ae2c81424b4939c000ca34aa8", "guid": "bfdfe7dc352907fc980b868725387e98a64f8069bd87d144da648379ab47ab37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca6b41528a65942e006e0bb507049c33", "guid": "bfdfe7dc352907fc980b868725387e988c96dfc3e27c45de9ab5e6c3cc48d6e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98045b7d1b6d3c2be4cecf8908dc263561", "guid": "bfdfe7dc352907fc980b868725387e98f7521d0015268273e0bba7ae6f0861bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e9f5658f5986626b182aebc347b9ba", "guid": "bfdfe7dc352907fc980b868725387e98a64c2ab6006e8f33bde1108ec713bd0e"}], "guid": "bfdfe7dc352907fc980b868725387e986ab5620dc55715e8e8f6fcd49a2ef83a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e984fe03a62b0f3efa2b48d3a3971811d17"}], "guid": "bfdfe7dc352907fc980b868725387e98319a14c9389993bee5d96d155795db97", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987cd2512697fd8475ad5d01e85d7d220e", "targetReference": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9"}], "guid": "bfdfe7dc352907fc980b868725387e9868992c43aa4117f939ab7493574523f6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982a930221dc4925ae3ad26ac05af9179d", "name": "in_app_purchase_storekit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b48307e2bc58dc7155fc2e80bc197afb", "name": "in_app_purchase_storekit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
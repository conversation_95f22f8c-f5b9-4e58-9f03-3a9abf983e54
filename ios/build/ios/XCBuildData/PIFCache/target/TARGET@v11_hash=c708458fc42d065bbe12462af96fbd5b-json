{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4ed923f6bbfa9807a0ae4120ae6a2fa", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SAMKeychain/SAMKeychain-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SAMKeychain/SAMKeychain-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SAMKeychain/SAMKeychain.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SAMKeychain", "PRODUCT_NAME": "SAMKeychain", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c2c14fd0c3b8903cda53687cff1bef62", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837ad111f6800017dad9108bb5847fae5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SAMKeychain/SAMKeychain-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SAMKeychain/SAMKeychain-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SAMKeychain/SAMKeychain.modulemap", "PRODUCT_MODULE_NAME": "SAMKeychain", "PRODUCT_NAME": "SAMKeychain", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9800f6bc080699c8cb76be8e7e9fab6b49", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837ad111f6800017dad9108bb5847fae5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SAMKeychain/SAMKeychain-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SAMKeychain/SAMKeychain-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SAMKeychain/SAMKeychain.modulemap", "PRODUCT_MODULE_NAME": "SAMKeychain", "PRODUCT_NAME": "SAMKeychain", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98353b19694c3c89bfd75e355d0ba17a43", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981cb2c2047d74382084f8fba7108a66f0", "guid": "bfdfe7dc352907fc980b868725387e98285bb3ad2cdb87861310cc79e4242c0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f6ed7f0a7b0076dcd972a22b03f7d7a", "guid": "bfdfe7dc352907fc980b868725387e98a2c3bbfff80a17e21fbf442a59521544", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b2f56b68014a93b0562f6c270efe42a", "guid": "bfdfe7dc352907fc980b868725387e98a2f40e2dfc1fbc36d203893800466f0e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983d6040568a23de4261d43155b6e2496a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"additionalCompilerOptions": "-DOS_OBJECT_USE_OBJC=0", "fileReference": "bfdfe7dc352907fc980b868725387e986bce156fcc605d25b21c61e74f646ff7", "guid": "bfdfe7dc352907fc980b868725387e98d0eb6a97e26493deecf39d81dda73832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d67ba8b8b18d29b56e2e5c63e2e317b", "guid": "bfdfe7dc352907fc980b868725387e98cd0a23d26567ee5af271b043cda67cfb"}, {"additionalCompilerOptions": "-DOS_OBJECT_USE_OBJC=0", "fileReference": "bfdfe7dc352907fc980b868725387e98c651df5869690a95aff8799d50d9e754", "guid": "bfdfe7dc352907fc980b868725387e989ea507c4e433b5b16cc1d8c39c42d8bf"}], "guid": "bfdfe7dc352907fc980b868725387e9835764122fd36bc8d30e69f839ce9ab1d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e98e7646b79c9930a92cb70b54d79b5a071"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98226c4235dc94da08f85a05de7f32e4d8", "guid": "bfdfe7dc352907fc980b868725387e989730bc5fa9f4262a9d41063882a791b7"}], "guid": "bfdfe7dc352907fc980b868725387e983e7b227c242df1d752be67f0886197f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c8c2d59d3372aae2a4c29a3c17addffc", "guid": "bfdfe7dc352907fc980b868725387e98dbe66597f5f069db72273a1226acbfd4"}], "guid": "bfdfe7dc352907fc980b868725387e98bc9dccf98eff81e12674290dd5983002", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98ab481924b0f0da2f067ee22a843e9cec", "name": "SAMKeychain", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9887f5f59cb146b23d2bd54d1c56ba027b", "name": "SAMKeychain.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
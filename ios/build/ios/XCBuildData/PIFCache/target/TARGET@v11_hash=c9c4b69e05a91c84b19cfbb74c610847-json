{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988471e466f06a38f7fc4885d3263c5bbf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/lexo_ttplayer_decryption/lexo_ttplayer_decryption-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/lexo_ttplayer_decryption/lexo_ttplayer_decryption-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/lexo_ttplayer_decryption/lexo_ttplayer_decryption.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "lexo_ttplayer_decryption", "PRODUCT_NAME": "lexo_ttplayer_decryption", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9889de120835fa367110c0340aa17d1383", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b8b0019ec3d88e9566ffa25af629fd8a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/lexo_ttplayer_decryption/lexo_ttplayer_decryption-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/lexo_ttplayer_decryption/lexo_ttplayer_decryption-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/lexo_ttplayer_decryption/lexo_ttplayer_decryption.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "lexo_ttplayer_decryption", "PRODUCT_NAME": "lexo_ttplayer_decryption", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988cf8561871bd554f9e714cc023933a36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b8b0019ec3d88e9566ffa25af629fd8a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/lexo_ttplayer_decryption/lexo_ttplayer_decryption-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/lexo_ttplayer_decryption/lexo_ttplayer_decryption-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/lexo_ttplayer_decryption/lexo_ttplayer_decryption.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "lexo_ttplayer_decryption", "PRODUCT_NAME": "lexo_ttplayer_decryption", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fa333b42e46a14794d91d8e51aff764", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98753ec86d377dda7f0e632110ef1d04db", "guid": "bfdfe7dc352907fc980b868725387e98dacb79922f8aebfea21184dd5b69d15d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cf31422d9e0a73ec3ed78ef440f497d", "guid": "bfdfe7dc352907fc980b868725387e98fe1d70303c60eb1705c349766674e7f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f5140ea50631519cd24bc6921d41cb6", "guid": "bfdfe7dc352907fc980b868725387e98b7b895fa7e7c8680d8a0b5dd5a16e405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98853125334bd91006cd2fee3c0f076b9a", "guid": "bfdfe7dc352907fc980b868725387e980d389f9356ab3736975aba204764ae0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a1659734f00b9fcd04fa0186a6e1f1a", "guid": "bfdfe7dc352907fc980b868725387e982ea307cd74dfc8922aa6bc7b33713c42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1f00ea5c2be00085d31dbc77f21cc29", "guid": "bfdfe7dc352907fc980b868725387e981c6086883ab414e458e12b5414128b71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989861f698d3d76f542a6d10e451d15be3", "guid": "bfdfe7dc352907fc980b868725387e98cae4a740d2a60a842c1a95ef9ebd41e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d099002991ff09ac7a383970f50143a", "guid": "bfdfe7dc352907fc980b868725387e980c1c8679dc84e5bc483671ee2dd0fb1d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98057673c7d05475282136e6b916b8301d", "guid": "bfdfe7dc352907fc980b868725387e987450d4f7d4926f7e525d8b1a9eba8840", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b5adcd1833b434ed6839fdc1d600800", "guid": "bfdfe7dc352907fc980b868725387e98fb3fb43a3987af5f063d78e005d033e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa2dee9ab1bb5eea36a6e5a7a640d26", "guid": "bfdfe7dc352907fc980b868725387e9870b4eaa12ccd603f756e8b069953151c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9002a63be7df9105a1cfbe5048c97f9", "guid": "bfdfe7dc352907fc980b868725387e9858ce160203e6a3cd50e379aa16203a49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873fbbe6dc3e14563f8e020fb8fca1afd", "guid": "bfdfe7dc352907fc980b868725387e980b8898687a3f860c098fdbb2c2175dc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a47d3e7ac685f741598c3f4e3f5ca774", "guid": "bfdfe7dc352907fc980b868725387e987ce21669355c4e98307481525398ce88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c2d299a48064d11432d002c5929c9d5", "guid": "bfdfe7dc352907fc980b868725387e98d5f5b926a695c25a3cc5a918b6fbbfcd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984a998cb09f5ef87129dcfdbb737cf3c0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9806b69722363185c8dc659ba7e3f79909", "guid": "bfdfe7dc352907fc980b868725387e98fbc6400183d6aa6418affcd44ff5bf73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b56ac428f3d1d0a9860b5b99f9dd0776", "guid": "bfdfe7dc352907fc980b868725387e98778bb86cbc4229a714b20d62d80c8d00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d7cc3f9675e8e5a5e18d9abfe455b6", "guid": "bfdfe7dc352907fc980b868725387e98850b1e288c0b259b609868109ddb4086"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5b93d6c775be6dc30d30812f5e7d335", "guid": "bfdfe7dc352907fc980b868725387e980f527295f899bd7195bab767b6e7d172"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a2d217e4d0edf0861ea7d1b36560c97", "guid": "bfdfe7dc352907fc980b868725387e9846459a492a771e6fa6009e94c2cd92fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab738dbde0cdabffb38ac627601e19d", "guid": "bfdfe7dc352907fc980b868725387e98db4f6b03a62d9070155ba807a511d221"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863150ed70c673d779a2e8173c51a8f4b", "guid": "bfdfe7dc352907fc980b868725387e980449c3d1da1c389ed687061d53a9b6cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d7c6dca8c85afcc00d5be12ab05e7f0", "guid": "bfdfe7dc352907fc980b868725387e987f10c6c25f7040d3f0a6333ff919589b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be49f0b09ddb1fd0c18c980a30bf7c9a", "guid": "bfdfe7dc352907fc980b868725387e98569fdeddf6194df3d130164ab250a77e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980004476c66552c609cc60f34a440c5dc", "guid": "bfdfe7dc352907fc980b868725387e98b57a9a2621d595f467e023543e632245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad202cea0003ddd464bff89b3baefbb5", "guid": "bfdfe7dc352907fc980b868725387e98aef8bdacc9357d71821054f806314600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7c8bcda6f41290090c7bd836ed10328", "guid": "bfdfe7dc352907fc980b868725387e9831ff7fe0e6e96acf15acceeea29dd56c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e4eac96bb85036681be578b8bb2aef2", "guid": "bfdfe7dc352907fc980b868725387e98ae59461012b5f75c034e75babf388156"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858d673677c58aba1c939a35fea836287", "guid": "bfdfe7dc352907fc980b868725387e985866ec513e3cc2a3d9fc34e595b16872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b9807db6ee94882023603dfee2a6f37", "guid": "bfdfe7dc352907fc980b868725387e98018eb3636e6204457ad050f04acaf1c5"}], "guid": "bfdfe7dc352907fc980b868725387e9868108af4d9ffa5f02f6251194210be7c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e982af462dc73232a246985eda403e8af86"}], "guid": "bfdfe7dc352907fc980b868725387e989b0533fad45202916dc1759d4337baaf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981979fc7f550e4ed02c7a709e0096f2d3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981cab35117ef190eb39b41697699c0322", "name": "DRMLib"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e988ba6a5c0a072ab37e0ed5830d4ad77ee", "name": "TTSDK"}], "guid": "bfdfe7dc352907fc980b868725387e98c8cb412d247f0d24a89333afd221670f", "name": "lexo_ttplayer_decryption", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e984dbca0dfbaaa5956f61cf8684a45fb07", "name": "lexo_ttplayer_decryption.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
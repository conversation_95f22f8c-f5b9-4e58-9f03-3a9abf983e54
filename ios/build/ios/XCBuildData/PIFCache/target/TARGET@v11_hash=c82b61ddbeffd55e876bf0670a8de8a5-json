{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984dac1034fc48a1669cd439823fabba94", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98162636a40e5de6482086bb0661e5a5b2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986644ed45f333833d0b73d1c066494226", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eb30db75f54c911e54bbfcb1db5c7b04", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986644ed45f333833d0b73d1c066494226", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983232eec7fe3429d2a651ee9b321b3d2b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e011722c7d6fe18c3bd40fa6ffef2db", "guid": "bfdfe7dc352907fc980b868725387e9833f68d4997463eb8555a864e464729bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98935aedf8e056978ed10080e6288f7eee", "guid": "bfdfe7dc352907fc980b868725387e98bd5a439dcb968370645b5421ac633638", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846776e5e6462d0d3a1278981fdfefe89", "guid": "bfdfe7dc352907fc980b868725387e98dfd4f6035c85ad950a1c83d52b150898", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826db4a13b61fadb00726f94e8f7520be", "guid": "bfdfe7dc352907fc980b868725387e98d07db355e9f47079f460d14e3dd65bbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98323dfa45d7057cd37cece30cd5e8cb0c", "guid": "bfdfe7dc352907fc980b868725387e986a12d9412d026b597276e9af56226440", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c5437a931cf48503f1e27f27a2fdffc", "guid": "bfdfe7dc352907fc980b868725387e98db53298a71e5ee4b9145118292c61525", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6213ef92d37bd47b006671bce54531", "guid": "bfdfe7dc352907fc980b868725387e98de4dde719c427efcd15acb83bba152c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c6d7ff9569c30654a8d862f146834b6", "guid": "bfdfe7dc352907fc980b868725387e983b6eae12d4dde96a88aa055f1d96d996", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b634d3c3b4378ee8ba71a9aa4d4e7a9", "guid": "bfdfe7dc352907fc980b868725387e98ce619e71c139a4e7e01ff07d6b977052", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4b927d12752418a74aaf2640959111f", "guid": "bfdfe7dc352907fc980b868725387e9871540d80cafa0d25b659da603e726a6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98774f2e990ca81a7570ee00f15b348877", "guid": "bfdfe7dc352907fc980b868725387e9822382e2c1c040317654c75e49e9ab5dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b326ec306e9d49cc1926a28c9981868b", "guid": "bfdfe7dc352907fc980b868725387e98e700c186efbd35241f0e1933d3ec4754", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cfd1fb215e0c5846379cdd4dfbe35f5", "guid": "bfdfe7dc352907fc980b868725387e9814b08d880b2d0db163953e926a5826dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c3f5e9cf85d3fe4a1df235e2c38afc8", "guid": "bfdfe7dc352907fc980b868725387e981c24c21774ed6d0cdb606dfd29e45fc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7dd25944d522bdc4c0a359d19a167d2", "guid": "bfdfe7dc352907fc980b868725387e986ad9929fc9180f230c002f2d11416480", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98117d156cd1d39100b9506808d392ea88", "guid": "bfdfe7dc352907fc980b868725387e989930b4bc77a4ea523015f9fc613f569a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982639ae80f976f5ecf97da573a0caed77", "guid": "bfdfe7dc352907fc980b868725387e98a8372e0ae1064f697f317b58fb8d5cc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df915a42a96c2933c66c60dfa9737cc5", "guid": "bfdfe7dc352907fc980b868725387e9847668a1ff5b94e8ceaa935959927231e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f243bb7d120caf0176e2beded3718d0", "guid": "bfdfe7dc352907fc980b868725387e98cfcf74f8c1828b8bce326c68f25b1df2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818bdcd14c0145607e5e42c8eb57408ab", "guid": "bfdfe7dc352907fc980b868725387e9836653dfc51ac943088e7c7b25a130196", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c99453232b8efcfc0e2e794f7fcce8ab", "guid": "bfdfe7dc352907fc980b868725387e986aee99899ba2a25f3fa91705e747a955", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985008f3f35609ea997c0aae90bb97aa8a", "guid": "bfdfe7dc352907fc980b868725387e980750f40cec6b28f31df54e130c278632", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98006cfa65cdffe34e092074373ae09713", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c6a4d9838bab61c9432e946dac89eb75", "guid": "bfdfe7dc352907fc980b868725387e98952bf3a2fc4b8c23b8ac6cc42f0fbbb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d006d8693b3bf5deca6dbd3fc9dc55ce", "guid": "bfdfe7dc352907fc980b868725387e98e2539d435c7b51a590765ab45f8ec21b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981380d4ab60c459240067f6d4df6acf95", "guid": "bfdfe7dc352907fc980b868725387e98554620480c1abaf2e3acdb1546a3b108"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d344adcb7630a3113dacdea33b990505", "guid": "bfdfe7dc352907fc980b868725387e98fd0f2d03e684b658f7861179390dbb14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c10bec3a06bf5d4233097178e567f3", "guid": "bfdfe7dc352907fc980b868725387e9884507720635b1d14efaa82d7be4242d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0d5d90e7020429f48a1aa08196f9032", "guid": "bfdfe7dc352907fc980b868725387e98d5b59f7a162d34ff91a2aa2773a623d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f806426b2e1bbe6d98596719ff6f32", "guid": "bfdfe7dc352907fc980b868725387e9800d25752e555c69e06ecb3f7d395d642"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d84fb7c2645dfc0cd5bc69e4cf17966", "guid": "bfdfe7dc352907fc980b868725387e98c3c64329a45a3f112e56f9518ea83ba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98840d556a5e7a20d67b8190933460cc05", "guid": "bfdfe7dc352907fc980b868725387e98cb4c2ee83c214651c50666853fbfc67f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856f3a23ec79e28eca3333c7cd6f964e8", "guid": "bfdfe7dc352907fc980b868725387e9806671765263f63a048852af9ec31b595"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7b911247c3385e7df679c7816d27782", "guid": "bfdfe7dc352907fc980b868725387e982401d11e5765af1ee65d7e47f03b151c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e257f2acdc836803d08e834ffd8e251c", "guid": "bfdfe7dc352907fc980b868725387e98772d487b1f7da413b5da6e5b98bc900d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980640b921f0aa37654ca070ffa5648602", "guid": "bfdfe7dc352907fc980b868725387e9888e7b83d665b4abc3a646f206446d373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e2ad56dba5c8373f1f1f9047edea461", "guid": "bfdfe7dc352907fc980b868725387e98d8bf24cfa1fd6a18ab8057b88720e586"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98522530f271635fd8004ceb14b6ada8cb", "guid": "bfdfe7dc352907fc980b868725387e988d8b633ecee02250cea2bc4d1847c397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861899d91a922ad58901632c479f63e13", "guid": "bfdfe7dc352907fc980b868725387e98712d7393925e38e5bde49742f214463f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac1635ffa62285f0210656085a20e9b", "guid": "bfdfe7dc352907fc980b868725387e98e864fa7f227726358fb4b9e9d0140dce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a88d9f051d010e36d65f9d34a1929d6", "guid": "bfdfe7dc352907fc980b868725387e98d7e7271f7994e6cd47d471c795b1ac1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805f4a85f3e7c1c2cc3f0cbbb510664b4", "guid": "bfdfe7dc352907fc980b868725387e9895456b8f7614c320da15402903a39d47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c970d71b6fc93718b617a5178dd9bc3", "guid": "bfdfe7dc352907fc980b868725387e98e78c705b2756519666768f20ea2a88d0"}], "guid": "bfdfe7dc352907fc980b868725387e9829bce812dcc574d02a38e11c3e47ea36", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e986d6dcd9e15bf7f10d5063ddf81efed67"}], "guid": "bfdfe7dc352907fc980b868725387e98dad65d000e3bfc0b4827244602414a59", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98751e059e37ec6a0960902c633acbe8e8", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e985733311432d3292bbbc5a477ff7c26c8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9828521173eb6715c9a4ce1364be7fe1ab", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/ios-ntp/ios-ntp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/ios-ntp/ios-ntp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/ios-ntp/ios-ntp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "ios_ntp", "PRODUCT_NAME": "ios_ntp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee3ddfc323a1545330579e16499f7741", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9864c1e3e8eb324be1b62356a515fc02bd", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/ios-ntp/ios-ntp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/ios-ntp/ios-ntp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/ios-ntp/ios-ntp.modulemap", "PRODUCT_MODULE_NAME": "ios_ntp", "PRODUCT_NAME": "ios_ntp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da9486b1e6719f7a540953e257295d1e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9864c1e3e8eb324be1b62356a515fc02bd", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/ios-ntp/ios-ntp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/ios-ntp/ios-ntp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/ios-ntp/ios-ntp.modulemap", "PRODUCT_MODULE_NAME": "ios_ntp", "PRODUCT_NAME": "ios_ntp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9815af37ab406a4e6ec7b0c1064dfef5aa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98296df46691099a3c821a2a8f09054ba4", "guid": "bfdfe7dc352907fc980b868725387e9846b45607344395b562b5f022c80d24c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f0b5469d6c96d8b20bb4fb6e936227", "guid": "bfdfe7dc352907fc980b868725387e9876a416a6ce39d2d989fcf2318d00cc72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7d382fa76bdf7ecb5f12a5358eaa5f6", "guid": "bfdfe7dc352907fc980b868725387e98a40bf7434b5aa46d6868d7196beb9205", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d241518ceb193f0ffd14ced9429ddccf", "guid": "bfdfe7dc352907fc980b868725387e98ed10d380a0fb01c2b2b379aeb19defd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d48dd4df07795f1ac134d8aef787e8d5", "guid": "bfdfe7dc352907fc980b868725387e98155410064b65857a2f766eb6d8130a3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0e9996410ca451b3674166ef30b9643", "guid": "bfdfe7dc352907fc980b868725387e98329c66a99d3f14f0f8c2a24e7d54275d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1d182b4b6f05d376a4dcb7d1d071bb7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9809472281707c6cfd9f87f05e3c9bf7be", "guid": "bfdfe7dc352907fc980b868725387e9829ca31bec7064075797d7716b18431c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd8113f2c2281b6d0e2497eb4a80b388", "guid": "bfdfe7dc352907fc980b868725387e98f53feffda92fc78e13a0d16aa3cfced3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873b042476c572390b8a87b955f3dd577", "guid": "bfdfe7dc352907fc980b868725387e98d83a93ecd7549b99c6660a2af051b3e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc48919e572cee38d2dc0798fc508a4b", "guid": "bfdfe7dc352907fc980b868725387e98343b46f55d143c56a2e8e5ed36be3338"}], "guid": "bfdfe7dc352907fc980b868725387e98535d6e12947cfc3381131a18492d0267", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e9828b5f5f13a355c9bd82c77c69673dfda"}], "guid": "bfdfe7dc352907fc980b868725387e98d7b5f568e81df1a2c177f3167da69335", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f1788a3d79bc3c3977bd8e85c88da3ff", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98674427677ed50870bbe9310a50295dc1", "name": "CocoaAsyncSocket"}], "guid": "bfdfe7dc352907fc980b868725387e98d833960feb5c94884cbce469a34b365a", "name": "ios-ntp", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8e74217c8396dc1b5cd53feb65e62e0", "name": "ios_ntp.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
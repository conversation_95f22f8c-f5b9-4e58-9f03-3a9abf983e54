{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d4a55a696c8c2c3ade34d19c3b37c42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6737555ff15c15fe26c4791ade2037f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846f55ee928a4993f9cd627700861d78b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b07d3a6f8c8ae4da3a12d89db86ed5b6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846f55ee928a4993f9cd627700861d78b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98381ca37145d595cfb1f06fe2c13cf813", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98311f628f8c8e9b74b2c95e98119c2d25", "guid": "bfdfe7dc352907fc980b868725387e9861aa82a7c299057a530934954b257566"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efe3af1853791bf47ceef1b3729cbd60", "guid": "bfdfe7dc352907fc980b868725387e98833e503366ce7d6f5bd0651b8b80033e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98874208ecca937673bfed8ead3f02a35f", "guid": "bfdfe7dc352907fc980b868725387e98cd8bb6528fde0c8b49dc9e428fe7e363"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfacd8d06085de3d9d337f96e4924abd", "guid": "bfdfe7dc352907fc980b868725387e98b22802d9223dc67d84d8e17a532c9080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98188cfbc4f88c9582ca6a38b8744b2132", "guid": "bfdfe7dc352907fc980b868725387e9815d0a28a5326b3a716e459e54ff9430b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcbad17bfe37bd7e1c0343d2baac2fda", "guid": "bfdfe7dc352907fc980b868725387e9844c251b12cbe98a7ac4e86f5f244b239"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b03e75ccebe5d76ee8d74e17be190ecf", "guid": "bfdfe7dc352907fc980b868725387e988677ffb435559344d721e674f4d7cbc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ee792ebae9696f359894531aab7ea79", "guid": "bfdfe7dc352907fc980b868725387e983591ec8ad450a50644fef30a0b25888e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b39906f9eb33d6e97332e377bf713ac", "guid": "bfdfe7dc352907fc980b868725387e981005d2630f74b06b24c295294fe24416", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98282e37bf52bbf1c20e8c54bcde471416", "guid": "bfdfe7dc352907fc980b868725387e9843351fa36e1d5f3b4ec6803f91b51b02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d83124b8627721a0c50f0e54f78c8b97", "guid": "bfdfe7dc352907fc980b868725387e98e398f74e1b0d09d45f8afa85b97f77f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981781ab5aa5f3f23ff1b46631c58caa4b", "guid": "bfdfe7dc352907fc980b868725387e98b66eb4ccd115536603c14849e3321a73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b34dda49637034a3e6d06d739fe3e5ea", "guid": "bfdfe7dc352907fc980b868725387e98c938b2cedb2422936669f82041e56ae5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d9b650169636ef0a7d25d809fcffeda", "guid": "bfdfe7dc352907fc980b868725387e98b60ae82b06f37b96cd5eda0b32daf326"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f7503489f704d44994663fc3dd661a", "guid": "bfdfe7dc352907fc980b868725387e98d100a00b5f5952fcb7c169a6d8f28eed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98555f6c45f300f393665d3339a95b97df", "guid": "bfdfe7dc352907fc980b868725387e98314d1bce0a1e54a659d73bccae6f3b56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f94936aa7a070ec5a0ee6e2c20358a2", "guid": "bfdfe7dc352907fc980b868725387e98f3bd55aa657bea7a77e2f59572a9ddcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98792247c6e834b4b2fb682f0d5ecb3bf8", "guid": "bfdfe7dc352907fc980b868725387e98adc794cf8f08c7c1ca2a8757e9c2bf30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98213968af30bcdaa4027e8fcf6e311a6a", "guid": "bfdfe7dc352907fc980b868725387e9851aff6e105aaf9db2555a2eaf0e06858", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c353ea9cfbf6039b6761940150b6852", "guid": "bfdfe7dc352907fc980b868725387e98ba39d0ce809dcff2a93574ffc61f092e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dd87edf448fcbe86034109292d99709", "guid": "bfdfe7dc352907fc980b868725387e9808ba012723697e43a887c3164df0c502", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ec30b819ed736d1fc82d63a675e3603", "guid": "bfdfe7dc352907fc980b868725387e98fd1077251da35349b5d225b9de65fdf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e94944b492243f6570833851a61e4e8f", "guid": "bfdfe7dc352907fc980b868725387e985dea6bf5a667a9bad0f3079f79b777bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98da1505be6f10c19714b64957531e8f8a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba49c38b7bb08811b5dad7819cce0597", "guid": "bfdfe7dc352907fc980b868725387e98fe71e921d823a98fd0bb0dda9ca43456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a081696d28d0bcce347f48798d06328", "guid": "bfdfe7dc352907fc980b868725387e98a186e145e6d2cbf108eb0e14e136463a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b5fd375918d88d0f6c4e49209f3ae60", "guid": "bfdfe7dc352907fc980b868725387e98bafa85a35b45147544c8d86a72ba0bfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9b9eb8cf94e02bf1983680cb6227142", "guid": "bfdfe7dc352907fc980b868725387e98b76cd5d05d5355a56ae1c69591ce3409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c2e8ce65850e78db5ede217cec6ea8d", "guid": "bfdfe7dc352907fc980b868725387e989ba13c44d02e6c78fcd6dc25c86c122c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6d4232ec3c7a30ac2d0d7233bf0197b", "guid": "bfdfe7dc352907fc980b868725387e98017d0100818e45b9690d3dd18b324380"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851fa4dfdbe3f96d4b9e5fdd168a10bd1", "guid": "bfdfe7dc352907fc980b868725387e986893ff20460b3eb8377af733e12cb3c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98326eedd852062e97d58a097a9169eab0", "guid": "bfdfe7dc352907fc980b868725387e98a38d20b3c3b6f4f1ddfb8cfdbef94efd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d500fdc294dacd0d231c73a0db23348", "guid": "bfdfe7dc352907fc980b868725387e980f64bb384586e17ff3b61194e13726d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850c7e8d78a6171f842cd179b5f57a6e9", "guid": "bfdfe7dc352907fc980b868725387e98fadf5846d22883d4420991193ad15c12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e02ef31d9f222277c98c6e178c75fa14", "guid": "bfdfe7dc352907fc980b868725387e98c43d29bf2acd241e010c29450ad55e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dec7b20b6d3938f712c3d1a601b805a", "guid": "bfdfe7dc352907fc980b868725387e9853ea90540e2c9cd0657689d54379bc6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a5cccf2f451ee28a68a6bc9da773528", "guid": "bfdfe7dc352907fc980b868725387e98a5a9eee816498eda848e11ae9731d688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983af799cdd9c731d422a08a0c9c203cf2", "guid": "bfdfe7dc352907fc980b868725387e9803b5983f27331cd20c25a4b66ad661ad"}], "guid": "bfdfe7dc352907fc980b868725387e98c6eb3e81e5d1931b21a8ad6c519fb8ea", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e98100dad6866333b77ca6bec6dfeb54b0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bf9f81912b8a94a8584885abd980044", "guid": "bfdfe7dc352907fc980b868725387e9882a41ca2184f96e16a47b237ea1b6a10"}], "guid": "bfdfe7dc352907fc980b868725387e9823dda3e2196e312a888a735af4789e65", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fdc5616a2063b57173aedb745e185542", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98aefafe33d2c4e652e840ad275a7bab05", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
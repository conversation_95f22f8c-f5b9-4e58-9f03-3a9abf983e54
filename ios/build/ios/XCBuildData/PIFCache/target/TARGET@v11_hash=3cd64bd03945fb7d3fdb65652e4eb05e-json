{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982c600157b631547595b818c98fa89a5b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f60f21207bdf48370aadaa7a5944056e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cc248d399e268ad6f869dea9efb5ecce", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca83b02fd91b34a498c4c1d10d117548", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cc248d399e268ad6f869dea9efb5ecce", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d212063812730634d67c1838935e42c2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f4523a7c2791612ab294592a09ee3895", "guid": "bfdfe7dc352907fc980b868725387e98cf268d0591cf01ff9e9aa016c7b4b645", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed430bbdf063a7bd3868bf8784c88332", "guid": "bfdfe7dc352907fc980b868725387e989829b04e17135bee713425298a503cb4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c19d2b4f37442ea98032409f4d8a675", "guid": "bfdfe7dc352907fc980b868725387e9847b5f576f148da7469712a2b677e2cb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831a6ef2b034e85df4c782c3894dc1d80", "guid": "bfdfe7dc352907fc980b868725387e9889a8c7d27ff2d452aacea92b7e1e4ca3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d19834e99ab59f57ea2613a644ca71f", "guid": "bfdfe7dc352907fc980b868725387e98b3d21d56da434ddbbfee8455ecde033b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2b549f540372e15071c59d3c2e8158f", "guid": "bfdfe7dc352907fc980b868725387e98d754a7386b4daf1bddfcce10156713a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c184ba9f53b4a63f581f31e93aae842", "guid": "bfdfe7dc352907fc980b868725387e9854b999948bdffc657178484aa06842ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e63007e38d524dc384d6f6e8a1b8fe", "guid": "bfdfe7dc352907fc980b868725387e98446f59b5447988bdf58b00ebf5e69407", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800552d7f5a822d09758b9f9a950a90a4", "guid": "bfdfe7dc352907fc980b868725387e987db7b47c9e812b1e5d23bebe22b8d069", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac2fca3a49c93548458895195d882270", "guid": "bfdfe7dc352907fc980b868725387e98416d4908a736c32bc66f8fe59f16555d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a7761eaa9aca916c5d15d4bd588434", "guid": "bfdfe7dc352907fc980b868725387e989d2d6958e3eac558e3db91b0d8abfcd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e29d1fcdf6fd9ae04a86a57235136d9", "guid": "bfdfe7dc352907fc980b868725387e980f5645dcf3c3affefcda0679bd0c1876", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15bd6576fe475db70813e3c7a1f6902", "guid": "bfdfe7dc352907fc980b868725387e98ff6389590beb91371268c67c7c9e44aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887704b0e04f81a1d8baabf15205c9cce", "guid": "bfdfe7dc352907fc980b868725387e9811f74d0cc73470e4677ed5946051f565", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b5cffa73581607071b928d885cc3556", "guid": "bfdfe7dc352907fc980b868725387e98ef77a2584fe9d0b53e7384cc112d2b07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981acdd7a8fb34d20dfd7bfa17146d85df", "guid": "bfdfe7dc352907fc980b868725387e9854d3e2b9d163e0b3d0f728a910d2acde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e2695c108aa00cce5717d61bc42c5ee", "guid": "bfdfe7dc352907fc980b868725387e98681f7da512a8600f21ed77e10b289162", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d1e2533ce4203b42c1f3652d5976c7", "guid": "bfdfe7dc352907fc980b868725387e988e36a74e7de7e1719247e5251466900f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f7ac868aa8977eccbc6f5a944516ad6", "guid": "bfdfe7dc352907fc980b868725387e987400c2fd5d509d441814709caf770037", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4bb614e1c0731a89154a6a26008300", "guid": "bfdfe7dc352907fc980b868725387e984d1d23ae125cdad1bfa5423707a5750f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d89ccfd000973e0d6c095c29d65ddb5f", "guid": "bfdfe7dc352907fc980b868725387e983c2f2ea62b024b4c6b2b56740587afb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aad60ee8d371258604095dfee0d1dc00", "guid": "bfdfe7dc352907fc980b868725387e98f371dcda383928e2d1f31d75afd39049", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcddc3a4ad8bd11b4059ac58a6ae2be0", "guid": "bfdfe7dc352907fc980b868725387e98873072a7f0c0f5f05047afbc3d381f61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ed22f90e4787db74efe8b6dea36e094", "guid": "bfdfe7dc352907fc980b868725387e98a13e606cbbfd39a170e763f81b8c82b0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985da9e827eef44a73ab95aff9462753c7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d2e12e397178cf97db5a525751285187", "guid": "bfdfe7dc352907fc980b868725387e98ee1ad9e39638974c79a3e84d294aa799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982840d3fa064280dc353162df96631dd1", "guid": "bfdfe7dc352907fc980b868725387e98c11cbd6cb16e15cff65d12e813c21b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f916af9bb986998c1263bf40cbba0008", "guid": "bfdfe7dc352907fc980b868725387e9872caba2e4db2d23ebe254942b310eda0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfad1fc7910972865ffb1780be87f3a3", "guid": "bfdfe7dc352907fc980b868725387e983b58d2a48279d3a1f9a5f38a3012af23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98603d1860ae4a09c615fbd2b56d057b79", "guid": "bfdfe7dc352907fc980b868725387e984fe9c5f7a7267c22be83ae3d181265c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c299c0af9fc468a016d213d339ce4f56", "guid": "bfdfe7dc352907fc980b868725387e984341dadb1f2a53e6660449c7d255093d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1c04ea1223b773d4e74bf881944f66b", "guid": "bfdfe7dc352907fc980b868725387e9870c7edeef67f528f3504b26ec2faf03b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98276ef6f341e06d01ab08c4058d53bd6f", "guid": "bfdfe7dc352907fc980b868725387e98ba653372ac19c2fac55cbd610505f4f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cacffd5737760572a1d630302f6838d2", "guid": "bfdfe7dc352907fc980b868725387e986843c1fe1e24477b0f29905b439420c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bbe2f6926e70a1dfb39e6330c622318", "guid": "bfdfe7dc352907fc980b868725387e98c62567e26569c30798cdfdf04f33103e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc35f7f03c5011bef8269ab606cf4b5a", "guid": "bfdfe7dc352907fc980b868725387e98c11783cf5fb5e69cf0b3482b82c98dbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829f59b1404fa0baa85c7627d6fd142cf", "guid": "bfdfe7dc352907fc980b868725387e98f111a4fb70b24d87b43b39b8d3c9c8ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983678616c81fec313cbb8715874d68822", "guid": "bfdfe7dc352907fc980b868725387e98d8642ac20f20d1b8a1773f52e5fce9dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98948d3b2e7105c0cde2f46792fcbe3f6d", "guid": "bfdfe7dc352907fc980b868725387e98da27b7305c7cbf6eb4b56c192788fa5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98194dbbaca4cb3c43f5b3c585872c2ce0", "guid": "bfdfe7dc352907fc980b868725387e98b14d49436f67a511d89e7718d7b1da53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6879a075835e712327565bc36861320", "guid": "bfdfe7dc352907fc980b868725387e98fed316f13816fbc2f25c8094b702f14f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fa3dd7da3eb7afbf3a9a7530dabd594", "guid": "bfdfe7dc352907fc980b868725387e98f6b0fdfd95ce5c14df12264d32b3d845"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3afe39b44cce672aba56187c0e20bb0", "guid": "bfdfe7dc352907fc980b868725387e9824330a36d40ea0c80b4a659b99a4e789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988983a15e8baa52b2ff97be8dfea613e4", "guid": "bfdfe7dc352907fc980b868725387e98bf19dba0c89cddeaf05a0f7a639ee39c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0f67894ac24a0a7dff3f34e029c5c95", "guid": "bfdfe7dc352907fc980b868725387e981025d08761df4f9d38a12430c903bbff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984521b5c155341d68a9f575506528ae60", "guid": "bfdfe7dc352907fc980b868725387e98de00129e3270ecf6ff3b9a1449b10c5c"}], "guid": "bfdfe7dc352907fc980b868725387e98ef387155c7be58dd1abcd21a411a4e6e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e98c961a969fb41a7bdab90c538b30528fd"}], "guid": "bfdfe7dc352907fc980b868725387e9817c34e700eb0a17b1be9eca81b57986b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984da9032ff76790acb4b37e742746ef15", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98cd53d937e824fad9f4527eeeb684cb40", "name": "Google-Mobile-Ads-SDK"}, {"guid": "bfdfe7dc352907fc980b868725387e9804037656a8578d8e730f9d99c54e40e2", "name": "google_mobile_ads-google_mobile_ads"}, {"guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview"}], "guid": "bfdfe7dc352907fc980b868725387e9811f9347c979613c5502173cd5b43060d", "name": "google_mobile_ads", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b1f16b2926bf8e21151b2cb149aa6540", "name": "google_mobile_ads.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820fc5bbd26a5e571019fda5003045e94", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ab9759c01c118828e6a7afe450bffd5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98705db18067dd50195b132f8f7e532c8b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed278e5f520c525a2067932639ada823", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98705db18067dd50195b132f8f7e532c8b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ec852a8d1adada10d1ac783212da0ea", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a94acbd3d78465296228ffe270eee66", "guid": "bfdfe7dc352907fc980b868725387e98886cce8d89eedd0e8bab1ebae394ce44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850ff8c9cbf340e7efb8eaa4357ec0970", "guid": "bfdfe7dc352907fc980b868725387e9814d4634efa63a6d2d505baa31e1ac713"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ba1e972549a1c43d5b0f6b68937789", "guid": "bfdfe7dc352907fc980b868725387e98320cd36a8701febd579756d6afaaf887"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c171dae741887034b32e97abafed083d", "guid": "bfdfe7dc352907fc980b868725387e989fc70d6b14cc9ff6f9284594eda21c4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7cefdfa8233acdcc740ab988c7ec216", "guid": "bfdfe7dc352907fc980b868725387e987ad8ad9016d3b0417c705cf13bf56cd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984656a9c3a8a2bc14041803f0a87bc759", "guid": "bfdfe7dc352907fc980b868725387e98d703dfd4c027fb2c6536f0b761e5693b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db6dcb43785f7a4b4d4f3fbb6b2b9a18", "guid": "bfdfe7dc352907fc980b868725387e98e3a5d36fe9ddc383dd52cfb2f4aa3d2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d002cd7477d1713aa6436715a5f7fc8c", "guid": "bfdfe7dc352907fc980b868725387e988c0cbdb0ce4d520f771f0a0119fc0a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813cfac1beb4afe40799a1aed53d324bc", "guid": "bfdfe7dc352907fc980b868725387e9841f34774f1d1f35fd1421e074705026a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0e9a7d92bb7533612631c05af2db1f3", "guid": "bfdfe7dc352907fc980b868725387e980709a52f0b40d2f4f632a567e77f0052"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a359dabb7a1600ce138c139c611b093", "guid": "bfdfe7dc352907fc980b868725387e98e7e5ef36f766a85235666117b7e3af60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd4a5c9c4b345abcb8a5b3ef09238d4", "guid": "bfdfe7dc352907fc980b868725387e981219ec7c40082ae325734559e6ff1972", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c97b078fe81b88fc3ce47dded7b9609", "guid": "bfdfe7dc352907fc980b868725387e988f15e339179ba61cee0efcba36dc94d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d486a552c83d84180cd088fed8618c6", "guid": "bfdfe7dc352907fc980b868725387e98c613f6e9f1268a6fd9760f88f3ba1cbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f9e680f4305003c4c0c7a3c93caeb5e", "guid": "bfdfe7dc352907fc980b868725387e98cef3f95e150d8d212f3fd2bc94dbbc73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981be4cf3833762590bf2ad1799823cdde", "guid": "bfdfe7dc352907fc980b868725387e98c03b9eab66a77826f26ccfe61cd18596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3695e504430e0e081eeafcd88067c2d", "guid": "bfdfe7dc352907fc980b868725387e98005c9eec2b5c8702ffcaf744ee071c9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805c8d272d87a5780bebe24d55e3953f6", "guid": "bfdfe7dc352907fc980b868725387e98bb76d4700128219548c4ec6ddd809bdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d1c5c36e141126b3ee15efede370fa", "guid": "bfdfe7dc352907fc980b868725387e986927c359f2dda2d0de174559b9012d84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f93330e17ef51246803a4e3d2dd1659", "guid": "bfdfe7dc352907fc980b868725387e981e07ec96bdb3b4f1c8571c17df42f556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98598ae200e01ba22ad2a139cc17c91e41", "guid": "bfdfe7dc352907fc980b868725387e98e8a59315c0e4f60820a2913515601152", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852d5c7836cf249b61792ce06dd0436b7", "guid": "bfdfe7dc352907fc980b868725387e989370a0471632ea106ad7ef12faf7eb58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876ea21301467f0ec704ccc8ca4eeb36e", "guid": "bfdfe7dc352907fc980b868725387e985a8665f65a5b1496a289fc9a37fd5d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad438ae9a5108f4cf3f2604a7417f79b", "guid": "bfdfe7dc352907fc980b868725387e985ffe850fe17544bc1ca5e12098182a72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f845c6ff5e3677ff31a1433b84f60e14", "guid": "bfdfe7dc352907fc980b868725387e98103c9274d36cf279ca06210c7d6fc9d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844b95bfaf9fa07c4953263ac4e670843", "guid": "bfdfe7dc352907fc980b868725387e9849661bc209a55c852f76889cf6a1e997", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fdaabb4da1ba975df5b69c32a71bbb5", "guid": "bfdfe7dc352907fc980b868725387e980cec98cb2e90c63aa654a612ef32167e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f9aca8f5ba1113006d3def2ec33da78", "guid": "bfdfe7dc352907fc980b868725387e984db1948fead7015391334ac8735be24a"}], "guid": "bfdfe7dc352907fc980b868725387e987f5b8c6929a27c0c9bd2e06b911bd5b4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9811188527c73b85ae78d87919bf829ea6", "guid": "bfdfe7dc352907fc980b868725387e989fd85f031f44e7e867837755a95d792e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5d2f636982877abdf85e12f9dcedcec", "guid": "bfdfe7dc352907fc980b868725387e983a357ebf5ac631d9351567fa4c1a590a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a3f62c335c09e53041abb1cf2c1e45b", "guid": "bfdfe7dc352907fc980b868725387e983ff901e1c348f871af84eaa18946dc86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7603b90c5da836b49eabed304cc7d4c", "guid": "bfdfe7dc352907fc980b868725387e986e0959b19781b54bbcbee026cba50b57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4f44ab44b575257fde2b7bfd51fa596", "guid": "bfdfe7dc352907fc980b868725387e9840c005f94a80219ea5597c3482850da9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824e17ec5b875f826273cd481f0964826", "guid": "bfdfe7dc352907fc980b868725387e98425fb0d289c9745ca8ac6723d38ade98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98669dd34939a093e161ee81c779811db0", "guid": "bfdfe7dc352907fc980b868725387e9847bfe48de35e2a18da782a45e7538de1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5ef1d8c92d671752510972e0c069fc6", "guid": "bfdfe7dc352907fc980b868725387e9801fe482d894d5861f713698ed9ee48fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827e663724c7d2109fd41d94e11df044b", "guid": "bfdfe7dc352907fc980b868725387e989761f53b0f553eb48fefe5e27e6df52a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a73ba63092b629d8d6f30fd1c3c1a5f1", "guid": "bfdfe7dc352907fc980b868725387e985e004db72a88aabda259dd96fe2d878e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b0bf3972d3fed0b30b592c5d109ced4", "guid": "bfdfe7dc352907fc980b868725387e987403a31bf817ee8bcfa02bd720133be0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efdac1d34569e7d348412e2b626f31de", "guid": "bfdfe7dc352907fc980b868725387e983e4e4a667d359ad7936ffb1a019c396e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb8396752e5490c98cca440ebc9edf76", "guid": "bfdfe7dc352907fc980b868725387e98dc10bd3e500f94a88b8237eb3d5976eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab14c41ec5fc4270b085143709110de6", "guid": "bfdfe7dc352907fc980b868725387e98d0d3e46f3d082a79fde397aaae6ff8cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98657894218b2e450dc5c55facd89c7aa3", "guid": "bfdfe7dc352907fc980b868725387e98915f338ff896f522a21633c22b745224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9ef34d2f9cf3e784b18188ee25be91", "guid": "bfdfe7dc352907fc980b868725387e98f6e22be9228fb66854e2d978db3d0fb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876f49bad5fccb776e9a84954fc3b879a", "guid": "bfdfe7dc352907fc980b868725387e98ae74b6a1ad2caa4d0bed9d8659c899db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98460b6b07e53d1bd54b65b21b8b68da07", "guid": "bfdfe7dc352907fc980b868725387e9847a9e3ab0f288ebfbc84fcc9dd22948b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a9a5d45d493ff7b5d8d854c95186fd0", "guid": "bfdfe7dc352907fc980b868725387e983247400d15e10e10db86e4c31c9710e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847c6430e43462e2c622ef78c7de34afc", "guid": "bfdfe7dc352907fc980b868725387e98b0e3693309aa6dd9fddf8e4e0c1ef477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4019d1ced6b17a5b50961e273943032", "guid": "bfdfe7dc352907fc980b868725387e98b58454db49475740727c589fbd08bd53"}], "guid": "bfdfe7dc352907fc980b868725387e984f5e94e465f5903eadcd32566152e573", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982553f4b48916e75c2c5e42b75c936410", "guid": "bfdfe7dc352907fc980b868725387e98d5751d5d5edfe95cbeeb7320d75636f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98333fa448890282c15fb59cabeeb11050", "guid": "bfdfe7dc352907fc980b868725387e98606f654508a766c82daac06f2cf31545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e989ba13964eec132954b26eb3135d7fe0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6dcfe72f1c72ccc202576b1f841b4fa", "guid": "bfdfe7dc352907fc980b868725387e98a86e502fca8e7bbfc825b02ec2da6001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98226c4235dc94da08f85a05de7f32e4d8", "guid": "bfdfe7dc352907fc980b868725387e984b2fdd52b6577de327fc05ea8cc3e6eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bf9f81912b8a94a8584885abd980044", "guid": "bfdfe7dc352907fc980b868725387e9829688bd5d8608456cc667396d5869b23"}], "guid": "bfdfe7dc352907fc980b868725387e98b2fa6105a1ff6ac5133924a8b9d0316f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98edbfd520c3bab10d90ee1126f6a977b9", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e987f0db34de032e064343027920ce15adb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
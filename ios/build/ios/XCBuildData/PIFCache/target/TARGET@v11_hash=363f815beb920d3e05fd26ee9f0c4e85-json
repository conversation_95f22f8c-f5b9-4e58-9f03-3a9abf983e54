{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9885072c5d3ff7c99239b9ba83a5bc7587", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d44943b2b7905ad874aec24caf4cb30c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2da5e84bf8f89da6a6be0708e118f56", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bd20c8de4538ad9c2d01d229547fa0ef", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2da5e84bf8f89da6a6be0708e118f56", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9899b8e7046d6d55ceaa1a8e880557fffa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c71cee38d72410cfac2bc046139456f", "guid": "bfdfe7dc352907fc980b868725387e98a37656aa3441d1d48c265b9d9bb7ef21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6060f0fe0a6316eeb77ad437e6f72c9", "guid": "bfdfe7dc352907fc980b868725387e9800ae6dc3427b618eb39e523d0c51a4c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c117b85730d4b216abec48a1b2b3d2c6", "guid": "bfdfe7dc352907fc980b868725387e98e59b93898efe6a9d6a715fbe80ad10c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b42f2e3c161e74cc6e746a85f507fb1", "guid": "bfdfe7dc352907fc980b868725387e98a811fd381ccf41085a7ec717fc1023bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdca9fd1d2db411c3cb57159e0010fa0", "guid": "bfdfe7dc352907fc980b868725387e9897ab1b8d51230d5f2855e5930bea70d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac48c4b78d97df631358aba802868064", "guid": "bfdfe7dc352907fc980b868725387e98ec31527e149fc25f1e8df8d5bf78d065", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae0b29167ebf89014973f9ebafae52be", "guid": "bfdfe7dc352907fc980b868725387e9818e62a453eac4d8f0c2485e8a07b1c0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e3e699923621c2707c97adace3f25ee", "guid": "bfdfe7dc352907fc980b868725387e98daba3f4d7f43a795be55e733119dd8d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c79a8dd0f4c35515e41711685bde164e", "guid": "bfdfe7dc352907fc980b868725387e98b0e910eb8b6a3d1ffb4bf08f9fa9c188", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985154a6da7a55a1315aad3fd23bf231bc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982b6ede017f8962fb2b269efe0d7e3d4c", "guid": "bfdfe7dc352907fc980b868725387e981bb537f42d7f01cf6de2b61242cc9f61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fbb7875f484c486ad01a655f5479279", "guid": "bfdfe7dc352907fc980b868725387e9800ac32eed9f875f8ccbaf0a6ed854db5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897d5447c85616596e998811e30f1ef2b", "guid": "bfdfe7dc352907fc980b868725387e98cbed9f2f42b99d313b1ad1bed4a5228d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ce221866e2427eb2dffb1d008dc1ce", "guid": "bfdfe7dc352907fc980b868725387e98c679142244914601b5fd0296afc415ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98136583ec4b2dabcdcbb00f7f00c5c2d8", "guid": "bfdfe7dc352907fc980b868725387e98b8248f36fab21aeb0f49fcff2600da98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe6583d1193a1ddf890ad9166a019233", "guid": "bfdfe7dc352907fc980b868725387e9836dd79e82dcd03890e511efe3c125b50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98656b36a55c3a4d49724bb249bed41f82", "guid": "bfdfe7dc352907fc980b868725387e98ea6624d027078c57aea3fd93d93e7a5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ef018618d27c96cefd065a2b0e939f", "guid": "bfdfe7dc352907fc980b868725387e98c7f18b337b11f88c2a3fd1359c83a3f3"}], "guid": "bfdfe7dc352907fc980b868725387e98f368ba15d18cf715596660ccf4d7ce84", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ecf5e6e0b6903a63608815380e99d9c1", "guid": "bfdfe7dc352907fc980b868725387e9807d3135e2e58a212e33f984cc97b775d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98226c4235dc94da08f85a05de7f32e4d8", "guid": "bfdfe7dc352907fc980b868725387e9892fed813deae321c7a935c526fe0235a"}], "guid": "bfdfe7dc352907fc980b868725387e9802cbc7e5269c1b8ceee298a3241bd27f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c48cf2a8ba57042347cb715698519f91", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e9886badc7aa5ffee8ed1c6170bcc6d3420", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98a1df44d109cc05f42043cbce222c3561", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
//
//  SmallTemplateNativeAdFactory.swift
//  Runner
//
//  Created by cunseng on 2025/4/17.
//
import google_mobile_ads
import Kingfisher
import UIKit

// TODO: Implement ListTileNativeAdFactory
class CustomTemplateNativeAdFactory : FLTNativeAdFactory {

    func createNativeAd(_ nativeAd: GADNativeAd,
                        customOptions: [AnyHashable : Any]? = nil) -> GADNativeAdView? {
                
        var useSmall = true
        if let customOptions, let useLarge = customOptions["isUselarge"] as? Bool, useLarge == true {
            useSmall = false
        }
        if useSmall {
            return CustomSmallTemplateView(nativead: nativeAd)
        } else {
            return CustomFullScreenTemplateView(nativead: nativeAd)
        }
    }
}

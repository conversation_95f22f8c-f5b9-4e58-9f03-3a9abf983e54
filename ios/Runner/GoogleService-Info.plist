<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>402592875397-recfqju7e0a1ole8h9me4e4adi9lglb8.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.402592875397-recfqju7e0a1ole8h9me4e4adi9lglb8</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>402592875397-h9u33kobjiob8s9lq91mjmh9ak49ivp0.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyBm9xfZwiIilQVGpB3TZMzZAEuQbz7_A2k</string>
	<key>GCM_SENDER_ID</key>
	<string>402592875397</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>cn.hqz.developer.ios</string>
	<key>PROJECT_ID</key>
	<string>fzfbtest</string>
	<key>STORAGE_BUCKET</key>
	<string>fzfbtest.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false/>
	<key>IS_ANALYTICS_ENABLED</key>
	<false/>
	<key>IS_APPINVITE_ENABLED</key>
	<true/>
	<key>IS_GCM_ENABLED</key>
	<true/>
	<key>IS_SIGNIN_ENABLED</key>
	<true/>
	<key>GOOGLE_APP_ID</key>
	<string>1:402592875397:ios:0265d00afa72a75decbaec</string>
</dict>
</plist>

//
//  Untitled.swift
//  Runner
//
//  Created by cunseng on 2025/5/13.
//
import SnapKit
import UIKit

class CustomSmallTemplateView: GADNativeAdView {
    
    // 创建所需的视图组件
    private let bgView = UIView()
    private let mediaContentView = UIView()
    private let headlineLbl = UILabel()
    private let advertiserLbl = UILabel()
    private let callToActionBtn = UIButton()
    private let storeLbl = UILabel()
    private let adBridge = UIImageView()

    init(nativead: GADNativeAd) {
        super.init(frame: .zero)
        bgView.translatesAutoresizingMaskIntoConstraints = false
        bgView.backgroundColor = UIColor.init(red: 66/255.0, green: 66/255.0, blue: 66/255.0, alpha: 1)
        bgView.layer.masksToBounds = true
        bgView.layer.cornerRadius = 6
        // 设置视图属性
//        self.translatesAutoresizingMaskIntoConstraints = false
        
        // 配置mediaView
        let mediaView = GADMediaView()
        // mediaView.translatesAutoresizingMaskIntoConstraints = false
        mediaView.contentMode = .scaleAspectFill
        mediaView.clipsToBounds = true
        // mediaContentView.translatesAutoresizingMaskIntoConstraints = false
        mediaContentView.contentMode = .scaleAspectFill
        mediaContentView.addSubview(mediaView)
        mediaContentView.clipsToBounds = true
        mediaView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 配置headlineLabel
        headlineLbl.translatesAutoresizingMaskIntoConstraints = false
        headlineLbl.font = UIFont.boldSystemFont(ofSize: 14)
        headlineLbl.numberOfLines = 1
        headlineLbl.textColor = .white
        
        // 配置advertiserLabel
        advertiserLbl.translatesAutoresizingMaskIntoConstraints = false
        advertiserLbl.font = UIFont.systemFont(ofSize: 14)
        
        // 配置callToActionButton
        callToActionBtn.translatesAutoresizingMaskIntoConstraints = false

        storeLbl.translatesAutoresizingMaskIntoConstraints = false
        storeLbl.font = UIFont.systemFont(ofSize: 12)
        storeLbl.textColor = .white

        adBridge.translatesAutoresizingMaskIntoConstraints = false
        adBridge.image = UIImage(named: "ad_brage")
        // 添加视图到层次结构
        addSubview(bgView)
        bgView.addSubview(mediaContentView)
        bgView.addSubview(headlineLbl)
        bgView.addSubview(adBridge)
        bgView.addSubview(advertiserLbl)
        bgView.addSubview(callToActionBtn)
        bgView.addSubview(storeLbl)

        bgView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.leading.equalToSuperview()
            make.height.equalTo(120)
            make.width.equalTo(UIScreen.main.bounds.width-32)
        }

        // 设置约束
        mediaContentView.snp.makeConstraints { make in
            make.size.equalTo(120).priority(.required)
            make.leading.equalToSuperview()
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        headlineLbl.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.trailing.equalToSuperview().offset(-10)
            make.leading.equalTo(mediaContentView.snp.trailing).offset(10)
            // make.width.equalTo(UIScreen.main.bounds.width - 40 - 120 - 20)
            make.height.equalTo(20)
        }
        
        adBridge.snp.makeConstraints { make in
            make.top.equalTo(headlineLbl.snp.bottom).offset(9)
            make.size.equalTo(20)
            make.leading.equalTo(mediaContentView.snp.trailing).offset(10)
        }
        
        advertiserLbl.snp.makeConstraints { make in
            make.top.equalTo(headlineLbl.snp.bottom).offset(9)
            make.leading.equalTo(adBridge.snp.trailing).offset(10)
            make.trailing.equalTo(headlineLbl.snp.trailing)
            make.height.equalTo(20)
        }
        
        storeLbl.snp.makeConstraints { make in
            make.top.equalTo(headlineLbl.snp.bottom).offset(9)
            make.leading.equalTo(adBridge.snp.trailing).offset(10)
            make.trailing.equalTo(headlineLbl.snp.trailing)
            make.height.equalTo(20)
        }

        callToActionBtn.snp.makeConstraints { make in
            make.bottom.equalTo(mediaContentView.snp.bottom).offset(-11)
            make.trailing.equalToSuperview().offset(-17)
            make.leading.equalTo(mediaContentView.snp.trailing).offset(10)
            make.height.equalTo(30)
        }
        
                // 将创建的视图组件赋值给GADNativeAdView的属性
        self.mediaView = mediaView
        self.headlineView = headlineLbl
        self.advertiserView = advertiserLbl
        self.callToActionView = callToActionBtn
        self.storeView = storeLbl


        // 安全地设置headline
        if let headlineView = self.headlineView as? UILabel {
            headlineView.text = nativead.headline
        }

        // 安全地设置media content
        if let mediaView = self.mediaView {
            mediaView.mediaContent = nativead.mediaContent
            mediaView.contentMode = .scaleAspectFill
        }

        // 安全地设置advertiser
        if let advertiserView = self.advertiserView as? UILabel {
            advertiserView.text = nativead.advertiser
            advertiserView.textColor = .white
        }

        if let storeview = self.storeView as? UILabel {
            storeview.text = nativead.store
            storeview.textColor = .white
        }

        if let callToActionView = self.callToActionView as? UIButton {
            callToActionView.setTitle(nativead.callToAction, for: .normal)
            callToActionView.titleLabel?.font = UIFont.systemFont(ofSize: 13)
            callToActionView.backgroundColor = UIColor.init(red: 81/255.0, green: 154/255.0, blue: 246/255.0, alpha: 1)
            callToActionView.layer.cornerRadius = 15
            callToActionView.layer.masksToBounds = true
            callToActionView.setTitleColor(.white, for: .normal)
        }

        self.nativeAd = nativead

        // 设置自适应
//        self.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

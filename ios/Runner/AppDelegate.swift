import Flutter
import UIKit
// This is required for calling FlutterLocalNotificationsPlugin.setPluginRegistrantCallback method.
import flutter_local_notifications
import FBAudienceNetwork
import AppTrackingTransparency
import ActivityKit

@main
@objc class AppDelegate: FlutterAppDelegate {
    var appLaunchOptions: [UIApplication.LaunchOptionsKey: Any]?
    var canOpenURL = false {
        didSet {
            excuteOpenURLTasks()
        }
    }
    var openURLTasks: [OpenURLTask] = []
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        appLaunchOptions = launchOptions;
        
        registerFlutterChannelManager()
        
        GeneratedPluginRegistrant.register(with: self)
        
        FLTGoogleMobileAdsPlugin.registerNativeAdFactory(self, factoryId: "CustomTemplateView", nativeAdFactory: CustomTemplateNativeAdFactory())
        
        configNotification()
        setThirdPartyConfig()
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    override func applicationWillTerminate(_ application: UIApplication) {
        // 停止所有Live Activities
        if #available(iOS 16.2, *) {
            Task {
                for activity in Activity<LiveActivitiesAppAttributes>.activities {
                    await activity.end(nil, dismissalPolicy: .immediate)
                }
            }
        } else if #available(iOS 16.1, *) {
            Task {
                for activity in Activity<LiveActivitiesAppAttributes>.activities {
                    await activity.end()
                }
            }
        }
        
        super.applicationWillTerminate(application)
    }
    
    override func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([any UIUserActivityRestoring]?) -> Void) -> Bool {
        self.checkUniversalLink(application, continue: userActivity, restorationHandler: restorationHandler)
        return true
    }
    
    override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        return self.handleApplication(app, open: url, options: options);
    }
    
    private func registerFlutterChannelManager() {
        if let controller : FlutterViewController = window?.rootViewController as? FlutterViewController {
            // 注册方法通道
            FlutterChannelMethodManager.instance.registerChannelMethods(controller: controller)
        }
        
        if let plugin = self.registrar(forPlugin: "DeviceOrientationHandler") {
            DeviceOrientationHandler.register(with: plugin)
        }
    }
    
    private func configNotification() {
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().delegate = self
        }
    }
    
    private func setThirdPartyConfig() {
        FBAudienceNetworkAds.initialize(with: nil, completionHandler: nil)
        // This is required to make any communication available in the action isolate.
        FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in
            GeneratedPluginRegistrant.register(with: registry)
        }
        if #available(iOS 17.0, *) {
            // Do nothing
        } else {
            // Set isAdvertiserTrackingEnabled to true if a device provides permission
            if #available(iOS 14, *) {
                let status = ATTrackingManager.trackingAuthorizationStatus
                FBAdSettings.setAdvertiserTrackingEnabled(status == .authorized)
            } else {
                FBAdSettings.setAdvertiserTrackingEnabled(true)
            }
        }
    }
}

struct LiveActivitiesAppAttributes: ActivityAttributes, Identifiable {
    public typealias LiveDeliveryData = ContentState // don't forget to add this line, otherwise, live activity will not display it.
    
    public struct ContentState: Codable, Hashable { }
    
    var id = UUID()
}

extension LiveActivitiesAppAttributes {
    func prefixedKey(_ key: String) -> String {
        return "\(id)_\(key)"
    }
}

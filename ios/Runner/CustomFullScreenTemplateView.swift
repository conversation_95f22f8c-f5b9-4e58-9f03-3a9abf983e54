//
//  Untitled.swift
//  Runner
//
//  Created by cunseng on 2025/5/13.
//
import Kingfisher

import SnapKit
import UIKit

class CustomFullScreenTemplateView: GADNativeAdView {
    
    // 创建所需的视图组件
    private let mediaContentView = UIView()
    
    private let otherView = UIView()
    private let iconImageView = UIImageView()
    private let headlineLbl = UILabel()
    private let advertiserLbl = UILabel()
    private let callToActionBtn = UIButton()
    private let bodyLbl = UILabel()
    private let adBridge = UILabel()

    init(nativead: GADNativeAd) {
        super.init(frame: .zero)

        // 设置视图属性
        self.translatesAutoresizingMaskIntoConstraints = false
        
        otherView.backgroundColor = UIColor.init(red: 35/255.0, green: 37/255.0, blue: 42/255.0, alpha: 0.5)
        otherView.layer.cornerRadius = 10
        otherView.layer.masksToBounds = true

        // 配置mediaView
        let mediaView = GADMediaView()
        mediaView.translatesAutoresizingMaskIntoConstraints = false
        mediaContentView.addSubview(mediaView)
        mediaContentView.clipsToBounds = true
        mediaView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 配置headlineLabel
        headlineLbl.translatesAutoresizingMaskIntoConstraints = false
        headlineLbl.font = UIFont.boldSystemFont(ofSize: 14)
        headlineLbl.textColor = .white
        headlineLbl.numberOfLines = 1
        
        // 配置advertiserLabel
        advertiserLbl.translatesAutoresizingMaskIntoConstraints = false
        advertiserLbl.font = UIFont.systemFont(ofSize: 14)
        
        // 配置callToActionButton
        callToActionBtn.translatesAutoresizingMaskIntoConstraints = false

        bodyLbl.translatesAutoresizingMaskIntoConstraints = false
        bodyLbl.font = UIFont.systemFont(ofSize: 12)
        bodyLbl.numberOfLines = 2
        bodyLbl.textColor = .white

        adBridge.text = "AD"
        adBridge.font = UIFont.systemFont(ofSize: 12)
        adBridge.textColor = .white
        adBridge.textAlignment = .center
        adBridge.backgroundColor = .init(white: 0, alpha: 0.3)
        
        // 添加视图到层次结构
        addSubview(mediaContentView)
        addSubview(otherView)
        otherView.addSubview(iconImageView)
        otherView.addSubview(headlineLbl)
        otherView.addSubview(adBridge)
        otherView.addSubview(advertiserLbl)
        otherView.addSubview(callToActionBtn)
        otherView.addSubview(bodyLbl)
        // 设置约束
        mediaContentView.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
            make.trailing.equalToSuperview()
            make.height.equalTo(UIScreen.main.bounds.size.height - 103)
            make.width.equalTo(UIScreen.main.bounds.size.width)
        }
        otherView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-60)
            make.bottom.equalToSuperview()
            make.height.equalTo(160)
        }

        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.equalToSuperview().offset(12)
            make.size.equalTo(34)
        }

        headlineLbl.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-10)
            make.leading.equalTo(iconImageView.snp.trailing).offset(8)
            make.height.equalTo(34)
        }
        
        adBridge.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.width.equalTo(32)
            make.height.equalTo(23)
            make.trailing.equalToSuperview()
        }
        
        advertiserLbl.snp.makeConstraints { make in
            make.top.equalTo(headlineLbl.snp.bottom).offset(9)
            make.leading.equalTo(adBridge.snp.trailing).offset(10)
            make.trailing.equalTo(headlineLbl.snp.trailing)
            make.height.equalTo(20)
        }
        
        bodyLbl.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(10)
            make.trailing.equalToSuperview().offset(-12)
            make.height.equalTo(40)
        }

        callToActionBtn.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-20)
            make.trailing.equalToSuperview().offset(-50)
            make.leading.equalToSuperview().offset(10)
            make.height.equalTo(32)
        }
        
                // 将创建的视图组件赋值给GADNativeAdView的属性
        self.mediaView = mediaView
        self.iconView = iconImageView
        self.headlineView = headlineLbl
        self.advertiserView = advertiserLbl
        self.callToActionView = callToActionBtn
        self.bodyView = bodyLbl


        // 安全地设置headline
        if let headlineView = self.headlineView as? UILabel {
            headlineView.text = nativead.headline
        }

        // 安全地设置icon
        if let image = nativead.icon?.image {
            if let iconView = self.iconView as? UIImageView {
                iconView.image = image
            }
        } else {
            DispatchQueue.main.async {
                if let iconView = self.iconView as? UIImageView {
                    iconView.kf.setImage(with: nativead.icon?.imageURL)
                }
            }
        }

        // 安全地设置media content
        if let mediaView = self.mediaView {
            mediaView.mediaContent = nativead.mediaContent
            mediaView.contentMode = .scaleAspectFit
        }

        // 安全地设置advertiser
        if let advertiserView = self.advertiserView as? UILabel {
            advertiserView.text = nativead.advertiser
        }

        if let bodyview = self.bodyView as? UILabel {
            bodyview.text = nativead.body
        }

        if let callToActionView = self.callToActionView as? UIButton {
            callToActionView.setTitle(nativead.callToAction, for: .normal)
            callToActionView.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            callToActionView.backgroundColor = UIColor.init(red: 81/255.0, green: 154/255.0, blue: 246/255.0, alpha: 1)
            callToActionView.layer.cornerRadius = 15
            callToActionView.layer.masksToBounds = true
            callToActionView.setTitleColor(.white, for: .normal)
        }

        self.nativeAd = nativead

        // 设置自适应
        self.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

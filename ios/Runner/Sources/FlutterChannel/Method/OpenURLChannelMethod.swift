//
//  OpenURLChannelMethod.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/20.
//

import Foundation

class OpenURLChannelMethod: FlutterChannelMethodProtocol {
    var channelName: String {
        // 与flutter约定好的通道名
        return "com.flareflow.ios/appDelegate"
    }
    
    var methodName: String {
        // flutter调用的方法名
        return "canOpenURL"
    }
    
    func callMethod(arguments: Any?, result: @escaping FlutterResult) {
        // 返回结果
        guard let delegate = UIApplication.shared.delegate as? AppDelegate else {
            result(nil)
            return
        }
        delegate.canOpenURL = true
        result(nil)
    }
}

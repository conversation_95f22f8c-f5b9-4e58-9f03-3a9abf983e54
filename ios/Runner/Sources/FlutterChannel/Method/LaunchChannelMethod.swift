//
//  LaunchChannelMethod.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/20.
//

import Foundation

class LaunchChannelMethod: FlutterChannelMethodProtocol {
    var channelName: String {
        // 与flutter约定好的通道名
        return "com.flareflow.ios/appDelegate"
    }
    
    var methodName: String {
        // flutter调用的方法名
        return "getLaunchOptions"
    }
    
    func callMethod(arguments: Any?, result: @escaping FlutterResult) {
        // 返回结果
        guard let delegate = UIApplication.shared.delegate as? AppDelegate else {
            result(nil)
            return
        }
        var options: [String : String]?
        if let appLaunchOptions = delegate.appLaunchOptions {
            options = [:]
            appLaunchOptions.forEach { (key, value) in
                if let stringValue = value as? String {
                    options?[key.rawValue] = stringValue
                } else if let urlValue = value as? URL {
                    options?[key.rawValue] = urlValue.absoluteString
                }
            }
        }
        result(options)
    }
}

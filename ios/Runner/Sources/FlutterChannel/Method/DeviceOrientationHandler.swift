//
//  DeviceOrientationHandler.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/27.
//

import Foundation

public class DeviceOrientationHandler: NSObject {
    static let eventChannelName = "com.flareflow.ios/device_orientation_stream"
    
    var eventSink: FlutterEventSink?
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let instance = DeviceOrientationHandler()
        instance.setup()
        
        let eventChannel = FlutterEventChannel(name: eventChannelName, binaryMessenger: registrar.messenger())
        eventChannel.setStreamHandler(instance)
    }
    
    func setup() {
        NotificationCenter.default.addObserver(forName: UIDevice.orientationDidChangeNotification, object: nil, queue: .main) { [weak self] notification in
            self?.eventSink?(UIDevice.current.orientation.rawValue)
        }
    }
}

extension DeviceOrientationHandler: FlutterStreamHandler {
    public func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.eventSink = events
        return nil
    }
    
    public func onCancel(withArguments arguments: Any?) -> FlutterError? {
        eventSink = nil
        return nil
    }
}

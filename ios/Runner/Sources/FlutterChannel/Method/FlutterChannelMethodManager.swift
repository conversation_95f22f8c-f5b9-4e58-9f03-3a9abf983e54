//
//  FlutterChannelMethodManager.swift
//  Runner
//
//  Created by jy on 2025/3/25.
//

import Foundation
import Flutter

/** 使用示例：在FlutterChannelMethodManager的registerChannelMethods方法中注册遵循FlutterChannelMethodProtocol协议的实例
class SomeNativeFeature: FlutterChannelMethodProtocol {
    var channelName: String {
        // 与flutter约定好的通道名
        return "com.flareflow.platform_service"
    }
    
    var methodName: String {
        // flutter调用的方法名
        return "someNativeFeature"
    }
    
    func callMethod(arguments: Any?, result: @escaping FlutterResult) {
        // 实现你的原生功能
        guard let args = arguments as? [String: Any] else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Arguments invalid", details: nil))
            return
        }
        
        // 处理参数args并执行原生功能
        // ...
        
        // 返回结果
        result(true)
    }
}
 */

class FlutterChannelMethodManager {
    /// 单例
    static let instance: FlutterChannelMethodManager = .init()
    
    /// 注册通道
    /// - Parameter controller: flutter控制器
    func registerChannelMethods(controller: FlutterViewController) {
        self.controller = controller
        
        /** 在此处注册 Begin **/
//         self.register(method: SomeNativeFeature())
        self.register(method: SystemOrientationPortrait())
        self.register(method: SystemOrientationLandscape())
        self.register(method: LaunchChannelMethod())
        self.register(method: OpenURLChannelMethod())
        /** 在此处注册 End **/
    }
    
    /// 方法和通道的映射
    private var methodsForChannel: [ String : [ String : FlutterChannelMethodProtocol] ] = [:]
    
    /// flutter控制器
    weak private var controller: FlutterViewController?
    
    /// 注册方法
    /// - Parameter method: 方法实现对象
    private func register(method: FlutterChannelMethodProtocol) {
        guard let controller = self.controller else {
            return
        }
        let channelName: String = method.channelName
        var methodForName: [ String : FlutterChannelMethodProtocol] = self.methodsForChannel[channelName] ?? [:]
        
        if methodForName.isEmpty {
            // 没注册过，注册method channel
            let channel = FlutterMethodChannel(name: channelName, binaryMessenger: controller.binaryMessenger)
            
            channel.setMethodCallHandler({ [weak self]
                (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
                guard let self = self else {
                    result(FlutterMethodNotImplemented)
                    return
                }
                guard let methodForNameMap: [ String : FlutterChannelMethodProtocol] = self.methodsForChannel[channelName] else {
                    result(FlutterMethodNotImplemented)
                    return
                }
                guard let methodModel: FlutterChannelMethodProtocol = methodForNameMap[call.method] else {
                    result(FlutterMethodNotImplemented)
                    return
                }
                // 执行方法
                methodModel.callMethod(arguments: call.arguments, result: result)
            })
        }
        
        methodForName[method.methodName] = method
        self.methodsForChannel[channelName] = methodForName
    }
}

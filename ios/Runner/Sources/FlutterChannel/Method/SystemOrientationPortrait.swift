//
//  Untitled.swift
//  Runner
//
//  Created by cunseng on 2025/5/9.
//

class SystemOrientationPortrait: FlutterChannelMethodProtocol {
    var channelName: String {
        // 与flutter约定好的通道名
        return "com.flareflow.ios/screen_orientation"
    }
    
    var methodName: String {
        // flutter调用的方法名
        return "setPortrait"
    }
    
    func callMethod(arguments: Any?, result: @escaping FlutterResult) {
        // 实现你的原生功能
//        guard let args = arguments as? [String: Any] else {
//            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Arguments invalid", details: nil))
//            return
//        }
        
        // 处理参数args并执行原生功能
        // ...
        UIDevice.current.setValue(UIInterfaceOrientation.portrait, forKey: "orientation")

        // 返回结果
        result(true)
    }
}

//
//  FlutterChannelProtocol.swift
//  Runner
//
//  Created by jy on 2025/3/25.
//

import Foundation
import Flutter

protocol FlutterChannelMethodProtocol {
    
    /// 通道名
    var channelName: String { get }
    
    /// 方法名
    var methodName: String { get }
    
    /// 执行方法
    /// - Parameters:
    ///   - arguments: 参数
    ///   - result: 回调结果
    func callMethod(arguments: Any?, result: @escaping FlutterResult)
}

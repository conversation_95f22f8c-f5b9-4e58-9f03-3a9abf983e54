//
//  AppDelegate+OpenURL.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/20.
//

import Foundation

extension AppDelegate {
    func handleApplication(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        if (self.canOpenURL == true) {
            return super.application(app, open: url, options: options)
        }
        // 不允许open URL，暂存任务，等canOpenURL设为true的时候再执行
        openURLTasks.append(OpenURLTask(
            app: app,
            url: url,
            options: options
        ))
        return true;
    }
    
    /// 执行open URL任务
    func excuteOpenURLTasks() {
        openURLTasks.forEach { task in
            super.application(task.app, open: task.url, options: task.options)
        }
        openURLTasks.removeAll()
    }
}

struct OpenURLTask {
    var app: UIApplication
    var url: URL
    var options: [UIApplication.OpenURLOptionsKey : Any] = [:]
}

//
//  AppDelegate+Attribution.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/14.
//

import Foundation
import AppAttribution

extension AppDelegate {
    
    func checkUniversalLink(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([any UIUserActivityRestoring]?) -> Void) {
        self.parse(continue: userActivity, restorationHandler: restorationHandler)
        AttributionManager.application(application: application, continue: userActivity, restorationHandler: restorationHandler)
    }
    
    private func parse(continue userActivity: NSUserActivity, restorationHandler: @escaping ([any UIUserActivityRestoring]?) -> Void) {
        // 确保是从网页链接启动
        guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
              let url = userActivity.webpageURL else {
            restorationHandler(nil)
            return;
        }
        
        let urlComponents = URLComponents(url: url, resolvingAgainstBaseURL: true)
        let queryItems = urlComponents?.queryItems
        
        var success = self.parseShortIdToFlutter(queryItems: queryItems, key: "attr", restorationHandler: restorationHandler)
        if !success {
            success = self.parseShortIdToFlutter(queryItems: queryItems, key: "campaign_name", restorationHandler: restorationHandler)
        }
    }
    
    private func parseShortIdToFlutter(queryItems: [URLQueryItem]?, key: String, restorationHandler: @escaping ([any UIUserActivityRestoring]?) -> Void) -> Bool {
        let attrItem = queryItems?.first { item in
            item.name == key
        }
        guard let attrValue = attrItem?.value else {
            restorationHandler(nil)
            return false
        }
        
        let attrArr = attrValue.components(separatedBy: "_")
        if attrArr.isEmpty {
            restorationHandler(nil)
            return false
        }
        
        var shortId: String?
        for item in attrArr {
            if !item.contains("shortid") {
                continue
            }
            let kv = item.components(separatedBy: "-")
            shortId = kv.last
        }
        guard let shortId = shortId else {
            restorationHandler(nil)
            return false
        }
        self.sendShortIdToFlutter(shortId: shortId)
        restorationHandler(nil)
        return true;
    }
    
    /// 发送短剧id给flutter
    /// - Parameter shortId: 短剧id
    private func sendShortIdToFlutter(shortId: String?) {
        guard let shortId = shortId else {
            return
        }
        guard let controller = window?.rootViewController as? FlutterViewController else {
            return
        }
        
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 0.5, execute: DispatchWorkItem(block: {
            let channel = FlutterMethodChannel(name: "com.flareflow.ios/deeplink", binaryMessenger: controller.binaryMessenger)
            
            channel.invokeMethod("handleDeepLink", arguments: ["shortid" : shortId]) { result in
                print("handleDeepLink result \(result ?? "")")
            }
        }))
    }
}

PODS:
  - advertising_id (0.0.1):
    - Flutter
  - Alamofire (5.10.2)
  - AnalyticsKit (1.6.0):
    - FingerPrint
    - ios-ntp
    - WCDB.swift
  - app_tracking_transparency (0.0.1):
    - Flutter
  - AppAttribution (2.2.0):
    - Alamofire
    - CocoaAsyncSocket
    - MMKV
    - SweetSugar (>= 1.1.1)
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - appsflyer_sdk (6.8.0):
    - AppsFlyerFramework (= 6.12.2)
    - Flutter
  - AppsFlyerFramework (6.12.2):
    - AppsFlyerFramework/Main (= 6.12.2)
  - AppsFlyerFramework/Main (6.12.2)
  - audio_session (0.0.1):
    - Flutter
  - CocoaAsyncSocket (7.6.5)
  - connectivity_plus (0.0.1):
    - Flutter
  - CryptoSwift (1.8.4)
  - deeplink_dev (0.0.1):
    - AppAttribution (~> 2.2.0)
    - AppsFlyerFramework (~> 6.12.0)
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - dlink_analytics (0.0.1):
    - AnalyticsKit (= 1.6.0)
    - Flutter
  - dlink_fingerprint (0.0.1):
    - FingerPrint
    - Flutter
  - DRMLib (2.3.0):
    - CryptoSwift (>= 1.8.3)
  - DTTJailbreakDetection (0.4.0)
  - FBAEMKit (17.1.0):
    - FBSDKCoreKit_Basics (= 17.1.0)
  - FBAudienceNetwork (6.16.0)
  - FBSDKCoreKit (17.1.0):
    - FBAEMKit (= 17.1.0)
    - FBSDKCoreKit_Basics (= 17.1.0)
  - FBSDKCoreKit_Basics (17.1.0)
  - FBSDKLoginKit (17.1.0):
    - FBSDKCoreKit (= 17.1.0)
  - FingerPrint (1.0.0):
    - SAMKeychain (~> 1.5.3)
  - Firebase/Analytics (11.10.0):
    - Firebase/Core
  - Firebase/Auth (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.10.0)
  - Firebase/Core (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Crashlytics (11.10.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - firebase_analytics (11.4.5):
    - Firebase/Analytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.5.2):
    - Firebase/Auth (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_crashlytics (4.3.5):
    - Firebase/Crashlytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.5):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.10.0):
    - FirebaseAnalytics/AdIdSupport (= 11.10.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.11.0)
  - FirebaseAuth (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.11.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.11.0)
  - FirebaseSessions (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_app_group_directory (0.0.1):
    - Flutter
  - flutter_facebook_auth (7.1.1):
    - FBSDKLoginKit (~> 17.1.0)
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - gma_mediation_meta (1.1.0):
    - Flutter
    - GoogleMobileAdsMediationFacebook (~> 6.16.0.0)
  - Google-Mobile-Ads-SDK (11.13.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (5.3.1):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 11.13.0)
    - webview_flutter_wkwebview
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (11.10.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.10.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.10.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMobileAdsMediationFacebook (6.16.0.0):
    - FBAudienceNetwork (= 6.16.0)
    - Google-Mobile-Ads-SDK (~> 11.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUserMessagingPlatform (3.0.0)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - in_app_review (2.0.0):
    - Flutter
  - ios-ntp (1.1.9):
    - CocoaAsyncSocket
  - isar_flutter_libs (1.0.0):
    - Flutter
  - JNKeychain (0.1.4)
  - Kingfisher (8.3.2)
  - leak_detector (0.0.1):
    - Flutter
  - lexo_ttplayer_decryption (0.0.1):
    - DRMLib (= 2.3.0)
    - Flutter
    - lexo_ttplayer_decryption/premium (= 0.0.1)
  - lexo_ttplayer_decryption/premium (0.0.1):
    - DRMLib (= 2.3.0)
    - Flutter
    - TTSDK/Player
  - live_activities (0.0.1):
    - Flutter
  - MMKV (2.1.0):
    - MMKVCore (~> 2.1.0)
  - mmkv_ios (2.1.0):
    - Flutter
    - MMKV (< 2.2, >= 2.1.0)
  - MMKVCore (2.1.1)
  - mobile_device_identifier (0.0.1):
    - Flutter
    - JNKeychain
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RangersAppLog/Core (6.16.9):
    - RangersAppLog/Encryptor/VOLC
  - RangersAppLog/Encryptor/VOLC (6.16.9)
  - RangersAppLog/Host/SG (6.16.9):
    - RangersAppLog/Core
  - RecaptchaInterop (101.0.0)
  - safe_device (1.0.0):
    - DTTJailbreakDetection
    - Flutter
  - SAMKeychain (1.5.3)
  - screen_capture_restrictions (0.0.1):
    - Flutter
  - sensors_plus (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - SnapKit (5.7.1)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SweetSugar (1.1.1)
  - TTSDK/Core (**********-standard):
    - RangersAppLog/Core (~> 6.16.8-bp)
    - RangersAppLog/Host/SG (~> 6.16.8-bp)
  - TTSDK/HttpDNS (**********-standard)
  - TTSDK/MDLMediaDataLoader/Main (**********-standard)
  - TTSDK/Player (**********-standard):
    - TTSDK/Core
    - TTSDK/HttpDNS
    - TTSDK/MDLMediaDataLoader/Main
    - TTSDK/PlayerCore/Normal
    - TTSDK/Reachability
    - TTSDK/TopSignature
    - TTSDK/VCN
    - TTSDK/VolcLog
  - TTSDK/PlayerCore/Base (**********-standard):
    - TTSDK/Core
    - TTSDK/Tools
  - TTSDK/PlayerCore/Normal (**********-standard):
    - TTSDK/PlayerCore/Base
    - TTSDK/TTFFmpeg/Normal
    - TTSDK/VideoProcessor
  - TTSDK/Reachability (**********-standard)
  - TTSDK/SSL (**********-standard)
  - TTSDK/Tools (**********-standard):
    - TTSDK/SSL
  - TTSDK/TopSignature (**********-standard)
  - TTSDK/TTFFmpeg/Base (**********-standard):
    - TTSDK/Tools
  - TTSDK/TTFFmpeg/Normal (**********-standard):
    - TTSDK/TTFFmpeg/Base
  - TTSDK/VCN (**********-standard)
  - TTSDK/VideoProcessor (**********-standard)
  - TTSDK/VolcLog (**********-standard):
    - TTSDK/SSL
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - WCDB.swift (2.1.10):
    - WCDBOptimizedSQLCipher (= 1.4.7)
  - WCDBOptimizedSQLCipher (1.4.7)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - advertising_id (from `.symlinks/plugins/advertising_id/ios`)
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - deeplink_dev (from `.symlinks/plugins/deeplink_dev/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - dlink_analytics (from `.symlinks/plugins/dlink_analytics/ios`)
  - dlink_fingerprint (from `.symlinks/plugins/dlink_fingerprint/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_app_group_directory (from `.symlinks/plugins/flutter_app_group_directory/ios`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - gma_mediation_meta (from `.symlinks/plugins/gma_mediation_meta/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - isar_flutter_libs (from `.symlinks/plugins/isar_flutter_libs/ios`)
  - Kingfisher (~> 8.3.2)
  - leak_detector (from `.symlinks/plugins/leak_detector/ios`)
  - lexo_ttplayer_decryption (from `.symlinks/plugins/lexo_ttplayer_decryption/ios`)
  - live_activities (from `.symlinks/plugins/live_activities/ios`)
  - mmkv_ios (from `.symlinks/plugins/mmkv_ios/ios`)
  - mobile_device_identifier (from `.symlinks/plugins/mobile_device_identifier/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - safe_device (from `.symlinks/plugins/safe_device/ios`)
  - screen_capture_restrictions (from `.symlinks/plugins/screen_capture_restrictions/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - SnapKit
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - TTSDK/Player (= **********-standard)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  https://github.com/byteplus-sdk/byteplus-specs.git:
    - TTSDK
  https://github.com/DLinkSDK/deeplink-dev-specs.git:
    - AnalyticsKit
    - AppAttribution
    - FingerPrint
  https://github.com/LexoVideo/lexo-dev-specs.git:
    - DRMLib
  https://github.com/volcengine/volcengine-specs.git:
    - RangersAppLog
  trunk:
    - Alamofire
    - AppAuth
    - AppsFlyerFramework
    - CocoaAsyncSocket
    - CryptoSwift
    - DTTJailbreakDetection
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMobileAdsMediationFacebook
    - GoogleSignIn
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - ios-ntp
    - JNKeychain
    - Kingfisher
    - MMKV
    - MMKVCore
    - nanopb
    - OrderedSet
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - SAMKeychain
    - SnapKit
    - SweetSugar
    - WCDB.swift
    - WCDBOptimizedSQLCipher

EXTERNAL SOURCES:
  advertising_id:
    :path: ".symlinks/plugins/advertising_id/ios"
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  deeplink_dev:
    :path: ".symlinks/plugins/deeplink_dev/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  dlink_analytics:
    :path: ".symlinks/plugins/dlink_analytics/ios"
  dlink_fingerprint:
    :path: ".symlinks/plugins/dlink_fingerprint/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_app_group_directory:
    :path: ".symlinks/plugins/flutter_app_group_directory/ios"
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  gma_mediation_meta:
    :path: ".symlinks/plugins/gma_mediation_meta/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  isar_flutter_libs:
    :path: ".symlinks/plugins/isar_flutter_libs/ios"
  leak_detector:
    :path: ".symlinks/plugins/leak_detector/ios"
  lexo_ttplayer_decryption:
    :path: ".symlinks/plugins/lexo_ttplayer_decryption/ios"
  live_activities:
    :path: ".symlinks/plugins/live_activities/ios"
  mmkv_ios:
    :path: ".symlinks/plugins/mmkv_ios/ios"
  mobile_device_identifier:
    :path: ".symlinks/plugins/mobile_device_identifier/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  safe_device:
    :path: ".symlinks/plugins/safe_device/ios"
  screen_capture_restrictions:
    :path: ".symlinks/plugins/screen_capture_restrictions/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  advertising_id: d5de9e659986092d7ca50977dc50f4f4fcd4c30a
  Alamofire: 7193b3b92c74a07f85569e1a6c4f4237291e7496
  AnalyticsKit: 0901c8693d0ad35e2f6f1768589148d15a3057f2
  app_tracking_transparency: ****************************************
  AppAttribution: fea10381774bcc3fa66c2d0e61cbfbaa3d09f258
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  appsflyer_sdk: 1948a2d88002d822e624f4cb52a62d16a01a13d1
  AppsFlyerFramework: 6eb4d89d2eb9a6632317f1055b359d9fd85fd5ff
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  deeplink_dev: f7572ac5eb4c0c561c6771df2d3a3957f5cdbdbb
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  dlink_analytics: e6b70817e946ec4ef173244593970cb56c5f7a5b
  dlink_fingerprint: 056985482dae0595ba7790a2738563662a686099
  DRMLib: ****************************************
  DTTJailbreakDetection: 5e356c5badc17995f65a83ed9483f787a0057b71
  FBAEMKit: cb719c53575a3be86ea873279f30d6a2c4e15881
  FBAudienceNetwork: d1670939884e3a2e0ad98dca98d7e0c841417228
  FBSDKCoreKit: ecdb980a24633ccb012700299ceb16d0235e14d2
  FBSDKCoreKit_Basics: 045101c4a9ef10c845347424d73a29aae02c3e43
  FBSDKLoginKit: 69eb59b2f839aba635616df6e422acd0ca88030a
  FingerPrint: a22cb55f8c0702cfad3908fe1d2a0c3914078e5a
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_analytics: 1998960b8fa16fd0cd9e77a6f9fd35a2009ad65e
  firebase_auth: 34c515a8fa76a4ef678ac5ad5cfa3bb3120f5ab5
  firebase_core: 2d4534e7b489907dcede540c835b48981d890943
  firebase_crashlytics: 961a0812ba79ed8f89a8d5d1e3763daa6267a87a
  firebase_messaging: 75bc93a4df25faccad67f6662ae872ac9ae69b64
  FirebaseAnalytics: 4e42333f02cf78ed93703a5c36f36dd518aebdef
  FirebaseAppCheckInterop: f23709c9ce92d810aa53ff4ce12ad3e666a3c7be
  FirebaseAuth: c4146bdfdc87329f9962babd24dae89373f49a32
  FirebaseAuthInterop: ac22ed402c2f4e3a8c63ebd3278af9a06073c1be
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseCrashlytics: 84b073c997235740e6a951b7ee49608932877e5c
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  FirebaseRemoteConfigInterop: 85bdce8babed7814816496bb6f082bc05b0a45e1
  FirebaseSessions: 9b3b30947b97a15370e0902ee7a90f50ef60ead6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_app_group_directory: 55b5362007d1c0cb45dc1dd1e94f67d615f45a6b
  flutter_facebook_auth: 8a9ad5fbfc25b77e7a53ea8986866950b27edcae
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  flutter_native_splash: c32d145d68aeda5502d5f543ee38c192065986cf
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  flutter_timezone: 7c838e17ffd4645d261e87037e5bebf6d38fe544
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  gma_mediation_meta: 54e627e72c72310eced40e7b03c98d1365efe22b
  Google-Mobile-Ads-SDK: 14f57f2dc33532a24db288897e26494640810407
  google_mobile_ads: 16ad301354fa6a7ea55770c6254bd4339bf8558b
  google_sign_in_ios: 19297361f2c51d7d8ac0201b866ef1fa5d1f94a8
  GoogleAppMeasurement: 36684bfb3ee034e2b42b4321eb19da3a1b81e65d
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMobileAdsMediationFacebook: 234b9cecc2f9c751567a71d035974c00e6803777
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  in_app_review: 5596fe56fab799e8edb3561c03d053363ab13457
  ios-ntp: 49a4ef0866aee5343627710c1c7de71d7c064417
  isar_flutter_libs: 9fc2cfb928c539e1b76c481ba5d143d556d94920
  JNKeychain: fb6cc9ec95959ba46cd95d0ee6f7a05e41da9f42
  Kingfisher: 0621d0ac0c78fecb19f6dc5303bde2b52abaf2f5
  leak_detector: 62d51705344387b23e6ace392a3e87d48fe6dc5e
  lexo_ttplayer_decryption: ea38748e512d7847ce9710118512024292ebdda2
  live_activities: f2e133059358f99655c8d181d65ff54f024a6e93
  MMKV: ce484c1ac40bf76d5f09a0195d2ec5b3d3840d55
  mmkv_ios: d0e1c7aae5feba9ca6d097cced432f0adf991a5f
  MMKVCore: 1eb661c6c498ab88e3df9ce5d8ff94d05fcc0567
  mobile_device_identifier: fc935973d028805e3e09428cc615ecfc22624e5a
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RangersAppLog: d4fa640a806e5fef583b07bea2f610684e81161e
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  safe_device: 2573e42a36900686b7bf02a87ac60c5c3a79a015
  SAMKeychain: 483e1c9f32984d50ca961e26818a534283b4cd5c
  screen_capture_restrictions: 87d079ccb4888eb319e23c5a0031535a4f2dc429
  sensors_plus: 1c5f0a01ce21c609a4df404c4e6879d62bce287f
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SweetSugar: 40d60b273b866b8d69d177a62c45b9e4b93cb03c
  TTSDK: db978b55ce67fa5e84fb9fc1ca0e8cb69a6e432b
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  wakelock_plus: 04623e3f525556020ebd4034310f20fe7fda8b49
  WCDB.swift: 21ca53849dabc6c98e6645f3fb669f45a47e7dc9
  WCDBOptimizedSQLCipher: 9bceac087eff9625b151838d3a30364f53f71ad9
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 4547784b2cedf2499c9650d63efc15eb942908ae

COCOAPODS: 1.16.2

import Foundation
import SwiftUI

func getHSize(_ size: CGFloat) -> CGFloat {
    return (size / 393.0) * UIScreen.main.bounds.size.width;
}

func getVSize(_ size: CGFloat) -> CGFloat {
    return (size / 852.0) * UIScreen.main.bounds.size.height;
}

struct FFLiveActivityHelper  {

    static func formatTime(_ seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        let remainingSeconds = seconds % 60
        
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, remainingSeconds)
        } else {
            return String(format: "%02d:%02d", minutes, remainingSeconds)
        }
    }
    
    static func attributedText(fullText: String, subText: String, subTextColor: Color) -> AttributedString {
       var attributedString = AttributedString(fullText)
       if let range = attributedString.range(of: subText) {
           attributedString[range].foregroundColor = subTextColor
       }
       return attributedString
    }
}

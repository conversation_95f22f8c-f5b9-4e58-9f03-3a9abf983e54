//
//  FFLiveActivity.swift
//  FFLiveActivity
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/9.
//

import ActivityKit
import WidgetKit
import SwiftUI

struct LiveActivitiesAppAttributes: ActivityAttributes, Identifiable {
    public typealias LiveDeliveryData = ContentState // don't forget to add this line, otherwise, live activity will not display it.
    
    public struct ContentState: Codable, Hashable { }
    
    var id = UUID()
}

extension LiveActivitiesAppAttributes {
    func prefixedKey(_ key: String) -> String {
        return "\(id)_\(key)"
    }
}

// Create shared default with custom group
let sharedDefault = UserDefaults(suiteName: "group.hqz.main")

@available(iOSApplicationExtension 16.1, *)
struct FFLiveActivityWidget: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: LiveActivitiesAppAttributes.self) { context in
            // 锁屏界面显示的内容
            let typeNumber: Int = getValueForKey(key: "type", attribute: context.attributes) ?? 0
            let title: String = getValueForKey(key: "title", attribute: context.attributes) ?? ""
            let logoImage: String = getValueForKey(key: "logoImage", attribute: context.attributes) ?? ""
            let buttonTitle: String = getValueForKey(key: "buttonTitle", attribute: context.attributes) ?? ""
            let shortsImagePlaceholder: String = getValueForKey(key: "shortsImagePlaceholder", attribute: context.attributes) ?? ""
            let shortsImage: String = getValueForKey(key: "shortsImage", attribute: context.attributes) ?? ""
            let schemeURL: String = getValueForKey(key: "schemeURL", attribute: context.attributes) ?? "ff://live_activity"
            
            let type = FFLiveActivityType(rawValue: typeNumber) ?? .defaultType
            
            Link(destination: URL(string: schemeURL)!) {
                switch type {
                case .signIn:
                    let model = FFLiveActivitySignInModel(
                        type: type,
                        title: title,
                        logoImage: logoImage,
                        buttonTitle: buttonTitle,
                        coinsImage: getValueForKey(key: "coinsImage", attribute: context.attributes) ?? "",
                        bonusImage: getValueForKey(key: "bonusImage", attribute: context.attributes) ?? "",
                        remainText: getValueForKey(key: "remainText", attribute: context.attributes) ?? "",
                        remainTime: getValueForKey(key: "remainTime", attribute: context.attributes) ?? 0,
                    )
                    FFLiveActivitySignInView(model: model)
                case .watch:
                    let model = FFLiveActivityWatchModel(
                        type: type,
                        title: title,
                        logoImage: logoImage,
                        buttonTitle: buttonTitle,
                        shortsImagePlaceholder: shortsImagePlaceholder,
                        shortsImage: shortsImage,
                        playImage: getValueForKey(key: "playImage", attribute: context.attributes) ?? "",
                        shortPlayName: getValueForKey(key: "shortPlayName", attribute: context.attributes) ?? "",
                        startTimeText: getValueForKey(key: "startTimeText", attribute: context.attributes) ?? "",
                        startTime: getValueForKey(key: "startTime", attribute: context.attributes) ?? 0,
                    )
                    FFLiveActivityWatchView(model: model)
                case .recommend:
                    let model = FFLiveActivityRecommendModel(
                        type: type,
                        title: title,
                        logoImage: logoImage,
                        buttonTitle: buttonTitle,
                        shortsImagePlaceholder: shortsImagePlaceholder,
                        shortsImage: shortsImage,
                        playImage: getValueForKey(key: "playImage", attribute: context.attributes) ?? "",
                        collectText: getValueForKey(key: "collectText", attribute: context.attributes) ?? "",
                        collectNumber: getValueForKey(key: "collectNumber", attribute: context.attributes) ?? 0,
                    )
                    FFLiveActivityRecommendView(model: model)
                default:
                    let model = FFLiveActivityDefaultModel(
                        type: type,
                        title: title,
                        logoImage: logoImage,
                        buttonTitle: buttonTitle,
                        shortsImagePlaceholder: shortsImagePlaceholder,
                    )
                    FFLiveActivityDefaultView(model: model)
                }
            }
        } dynamicIsland: { context in
            // 灵动岛显示的内容
            DynamicIsland {
                // 展开状态
                DynamicIslandExpandedRegion(.center) {
                    
                }
            } compactLeading: {
                // 紧凑状态 - 左侧
                
            } compactTrailing: {
                // 紧凑状态 - 右侧
                
            } minimal: {
                // 最小状态
            }
        }
    }
    
    private func getValueForKey<T>(key: String, attribute: LiveActivitiesAppAttributes) -> T? {
        if T.self is String.Type {
            guard let value = sharedDefault?.string(forKey: attribute.prefixedKey(key)) else {
                return nil
            }
            return value as? T
        }
        if T.self is Int.Type {
            guard let value = sharedDefault?.integer(forKey: attribute.prefixedKey(key)) else {
                return nil
            }
            return value as? T
        }
        return nil
    }
}

#if DEBUG
// 预览提供者
@available(iOSApplicationExtension 16.2, *)
struct FFLiveActivity_Previews: PreviewProvider {
    static var previews: some View {
        LiveActivitiesAppAttributes()
            .previewContext(LiveActivitiesAppAttributes.ContentState(), viewKind: .content)
            .previewDisplayName("FFLiveActivity")
    }
}
#endif

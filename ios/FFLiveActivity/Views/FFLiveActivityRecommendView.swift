//
//  FFLiveActivityRecommendView.swift
//  demo
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/9.
//

import Foundation
import SwiftUI

struct FFLiveActivityRecommendModel {
    var type: FFLiveActivityType
    var title: String
    var logoImage: String
    var buttonTitle: String
    var shortsImagePlaceholder: String
    var shortsImage: String
    var playImage: String
    var collectText: String
    var collectNumber: Int
}

struct FFLiveActivityRecommendView: View {
    var model: FFLiveActivityRecommendModel
    
    init(model: FFLiveActivityRecommendModel) {
        self.model = model
    }
    
    var body: some View {
        ZStack {
            HStack {
                if let shortsImage = UIImage(contentsOfFile: self.model.shortsImage) ?? UIImage(contentsOfFile: self.model.shortsImagePlaceholder) {
                    Image(uiImage: shortsImage)
                        .resizable()
                        .frame(width: getHSize(95), height: getVSize(125))
                        .padding(.top, getVSize(14))
                        .padding(.leading, getHSize(12))
                        .padding(.bottom, getVSize(16))
                }
                
                VStack(alignment: .leading) {
                    Text(self.model.title)
                        .font(Font.poppins(size: 15))
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: 0x020202))
                        .lineLimit(1)
                        .padding(.trailing, getHSize(47))
                        
                    let numberString = "\(self.model.collectNumber)"
                    Text(FFLiveActivityHelper.attributedText(fullText: self.model.collectText,
                                                             subText: numberString,
                                                             subTextColor: Color(hex: 0xF6610F)))
                        .foregroundColor(Color(hex: 0x020202))
                        .font(Font.poppins(size: 13))
                        .fontWeight(.medium)
                        .lineLimit(1)
                        .padding(.top, getVSize(8))
                    
                    FFActivityOpenAppButton(title: self.model.buttonTitle,
                                            iconPath: self.model.playImage,
                                            iconWidth: getHSize(18),
                                            iconHeight: getVSize(18))
                        .padding(.top, getVSize(18))
                        .padding(.bottom, getVSize(16))
                }
                .padding(.leading, getHSize(8))
                .padding(.trailing, getHSize(12))
                .padding(.top, getVSize(36))
            }
            FFActivityLogo(imagePath: self.model.logoImage)
        }
        .frame(maxWidth: getHSize(372), maxHeight: getVSize(158))
        .background(Color.white)
        .cornerRadius(getVSize(20))
    }
}

//
//  FFLiveActivityDefaultView.swift
//  demo
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/9.
//

import Foundation
import SwiftUI

struct FFLiveActivityDefaultModel {
    var type: FFLiveActivityType
    var title: String
    var logoImage: String
    var buttonTitle: String
    var shortsImagePlaceholder: String
}

struct FFLiveActivityDefaultView: View {
    var model: FFLiveActivityDefaultModel
    
    init(model: FFLiveActivityDefaultModel) {
        self.model = model
    }
    
    var body: some View {
        ZStack {
            HStack {
                if let shortsImagePlaceholder = UIImage(contentsOfFile: self.model.shortsImagePlaceholder) {
                    Image(uiImage: shortsImagePlaceholder)
                        .resizable()
                        .frame(width: getHSize(95), height: getVSize(125))
                        .padding(.top, getVSize(14))
                        .padding(.leading, getHSize(12))
                        .padding(.bottom, getVSize(16))
                }
                
                VStack(alignment: .leading) {
                    Spacer()
                    Text(self.model.title)
                        .font(Font.poppins(size: 16))
                        .fontWeight(.medium)
                        .foregroundColor(Color(hex: 0x020202))
                        .padding(.trailing, getHSize(12))
                        .padding(.bottom, getVSize(15))
                        .lineLimit(2)
                    FFActivityOpenAppButton(title: self.model.buttonTitle,
                                            iconPath: "",
                                            iconWidth: 0,
                                            iconHeight: 0)
                        .padding(.bottom, getVSize(16))
                }
                .padding(.leading, getHSize(8))
                .padding(.trailing, getHSize(12))
                
            }
            FFActivityLogo(imagePath: self.model.logoImage)
        }
        .frame(maxWidth: getHSize(372), maxHeight: getVSize(158))
        .background(Color.white)
        .cornerRadius(getVSize(20))
    }
}

#if DEBUG
// 预览提供者
@available(iOSApplicationExtension 16.2, *)
struct FFLiveActivity_Default_Previews: PreviewProvider {
    static var previews: some View {
        LiveActivitiesAppAttributes()
            .previewContext(LiveActivitiesAppAttributes.ContentState(), viewKind: .content)
            .previewDisplayName("FFLiveActivity")
    }
}
#endif

//
//  FFLiveActivityWatchView.swift
//  demo
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/9.
//

import Foundation
import SwiftUI

struct FFLiveActivityWatchModel {
    var type: FFLiveActivityType
    var title: String
    var logoImage: String
    var buttonTitle: String
    var shortsImagePlaceholder: String
    var shortsImage: String
    var playImage: String
    var shortPlayName: String
    var startTimeText: String
    var startTime: Int
}

struct FFLiveActivityWatchView: View {
    var model: FFLiveActivityWatchModel
    
    init(model: FFLiveActivityWatchModel) {
        self.model = model
    }
    
    var body: some View {
        ZStack {
            HStack {
                if let shortsImage = UIImage(contentsOfFile: self.model.shortsImage) ?? UIImage(contentsOfFile: self.model.shortsImagePlaceholder) {
                    Image(uiImage: shortsImage)
                        .resizable()
                        .frame(width: getHSize(95), height: getVSize(125))
                        .padding(.top, getVSize(14))
                        .padding(.leading, getHSize(12))
                        .padding(.bottom, getVSize(16))
                }
                
                VStack(alignment: .leading) {
                    Text(self.model.title)
                        .font(Font.poppins(size: 13))
                        .fontWeight(.regular)
                        .foregroundColor(Color(hex: 0x020202))
                        .lineLimit(1)
                        .padding(.top, getVSize(4))
                        .padding(.trailing, getHSize(47))
                    
                    Text(self.model.shortPlayName)
                        .font(Font.poppins(size: 15))
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: 0x020202))
                        .lineLimit(1)
                        .padding(.top, getVSize(4))
                        .padding(.trailing, getHSize(47))

                    let timeString = FFLiveActivityHelper.formatTime(self.model.startTime)
                    Text(FFLiveActivityHelper.attributedText(fullText: self.model.startTimeText,
                                                             subText: timeString,
                                                             subTextColor: Color(hex: 0xF6610F)))
                        .foregroundColor(Color(hex: 0x020202))
                        .font(Font.poppins(size: 13))
                        .fontWeight(.medium)
                        .lineLimit(1)
                        .padding(.top, getVSize(4))

                    Spacer()
                    FFActivityOpenAppButton(title: self.model.buttonTitle,
                                            iconPath: self.model.playImage,
                                            iconWidth: getHSize(18),
                                            iconHeight: getVSize(18))
                        .padding(.bottom, 16)
                }
                .padding(.leading, getHSize(8))
                .padding(.trailing, getHSize(12))
                .padding(.top, getVSize(13))
                
            }
            FFActivityLogo(imagePath: self.model.logoImage)
        }
        .frame(maxWidth: getHSize(372), maxHeight: getVSize(158))
        .background(Color.white)
        .cornerRadius(getVSize(20))
    }
}

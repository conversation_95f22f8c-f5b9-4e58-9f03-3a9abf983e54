//
//  FFLiveActivityViewComponents.swift
//  demo
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/9.
//

import Foundation
import SwiftUI

// 打开应用按钮
struct FFActivityOpenAppButton: View {
    var title: String
    var iconPath: String
    var iconWidth: CGFloat
    var iconHeight: CGFloat
    
    init(title: String, iconPath: String, iconWidth: CGFloat, iconHeight: CGFloat) {
        self.title = title
        self.iconPath = iconPath
        self.iconWidth = iconWidth
        self.iconHeight = iconHeight
    }
    var body: some View {
        HStack() {
            Spacer()
            if let iconImage = UIImage(contentsOfFile: self.iconPath) {
                Image(uiImage: iconImage)
                    .resizable()
                    .frame(width: self.iconWidth, height: self.iconHeight)
                    .padding(.trailing, getHSize(5))
            }
            Text(self.title)
                .font(Font.poppins(size: 14))
                .fontWeight(.medium)
                .foregroundColor(.white)
            Spacer()
        }
        .frame(maxWidth: .infinity)
        .frame(height: getVSize(35))
        .background(Color(hex: 0xFF4500))
        .cornerRadius(getVSize(17.5))
    }
}

struct FFActivityLogo: View {
    var imagePath: String
    
    init(imagePath: String) {
        self.imagePath = imagePath
    }
    
    var body: some View {
        VStack {
            if let logoImage = UIImage(contentsOfFile: self.imagePath) {
                HStack {
                    Spacer()
                    Image(uiImage: logoImage)
                        .resizable()
                        .frame(width: getHSize(23), height: getVSize(23))
                        .padding(.top, getVSize(12))
                        .padding(.trailing, getHSize(12))
                }
                Spacer()
            }
        }
    }
}

//
//  FFLiveActivitySignInView.swift
//  demo
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/9.
//

import Foundation
import SwiftUI

struct FFLiveActivitySignInModel {
    var type: FFLiveActivityType
    var title: String
    var logoImage: String
    var buttonTitle: String
    var coinsImage: String
    var bonusImage: String
    var remainText: String
    var remainTime: Int
}

struct FFLiveActivitySignInView: View {
    var model: FFLiveActivitySignInModel
    
    init(model: FFLiveActivitySignInModel) {
        self.model = model
    }
    
    var body: some View {
        ZStack {
            VStack(alignment: .leading) {
                HStack(alignment: .center) {
                    if let coinsImage = UIImage(contentsOfFile: self.model.coinsImage) {
                        Image(uiImage: coinsImage)
                            .resizable()
                            .frame(width: getHSize(62), height: getVSize(62))
                            .padding(.top, getVSize(13))
                            .padding(.leading, getHSize(12))
                    }
                    
                    VStack(alignment: .leading) {
                        Text(self.model.title)
                            .font(Font.poppins(size: 13))
                            .fontWeight(.medium)
                            .foregroundColor(Color(hex: 0x020202))
                            .lineLimit(1)
                            .padding(.trailing, getHSize(28))
                            
                        let timeString = FFLiveActivityHelper.formatTime(self.model.remainTime)
                        Text(FFLiveActivityHelper.attributedText(fullText: self.model.remainText,
                                                                 subText: timeString,
                                                                 subTextColor: Color(hex: 0xF6610F)))
                            .foregroundColor(Color(hex: 0x020202))
                            .font(Font.poppins(size: 20))
                            .fontWeight(.semibold)
                            .lineLimit(1)
                            .padding(.top, getVSize(4))
                            .frame(maxWidth: .infinity, alignment:.leading)
                        
                        Spacer()
                    }
                    .padding(.top, getVSize(22))
                    .padding(.leading, getHSize(5))
                    .padding(.trailing, getHSize(12))
                }
                Spacer()
                FFActivityOpenAppButton(title: self.model.buttonTitle,
                                        iconPath: self.model.bonusImage,
                                        iconWidth: getHSize(19),
                                        iconHeight: getVSize(16))
                    .padding(.leading, getHSize(12))
                    .padding(.trailing, getHSize(12))
                    .padding(.bottom, getVSize(16))
            }
            FFActivityLogo(imagePath: self.model.logoImage)
        }
        .frame(maxWidth: getHSize(372), maxHeight: getVSize(141))
        .background(Color.white)
        .cornerRadius(getVSize(20))
    }
}

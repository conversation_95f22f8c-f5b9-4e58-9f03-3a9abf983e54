import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/utils/events.dart';

import '../../api/rewards_bind_api.dart';
import '../../common/log/ff_log.dart';
import '../../model/rewards_bind/bind_event.dart';

enum LoadingStatus {
  loading,
  success,
  failed,
}

class AccountInfoController extends GetxController {
// 是否绑定的状态  不为空表示已经绑定
  var facebookBoundValue = "".obs;
  var phoneBoundValue = "".obs;
  var emailBoundValue = "".obs;
  late Rx<LoadingStatus> loadingStatus = LoadingStatus.loading.obs;
  StreamSubscription<BindStateEvent>? streamSubscription;

  @override
  void onInit() {
    streamSubscription = eventBus.on<BindStateEvent>().listen((event) {
      getBindList();
    });

    super.onInit();
  }

  @override
  void onClose() {
    streamSubscription?.cancel();
    super.onClose();
  }

  @override
  void onReady() {
    getBindList();
    super.onReady();
  }

  Future<void> getBindList() async {
    // 10代表谷歌 20代表facebook 30代表twitter 40代表github 50代表email 60代表手机号 70代表whatsapp 80代表apple 90代表匿名登录
    var result = await ApiRewardsBind.getBindList();
    result?.providers.forEach((item) {
      if (item.provider == 20) {
        facebookBoundValue.value = item.providerId;
      } else if (item.provider == 50) {
        emailBoundValue.value = item.providerId;
      } else if (item.provider == 60) {
        phoneBoundValue.value = item.providerId;
      }
    });

    if (result != null) {
      loadingStatus.value = LoadingStatus.success;
    } else {
      FFLog.error("绑定状态返回为空");
      loadingStatus.value = LoadingStatus.failed;
    }
  }
}

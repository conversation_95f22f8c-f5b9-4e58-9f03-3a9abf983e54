import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/rewards_bind/bind_options.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../common/event/event_value.dart';
import '../../components/empty/empty.dart';
import '../../components/loading/ffloading.dart';
import '../../components/nav_bar/ffnav_bar.dart';
import '../../i18n/trans.dart';
import '../../utils/auth.dart';
import 'account_info_controller.dart';

class AccountInfoPage extends StatefulWidget {
  const AccountInfoPage({super.key});

  @override
  State<AccountInfoPage> createState() => _AccountInfoPageState();
}

class _AccountInfoPageState extends State<AccountInfoPage> {
  final AccountInfoController controller = Get.put(AccountInfoController());

  var titleStyle = TextStyle(
      fontWeight: FontWeight.w500, fontSize: 16.sp, color: Colors.white);

  var subTitleStyle = TextStyle(
      fontWeight: FontWeight.w400,
      fontSize: 12.sp,
      color: const Color(0xFF959595));

  @override
  Widget build(BuildContext context) {
    Widget divider = Divider(
      indent: 20.w,
      endIndent: 20.w,
      thickness: 0.8,
      height: 1,
      color: Colors.white.withValues(alpha: 0.2),
    );
    return Scaffold(
      appBar: FFNavBar(
        showBackIcon: true,
        title: AppTrans.accountInfo(),
      ),
      body: Obx(() {
        if (controller.loadingStatus.value == LoadingStatus.loading) {
          // 加载中
          return const FFLoadingWidget();
        }
        if (controller.loadingStatus.value == LoadingStatus.failed) {
          // 加载失败
          return EmptyWidget(
            type: EmptyType.noNetwork,
            onRefresh: () async {
              Get.loading();
              await controller.getBindList();
              Get.dismiss();
            },
            pageFrom: EmptyPageFrom.rewards,
          );
        }

        return ListView(
          children: [
            buildFaceBookItem(),
            divider,
            buildEmailItem(),
            divider,
            buildPhoneItem(),
            divider,
          ],
        );
      }),
    );
  }

  Widget buildPhoneItem() {
    return _buildItem(
      title: AppTrans.phone(),
      subtitle: controller.phoneBoundValue.value.isNotEmpty
          ? controller.phoneBoundValue.value
          : AppTrans.unbound(),
      trailing: controller.phoneBoundValue.value.isEmpty
          ? TextButton(
              onPressed: () async {
                //   发起phone 绑定
                await AppNavigator.startRewardsBindPage(BindOptionsModel(
                    type: RewardsBindType.Phone,
                    sourceBindType: SourceBindType.AccountInfo));
              },
              child: Text(
                AppTrans.bindNow(),
                style: const TextStyle(color: Color(0xFFFFE990)),
              ),
            )
          : const SizedBox(),
    );
  }

  Widget buildEmailItem() {
    return _buildItem(
      title: AppTrans.email(),
      subtitle: controller.emailBoundValue.value.isNotEmpty
          ? controller.emailBoundValue.value
          : AppTrans.unbound(),
      trailing: controller.emailBoundValue.value.isEmpty
          ? TextButton(
              onPressed: () async {
                // 发起email 绑定
                await AppNavigator.startRewardsBindPage(BindOptionsModel(
                    type: RewardsBindType.Email,
                    sourceBindType: SourceBindType.AccountInfo));
              },
              child: Text(
                AppTrans.bindNow(),
                style: const TextStyle(color: Color(0xFFFFE990)),
              ),
            )
          : const SizedBox(),
    );
  }

  Widget buildFaceBookItem() {
    return _buildItem(
      title: AppTrans.facebook(),
      subtitle: controller.facebookBoundValue.value.isNotEmpty
          ? controller.facebookBoundValue.value
          : AppTrans.unbound(),
      trailing: controller.facebookBoundValue.value.isEmpty
          ? TextButton(
              onPressed: () async {
                //发起facebook 绑定
                var isSuccess = await Auth.signInWithFacebook(
                    EventValue.task_facebook_login);
                FFLog.info("fb 登录结果。$isSuccess");
                if (isSuccess) {
                  await controller.getBindList();
                }
              },
              child: Text(
                AppTrans.bindNow(),
                style: const TextStyle(color: Color(0xFFFFE990)),
              ),
            )
          : const SizedBox(),
    );
  }

  Widget _buildItem({
    required String title,
    required String subtitle,
    required Widget trailing,
  }) {
    return Container(
      height: 64.sp,
      padding: EdgeInsets.symmetric(horizontal: 20.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 24.sp,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    title,
                    style: titleStyle,
                  ),
                ),
                SizedBox(height: 2.sp),
                Container(
                  height: 18.sp,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    subtitle,
                    style: subTitleStyle,
                  ),
                ),
              ],
            ),
          ),
          trailing,
        ],
      ),
    );
  }
}

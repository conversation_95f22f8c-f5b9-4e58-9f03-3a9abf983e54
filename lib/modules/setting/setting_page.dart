import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/state_manager.dart';
import 'package:playlet/components/alert/action.dart';
import 'package:playlet/components/alert/index.dart';
import 'package:playlet/components/base/button.dart';
import 'package:playlet/components/nav_bar/ffnav_bar.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/setting/setting_controller.dart';
import 'package:playlet/modules/setting/widgets/cell.dart';

class SettingPage extends GetView<SettingController> {
  const SettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return FFNavBar(
      title: AppTrans.setting(),
      showBackIcon: true,
    );
  }

  Widget _buildBody() {
    return SafeArea(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildItems(),
          const Spacer(),
          _buildLogOut(),
          _buildAppVersion(),
        ],
      ),
    );
  }

  Widget _buildItems() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // SettingTitleCell(
        //   title: AppTrans.automaticEpisodeUnlock(),
        //   padding: EdgeInsets.only(left: 16.w, right: 20.w),
        //   right: Obx(
        //     () => FFSwitch(
        //       initValue: controller.unlockValue.value,
        //       onChanged: (value) async {
        //         await controller.unlockValueOnChnaged(value);
        //       },
        //     ),
        //   ),
        // ),
        // SizedBox(height: 6.w),
        SettingTitleArrowCell(
          title: AppTrans.accountInfo(),
          padding: EdgeInsets.only(left: 16.w, right: 20.w),
          onTap: () => controller.toAccountInfo(),
        ),

        SettingTitleArrowCell(
          title: AppTrans.userAgreement(),
          padding: EdgeInsets.only(left: 16.w, right: 20.w),
          onTap: () => controller.toUserAgreementPage(),
        ),
        SizedBox(height: 6.h),
        SettingTitleArrowCell(
          title: AppTrans.privacyPolicy(),
          padding: EdgeInsets.only(left: 16.w, right: 20.w),
          onTap: () => controller.toPrivacyPolicyPage(),
        ),
        SizedBox(height: 6.h),
        Obx(
          () => controller.userService.isLogin == false
              ? const SizedBox.shrink()
              : SettingTitleArrowCell(
                  title: AppTrans.deleteAccount(),
                  padding: EdgeInsets.only(left: 16.w, right: 20.w),
                  onTap: () {
                    FFAlert.action(
                      container: FFAlertActionContainer(
                        title: AppTrans.deleteAccountAlertTitle(),
                        detail: AppTrans.deleteAccountAlertDesc(),
                        actions: [
                          FFAlertActionButton.cancel(
                            text: AppTrans.cancel(),
                          ),
                          FFAlertActionButton.confirm(
                            text: AppTrans.confirm(),
                            onPressed: controller.onConfirmDeleteAccount,
                          ),
                        ],
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildLogOut() {
    return Obx(
      () => controller.userService.isLogin == false
          ? const SizedBox.shrink()
          : Padding(
              padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 35.h),
              child: FFButton.text(
                padding: EdgeInsets.all(12.sp),
                text: AppTrans.logOut(),
                textStyle: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF121212),
                  height: 2.4,
                ),
                onPressed: () {
                  FFAlert.action(
                    container: FFAlertActionContainer(
                      title: AppTrans.logOutAlertTitle(),
                      detail: AppTrans.logOutAlertDesc(),
                      actions: [
                        FFAlertActionButton.cancel(
                          text: AppTrans.cancel(),
                        ),
                        FFAlertActionButton.confirm(
                          text: AppTrans.confirm(),
                          onPressed: controller.onConfirmLogOut,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
    );
  }

  Widget _buildAppVersion() {
    return Obx(
      () => Padding(
        padding: EdgeInsets.symmetric(
          vertical: 35.sp,
        ),
        child: Text(
          '${AppTrans.version()} ${controller.appVersion.value}',
          style: TextStyle(
            color: const Color(0xFF9E9E9E),
            fontSize: 15.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}

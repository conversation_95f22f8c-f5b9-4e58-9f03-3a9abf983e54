import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SettingCell extends StatelessWidget {
  const SettingCell({
    super.key,
    required this.center,
    this.left,
    this.right,
    this.height,
    this.padding,
    this.centerPadding = EdgeInsets.zero,
    this.onTap,
  });

  final Widget center;
  final Widget? left;
  final Widget? right;
  final double? height;
  final EdgeInsets? padding;
  final EdgeInsets centerPadding;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final double cellHeight = height ?? 54.h;
    final EdgeInsets cellPadding =
        padding ?? EdgeInsets.symmetric(horizontal: 16.w);
    return InkWell(
      onTap: onTap,
      splashFactory: NoSplash.splashFactory,
      child: Container(
        height: cellHeight,
        padding: cellPadding,
        child: Row(
          children: [
            left ?? const SizedBox.shrink(),
            Expanded(
              child: Padding(
                padding: centerPadding,
                child: center,
              ),
            ),
            right ?? const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}

class SettingTitleCell extends SettingCell {
  final String title;

  SettingTitleCell({
    super.key,
    required this.title,
    super.left,
    super.right,
    super.padding,
    super.centerPadding,
    super.onTap,
  }) : super(
          center: Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        );
}

class SettingArrowCell extends SettingCell {
  SettingArrowCell({
    super.key,
    required super.center,
    super.left,
    super.padding,
    super.centerPadding,
    super.onTap,
  }) : super(
          right: Icon(
            Icons.arrow_forward_ios_rounded,
            size: 20.w,
            color: const Color(0xFF999999),
          ),
        );
}

class SettingTitleArrowCell extends SettingTitleCell {
  SettingTitleArrowCell({
    super.key,
    required super.title,
    super.left,
    super.padding,
    super.centerPadding,
    super.onTap,
  }) : super(
          right: Icon(
            Icons.arrow_forward_ios_rounded,
            size: 20.sp,
            color: const Color(0xFF999999),
          ),
        );
}

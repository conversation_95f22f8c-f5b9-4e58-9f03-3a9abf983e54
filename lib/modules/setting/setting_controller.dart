import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/api/base.dart';
import 'package:playlet/api/user.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/model/webview.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/recommend_service.dart';
import 'package:playlet/service/shorts_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/auth.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/token.dart';
import 'package:playlet/utils/track_event.dart';

class SettingController extends GetxController {
  final UserService userService = Get.find<UserService>();
  final RecommendService recommendService = Get.find<RecommendService>();
  late RxBool unlockValue;
  late RxString appVersion;
  late bool _isAccountDeleteRunning;
  late bool _isAccountLogoutRunning;

  @override
  void onInit() {
    unlockValue = (userService.userInfo.value?.autoUnlockEpisode ?? false).obs;
    appVersion = ''.obs;
    _isAccountDeleteRunning = false;
    _isAccountLogoutRunning = false;
    _initData();
    super.onInit();
  }

  Future<void> _initData() async {
    String version = await AppDeviceService.instance.getVersion();
    appVersion.value = version;
  }

  ////////////////////////////////////////////////
  /// actions
  ////////////////////////////////////////////////
  Future<void> unlockValueOnChnaged(bool value) async {
    bool result = await ApiUser.updateUserAutoUnlockResult(value: value);
    if (result == true) {
      unlockValue.value = value;
      userService.updateAutoUnlockEpisodeValue(value);
    } else {
      unlockValue.value = !value;
      SmartDialog.showNotify(msg: 'Failure', notifyType: NotifyType.failure);
    }
  }

  void toUserAgreementPage() {
    AppNavigator.startWebViewPage(
      WebviewModel(
        title: AppTrans.userAgreement(),
        url: Config.userAgreement,
      ),
    );
  }

  void toPrivacyPolicyPage() {
    AppNavigator.startWebViewPage(
      WebviewModel(
        title: AppTrans.privacyPolicy(),
        url: Config.privacyPolicy,
      ),
    );
  }

  Future<void> onConfirmDeleteAccount() async {
    if (_isAccountLogoutRunning == true) {
      return;
    }
    _isAccountDeleteRunning = true;
    Get.loading();
    bool result = await ApiUser.deleteAccount();

    if (result == true) {
      await _rebackLogin();
      Get.dismiss();
      Get.toast(AppTrans.succeeded());
      Get.back();
      _isAccountDeleteRunning = false;
      // 刷新可获得金币总数
      Get.find<UserService>().fetchRewardsTotalBonus();
    } else {
      Get.dismiss();
      _isAccountDeleteRunning = false;
      Get.toast(AppTrans.failed());
    }
    
  }

  Future<void> onConfirmLogOut() async {
    if (_isAccountLogoutRunning == true) {
      return;
    }
    _isAccountLogoutRunning = true;
    Get.loading();
    bool result = await ApiUser.loginOut();
    if (result == true) {
      await _rebackLogin();
      _trackLogOutEvent();
      Get.dismiss();
      Get.toast(AppTrans.succeeded());
      _isAccountLogoutRunning = false;
      Get.back();
      // 刷新可获得金币总数
      Get.find<UserService>().fetchRewardsTotalBonus();
    } else {
      _isAccountLogoutRunning = false;
      Get.dismiss();
      Get.toast(AppTrans.failed());
    }
  }

  void _trackLogOutEvent() {
    String type = "";
    if (userService.userInfo.value != null) {
      if (userService.userInfo.value!.firebaseSource == 10) {
        type = EventValue.google;
      } else if (userService.userInfo.value!.firebaseSource == 20) {
        type = EventValue.facebook;
      } else if (userService.userInfo.value!.firebaseSource == 80) {
        type = EventValue.apple;
      }
    }
    useTrackEvent(EventName.log_out,
        extra: {EventKey.type: type, EventKey.is_auto: "1"});
  }

  Future<void> _rebackLogin() async {
    try {
      await Auth.signOut().timeout(const Duration(seconds: 5));
    } catch (e) {
      FFLog.error("firebase登出超时: $e");
      // 即使超时也继续执行
    } finally {
      /// 清空Token
      await Token.removeToken();

      Get.find<ShortsService>().clear();

      /// 游客登录
      InitLoginResult? result = await ApiBase.initLogin();
      if (result != null) {
        /// 通知刷新页面信息
        if (userService.userInfo.value != null) {
          recommendService.onSwitchUser();
          eventBus.fire(InitLoginEventData(
            userInfo: userService.userInfo.value!,
          ));
        }
      } else {
        FFLog.debug('init login failure');
      }
    }
  }

  void toAccountInfo() {
    AppNavigator.startAccountInfoPage();
  }
}

import 'package:flutter/material.dart';
import 'package:playlet/config/index.dart';

class FeedbackEmail extends StatelessWidget {
  const FeedbackEmail({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E1E1E),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        Config.isProduction ? Config.contactEmail : Config.contactEmailDebug,
        style: TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:playlet/i18n/trans.dart';

class FeedbackInput extends StatefulWidget {
  const FeedbackInput({
    super.key,
    required this.controller,
    required this.focusNode,
    this.onChanged,
  });

  final TextEditingController controller;
  final FocusNode focusNode;
  final ValueChanged<String>? onChanged;

  @override
  State<FeedbackInput> createState() => _FeedbackInputState();
}

class _FeedbackInputState extends State<FeedbackInput> {
  bool _isActive = false;

  @override
  void initState() {
    widget.focusNode.addListener(focusNodeListenser);
    super.initState();
  }

  @override
  void dispose() {
    widget.focusNode.removeListener(focusNodeListenser);
    super.dispose();
  }

  void focusNodeListenser() {
    if (widget.focusNode.hasFocus == true) {
      setState(() {
        _isActive = true;
      });
    } else {
      setState(() {
        _isActive = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 136,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E1E1E),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: widget.controller,
        focusNode: widget.focusNode,
        keyboardType: TextInputType.multiline,
        textInputAction: TextInputAction.done,
        textAlign: TextAlign.start,
        maxLines: null,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.w400,
          height: 1.5,
        ),
        decoration: InputDecoration(
          filled: false,
          fillColor: Colors.orange,
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          isCollapsed: true,
          counterText: '',
          hintText: _isActive == true ? null : AppTrans.feedbackPlaceholder(),
          hintStyle: const TextStyle(
            color: Color(0xFF999999),
            fontSize: 14,
            fontWeight: FontWeight.w400,
            height: 1.5,
          ),
        ),
        onTapOutside: (event) {
          widget.focusNode.unfocus();
        },
        onChanged: (value) {
          if (widget.onChanged != null) {
            widget.onChanged!(value);
          }
        },
        onSubmitted: (value) {
          widget.focusNode.unfocus();
        },
      ),
    );
  }
}

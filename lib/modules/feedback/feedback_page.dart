import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:playlet/components/base/button.dart';
import 'package:playlet/components/nav_bar/ffnav_bar.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/feedback/feedback_controller.dart';
import 'package:playlet/modules/feedback/widgets/email.dart';
import 'package:playlet/modules/feedback/widgets/input.dart';

class FeedbackPage extends GetView<FeedbackController> {
  const FeedbackPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return FFNavBar(
      title: AppTrans.feedback(),
      showBackIcon: true,
    );
  }

  Widget _buildBody() {
    return SafeArea(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGroupItem(
            title: AppTrans.feedback(),
            child: FeedbackInput(
              controller: controller.feedbackController,
              focusNode: controller.feedbackFocusNode,
              onChanged: controller.feedbackOnChanged,
            ),
          ),
          _buildGroupItem(
            title: AppTrans.email(),
            child: const FeedbackEmail(),
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.all(20.w),
            child: Obx(
              () => FFButton.text(
                disable: controller.submitDisable.value,
                text: AppTrans.submit(),
                padding: EdgeInsets.all(12.sp),
                textStyle: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF121212),
                ),
                onPressed: controller.onSubmit,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupItem({
    required String title,
    required Widget child,
  }) {
    return Padding(
      padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 0.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 14.h),
            child: child,
          ),
        ],
      ),
    );
  }
}

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/email.dart';

class FeedbackController extends GetxController {
  late final TextEditingController feedbackController;
  late final FocusNode feedbackFocusNode;
  late RxBool submitDisable;

  final UserService userService = Get.find<UserService>();

  @override
  void onInit() {
    feedbackController = TextEditingController();
    feedbackFocusNode = FocusNode();
    submitDisable = true.obs;
    super.onInit();
  }

  @override
  void onClose() {
    feedbackController.dispose();
    feedbackFocusNode.dispose();
    super.onClose();
  }

  void feedbackOnChanged(String value) {
    submitDisable.value = value.isEmpty;
  }

  Future<void> onSubmit() async {
    if (EmailUtils.checkBody(feedbackController.text) == false) {
      FFLog.debug('未填写邮件内容');
      return;
    }
    String email =
        Config.isProduction ? Config.contactEmail : Config.contactEmailDebug;
    if (EmailUtils.checkEmail(email) == false) {
      FFLog.debug('请提供正确的邮件地址');
      return;
    }

    String emailBody = await _handleEmailBody();
    bool? result = await EmailUtils.sendEmail(
      to: email,
      subject: AppTrans.feedback(),
      body: emailBody,
    );
    if (result == true) {
      FFLog.debug('邮件发送成功');
    } else {
      FFLog.debug('邮件发送失败');
    }
  }

  /*
  */

  Future<String> _handleEmailBody() async {
    String version = await AppDeviceService.instance.getVersion();
    String uid = userService.userInfo.value?.userId ?? '';
    String system = Platform.isAndroid ? 'Android' : 'iOS';
    String systemVersion = '';
    if (Platform.isAndroid) {
      systemVersion =
          (await AppDeviceService.instance.getAndroidSdkVersion() ?? 0)
              .toString();
    } else {
      systemVersion =
          (await AppDeviceService.instance.getIosSystemVersion() ?? 0)
              .toString();
    }
    String deviceModel = await AppDeviceService.instance.getDeviceModel() ?? '';
    String deviceModelInfo = '$system-$systemVersion,$deviceModel';

    String note1 = 'Please provide a detailed description of your problem:\n';
    String note2 =
        '(Please use the space below to provide a detailed description of your problem or request. We will review your inquiry and aim to get back to you within 3-7 working days.)\n\n';
    String noteBody = '${feedbackController.text}' '\n\n';
    String note3 = 'Problem type:\n';
    String note4 =
        '(please select Function Issues, Payment Issues, Suggestions, others)\n\n';
    String note5 = 'Relevant Information:\n';
    String note6 = 'UID (Do not delete): $uid' '\n';
    String note7 = 'App Version (Do not delete): $version' '\n';
    String note8 =
        'Operating System and Device Model (Do not delete): $deviceModelInfo'
        '\n';
    String note9 = 'Screenshots or Attachments (if applicable):\n';
    String code = Uri.encodeQueryComponent('&');
    String note10 =
        'Payment Issues - Order number $code account ID (required):';

    return '$note1$note2$noteBody$note3$note4$note5$note6$note7$note8$note9$note10';
  }
}

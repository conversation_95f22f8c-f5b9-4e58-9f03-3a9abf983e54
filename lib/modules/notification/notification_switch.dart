import 'package:flutter/material.dart';

class NotificationSwitch extends StatefulWidget {
  const NotificationSwitch({
    super.key,
    required this.initValue,
    required this.onChanged,
    this.thumbColor = Colors.white,
    this.height = 22.0,
    this.width = 44.0,
  });
  final bool initValue;
  final ValueChanged<bool> onChanged;
  final Color thumbColor;
  final double height;
  final double width;

  @override
  State<NotificationSwitch> createState() => _NotificationSwitchState();
}

class _NotificationSwitchState extends State<NotificationSwitch> {
  late bool _value;

  @override
  void initState() {
    _value = widget.initValue;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _value = !_value;
          widget.onChanged(_value);
        });
      },
      child: Container(
        width: widget.width,
        height: widget.height,
        padding: const EdgeInsets.all(2.0),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(_value ? "assets/notification/ic_notification_switch_selected.png" : "assets/notification/ic_notification_switch_default.png"),
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }
}

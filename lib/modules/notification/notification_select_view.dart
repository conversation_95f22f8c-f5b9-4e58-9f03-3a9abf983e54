import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart' hide Trans;
import 'package:permission_handler/permission_handler.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/notification/notification_switch.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/notificaiton_dialog_service.dart';

class NotificationSelectView extends StatefulWidget {
  // 1 免费聚集上新，2 充值优化活动提醒，3 每日签到提醒
  final int type;

  const NotificationSelectView({super.key, required this.type});

  @override
  State<NotificationSelectView> createState() => _NotificationSelectViewState();
}

class _NotificationSelectViewState extends State<NotificationSelectView> {
  bool dailyCheckIn = false;
  bool freeEpisodes = false;
  bool rechargePromo = false;

  @override
  void initState() {
    dailyCheckIn = widget.type == 1;
    freeEpisodes = widget.type == 2;
    rechargePromo = widget.type == 3;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 300.sp,
        padding: EdgeInsets.symmetric(horizontal: 15.sp),
        decoration: BoxDecoration(
          color: const Color(0xff25252e),
          borderRadius: BorderRadius.circular(12.r),
          image: const DecorationImage(
            image: AssetImage("assets/notification/bg_notification_reward.png"),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 30.sp),
                _buildHeader(),
                SizedBox(height: 24.sp),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 17.sp, vertical: 14.sp),
                  decoration: BoxDecoration(
                    color: const Color(0xff1d1e27),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Column(
                    children: [
                      _buildSwitchOption(AppTrans.notificationSelectContentA(), dailyCheckIn, (value) {
                        setState(() => dailyCheckIn = value);
                      }),
                      _buildLine(),
                      _buildSwitchOption(AppTrans.notificationSelectContentB(), freeEpisodes, (value) {
                        setState(() => freeEpisodes = value);
                      }),
                      _buildLine(),
                      _buildSwitchOption(AppTrans.notificationSelectContentD(), rechargePromo,
                          (value) {
                        setState(() => rechargePromo = value);
                      }),
                    ],
                  ),
                ),
                SizedBox(height: 19.sp),
                _buildTurnOnButton(),
                SizedBox(height: 24.sp),
              ],
            ),
            Positioned(
                right: 3.sp,
                top: 18.sp,
                child: GestureDetector(
                  onTap: () {
                    Get.find<NotificationLoginService>().writeNotificationDialogShow(widget.type);
                    SmartDialog.dismiss(
                      tag: 'first_dialog_last_shown_${widget.type}',
                      result: false,
                    );
                  },
                  child: Assets.notification.icNotificationClose.image(width: 24.sp, height: 24.sp),
                ))
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 200.sp,
          child: Text(
            AppTrans.notificationSelectTitle(),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              height: 1.2,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchOption(String title, bool value, ValueChanged<bool> onChanged) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              color: const Color(0xff999999),
              fontSize: 11.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          NotificationSwitch(
            initValue: value,
            onChanged: onChanged,
            width: 22.sp,
            height: 12.sp,
          ),
        ],
      ),
    );
  }

  Widget _buildLine() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          width: 0.5,
          color: Colors.white.withAlpha(51),
        ),
      ),
    );
  }

  Widget _buildTurnOnButton() {
    return GestureDetector(
      onTap: () async {
        Get.find<NotificationLoginService>().writeNotificationDialogShow(widget.type);
        await requestNotificationPermission();
      },
      child: Container(
        width: 196.sp,
        height: 40.sp,
        decoration: BoxDecoration(
          color: const Color(0xFFFF4500),
          borderRadius: BorderRadius.circular(6.r),
        ),
        alignment: Alignment.center,
        child: Text(
          AppTrans.notificationTurnOn(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  // 请求通知权限
  Future<void> requestNotificationPermission() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        final status = await Permission.notification.status;
        FFLog.debug('当前通知权限状态: $status');
        if (status.isDenied) {
          var androidSdkVersion = await AppDeviceService.instance.getAndroidSdkVersion();
          if (Platform.isAndroid && (androidSdkVersion ?? 0) < 33) {
            // android 12及以下，没有系统权限弹框，直接打开系统通知设置界面
            // 打开系统通知设置页面
            SmartDialog.dismiss(tag: 'first_dialog_last_shown_${widget.type}', result: true);
            await openAppSettings();
          } else {
            SmartDialog.dismiss(tag: 'first_dialog_last_shown_${widget.type}', result: false);
            final result = await Permission.notification.request();
            FFLog.debug('请求通知权限结果: $result');
          }
        } else if (status.isPermanentlyDenied) {
          // 打开系统通知设置页面
          SmartDialog.dismiss(tag: 'first_dialog_last_shown_${widget.type}', result: true);
          await openAppSettings();
        }
      } else {
        SmartDialog.dismiss(tag: 'first_dialog_last_shown_${widget.type}', result: false);
      }
    } catch (e) {
      FFLog.error('请求通知权限失败: $e');
      SmartDialog.dismiss(tag: 'first_dialog_last_shown_${widget.type}', result: false);
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart' hide Trans;
import 'package:permission_handler/permission_handler.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/rewards/task_list_result.dart';
import 'package:playlet/service/notification/firebase_message_manager.dart';
import 'package:playlet/service/notification/notification_service.dart';
import 'package:playlet/service/notification/task/PermanentTask.dart';

import '../rewards/event/rewards_event.dart';

class NotificationRewardView extends StatefulWidget {
  final AppTaskReponseList appTask;

  const NotificationRewardView({super.key, required this.appTask});

  @override
  State<NotificationRewardView> createState() => _NotificationRewardViewState();
}

class _NotificationRewardViewState extends State<NotificationRewardView> with WidgetsBindingObserver {
  bool isFromSettings = false;

  @override
  void initState() {
    super.initState();
    /// 添加通知自定义弹框埋点
    RewardsEvent.submitTaskClick(from: EventValue.customize, type: EventValue.newbieTask, taskName: EventValue.customize);
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && isFromSettings) {
      SmartDialog.dismiss();
      Permission.notification.status.then((status) {
        if (status.isGranted && mounted) {
          // 启动常驻通知栏任务
          PermanentTask.instance.init();
          // 通知权限通过后，上报firebase token
          FirebaseMessageManager.instance.tokenUploadConfigInit();
          Get.find<NotificationService>().receiveReceiveRewardsRewards(widget.appTask.id);
          SmartDialog.showToast(AppTrans.notificationCustomReceived(widget.appTask.remark ?? ""));
          // 判断fsi权限弹框
          Get.find<NotificationService>().showUseFullScreenDialog();
          /// 添加通知任务完成埋点
          RewardsEvent.submitTaskFinish(from: EventValue.customize, type: EventValue.newbieTask, taskName: EventValue.customize, bonus: widget.appTask.taskBonus.toString());

        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Container(
      width: 300.sp,
      padding: EdgeInsets.symmetric(horizontal: 24.sp),
      decoration: BoxDecoration(
        color: const Color(0xff25252e),
        borderRadius: BorderRadius.circular(12.r),
        image: const DecorationImage(
          image: AssetImage("assets/notification/bg_notification_reward.png"),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          SizedBox(
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 17.sp),
                Assets.notification.icNotificationReward.image(width: 150.sp, height: 150.sp),
                Text(
                  AppTrans.notificationCustomTitle(widget.appTask.remark ?? ""),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(height: 19.sp),
                Text(
                  AppTrans.notificationCustomContent(),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: const Color(0xff999999),
                    fontSize: 14.sp,
                    height: 1.5,
                  ),
                ),
                SizedBox(height: 29.sp),
                SizedBox(
                  height: 40.sp,
                  width: 196.sp,
                  child: ElevatedButton(
                    onPressed: () async {
                      // 打开系统通知设置页面
                      await openAppSettings();
                      isFromSettings = true;
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xffff4500),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                    ),
                    child: Text(
                      AppTrans.notificationReceive(),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 9.sp),
                TextButton(
                  onPressed: () {
                    SmartDialog.dismiss();
                  },
                  child: Text(
                    AppTrans.later(),
                    style: TextStyle(
                      color: const Color(0xff828282),
                      fontSize: 16.sp,
                    ),
                  ),
                ),
                SizedBox(height: 27.sp),
              ],
            ),
          ),
          Positioned(
              right: 3.sp,
              top: 18.sp,
              child: GestureDetector(
                  onTap: () {
                    SmartDialog.dismiss();
                  },
                  child: Assets.notification.icNotificationClose.image(width: 24.sp, height: 24.sp)))
        ],
      ),
    ));
  }
}

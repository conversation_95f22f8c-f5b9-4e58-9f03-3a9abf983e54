import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/modules/debug/log/debug_log_controller.dart';

class DebugLogPage extends GetView<DebugLogController> {
  const DebugLogPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('查看日志'),
        actions: [
          Obx(() => IconButton(
            icon: Icon(controller.isReversed.value 
              ? Icons.arrow_upward 
              : Icons.arrow_downward),
            onPressed: () => controller.toggleOrder(),
          )),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadLogs(),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildLogTypeDropdown(), // 添加日志类型下拉框
          _buildSearchBar(),
          _buildKeywordChips(),
          Expanded(
            child: _buildLogList(),
          ),
        ],
      ),
    );
  }

  // 添加日志类型下拉框组件
  Widget _buildLogTypeDropdown() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          const Text('日志类型: ', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(
            child: Obx(() => DropdownButton<DebugLogType>(
              isExpanded: true,
              value: controller.selectedLogType.value,
              items: DebugLogType.values.map((DebugLogType type) {
                return DropdownMenuItem<DebugLogType>(
                  value: type,
                  child: Text(type.displayName),
                );
              }).toList(),
              onChanged: (DebugLogType? newValue) {
                if (newValue != null) {
                  controller.selectLogType(newValue);
                }
              },
            )),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    final TextEditingController textController = TextEditingController();
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: TextField(
        controller: textController,
        decoration: InputDecoration(
          hintText: '输入关键字进行过滤...',
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () => textController.clear(),
              ),
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {},
              ),
            ],
          ),
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          isDense: true,
        ),
        onSubmitted: (value) {
          controller.addKeyword(value);
          textController.clear();
        },
      ),
    );
  }

  Widget _buildKeywordChips() {
    return Obx(() => Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Wrap(
        spacing: 8.0,
        children: controller.keywords.map((keyword) {
          return Chip(
            label: Text(keyword),
            onDeleted: () => controller.removeKeyword(keyword),
          );
        }).toList(),
      ),
    ));
  }

  Widget _buildLogList() {
    return Obx(() => ListView.builder(
      itemCount: controller.filteredLogLines.length,
      itemBuilder: (context, index) {
        final line = controller.filteredLogLines[index];
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Colors.grey.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Text(
            line,
            style: const TextStyle(
              fontFamily: 'monospace',
              fontSize: 12,
            ),
          ),
        );
      },
    ));
  }
}


import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:playlet/common/log/file/ff_log_file_writer.dart';

// 定义日志类型枚举
enum DebugLogType {
  all,
  trackEvent,
  http,
}

// 扩展枚举以提供显示名称和关键字
extension DebugLogTypeExtension on DebugLogType {
  String get displayName {
    switch (this) {
      case DebugLogType.all:
        return '全部';
      case DebugLogType.trackEvent:
        return '埋点';
      case DebugLogType.http:
        return 'HTTP';
    }
  }
  
  String get keyword {
    switch (this) {
      case DebugLogType.all:
        return '';
      case DebugLogType.trackEvent:
        return '[TrackEvent]';
      case DebugLogType.http:
        return '[HTTP]';
    }
  }
}

class DebugLogController extends GetxController {
  final RxList<String> logLines = <String>[].obs;
  final RxList<String> keywords = <String>[].obs;
  final RxList<String> filteredLogLines = <String>[].obs;
  final RxBool isReversed = false.obs;
  
  // 当前选择的日志类型
  final Rx<DebugLogType> selectedLogType = DebugLogType.all.obs;
  
  @override
  void onInit() {
    super.onInit();
    loadLogs();
  }

  Future<void> loadLogs() async {
    try {
      final logDir = await FFLogFileWriter.getLogDirectory();
      final files = await logDir.list().toList();
      final logFiles = files.whereType<File>()
          .where((f) => f.path.contains('log_'))
          .toList();
      
      final sortedFiles = FFLogFileWriter.sortLogFiles(logFiles);
      
      logLines.clear();
      for (var file in sortedFiles) {
        final contents = await file.readAsLines();
        logLines.addAll(contents);
      }
      filterLogs();
    } catch (e) {
      if (kDebugMode) {
        print('加载日志失败: $e');
      }
    }
  }

  void addKeyword(String keyword) {
    if (keyword.isNotEmpty && !keywords.contains(keyword)) {
      keywords.add(keyword);
      filterLogs();
    }
  }

  void removeKeyword(String keyword) {
    keywords.remove(keyword);
    filterLogs();
  }

  void toggleOrder() {
    isReversed.value = !isReversed.value;
    filterLogs();
  }

  // 修改选择日志类型的方法
  void selectLogType(DebugLogType type) {
    // 移除旧类型的关键字
    final oldKeyword = selectedLogType.value.keyword;
    if (oldKeyword.isNotEmpty) {
      keywords.remove(oldKeyword);
    }
    
    // 更新选中的类型
    selectedLogType.value = type;
    
    // 如果选择了特定类型，添加对应关键字
    final keyword = type.keyword;
    if (keyword.isNotEmpty && !keywords.contains(keyword)) {
      keywords.add(keyword);
    }
    
    filterLogs();
  }

  void filterLogs() {
    if (keywords.isEmpty) {
      filteredLogLines.value = List.from(logLines);
    } else {
      filteredLogLines.value = logLines.where((line) {
        return keywords.every((keyword) => 
          line.toLowerCase().contains(keyword.toLowerCase()));
      }).toList();
    }
    
    if (isReversed.value) {
      filteredLogLines.value = filteredLogLines.reversed.toList();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
import 'dart:ui';

import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/debug/log/debug_log_controller.dart';
import 'package:playlet/modules/debug/log/debug_log_page.dart';
import 'package:playlet/modules/debug/proxy/debug_proxy_controller.dart';
import 'package:playlet/modules/debug/proxy/debug_proxy_page.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/notification/android_notification_manager.dart';
import 'package:playlet/service/notification/task/CheckTask.dart';
import 'package:playlet/service/notification/task/FixedPushTask.dart';
import 'package:playlet/service/notification/task/PermanentTask.dart';

class DebugItem {
  final String title;
  final VoidCallback onTap;
  DebugItem(this.title, this.onTap);
}

class DebugController extends GetxController {
  final items = <DebugItem>[].obs;

  @override
  void onInit() {
    super.onInit();

    List<DebugItem> list = [
      DebugItem("接口地址: ${Uri.parse(Config.baseUrl).host}", () {}),
      DebugItem("设置代理", () {
        if (!Get.isRegistered<DebugProxyController>()) {
          Get.put(DebugProxyController());
        }
        Get.to(() => const DebugProxyPage());
      }),
      DebugItem("查看日志", () {
        if (!Get.isRegistered<DebugLogController>()) {
          Get.put(DebugLogController());
        }
        Get.to(() => const DebugLogPage());
      }),
      DebugItem("测试签到相关任务", () {
        CheckTask.instance.initTestTask();
        AndroidNotificationManager.instance
            .removeTaskStorage(CheckTask.checkInAlert);
        AndroidNotificationManager.instance
            .removeTaskStorage(CheckTask.missCheckIn);
        CheckTask.instance.initCheckTask();
      }),
      DebugItem("测试奖励即将过期任务", () {
        CheckTask.instance.initBonusExpiringTask();
        AndroidNotificationManager.instance
            .removeTaskStorage(CheckTask.missCheckIn);
        AndroidNotificationManager.instance
            .removeTaskStorage(CheckTask.expiringSoon);
        CheckTask.instance.initCheckTask();
      }),
      DebugItem("测试固定推送相关任务", () {
        FixedPushTask.instance.fixedPushPeriodicTime = 1 * 60;
        FixedPushTask.instance.init();
      }),
      DebugItem("测试常驻推送相关任务", () {
        PermanentTask.instance.permanentNotificationPeriodicTime = 1 * 60;
        PermanentTask.instance.init();
      }),
      DebugItem("测试归因剧进入详情页之后逻辑", () {
        AppNavigator.startDetailsPage(DetailsOptions(
          businessId: 728639,
          playerEpisodeIndex: 0, // 默认从第一集开始播放
          scene: EventValue.immersion, 
          from: EventValue.campaign, // 归因剧来源
        ));
      }),
      DebugItem("测试兜底剧进入详情页之后逻辑", () {
        AppNavigator.startDetailsPage(DetailsOptions(
          businessId: 728639,
          playerEpisodeIndex: 0, // 默认从第一集开始播放
          scene: EventValue.immersion,
          from: EventValue.defaultValue, // 兜底剧来源
        ));
      }),
    ];
    items.clear();
    items.addAll(list);
  }

  @override
  void onClose() {
    super.onClose();
    Get.delete<DebugProxyController>();
    Get.delete<DebugLogController>();
  }
}

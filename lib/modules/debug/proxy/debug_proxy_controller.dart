import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/utils/safe_storage.dart';

class DebugProxyController extends GetxController {
  final ipTextField = TextEditingController();
  final portTextField = TextEditingController();
  
  // 定义存储键名
  static const String _proxyIpKey = 'proxy_ip';
  static const String _proxyPortKey = 'proxy_port';
  static const String _proxyEnableKey = 'proxy_enable';
  static const String _proxyDefaultPort = '8888';
  static final _storage = SafeStorage();
  
  static RxBool isProxyEnabled = false.obs;

  static void loadProxyConfigFromCache() {
    final savedIp = _storage.read<String>(_proxyIpKey);
    final savedPort = _storage.read<String>(_proxyPortKey) ?? _proxyDefaultPort;
    final savedEnable = _storage.read<bool>(_proxyEnableKey) ?? false;

    if (savedEnable) {
      Config.proxyIp = savedIp;
      Config.proxyPort = savedPort;
    } else {
      Config.proxyIp = null;
      Config.proxyPort = _proxyDefaultPort;
    }
  }

  @override
  void onInit() {
    super.onInit();
    _loadProxySettings();
  }

  @override
  void onClose() {
    ipTextField.dispose();
    portTextField.dispose();
    super.onClose();
  }

  // 从本地加载代理设置
  void _loadProxySettings() {
    final savedIp = _storage.read<String>(_proxyIpKey);
    final savedPort = _storage.read<String>(_proxyPortKey);
    final savedEnable = _storage.read<bool>(_proxyEnableKey);
    
    ipTextField.text = savedIp ?? '';
    portTextField.text = savedPort ?? _proxyDefaultPort;
    isProxyEnabled.value = savedEnable ?? false;
    
    updateConfig();
  }

  void toggleProxy(bool value) {
    isProxyEnabled.value = value;
  }

  void updateConfig() {
    if (isProxyEnabled.value && ipTextField.text.isNotEmpty && portTextField.text.isNotEmpty) {
      Config.proxyIp = ipTextField.text;
      Config.proxyPort = portTextField.text;
    } else {
      Config.proxyIp = null;
      Config.proxyPort = _proxyDefaultPort;
    }
  }

  bool _isValidIpAddress(String ip) {
    if (ip.isEmpty) return false;
    final parts = ip.split('.');
    if (parts.length != 4) return false;
    return parts.every((part) {
      final n = int.tryParse(part);
      return n != null && n >= 0 && n <= 255;
    });
  }

  bool _isValidPort(String port) {
    final n = int.tryParse(port);
    return n != null && n > 0 && n <= 65535;
  }

  void saveProxy() {
    final ip = ipTextField.text;
    final port = portTextField.text;
    
    if (ip.isNotEmpty && !_isValidIpAddress(ip)) {
      Get.snackbar('错误', '无效的IP地址');
      return;
    }
    
    if (port.isNotEmpty && !_isValidPort(port)) {
      Get.snackbar('错误', '无效的端口号');
      return;
    }

    _storage.write(_proxyIpKey, ip);
    _storage.write(_proxyPortKey, port);
    _storage.write(_proxyEnableKey, isProxyEnabled.value);
    updateConfig();
    
    Get.back();
  }

  void resetProxy() {
    // 从本地移除配置
    _storage.remove(_proxyIpKey);
    _storage.remove(_proxyPortKey);
    _storage.remove(_proxyEnableKey);
    
    isProxyEnabled.value = false;
    updateConfig();
    
    ipTextField.clear();
    portTextField.text = _proxyDefaultPort;
  }
}
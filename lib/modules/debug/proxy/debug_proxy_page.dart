import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/modules/debug/proxy/debug_proxy_controller.dart';

class DebugProxyPage extends GetView<DebugProxyController> {
  const DebugProxyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置代理'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Obx(() => SwitchListTile(
              title: const Text('启用代理'),
              value: DebugProxyController.isProxyEnabled.value,
              onChanged: controller.toggleProxy,
            )),
            const SizedBox(height: 16),
            TextField(
              controller: controller.ipTextField,
              decoration: const InputDecoration(
                labelText: 'IP 地址',
                hintText: '请输入代理服务器 IP',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller.portTextField,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: '端口',
                hintText: '请输入代理服务器端口',
              ),
            ),
            const SizedBox(height: 32),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: controller.saveProxy,
                  child: const Text('保存', style: TextStyle(color: Colors.white)),
                ),
                ElevatedButton(
                  onPressed: controller.resetProxy,
                  child: const Text('重置', style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

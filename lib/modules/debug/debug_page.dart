

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/modules/debug/debug_controller.dart';

class DebugPage extends GetView<DebugController> {
  const DebugPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('调试'),
      ),
      body: Obx(
        () => ListView.builder(
          itemCount: controller.items.length,
          itemBuilder: (context, index) {
            final item = controller.items[index];
            return ListTile(
              title: Text(item.title),
              onTap: item.onTap,
            );
          },
        ),
      ),
    );
  }
}

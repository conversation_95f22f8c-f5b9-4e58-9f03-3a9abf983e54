import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/loading/ffloading.dart';
import 'package:playlet/components/nav_bar/ffnav_bar.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/subscription/subscription_controller.dart';
import 'package:playlet/modules/subscription/widgets/subscription_agreement.dart';
import 'package:playlet/modules/subscription/widgets/subscription_detail_body.dart';
import 'package:playlet/modules/subscription/widgets/subscription_list_body.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../common/event/event_value.dart';
import '../../common/event/track_event.dart';
import '../../utils/track_event.dart';

class SubscriptionPage extends GetView<SubscriptionController> {
  const SubscriptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        backgroundColor:
            controller.userService.userInfo.value?.isSubscription == true
                ? const Color(0xFF231C18)
                : const Color(0xFF000000),
        extendBodyBehindAppBar: true,
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return FFNavBar(
      title: AppTrans.subscription(),
      showBackIcon: true,
      alpha: 0.0,
      rightWidgets: [
        TextButton(
          onPressed: () {
            // 充值恢复按钮点击埋点
            useTrackEvent(TrackEvent.restore_click,
                extra: {TrackEvent.scene: EventValue.subscribe});
            FFLog.debug("点击补单按钮");
            Get.loading();
            controller.paymentService.restorePurchases();
          },
          child: Text(
            AppTrans.restore(),
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xFFE8E8E8),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    if (controller.loadingStatus.value == SubscriptionLoadingStatus.loading) {
      // 加载中
      return const Center(child: FFLoadingWidget());
    }
    if (controller.loadingStatus.value == SubscriptionLoadingStatus.failed) {
      // 加载失败
      return EmptyWidget(
        pageFrom: EmptyPageFrom.topup,
        type: EmptyType.noNetwork,
        onRefresh: () {
          controller.loadData();
        },
      );
    }

    return Stack(
      alignment: Alignment.topCenter,
      children: [
        controller.userService.userInfo.value?.isSubscription == true
            ? Assets.store.imgSubscriptionDetailBg.image(
                width: 393.w,
                fit: BoxFit.fitWidth,
              )
            : Assets.store.imgSubscriptionListBg.image(
                width: 393.w,
                fit: BoxFit.fitWidth,
              ),
        SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 17.w, vertical: 20.h),
            child: Column(
              children: [
                controller.userService.userInfo.value?.isSubscription == true &&
                        controller
                            .paymentService.subProductEd.value.skuId.isNotEmpty
                    ? Obx(
                        () => SubscriptionDetailBody(
                          currentSubscription:
                              controller.paymentService.subProductEd.value,
                          userInfo: controller.userService.userInfo.value!,
                          currency: controller.paymentService.currency.value,
                        ),
                      )
                    : Obx(
                        () => SubscriptionListBody(
                          userInfo: controller.userService.userInfo.value!,
                          result:
                              controller.paymentService.subProductResult.value,
                        ),
                      ),
                const SubscriptionAgreement(),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

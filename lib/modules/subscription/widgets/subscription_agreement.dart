import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:playlet/components/store/agreement_row_widget.dart';
import 'package:playlet/components/store/subscription_agreement_widget.dart';
import 'package:playlet/i18n/trans.dart';

import '../../../utils/index.dart';

class SubscriptionAgreement extends StatelessWidget {
  const SubscriptionAgreement({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 30.h),
        SubscriptionAgreementWidget(
          title: AppTrans.subscriptionAgreement(),
          content: AppTrans.subscriptionInfo(Platform.isIOS?"App Store":"Google Play"),
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 30.h),
        AgreementRow(
          onPrivacyAgreementTap: () {
            Utils.openPrivacyPolicy();
          },
          onUserAgreementTap: () {
            Utils.openUserAgreement();
          },
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/alert/remind.dart';
import 'package:playlet/components/store/subscribe_item.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/store/store_subscription_product_result.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../../common/event/event_value.dart';
import '../../../common/log/ff_log.dart';
import '../../../common/utils/screen_utils.dart';
import '../../../model/login.dart';
import '../../../service/payment/payment_model.dart';
import '../../../service/payment/payment_service.dart';

class SubscriptionListBody extends StatefulWidget {
  const SubscriptionListBody({
    super.key,
    required this.result,
    required this.userInfo,
  });

  final UserResponse userInfo;
  final StoreSubscriptionProductResult result;

  @override
  State<SubscriptionListBody> createState() => _SubscriptionListBodyState();
}

class _SubscriptionListBodyState extends State<SubscriptionListBody> {
  PaymentService paymentService = Get.find<PaymentService>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SubscriptionListUserInfoWidget(widget.userInfo),
        SizedBox(height: 30.h),
        ...?widget.result.productList?.map((product) {
          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: SubscribeItem(
              product: product,
              currency: paymentService.currency.value,
              onTap: () async {
                Get.loading();
                // 在这里添加点击事件的逻辑
                String skuId = product.skuId;
                String productId = product.productId.toString();
                FFLog.debug('发起订阅。skuId $skuId   productId $productId ');
                if (skuId.isNotEmpty && productId.isNotEmpty) {
                  var isSuccess = await paymentService.startSubscription(
                    skuId: skuId,
                    productId: productId,
                    source: SourceType.subscriptionAggregationPage,
                    amount: product.firstAmount,
                    strSource: EventValue.subscribe,
                    playDirection: ScreenUtils.isLandscape(Get.context!)
                        ? EventValue.horizontal
                        : EventValue.vertical,
                  );
                  if (isSuccess) {
                    //资产保护提醒弹窗
                    _handleProtectProperityRemindAlert();
                  }
                  Get.dismiss();
                  FFLog.debug("购买是否完成。$isSuccess");
                } else {
                  FFLog.error("订阅错误是否完成。$skuId $productId");
                }
              },
            ),
          );
        }),
      ],
    );
  }

  Future<void> _handleProtectProperityRemindAlert() async {
    //资产保护提醒弹窗
    if (checkShouldShowProtectProperityRemindAlert() == true) {
      bool? result = await showProtectProperityRemindAlert(inDetails: true);
      if (result != null && result == true) {
        await Get.loginDialog(
          from: EventValue.assetProtection,
          fbRewardsTrackFrom: EventValue.assetProtection,
        );
      }
    }
  }
}

class SubscriptionListUserInfoWidget extends StatelessWidget {
  final UserResponse userInfo;

  const SubscriptionListUserInfoWidget(this.userInfo, {super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildAvatar(),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                (userInfo.nickName == null || userInfo.nickName!.isEmpty)
                    ? AppTrans.guest()
                    : userInfo.nickName!,
                style: TextStyle(
                  fontSize: 16.0.sp,
                  fontWeight: FontWeight.w400,
                  color: Colors.white,
                ),
              ),
              _buildTotalMembershipTime(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 40.w,
      height: 40.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        border: Border.all(
          color: const Color(0xFF434343),
          width: 1.45.w,
        ),
      ),
      child: Assets.profile.avatar.image(),
    );
  }

  Widget _buildTotalMembershipTime() {
    return Text(
      AppTrans.totalMembership(),
      style: const TextStyle(
        fontSize: 11.0,
        fontWeight: FontWeight.w400,
        color: Color(0xFFD2D2D2),
      ),
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart' as intl;
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/model/store/store_subscription_product_result.dart';
import 'package:playlet/modules/profile/utils/subscription.dart';
import 'package:playlet/service/app_device_service.dart';

import '../../../common/event/event_value.dart';
import '../../../common/event/track_event.dart';
import '../../../utils/track_event.dart';

class SubscriptionDetailBody extends StatefulWidget {
  const SubscriptionDetailBody({
    super.key,
    required this.currentSubscription,
    required this.userInfo,
    required this.currency,
  });

  final SubProductList? currentSubscription;
  final UserResponse userInfo;
  final String currency;

  @override
  State<SubscriptionDetailBody> createState() => _SubscriptionDetailBodyState();
}

class _SubscriptionDetailBodyState extends State<SubscriptionDetailBody> {
  @override
  void initState() {
    // 已订阅
    useTrackEvent(TrackEvent.subscribe_thing_show, extra: {
      TrackEvent.amount: widget.currentSubscription?.payAmount??"", // 待补充 // 传本次创建订单金额，单位美金（单位不需要传，只传金额数字）
      TrackEvent.scene: EventValue.subscribe, // 沉浸页的待解锁弹窗：ads_coins 充值页：recharge 订阅详情页 subscrib
      TrackEvent.product_id: widget.currentSubscription?.productId.toString()??"", // 传入后台该位置的商品id
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SubscriptionDetailUserInfoWidget(
          avatar: widget.userInfo.headPic,
          name: widget.userInfo.nickName,
          subscriptionEndTime: widget.userInfo.subscriptionEndTime,
        ),
        SubscriptionDetailBaseInfoWidget(
          subscriptionType: widget.userInfo.subscriptionType,
        ),
        SubscriptionDetailInfoWidget(
          userInfo: widget.userInfo,
          currentSubscription: widget.currentSubscription,
          currency: widget.currency,
        ),
      ],
    );
  }
}

class SubscriptionDetailUserInfoWidget extends StatelessWidget {
  const SubscriptionDetailUserInfoWidget({
    super.key,
    required this.avatar,
    required this.name,
    required this.subscriptionEndTime,
  });

  final String? avatar;
  final String? name;
  final int? subscriptionEndTime;

  String _formatTime() {
    if (subscriptionEndTime == null) return '';
    DateTime date = DateTime.fromMillisecondsSinceEpoch(subscriptionEndTime!);
    final dateFormatter = intl.DateFormat(
      'yyyy-MM-dd',
      AppDeviceService.instance.getSystemLanguage(),
    );
    return dateFormatter.format(date);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildAvatar(),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            children: [
              Row(
                children: [
                  Flexible(child: _buildName()),
                  _buildVipIcon(),
                ],
              ),
              Row(
                children: [
                  _buildTotalMembershipTime(),
                  SizedBox(width: 8.w),
                  _buildTime(),
                  SizedBox(width: 8.w),
                  _buildMaturity(),
                  SizedBox(width: 8.w),
                  _buildTimeIcon(),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 40.w,
      height: 40.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        border: Border.all(
          color: const Color(0xFF434343),
          width: 1.45.w,
        ),
      ),
      child: avatar == null || avatar!.isEmpty
          ? Assets.profile.avatar.image()
          : CachedNetworkImage(
              imageUrl: avatar ?? '',
              placeholder: (context, url) => Assets.profile.avatar.image(),
              errorListener:(value) {
                FFLog.error(value, tag: "SubscribAvatarWidget");
              },
            ),
    );
  }

  Widget _buildName() {
    return Text(
      (name == null || name!.isEmpty) ? AppTrans.guest() : name!,
      style: const TextStyle(
        fontSize: 16.0,
        fontWeight: FontWeight.w400,
        color: Colors.white,
      ),
    );
  }

  Widget _buildVipIcon() {
    return Assets.profile.vip.image(
      width: 20.w,
      height: 20.w,
    );
  }

  Widget _buildTotalMembershipTime() {
    return Text(
      AppTrans.totalMembership(),
      style: const TextStyle(
        fontSize: 11.0,
        fontWeight: FontWeight.w400,
        color: Color(0xFFD2D2D2),
      ),
    );
  }

  Widget _buildTime() {
    return Text(
      _formatTime(),
      style: const TextStyle(
        fontSize: 11.0,
        fontWeight: FontWeight.w400,
        color: Color(0xFFF5E4CA),
      ),
    );
  }

  Widget _buildMaturity() {
    return Text(
      AppTrans.subscriptionMaturity(),
      style: const TextStyle(
        fontSize: 11.0,
        fontWeight: FontWeight.w400,
        color: Color(0xFFD2D2D2),
      ),
    );
  }

  Widget _buildTimeIcon() {
    return Assets.store.imgTimeWarning.image(
      width: 14.w,
      height: 14.w,
    );
  }
}

class SubscriptionDetailBaseInfoWidget extends StatelessWidget {
  const SubscriptionDetailBaseInfoWidget({
    super.key,
    required this.subscriptionType,
  });

  final int? subscriptionType;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildVipIcon(),
        SizedBox(width: 13.w),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTypeTitle(),
              _buildMyCurrentSubscriptions(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVipIcon() {
    return Assets.store.imgSubscriptionVipBig.image(
      width: 133.w,
      height: 133.w,
    );
  }

  Widget _buildTypeTitle() {
    return ShaderMask(
      shaderCallback: (rect) => const LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.center,
        colors: [
          Color(0xFFFF9937),
          Color(0xFFFBE840),
        ],
      ).createShader(rect),
      child: Text(
        SubscriptionUtils.getSubscriptionTypeDescription(subscriptionType),
        style: const TextStyle(
          fontSize: 19,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildMyCurrentSubscriptions() {
    return Text(
      AppTrans.subscriptionCurrent(),
      style: const TextStyle(
        fontSize: 12.0,
        fontWeight: FontWeight.w400,
        color: Color(0xFF999999),
      ),
    );
  }
}

class SubscriptionDetailInfoWidget extends StatelessWidget {
  const SubscriptionDetailInfoWidget({
    super.key,
    required this.userInfo,
    required this.currentSubscription,
    required this.currency,
  });

  final UserResponse userInfo;
  final SubProductList? currentSubscription;
  final String currency;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF5B422D),
              Color(0xFF231815),
            ],
          ),
          border: Border.all(
            color: const Color(0xFFFFFFFF).withValues(alpha: 0.2),
            width: 1.2.w,
          )),
      clipBehavior: Clip.antiAlias,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(16.0.w),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSubscriptionTypeTitle(),
                      SizedBox(height: 6.h),
                      _buildSubscriptionRenewalPrice(),
                      SizedBox(height: 6.h),
                      _buildSubscriptionUnlockRightsDesc(),
                      SizedBox(height: 6.h),
                      _buildSubscriptionOtherRightsDesc(),
                    ],
                  ),
                ),
                _buildSubscriptionTypeIcon(),
              ],
            ),
          ),
          _buildSubscriptionStateDesc(),
        ],
      ),
    );
  }

  Widget _buildSubscriptionTypeTitle() {
    return Text(
      SubscriptionUtils.getSubscriptionTypeDescription(
          currentSubscription?.type),
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Color(0xFFF2C181),
      ),
    );
  }

  Widget _buildSubscriptionRenewalPrice() {
    return Text(
      '${AppTrans.subscriptionRenewal()} $currency${currentSubscription?.payAmount}/${SubscriptionUtils.getSubscriptionTypeDescription(currentSubscription?.type)}',
      style: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: Color(0xFF999999),
      ),
    );
  }

  Widget _buildSubscriptionUnlockRightsDesc() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
      decoration: BoxDecoration(
        color: const Color(0xFF5F431B),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        '${AppTrans.unlockOne()} ${SubscriptionUtils.getSubscriptionTypeShortDescription(currentSubscription?.type)}',
        style: const TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w400,
          color: Color(0xFFF2C181),
        ),
      ),
    );
  }

  Widget _buildSubscriptionOtherRightsDesc() {
    return Text(
      AppTrans.autoRenew(),
      style: const TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w300,
        color: Color(0xFF999999),
      ),
    );
  }

  Widget _buildSubscriptionTypeIcon() {
    return currentSubscription?.type == 1
        ? Assets.store.imgSubscribeItemIcon7.image(
            width: 106.w,
            fit: BoxFit.fitWidth,
          )
        : currentSubscription?.type == 2
            ? Assets.store.imgSubscribeItemIcon31.image(
                width: 106.w,
                fit: BoxFit.fitWidth,
              )
            : currentSubscription?.type == 4
                ? Assets.store.imgSubscribeItemIcon365.image(
                    width: 106.w,
                    fit: BoxFit.fitWidth,
                  )
                : const SizedBox.shrink();
  }

  Widget _buildSubscriptionStateDesc() {
    return Container(
      width: double.infinity,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(vertical: 7.h),
      decoration: BoxDecoration(
        color: const Color(0xFF483824),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(18.r),
          bottomRight: Radius.circular(18.r),
        ),
      ),
      child: Text(
        AppTrans.subscribed(),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFFCBC3BA),
        ),
      ),
    );
  }
}

import 'package:get/get.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/service/payment/payment_events.dart';
import 'package:playlet/service/payment/payment_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/track_event.dart';

import '../../common/event/event_value.dart';
import '../../common/utils/screen_utils.dart';

enum SubscriptionLoadingStatus {
  loading,
  success,
  failed,
}

class SubscriptionController extends GetxController {
  final UserService userService = Get.find<UserService>();
  final PaymentService paymentService = Get.find<PaymentService>();

  late Rx<SubscriptionLoadingStatus> loadingStatus =
      SubscriptionLoadingStatus.loading.obs;

  @override
  void onInit() {
    var from = Get.arguments.toString();

    useTrackEvent(TrackEvent.subscribe_show, extra: {TrackEvent.from: from});

    var isImmersion = from == "immersion";

    var strScene = isImmersion ? "subscribe,coin_subscribe" : "subscribe";

    PaymentEvent.submitRechargeShow(
        strScene: strScene,
        reelId: "",
        episode: "",
        action: "other",
        lockBegin: "",
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );

    super.onInit();
  }

  @override
  void onReady() {
    loadData();

    if (userService.userInfo.value?.isSubscription == true) {
      var result = paymentService.subProductEd.value;
      PaymentEvent.submitSubscribeThingShow(
          result.firstAmount, 'subscribe', result.productId.toString());
    } else {
      paymentService.subProductResult.value.productList?.forEach((item) {
        PaymentEvent.submitSubscribeThingShow(
            item.firstAmount, 'subscribe', item.productId.toString());
      });
    }
    // 添加补单方法
    paymentService.startRecover(from: EventValue.subscribe);

    super.onReady();
  }

  @override
  void onClose() {
    PaymentEvent.submitRechargeShowEnd(
      strScene: 'subscribe',
      reelId: "",
      episode: "",
      action: "other",
      lockBegin: "",
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );

    super.onClose();
  }

  Future<void> loadData() async {
    if (paymentService.currency.value.isEmpty) {
      await paymentService.loadProducts();
    }

    await Get.find<UserService>().fetchUserInfo();
    if (userService.userInfo.value?.isSubscription == true) {
      loadingStatus.value = SubscriptionLoadingStatus.success;
    } else {
      loadingStatus.value = SubscriptionLoadingStatus.loading;
      await paymentService.getSubscriptionProductList();
      if (paymentService.subProductResult.value.productList != null) {
        loadingStatus.value = SubscriptionLoadingStatus.success;
      } else {
        loadingStatus.value = SubscriptionLoadingStatus.failed;
      }
    }
  }
}

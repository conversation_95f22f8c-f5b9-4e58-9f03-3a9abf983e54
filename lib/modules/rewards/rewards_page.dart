import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/rewards/widget/bottom_task_widget.dart';
import 'package:playlet/modules/rewards/widget/top_card_widget.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../components/empty/empty.dart';
import '../../components/loading/ffloading.dart';
import '../../components/nav_bar/ffnav_bar.dart';
import 'rewards_controller.dart';

class RewardsPage extends GetView<RewardsController> {
  const RewardsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: controller.onPopInvokedWithResult,
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: FFNavBar(
          showBackIcon: true,
          title: AppTrans.rewards(),
          onBackPressed: controller.onBackPressed,
        ),
        body: Obx(() {
          if (controller.loadingStatus.value == LoadingStatus.loading) {
            // 加载中
            return const FFLoadingWidget();
          }
          if (controller.loadingStatus.value == LoadingStatus.failed) {
            // 加载失败
            return EmptyWidget(
              type: EmptyType.noNetwork,
              onRefresh: () async {
                Get.loading();
                await controller.initData();
                Get.dismiss();
              },
              pageFrom: EmptyPageFrom.rewards,
            );
          }

          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(left: 10.w, right: 10.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TopCardWidget(controller.checkInList.value),
                  controller.isShowNewUser.value
                      ? Column(
                          children: buildTaskWidget(controller
                              .appTaskList.value.taskModuleResponseList),
                        )
                      : const SizedBox(),
                  SizedBox(
                    height: 16.h,
                  ),
                  controller.taskAdListResult.value.adBonusResponses?.isEmpty ==
                          true
                      ? const SizedBox()
                      : ADTaskWidget(AppTrans.dailyTasks()),
                  SizedBox(
                    height: 20.h,
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}

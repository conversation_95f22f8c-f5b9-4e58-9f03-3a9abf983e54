import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/alert/index.dart';
import 'package:playlet/components/alert/rewards_checkin_alert.dart';
import 'package:playlet/components/rewards/rewards_item.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/rewards/check_in_list_result.dart';
import 'package:playlet/modules/rewards/event/rewards_event.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/ad/ad_track_event.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/notificaiton_dialog_service.dart';
import 'package:slide_countdown/slide_countdown.dart';

import '../../../gen/assets.gen.dart';
import '../rewards_controller.dart';

class TopCardWidget extends GetView<RewardsController> {
  const TopCardWidget(CheckInListResult checkInList, {super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        children: [_buildTopBonus(), _buildSignList()],
      );
    });
  }

  Transform _buildSignList() {
    return Transform.translate(
      offset: const Offset(0, -20),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 9.w, right: 9.w, top: 12.h),
          child: Column(
            mainAxisSize: MainAxisSize.min, // 关键点

            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppTrans.dailyCheckIn(),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 16.h),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: List.generate(7, (index) {
                    return Padding(
                      padding: EdgeInsets.only(right: 6.w),
                      child: SizedBox(
                        width: 45.w,
                        child: RewardsItemWidget(
                          controller.checkInList.value.signRecords?[index],
                        ),
                      ),
                    );
                  }),
                ),
              ),
              SizedBox(height: 18.h),
              _buildConfirmButton(
                  controller.checkInList.value.nextSignTime ?? 0),
              SizedBox(height: 16.h),
            ],
          ),
        ),
      ),
    );
  }

  Row _buildTopBonus() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Assets.imgCoinB.image(width: 28.r, height: 28.r),
              SizedBox(width: 8.w),
              Text(
                AppTrans.myBonus(),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              SizedBox(width: 10.w),
              Text(
                "${controller.paymentService.userBalance.value.bonus}",
                style: TextStyle(
                  color: const Color(0xFFFFCD00),
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Assets.rewards.imgRewardsTop
            .image(height: 120.h, fit: BoxFit.fitHeight),
      ],
    );
  }

  Widget _buildConfirmButton(int nextSignTime) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Opacity(
          opacity: controller.timeEnd.value.inSeconds == 0 ? 1 : 0.5,
          child: GestureDetector(
            onTap: () async {
              if (controller.timeEnd.value.inSeconds == 0) {
                // 点击直接签到
                controller.checkIn();

                FFLog.debug('双倍弹框');
                if (!(controller.checkInList.value.clickDoubleAdToday ??
                    false)) {
                  FFLog.debug('双倍弹框');
                  AdTrackEvent.adPlacementShow(
                      AdType.reward, AdScene.checkInDouble);

                  RewardsEvent.submitCheckInDoubleRewardShow(
                      from: controller.from);

                  FFAlert.show(
                    onDismiss: () async {
                      /// 检查并弹出通知弹窗
                      NotificationLoginService notificationLoginService =
                          Get.find<NotificationLoginService>();
                      await notificationLoginService
                          .checkAndShowNotificationAlert(1);
                    },
                    container: _buildRewardsCheckInAlert(),
                  );
                } else {
                  /// 检查并弹出通知弹窗
                  NotificationLoginService notificationLoginService =
                      Get.find<NotificationLoginService>();
                  await notificationLoginService
                      .checkAndShowNotificationAlert(1);
                }

                // controller.checkIn();
              } else {
                FFLog.debug("已经签到过了");
              }
            },
            child: Container(
              width: 280.w,
              height: 46.sp,
              decoration: BoxDecoration(
                color: const Color(0xFFFF4500),
                borderRadius: BorderRadius.circular(51.r),
              ),
              child: Center(
                child: controller.timeEnd.value.inSeconds == 0
                    ? Text(
                        AppTrans.checkIn(),
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            AppTrans.nextCheckIn(),
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          Obx(() {
                            return SlideCountdown(
                              decoration: const BoxDecoration(
                                color: Colors.transparent,
                              ),
                              duration: controller.timeEnd.value,
                            );
                          }),
                        ],
                      ),
              ),
            ),
          ),
        )
      ],
    );
  }

  RewardsCheckinAlert _buildRewardsCheckInAlert() {
    return RewardsCheckinAlert(
      bonus: controller.todaySignRecords.value?.bonus ?? 0,
      onNowLook: () async {
        // 这里请求广告
        // success,代表看完了广告,得到了奖励

        RewardsEvent.submitCheckInDoubleRewardClick(from: controller.from);

        bool isSuccess =
            await AdManager().showFullScreenAd(AdScene.checkInDouble);
        if (isSuccess) {
          var isReceiver = await controller.receiveADDoubleRewards();
          controller.receiveADSuccess(isReceiver);
        }
      },
    );
  }
}

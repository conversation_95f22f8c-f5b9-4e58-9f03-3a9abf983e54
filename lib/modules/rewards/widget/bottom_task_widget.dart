import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/rewards/task_item.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/rewards/task_list_result.dart';

import '../../../model/rewards/task_ad_list_result.dart';
import '../../../model/rewards/task_type_model.dart';
import '../../../utils/rewards_strong.dart';
import '../rewards_controller.dart';

List<BottomTaskWidget> buildTaskWidget(
    List<TaskModuleResponseList>? taskModuleResponseList) {
  // 1-fb绑定 2-email绑定 3-手机绑定 4-获取奖励通知

  if (taskModuleResponseList == null) {
    return [];
  }
  return taskModuleResponseList.map((task) {
    return BottomTaskWidget(
      task.modelName ?? '', // 假设 TaskModuleResponseList 有 title 属性
      task.appTaskReponseList ?? [],
    );
  }).toList();
}

Widget buildADTaskWidget(List<AdBonusResponses>? adBonusResponseList) {
  if (adBonusResponseList == null) {
    return const SizedBox();
  }
  return ADTaskWidget(
    AppTrans.dailyTasks(), // 假设 TaskModuleResponseList 有 title 属性
  );
}

class BottomTaskWidget extends GetView<RewardsController> {
  final String title;
  final List<AppTaskReponseList> appTaskList;

  const BottomTaskWidget(this.title, this.appTaskList, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: const Color(0xFF1A1A1A),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(14.r),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.r),
                topRight: Radius.circular(10.r),
              ),
              color: const Color(0xFF2B2B2B),
            ),
            child: Row(
              children: [
                Text(
                  title,
                  style:
                      TextStyle(fontWeight: FontWeight.w600, fontSize: 16.sp),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          ListView.builder(
            itemCount: appTaskList.length,
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              var task = appTaskList[index];

              //    通知任务使用本地状态
              var status = (task.taskType == TaskType.getRewardNotification)
                  ? controller.notificationState.value
                  : task.status;
              return TaskNewUserItem(
                bonusText: '+${task.taskBonus}',
                progressText: "",
                imagePath: getImageByTaskType(task.taskType),
                descriptionText: task.taskName ?? "",
                initialState: status ?? TaskStatus.start,
                showLine: task != appTaskList.last,
                onButtonPressed: () async {
                  controller.onTaskButtonClick(task);
                },
              );
            },
          )
        ],
      ),
    );
  }

  getImageByTaskType(int? taskType) {
    switch (taskType) {
      case TaskType.fbBinding:
        return Assets.rewards.imgTaskListFacebook.path;
      case TaskType.emailBinding:
        return Assets.rewards.imgTaskListEmail.path;
      case TaskType.phoneBinding:
        return Assets.rewards.imgTaskListPhone.path;
      case TaskType.getRewardNotification:
        return Assets.rewards.imgTaskListNotification.path;
    }
  }
}

class ADTaskWidget extends GetView<RewardsController> {
  final String title;

  const ADTaskWidget(this.title, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: const Color(0xFF1A1A1A),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(14.r),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.r),
                topRight: Radius.circular(10.r),
              ),
              color: const Color(0xFF2B2B2B),
            ),
            child: Row(
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16.sp,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          ListView.builder(
            itemCount:
                controller.taskAdListResult.value.adBonusResponses?.length ?? 0,
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final task =
                  controller.taskAdListResult.value.adBonusResponses![index];
              var status = RewardsAdStrong.getAdTaskStatus(
                task.id.toString(),
              );
              return ADTaskItem(
                bonusText: '+${task.bonus}',
                progressText: "",
                initialState: status,
                showLine: index < (controller.taskAdListResult.value.adBonusResponses?.length ?? 0) - 1,
                onButtonPressed: () {
                  controller.onAdItemClick(task);
                },
              );
            },
          )
        ],
      ),
    );
  }
}

import '../../../common/event/track_event.dart';
import '../../../utils/track_event.dart';

class RewardsEvent {
  /// 福利中心页面展示 奖励页面展示成功时上报，记录前向来源
  ///
  /// [scene] 福利中心页面展示的前向来源场景，可选值如 "首页：discover"、"我的页面 - 奖励：profile_reward" 等
  static submitRewardShow({
    required String scene,
  }) {
    useTrackEvent(TrackEvent.rewardShow, extra: {
      TrackEvent.scene: scene,
    });
  }

  /// 完成每日签到
  ///
  /// [from] 完成每日签到的前向来源，可选值如 "首页：discover"、"我的页面 - 奖励：profile_reward" 等
  /// [bonus] 完成签到所获得的奖励数量，以字符串形式表示
  /// [day] 表示当前签到是本轮的第几天，范围为 1 - 7，以字符串形式表示
  static submitCheckInClick({
    required String from,
    required String bonus,
    required String day,
  }) {
    useTrackEvent(TrackEvent.checkInClick, extra: {
      TrackEvent.from: from,
      TrackEvent.bonus: bonus,
      TrackEvent.day: day,
    });
  }

  /// 翻倍弹窗展示
  ///
  /// [from] 翻倍弹窗展示的前向来源，可选值如 "首页：discover"、"我的页面 - 奖励：profile_reward" 等
  static submitCheckInDoubleRewardShow({
    required String from,
  }) {
    useTrackEvent(TrackEvent.checkInDoubleRewardShow, extra: {
      TrackEvent.from: from,
    });
  }

  /// 翻倍弹窗点击观看
  ///
  /// [from] 翻倍弹窗点击观看操作的前向来源，可选值如 "首页：discover"、"我的页面 - 奖励：profile_reward" 等
  static submitCheckInDoubleRewardClick({
    required String from,
  }) {
    useTrackEvent(TrackEvent.checkInDoubleRewardClick, extra: {
      TrackEvent.from: from,
    });
  }

  /// 做任务
  ///
  /// [from] 点击任务的前向来源，可选值如 "首页：discover"、"我的页面 - 奖励：profile_reward" 等
  /// [type] 任务所属的模块，可选值如 "新手任务：newbie_task"、"每日任务:daily_task"
  /// [taskName] 具体的任务名称，可选值如 "通知权限：notification"、"Facebook 账号登录：fb_login" 等
  static submitTaskClick({
    required String from,
    required String type,
    required String taskName,
  }) {
    useTrackEvent(TrackEvent.taskClick, extra: {
      TrackEvent.from: from,
      TrackEvent.type: type,
      TrackEvent.taskName: taskName,
    });
  }

  /// 任务完成
  ///
  /// [from] 任务完成的前向来源，可选值如 "首页：discover"、"我的页面 - 奖励：profile_reward" 等
  /// [type] 任务所属的模块，可选值如 "新手任务：newbie_task"、"每日任务:daily_task"
  /// [taskName] 具体的任务名称，可选值如 "通知权限：notification"、"Facebook 账号登录：fb_login" 等
  /// [bonus] 完成任务所获得的奖励数量，以字符串形式表示
  static submitTaskFinish({
    required String from,
    required String type,
    required String taskName,
    required String bonus,
  }) {
    useTrackEvent(TrackEvent.taskFinish, extra: {
      TrackEvent.from: from,
      TrackEvent.type: type,
      TrackEvent.taskName: taskName,
      TrackEvent.bonus: bonus,
    });
  }

  /// 首页右上角礼包点击
  static submitHomeRewardClick() {
    useTrackEvent(TrackEvent.homeRewardClick);
  }

  /// 福利中心入口点击
  ///
  /// [scene] 点击福利中心入口的场景，可选值如 "首页：discover"、"我的页面 - 奖励：profile_reward" 等
  static submitRewardClick({
    required String scene,
  }) {
    useTrackEvent(TrackEvent.rewardClick, extra: {
      TrackEvent.scene: scene,
    });
  }
}
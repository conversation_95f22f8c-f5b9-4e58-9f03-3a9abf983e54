import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/rewards/ad_task_receive_result.dart';
import 'package:playlet/service/ad/ad_track_event.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/adtrigger/ad_trigger_eventcollector.dart';
import 'package:playlet/service/good_review_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../api/rewards_api.dart';
import '../../common/event/event_value.dart';
import '../../model/rewards/check_in_list_result.dart';
import '../../model/rewards/task_ad_list_result.dart';
import '../../model/rewards/task_list_result.dart';
import '../../model/rewards/task_type_model.dart';
import '../../model/rewards_bind/bind_options.dart';
import '../../routers/app_navigator.dart';
import '../../service/ad/ad_manager.dart';
import '../../service/notificaiton_dialog_service.dart';
import '../../service/payment/payment_service.dart';
import '../../utils/auth.dart';
import '../../utils/rewards_strong.dart';
import 'event/rewards_event.dart';

enum LoadingStatus {
  loading,
  success,
  failed,
}

class RewardsController extends GetxController with WidgetsBindingObserver {
  final String notificationKey = "notificationKey";

  Rx<Duration> timeEnd = Duration.zero.obs;
  Rx<CheckInListResult> checkInList = CheckInListResult().obs;

  Rx<TaskListResult> appTaskList = TaskListResult().obs;
  Rx<TaskAdListResult> taskAdListResult = TaskAdListResult().obs;

  final PaymentService paymentService = Get.find<PaymentService>();
  final GoodReviewService goodReviewService = Get.find<GoodReviewService>();

  var notificationState = TaskStatus.start.obs;

  var isShowNewUser = false.obs;
  Rxn<SignRecords> todaySignRecords = Rxn<SignRecords>();

  late Rx<LoadingStatus> loadingStatus = LoadingStatus.loading.obs;
  var from = "";

  bool isTaskSuc = false;

  @override
  void onInit() {
    from = Get.arguments;
    RewardsEvent.submitRewardShow(scene: from);

    paymentService.refreshUserBalance();
    initData();
    AdTrackEvent.adPlacementShow(AdType.reward, AdScene.dailyReward);
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      FFLog.debug("返回前台 resumed");
      if (notificationState.value != TaskStatus.complete) {
        Get.loading();
        await checkNotification();
        Get.dismiss();
      }
    }
  }

  @override
  void onClose() async {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  Future<void> initData() async {
    await checkNotification();
    await getTask();
    await getADTask();
    await getRewards();
    notificationState.value = RewardsAdStrong.getAdTaskStatus(notificationKey);
    if (checkInList.value.signRecords?.isNotEmpty == true) {
      loadingStatus.value = LoadingStatus.success;
    } else {
      loadingStatus.value = LoadingStatus.failed;
    }
  }

  Future<void> getRewards() async {
    var result = await ApiRewards.getSignRecord();
    if (result != null) {
      checkInList.value = result;
      try {
        if (checkInList.value.signRecords?.isNotEmpty == true) {
          checkInList.value.signRecords?.forEach((item) {
            if (item.isToday == true) {
              todaySignRecords.value = item;
            }
          });
        }
      } catch (e) {
        FFLog.debug("本地金额错误");
      }
      getTimeEnd(checkInList.value.nextSignTime);
    }
  }

  Future<void> getTask() async {
    var result = await ApiRewards.getAppTaskList();

    if (result != null) {
      appTaskList.value = result;

      isShowNewUser.value = appTaskList.value.taskModuleResponseList?.first
              .appTaskReponseList?.isNotEmpty ==
          true;
    }
  }

  void getTimeEnd(int? nextSignTime) {
    timeEnd.value = Duration(seconds: nextSignTime ?? 0);
  }

  Future<void> checkIn() async {
    Get.loading();
    var result = await ApiRewards.checkIn();
    if (result != null) {
      // 签到成功之后刷新用户余额
      paymentService.refreshUserBalance();
      // 刷新可获得金币总数
      Get.find<UserService>().fetchRewardsTotalBonus();

      getTimeEnd(result.nextSignTime);
      if (todaySignRecords.value != null) {
        RewardsEvent.submitCheckInClick(
            from: from,
            bonus: todaySignRecords.value!.bonus.toString(),
            day: todaySignRecords.value!.day.toString());
      }
      // 通知实时活动签到成功
      Get.find<UserService>().signInStatus.value = SignInStatus.signed;
      await getRewards();
    }

    Get.dismiss();
  }

  // 获取签到双倍奖励
  Future<bool> receiveADDoubleRewards() async {
    var result = await ApiRewards.receiveDoubleCheckInRewards();
    if (result?.bonus != null) {
      Get.toast(AppTrans.notificationCustomReceived(result?.bonus.toString()));
    }

    return result != null;
  }

  void receiveADSuccess(bool isReceiver) async {
    if (isReceiver) {
      Get.loading();
      paymentService.refreshUserBalance();
      await initData();
      Get.find<UserService>().fetchRewardsTotalBonus();
      Get.dismiss();

      /// 检查并弹出通知弹窗
      NotificationLoginService notificationLoginService =
          Get.find<NotificationLoginService>();
      await notificationLoginService.checkAndShowNotificationAlert(1);
    }
  }

  Future<AdTaskReceiveResult?> receiveADCompleteRewards(int adId) async {
    var result = await ApiRewards.receiveADCompleteRewards(adId);
    if (result != null) {
      // 刷新可获得金币总数
      Get.find<UserService>().fetchRewardsTotalBonus();
      Get.toast(
          AppTrans.notificationCustomReceived(result.bounsNum.toString()));
    }
    return result;
  }

  Future<void> getADTask() async {
    var result = await ApiRewards.getADTaskList();

    var gotGoButton = false;

    if (result != null && result.adBonusResponses != null) {
      taskAdListResult.value = result;
      for (int i = 0; i < result.adBonusResponses!.length; i++) {
        var item = result.adBonusResponses![i];
        var status = RewardsAdStrong.getAdTaskStatus(item.id.toString());

        if (status != TaskStatus.receive) {
          String taskID = item.id.toString();
          int status =
              item.watched ?? false ? TaskStatus.complete : TaskStatus.start;
          // 需要可点的GO
          if (TaskStatus.start == status) {
            // 之前显示过GO,标记不点的GO
            if (gotGoButton) {
              RewardsAdStrong.setAdTaskStatus(taskID, TaskStatus.noUnLock);
            } else {
              RewardsAdStrong.setAdTaskStatus(taskID, TaskStatus.start);
              // 标记条要显示GO并可点
              gotGoButton = true;
            }
          } else {
            RewardsAdStrong.setAdTaskStatus(taskID, status);
          }
        }
      }
    }
  }

  Future<void> checkNotification() async {
    if (await checkNotificationIsisGranted()) {
      FFLog.debug("checkNotification 检查通过");

      RewardsAdStrong.setAdTaskStatus(notificationKey, TaskStatus.receive);
      notificationState.value = TaskStatus.receive;
      getTask();
    } else {
      FFLog.debug("checkNotification检查不通过");
      RewardsAdStrong.setAdTaskStatus(notificationKey, TaskStatus.start);
      notificationState.value = TaskStatus.start;
    }
  }

  Future<bool> checkNotificationIsisGranted() async {
    // 检查通知权限
    PermissionStatus status = await Permission.notification.status;
    return status.isGranted;
  }

  requestNotification() async {
    var status = await Permission.notification.request();
    if (status.isGranted) {
      notificationState.value = TaskStatus.receive;
      RewardsAdStrong.setAdTaskStatus(notificationKey, TaskStatus.receive);
      // getTask();
    } else {
      await openAppSettings();
    }
  }

  receiveTaskRewards(int id, int taskType) async {
    Get.loading();
    var result = await ApiRewards.receiveRewards(id);
    if (result != null) {
      // 新手任务领取成功
      isTaskSuc = true;
      Get.toast(
          AppTrans.notificationCustomReceived(result.taskBonus.toString()));

      getTask();
      paymentService.refreshUserBalance();

      // 刷新可获得金币总数
      Get.find<UserService>().fetchRewardsTotalBonus();

      switch (taskType) {
        // 通知任务直接领取奖励 不需要领奖 需要移动到登录成功之后
        // case TaskType.fbBinding:
        //   RewardsEvent.submitTaskFinish(from: from, type: TrackEvent.newbieTask, taskName: TrackEvent.fbLogin, bonus: result.taskBonus.toString());
        //   break;
        case TaskType.emailBinding:
          RewardsEvent.submitTaskFinish(
              from: from,
              type: EventValue.newbieTask,
              taskName: EventValue.provideEmail,
              bonus: result.taskBonus.toString());
          break;
        case TaskType.phoneBinding:
          RewardsEvent.submitTaskFinish(
              from: from,
              type: EventValue.newbieTask,
              taskName: EventValue.providePhone,
              bonus: result.taskBonus.toString());
          break;
        case TaskType.getRewardNotification:
          RewardsEvent.submitTaskFinish(
              from: from,
              type: EventValue.newbieTask,
              taskName: EventValue.notification,
              bonus: result.taskBonus.toString());
          break;
      }
    }
    Get.dismiss();
  }

  Future<void> onAdItemClick(AdBonusResponses task) async {
    var status = RewardsAdStrong.getAdTaskStatus(
      task.id.toString(),
    );

    if (status == TaskStatus.start) {
      RewardsEvent.submitTaskClick(
          from: from,
          type: EventValue.dailyTask,
          taskName: EventValue.watchRewardAds);
      // 这里请求广告
      // success,代表看完了广告,得到了奖励,允许去调用观看完接口
      bool isSuccess = await AdManager()
          .showFullScreenAd(AdScene.dailyReward, forceShowAd: true);

      if (isSuccess) {
        // 统计收集看完激励视频
        AdTriggerEventCollector.shared.collect(Event.watchRewardAdList);

        // TODO: 然后刷新当前任务信息,(提示得到奖励)
        // 面板关闭
        FFLog.debug("广告播放完毕");
        Get.loading();
        await RewardsAdStrong.setAdTaskStatus(
            task.id.toString(), TaskStatus.receive);
        await getADTask();
        Get.dismiss();
      }
    } else if (status == TaskStatus.receive) {
      if (task.id != null) {
        Get.loading();
        // 提示已经完成了
        var adTaskReceiveResult = await receiveADCompleteRewards(task.id!);
        if (adTaskReceiveResult != null) {
          // Ad任务领取成功
          isTaskSuc = true;
          FFLog.debug("领取成功  ${task.id.toString()}");
          RewardsAdStrong.setAdTaskStatus(
              task.id.toString(), TaskStatus.complete);

          RewardsEvent.submitTaskFinish(
              from: from,
              type: EventValue.dailyTask,
              taskName: EventValue.watchRewardAds,
              bonus: adTaskReceiveResult.bounsNum.toString());
          paymentService.refreshUserBalance();
          // 刷新可获得金币总数
          Get.find<UserService>().fetchRewardsTotalBonus();
          await getADTask();
        } else {
          FFLog.error("领取失败");
        }
      } else {
        FFLog.error("task id  为空");
      }
    } else {
      FFLog.debug("任务完成");
    }
    Get.dismiss();
  }

  Future<void> onTaskButtonClick(AppTaskReponseList task) async {
    if (task.taskType == null) {
      return;
    }

    switch (task.taskType) {
      case TaskType.fbBinding:
        //TODO facebook 只需要发起 不需要领奖 埋点问题
        RewardsEvent.submitTaskClick(
            from: from,
            type: EventValue.newbieTask,
            taskName: EventValue.fbLogin);

        var isSuccess = await Auth.signInWithFacebook(
          EventValue.task_facebook_login,
          fbRewardsTrackFrom: EventValue.rewardFbTask,
        );
        FFLog.info("fb 登录结果。$isSuccess");

        if (isSuccess) {
          Get.loading();
          await getTask();
          await paymentService.refreshUserBalance();
          // 刷新可获得金币总数
          await Get.find<UserService>().fetchRewardsTotalBonus();
          Get.dismiss();
        }

        break;

      case TaskType.emailBinding:
        if (task.status == TaskStatus.start) {
          RewardsEvent.submitTaskClick(
              from: from,
              type: EventValue.newbieTask,
              taskName: EventValue.provideEmail);
          AppNavigator.startRewardsBindPage(BindOptionsModel(
            type: RewardsBindType.Email,
            sourceBindType: SourceBindType.Rewards,
          ));
        } else if (task.status == TaskStatus.receive && task.id != null) {
          receiveTaskRewards(task.id!, task.taskType!);
        } else {
          FFLog.error("邮箱绑定未知状态");
        }

        break;

      case TaskType.phoneBinding:
        if (task.status == TaskStatus.start) {
          RewardsEvent.submitTaskClick(
              from: from,
              type: EventValue.newbieTask,
              taskName: EventValue.providePhone);
          AppNavigator.startRewardsBindPage(BindOptionsModel(
            type: RewardsBindType.Phone,
            sourceBindType: SourceBindType.Rewards,
          ));
        } else if (task.status == TaskStatus.receive && task.id != null) {
          receiveTaskRewards(task.id!, task.taskType!);
        } else {
          FFLog.error("绑定手机位置状体");
        }

        break;

      case TaskType.getRewardNotification:
        if (notificationState.value == TaskStatus.start) {
          RewardsEvent.submitTaskClick(
              from: from,
              type: EventValue.newbieTask,
              taskName: EventValue.notification);
          requestNotification();
        } else if (notificationState.value == TaskStatus.receive &&
            task.id != null) {
          receiveTaskRewards(task.id!, task.taskType!);
        } else {
          FFLog.error("通知任务未知状态");
        }
        break;
    }
  }

  onBackPressed() async {
    if (isTaskSuc) {
      await goodReviewService.onShow();
    }
    Get.back();
  }

  onPopInvokedWithResult(didPop, _) async {
    if (didPop == true) {
      return;
    }
    if (isTaskSuc) {
      await goodReviewService.onShow();
    }
    Get.back();
  }
}

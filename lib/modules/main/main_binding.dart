import 'package:get/get.dart';
import 'package:playlet/common/controller/bottom_nav_controller.dart';
import 'package:playlet/modules/home/<USER>';
import 'package:playlet/modules/mylist/mylist_controller.dart';
import 'package:playlet/modules/profile/profile_controller.dart';
import 'package:playlet/modules/reels/reels_controller.dart';
import 'main_controller.dart';

class MainBinding implements Bindings {
  @override
  void dependencies() {
    // 三个全局的controller 不会被销毁
    Get.put<BottomNavController>(BottomNavController());
    Get.put<MainController>(MainController());
    Get.put<HomeController>(HomeController());

    // 其他页面懒加载,fenix: true, 当被销毁后,再次 find 时, 会重新创建
    Get.lazyPut<ReelsController>(() => ReelsController(), fenix: true);
    Get.lazyPut<MylistController>(() => MylistController(), fenix: true);
    Get.lazyPut<ProfileController>(() => ProfileController(), fenix: true);
  }
}

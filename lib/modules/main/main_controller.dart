import 'dart:async';
import 'dart:io';

import 'package:dlink_analytics/analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:playlet/api/base.dart';
import 'package:playlet/api/upgrade.dart';
import 'package:playlet/common/controller/bottom_nav_controller.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/alert/meta.dart';
import 'package:playlet/model/main.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain_manager.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/analytics_service.dart';
import 'package:playlet/service/attribute_service.dart';
import 'package:playlet/service/good_review_service.dart';
import 'package:playlet/service/live_activity/live_activity_service.dart';
import 'package:playlet/service/notification/android_notification_manager.dart';
import 'package:playlet/service/notification/firebase_message_manager.dart';
import 'package:playlet/service/notification/notification_service.dart';
import 'package:playlet/service/payment/payment_service.dart';
import 'package:playlet/utils/app_analysis.dart';
import 'package:playlet/utils/app_usage_statistics.dart';
import 'package:playlet/utils/notification_util.dart';
import 'package:playlet/utils/track_event.dart';

class MainController extends GetxController {
  final BottomNavController bottomNavController =
      Get.find<BottomNavController>();

  final GoodReviewService goodReviewService = Get.find<GoodReviewService>();
  MainOptions? options;


  @override
  Future<void> onInit() async {
    await initLogin();
    super.onInit();
    options = Get.arguments;
    AppUsageStatistics.onForeground();
    NotificationUtil.onForeground();
    initAttribute();
  }

  @override
  void onReady() {
    super.onReady();
    checkNotificationGoPage();
    checkChagePage();
  }

  /// 切换tab
  void checkChagePage(){
    var selectIndex = options?.selectIndex;
    if(selectIndex !=null){
      bottomNavController.changePage(selectIndex);
    }
  }

  void onHomePageLoaded() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await ApiUpgrade.getUpgradeVersionManageInfo();
      Get.find<NotificationService>().requestNotificationPermission();

      // 初始化实时活动
      LiveActivityService liveActivityService = Get.find<LiveActivityService>();
      liveActivityService.init();
      // 允许跳转
      AppNavigator.canNavigate = true;
    });
    // 进入首页立即上报
    flushDataOnPageChange();
    
  }

  Future<void> onHomePageShowed() async {
    /// 1.添加
    await showMetaLoginAlert();
    await goodReviewService.onHomeShow();
    /// 2.展示
    DialogChainManager.instance.startOpportunityChain();
  }

  Future<void> initLogin() async {
    await ApiBase.initLogin();
    // 初始化商品本地化
    Get.find<PaymentService>().initInAppPurchase();
    // firebase token上传
    FirebaseMessageManager.instance.tokenUploadConfigInit();
    AppAnalysisStore.doSomethingIfUpdateFromOldVersion();
    FlutterNativeSplash.remove();
    // 初始化android通知栏任务
    initAndroidNotificationManager();
  }

  void initAttribute() async {
    await Get.find<AttributeService>().init();
  }

  // 检查是否有通知跳转
  void checkNotificationGoPage() {
    Get.find<NotificationService>().checkGoToDetail();
  }

  void initAndroidNotificationManager() {
    AndroidNotificationManager.instance.init();
  }

  onPopInvokedWithResult(didPop, _) async {
    if (didPop == true) {
      return;
    }
    // 安卓使用原生方法退到后台
    if (Platform.isAndroid) {
      const channel = MethodChannel('com.flareflow.android/background');
      try {
        await channel.invokeMethod('moveToBackground');
      } on PlatformException catch (e) {
        FFLog.info("Failed to move to background: '${e.message}'.");
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/common/controller/bottom_nav_controller.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/main/bottom_nav_widget.dart';
import 'package:playlet/modules/home/<USER>';
import 'package:playlet/modules/mylist/mylist_controller.dart';
import 'package:playlet/modules/mylist/mylist_page.dart';
import 'package:playlet/modules/profile/profile_page.dart';
import 'package:playlet/modules/profile/profile_controller.dart';
import 'package:playlet/modules/reels/reels_controller.dart';
import 'package:playlet/modules/reels/reels_page.dart';
import 'package:playlet/utils/get_extension.dart';
import 'main_controller.dart';

class MainPage extends GetView<MainController> {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
  return PopScope(
      canPop: false,
      onPopInvokedWithResult: controller.onPopInvokedWithResult,
      child: Scaffold(
        body: GetBuilder<BottomNavController>(
          id: "page_view",
          builder: (bottomNavController) {
            return _LazyTabView(
              currentIndex: bottomNavController.pageIndex.value,
            );
          },
        ),
        bottomNavigationBar: const BottomNavWidget(),
      ),
    );
  }
}

 
class _LazyTabView extends StatefulWidget {
  final int currentIndex;

  const _LazyTabView({
    Key? key,
    required this.currentIndex,
  }) : super(key: key);

  @override
  State<_LazyTabView> createState() => _LazyTabViewState();
}

class _LazyTabViewState extends State<_LazyTabView> {
  // 页面索引常量
  static const int kHomePageIndex = 0;
  static const int kReelsPageIndex = 1;
  static const int kMyListPageIndex = 2;
  static const int kProfilePageIndex = 3;
  static const int kPageCount = 4;

  // 用于存储已初始化的页面
  final List<Widget?> _pages = List.filled(kPageCount, null);

  @override
  Widget build(BuildContext context) {
    // 确保当前页面已初始化
    _ensurePageInitialized(widget.currentIndex);

    // 使用 Stack 和 Visibility 显示页面
    return Stack(
      children: List.generate(
        kPageCount,
        (index) => _pages[index] != null
            ? Visibility(
                visible: widget.currentIndex == index,
                maintainState: true,
                child: _pages[index]!,
              )
            : const SizedBox.shrink(),
      ),
    );
  }

  // 确保指定索引的页面已初始化
  void _ensurePageInitialized(int index) {
    if (_pages[index] == null) {
      // 页面尚未初始化，创建它
      switch (index) {
        case kHomePageIndex:
          _pages[kHomePageIndex] = const HomePage();
          FFLog.info('HomePage 被初始化', tag: 'main_page');
          break;
        case kReelsPageIndex:
          Get.ensureController<ReelsController>(
            () => ReelsController(),
            pageName: 'main_page',
          );
          _pages[kReelsPageIndex] = const ReelsPage();
          FFLog.info('ReelsPage 被初始化', tag: 'main_page');
          break;
        case kMyListPageIndex:
          Get.ensureController<MylistController>(
              () => MylistController(),
              pageName: 'main_page',
          );
          _pages[kMyListPageIndex] = const MylistPage();
          FFLog.info('MyListPage 被初始化', tag: 'main_page');
          break;
        case kProfilePageIndex:
          Get.ensureController<ProfileController>(
            () => ProfileController(),
            pageName: 'main_page',
          );
          _pages[kProfilePageIndex] = const ProfilePage();
          FFLog.info('ProfilePage 被初始化', tag: 'main_page');
          break;
      }
      // 如果在 setState 外部调用，需要触发重建
      if (mounted) setState(() {});
    }
  }
}

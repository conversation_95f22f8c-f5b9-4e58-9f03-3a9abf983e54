import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/loading/ffloading.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/gen/fonts.gen.dart';
import 'package:playlet/i18n/trans.dart';

import 'splash_controller.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  Widget _buildSplashPage() {
    return Stack(
            children: [
              SizedBox(
                width: Get.width,
                height: Get.height,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (Platform.isIOS) SizedBox(height: 10.sp),
                    Assets.splashLogo.image(
                      width: 270.sp, // 根据实际图片尺寸调整
                      height: 270.sp, // 根据实际图片尺寸调整
                      fit: BoxFit.fill,
                    ),
                    //  Image.asset(
                    //     'assets/splash_logo.png',
                    //     fit: BoxFit.fill,
                    //     width: 270.sp, // 根据实际图片尺寸调整
                    //     height: 270.sp, // 根据实际图片尺寸调整
                    //   ),
                    //SizedBox(height: 16.sp),
                    // Text(
                    //   Config.appName,
                    //   style: TextStyle(
                    //     fontSize: 38.sp,
                    //     color: Colors.white,
                    //     fontWeight: FontWeight.w600,
                    //   ),
                    // ),
                  ],
                ),
              ),
              Positioned(
                bottom: 90,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // CircularProgressIndicator(),
                    FFLoadingWidget(
                      icon: Assets.loading.dotLoading.path,
                    ),
                  ],
                ),
              )
            ],
          );
  }

  Widget _buildOpenScreenImage(SplashController controller) {
    return Obx(() => 
      Stack(children: [
        GestureDetector(
          onTap: () {
            unawaited(controller.onOpenScreenClick());
          },
          child: Image.file(
            File(controller.openScreenImagePath.value),
            width: Get.width,
            height: Get.height,
            fit: BoxFit.cover,
          ),
        ),
        Positioned(
          top: 68.sp,
          right: 30.sp,
          child: GestureDetector(
            onTap: () {
              controller.startMainPage();
            },
            child: Container(
              height: 32.sp,
              padding: EdgeInsets.symmetric(horizontal: 12.sp), // 添加水平内边距
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(16.sp),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min, // 使Row的宽度适应内容
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${controller.openScreenSeconds.value}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFFFFCD00),
                      fontFamily: FontFamily.poppins,
                      fontStyle: FontStyle.normal,
                    ),
                  ),
                  SizedBox(width: 6.sp),
                  Text(
                    AppTrans.skip(),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                      fontFamily: FontFamily.poppins,
                      fontStyle: FontStyle.normal,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ])
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GetBuilder<SplashController>(
        init: SplashController(),
        initState: (_) {},
        builder: (controller) {
          return Obx(() => 
            controller.openScreenImagePath.value.isNotEmpty ?
              _buildOpenScreenImage(controller) :
              _buildSplashPage()
          );
        },
      ),
    );
  }
}

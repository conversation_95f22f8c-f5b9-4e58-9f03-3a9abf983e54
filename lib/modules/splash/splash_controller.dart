import 'dart:async';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:playlet/api/base.dart';
import 'package:playlet/api/home_config.dart';
import 'package:playlet/api/user.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/modules/home/<USER>';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/ios_app_delegate_service.dart';
import 'package:playlet/service/app_service.dart';
import 'package:playlet/service/fallback_drama_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/resource_bit_manager.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';
import 'package:playlet/service/resource_bit/track_event/resource_bit_track_event.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/app_analysis.dart';
import 'package:playlet/utils/app_usage_statistics.dart';
import 'package:playlet/utils/auth.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/utils/track_event.dart';
import 'package:playlet/service/notification/android_notification_manager.dart';

class SplashController extends GetxController {
  ResourceBitModel? openScreenModel;
  RxString openScreenImagePath = ''.obs; // 开屏页图片本地地址
  RxInt openScreenSeconds = 5.obs;  // 开屏页展示时间，单位秒
  Timer? _openScreenTimer;

  @override
  void onInit() async {
     // 确保在页面构建完成后再移除原生闪屏
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FlutterNativeSplash.remove();
    });
    EasyRefresh.defaultHeaderBuilder = () => ClassicHeader(
          dragText: AppTrans.pullRefresh(),
          armedText: AppTrans.releaseReady(),
          readyText: AppTrans.refreshingMore(),
          processingText: AppTrans.refreshingMore(),
          processedText: AppTrans.succeeded(),
          noMoreText: AppTrans.noMore(),
          failedText: AppTrans.failed(),
          messageText: AppTrans.lastUpdated(),
        );
    EasyRefresh.defaultFooterBuilder = () => ClassicFooter(
          dragText: AppTrans.pullToLoad(),
          armedText: AppTrans.releaseReady(),
          readyText: AppTrans.loadingMore(),
          processingText: AppTrans.loadingMore(),
          processedText: AppTrans.succeeded(),
          noMoreText: AppTrans.noMore(),
          failedText: AppTrans.failed(),
          messageText: AppTrans.lastUpdated(),
        );
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    
    // 记录开始时间
    final startTime = DateTime.now();
    // 要展示够2秒
    int showSeconds = 2000;

    // 先初始化登录
    FFLog.info("开始初始化登录...");
    InitLoginResult? loginResult = await ApiBase.initLogin();
    if (loginResult == null) {
      FFLog.error("登录初始化失败，重试中...");
      // 登录失败，重试一次
      loginResult = await ApiBase.initLogin();
    }

    if (loginResult != null) {
      Get.find<AppService>().recordAppLaunch();
      Get.find<AppService>().trackInstallEvent();
      _reportLaunchEvent();
      AppUsageStatistics.trackActive();
      FFLog.info("登录成功，尝试1.0账号数据迁移...");
      await _migrateAccount();
      FFLog.info("获取配置信息...");
      // 获取首页配置
      await ApiHomeConfig.getHomeConfig();
      // 上报用户活跃
      Get.find<UserService>().reportActiveTime(true);
      FFLog.info("登录完成");

      // 预加载首页数据
      unawaited(HomeController.prepareHomeTabData());
      
      // 加载资源位配置
      await ResourceBitManager.getInstance().loadConfig();
      unawaited(Get.find<FallbackDramaService>().getFallbackDramas());
      
      // 计算已经过去的时间
      final elapsedTime = DateTime.now().difference(startTime);
      // 如果过去的时间少于2秒，则等待剩余时间，保证展示够2秒
      if (elapsedTime.inMilliseconds < showSeconds) {
        await Future.delayed(Duration(milliseconds: showSeconds - elapsedTime.inMilliseconds));
      }

      // 接下来1秒内如果开屏页资源加载完毕，就展示出来
      await _loadOpenScreenModel();
      if (openScreenImagePath.value.isEmpty) {
        FFLog.info("资源在1秒内未加载完成，直接进入主页");
        // 跳转到主页
        startMainPage();
        return;
      }
      FFLog.info("资源在1秒内加载完成，展示开屏页");

      // 开屏页展示倒计时
      _startOpenScreenTimer();
    } else {
      FFLog.error("登录初始化失败，无法继续");
      // 这里可以添加登录失败处理，例如显示错误提示
    }
  }

  /// 跳转到主页
  void startMainPage() {
    _stopOpenScreenTimer();
    AppNavigator.startMainPage();

    // 通知iOS原生可以跳转了
    IOSAppDelegateService appDelegateService = Get.find<IOSAppDelegateService>();
    appDelegateService.notifyIOSCanOpenURL();
  }

  void _reportLaunchEvent() {
    useTrackEvent(
      EventName.app_launch, 
      extra: {EventKey.from: AppAnalysisStore.getAppLaunchFrom()},
      priority: TrackEventPriority.high
    );  
  }

  // 迁移1.0账户
  Future<void> _migrateAccount() async {
    int? oldUserId = await Auth.getOldUserId();
    if (oldUserId != null) {
      FFLog.info("有获取到旧的用户Id:$oldUserId,进行账号迁移上报");
      // 账号迁移
      bool result = await ApiUser.migrateAccount(oldUserId);
      if (result) {
        FFLog.info("账号迁移上报成功，删除旧数据");
        // 删除旧数据
        Auth.removeOldUserStore();
      } else {
        FFLog.info("账号迁移上报失败");
      }
    } else {
      FFLog.info("没有获取到旧的用户Id，不是旧版本");
    }
  }
}

/// 资源位相关
extension SplashControllerResourceBitExtension on SplashController {
  /// 开屏页点击事件
  Future<void> onOpenScreenClick() async {
    startMainPage();
    final service = _getOpenScreenService();
    if (service == null) {
      return;
    }
    ResourceBitModel? model = openScreenModel;
    if (model == null) {
      return;
    }
    model.onResourceBitClick(service.resourceBitScene);
  }

  /// 获取开屏页资源图片本地地址
  Future<void> _loadOpenScreenModel() async {
    openScreenModel = null;
    openScreenImagePath.value = '';
    try {
      final service = _getOpenScreenService();
      if (service == null) {
        return;
      }
      FFLog.debug('开始获取开屏页', tag: 'ResourceBitManager');
      ResourceBitModel? model = await service.getResourceModel();
      if (model == null) {
        return;
      }
      String? path = await service.loadResource(model).timeout(const Duration(seconds: 1));
      if (path == null || path.isEmpty) {
        return;
      }
      await service.onShown(model);
      
      openScreenModel = model;
      openScreenImagePath.value = path;
      ResourceBitTrackEvent.reelShow(model);
      
    } catch (e) {
      FFLog.debug('加载开屏页资源失败: $e', tag: 'ResourceBitManager');
    }
  }

  ResourceBitBaseService? _getOpenScreenService() {
    final service = ResourceBitManager.getInstance().getService(ResourceBitScene.openScreen);
    return service;
  }

  /// 开始定时器
  void _startOpenScreenTimer() {
    _stopOpenScreenTimer();
    _openScreenTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      openScreenSeconds.value --;
      FFLog.debug("开屏页展示倒计时 ${openScreenSeconds.value}");
      if (openScreenSeconds.value == 0) {
        // 跳转到主页
        startMainPage();
      }
    });
  }

  /// 停止定时器
  void _stopOpenScreenTimer() {
    _openScreenTimer?.cancel();
    _openScreenTimer = null;
  }
}
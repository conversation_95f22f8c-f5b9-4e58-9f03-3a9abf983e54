import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../components/empty/empty.dart';
import '../../components/loading/ffloading.dart';
import '../../components/nav_bar/ffnav_bar.dart';
import '../../components/rewards_bind/bind_progress_widget.dart';
import '../../components/rewards_bind/email_input_widget.dart';
import '../../components/rewards_bind/phone_input_widget.dart';
import '../../components/rewards_bind/rewards_bind_button.dart';
import '../../i18n/trans.dart';
import '../../model/rewards_bind/bind_options.dart';
import 'rewards_bind_controller.dart';

class RewardsBindPage extends GetView<RewardsBindController> {
  const RewardsBindPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: FFNavBar(
        showBackIcon: true,
        title: controller.optionsModel.value.type == RewardsBindType.Phone
            ? AppTrans.mobileLogin()
            : AppTrans.emailBinding(),
      ),
      body: Obx(() {
        if (controller.loadingStatus.value == LoadingStatus.loading) {
          // 加载中
          return const FFLoadingWidget();
        }
        if (controller.loadingStatus.value == LoadingStatus.failed) {
          // 加载失败
          return EmptyWidget(
            type: EmptyType.noNetwork,
            onRefresh: () async {
              Get.loading();
              await controller.initCountryCodeInfoList();
              Get.dismiss();
            },
            pageFrom: EmptyPageFrom.rewards,
          );
        }

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Obx(() {
            return Column(
              children: [
                SizedBox(
                  height: 46.h,
                ),
                const BindProgressWidget(
                    currentStep: BindProgressWidget.inputState),
                SizedBox(
                  height: 46.h,
                ),
                controller.optionsModel.value.type == RewardsBindType.Phone
                    ? buildPhoneInputWidget()
                    : buildEmailInputWidget(),
                SizedBox(
                  height: 66.h,
                ),
                GetVerificationButton(
                  isEnabled: controller.isEnabled.value,
                  onPressed: () {
                    controller.sendVerification();
                  },
                ),
              ],
            );
          }),
        );
      }),
    );
  }

  Widget buildInputWidget() {
    return controller.optionsModel.value.type == RewardsBindType.Phone
        ? buildPhoneInputWidget()
        : buildEmailInputWidget();
  }

  Column buildEmailInputWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppTrans.enterEmailAccount(),
          style: TextStyle(fontWeight: FontWeight.w400, fontSize: 16.sp),
        ),
        SizedBox(
          height: 28.h,
        ),
        EmailInputWidget(
          onValidation: (bool isValid, String? inputValue) {
            controller.onVerificationPass(isValid, inputValue);
          },
        ),
      ],
    );
  }

  Column buildPhoneInputWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppTrans.enterPhoneNumber(),
          style: TextStyle(fontWeight: FontWeight.w400, fontSize: 16.sp),
        ),
        SizedBox(
          height: 28.h,
        ),
        PhoneInputWidget(
          controller.countryCodeInfoList,
          onValidation: (bool isValid, String? inputValue) {
            controller.onVerificationPass(isValid, inputValue);
          },
        ),
      ],
    );
  }
}

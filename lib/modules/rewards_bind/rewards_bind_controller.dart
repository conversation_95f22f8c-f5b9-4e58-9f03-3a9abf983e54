import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../api/rewards_bind_api.dart';
import '../../model/rewards_bind/bind_options.dart';
import '../../model/rewards_bind/country_count_resultdart.dart';
import '../../utils/rewards_strong.dart';

enum LoadingStatus {
  loading,
  success,
  failed,
}

class RewardsBindController extends GetxController {
  late Rx<LoadingStatus> loadingStatus = LoadingStatus.loading.obs;

  var isEnabled = false.obs;
  var countryCodeInfoList = <CountryCodeInfo>[].obs;

  var optionsModel = BindOptionsModel(
          type: RewardsBindType.Phone,
          sourceBindType: SourceBindType.AccountInfo)
      .obs;

  @override
  void onInit() {
    if (Get.arguments is BindOptionsModel) {
      optionsModel.value = Get.arguments as BindOptionsModel;
    }
    ApiRewardsBind.clearPhoneSendTimes();
    initCountryCodeInfoList();
    super.onInit();
  }

  Future<void> sendVerification() async {
    // 开始倒计时。todo 待确认 倒计时中是否允许修改电话号码。重新发送
    // if (RewardsBindStrong.getLeftTime() == 0) {
    Get.loading();
    var isSendSuccess = false;
    if (optionsModel.value.type == RewardsBindType.Phone) {
      //   发送短信验证码
      List<String> phones = optionsModel.value.text.split(' ');
      if (phones.length == 2) {
        String areaCode = phones.first;
        String phoneNumber = phones.last;
        ApiRewardsBind.increasePhoneSendTimes();
        isSendSuccess = await ApiRewardsBind.sendPhoneOtp(
          phone: phoneNumber,
          areaCode: areaCode,
          sendTimes: ApiRewardsBind.getPhoneSendTimes(),
        );
      }
    } else {
      //   发送邮箱验证码
      isSendSuccess = await ApiRewardsBind.sendEmail(optionsModel.value.text);
    }
    Get.dismiss();
    if (isSendSuccess) {
      FFLog.info("发送验证码成功");
      RewardsBindStrong.startCountdown();
      AppNavigator.startRewardsVerificationPage(optionsModel.value);
    }

    // }
  }

  void onVerificationPass(bool isValid, String? inputValue) {
    isEnabled.value = isValid;
    if (inputValue != null && inputValue.isNotEmpty) {
      FFLog.error("验证通过 $inputValue");
      optionsModel.value.text = inputValue;
    } else {
      FFLog.error("用户输入数据返回为空");
    }
  }

  initCountryCodeInfoList() async {
    // todo 接入验证码
    if (optionsModel.value.type == RewardsBindType.Phone) {
      var result = await ApiRewardsBind.getCountryAreaCodeInfoList();
      if (result != null && result.isNotEmpty) {
        countryCodeInfoList.value = result;
        loadingStatus.value = LoadingStatus.success;
      } else {
        FFLog.error("国家区号返回为空");
        loadingStatus.value = LoadingStatus.failed;
      }
    } else {
      loadingStatus.value = LoadingStatus.success;
    }
  }
}

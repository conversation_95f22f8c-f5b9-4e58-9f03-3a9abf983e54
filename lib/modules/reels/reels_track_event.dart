import 'package:get/get.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/utils/track_event.dart';

class ReelsTrackEvent {
  ReelsTrackEvent._();

  static void reelShow({
    required final int? shortPlayCode,
  }) {
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: shortPlayCode == null ? '' : shortPlayCode.toString(),
      TrackEvent.scene: TrackEvent.shorts,
      EventKey.moduleName: "",
      EventKey.moduleId: "",
    });
  }
}

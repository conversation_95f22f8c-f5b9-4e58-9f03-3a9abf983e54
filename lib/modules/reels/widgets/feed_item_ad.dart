import 'package:flutter/material.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_native_model.dart';

class FeedItemAd extends StatelessWidget {
  const FeedItemAd({super.key, required this.item});
  final ReelItem item;

  @override
  Widget build(BuildContext context) {
    Widget child = Container(
      color: const Color(0xFF4D4D4D),
      alignment: Alignment.center,
      child: Assets.imgFeedAdDefault.image(
        fit: BoxFit.fill,
      ),
    );

    if (item.type == ReelType.ad && item.adModel != null) {
      if (AdManager().shouldShowAd.value == false) {
        return child;
      }
      if (item.adModel is AdAmNativeModel) {
        final nativeModel = item.adModel! as AdAmNativeModel?;
        if (nativeModel == null) {
          return child;
        }
        if (item.adModel!.statusModel.status != AdLoadStatus.success) {
          return child;
        }

        Widget? widget;
        widget = nativeModel.shortsFeedWidget;
        if (widget == null) {
          return child;
        }
        return Center(child: widget);
      }
      return child;
    } else {
      return child;
    }
  }
}

import 'dart:async';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/api/details.dart';
import 'package:playlet/api/reels.dart';
import 'package:playlet/common/controller/bottom_nav_controller.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/modules/home/<USER>/home_event.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/routers/route_observer_custom.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/ad/ad_track_event.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_native_model.dart';
import 'package:playlet/service/good_review_service.dart';
import 'package:playlet/service/mmkv_service.dart';
import 'package:playlet/service/player_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/resource_bit_manager.dart';
import 'package:playlet/service/shorts_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/utils/track_event.dart';
import 'package:screen_capture_restrictions/screen_capture_restrictions.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

enum LoadingStatus {
  loading,
  success,
  failed,
}

class ReelsController extends GetxController with WidgetsBindingObserver {
  static const String tag = "ReelsController";

  final PlayerService playerService = Get.find<PlayerService>();
  final UserService userService = Get.find<UserService>();
  final BottomNavController bottomNavController =
      Get.find<BottomNavController>();

  final GoodReviewService goodReviewService = Get.find<GoodReviewService>();
  // 加载状态
  late Rx<LoadingStatus> loadingStatus = LoadingStatus.loading.obs;

  // RxList<Reel> reels = <Reel>[].obs;
  RxList<ReelItem> reelItems = <ReelItem>[].obs;

  // 真实的接口返回数据
  List<ForYouShortInfo> forYouList = [];

  RxInt currentIndex = 0.obs;

  Worker? everListener;

  bool isForeground = true;

  /// 用户手动暂停播放
  bool isUserPaused = false;

  int pageNum = 1;
  int pageSize = 20;
  bool isFinish = true;
  int? isColdBoot;
  int? lastShortPlayId;
  int? consecutiveTimes;
  String? realLanguageCode;

  RxBool enableInfiniteScroll = false.obs;

  final CarouselSliderController pageViewController =
      CarouselSliderController();

  StreamSubscription? routeSubscription;
  late final StreamSubscription initLoginSubscription;
  late final StreamSubscription pushNotificationToReelShortSubscription;
  late PushNotificationToReelShortEventData?
      pushNotificationToReelShortEventData;

  late bool isAdLoading;

  @override
  void onInit() {
    WidgetsBinding.instance.addObserver(this);
    AdManager().prefetchAds();
    routeSubscription =
        eventBus.on<RouteObserverCustomEvent>().listen(onRouterListener);
    _initData();
    initLoginSubscription = eventBus.on<InitLoginEventData>().listen((event) {
      _initData();
    });
    pushNotificationToReelShortEventData = null;
    pushNotificationToReelShortSubscription =
        eventBus.on<PushNotificationToReelShortEventData>().listen((event) {
      pushNotificationToReelShortEventData = event;
      pushNotificationJumpToShort();
      _setPrecacheList();
    });

    // 设置iOS设备即使在静音模式下也能播放声音
    playerService.configureAudioSessionForSilentMode();
    isAdLoading = false;
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    AdTrackEvent.adPlacementShow(AdType.native, AdScene.listFeed);
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    everListener?.dispose();
    routeSubscription?.cancel();
    initLoginSubscription.cancel();
    pushNotificationToReelShortSubscription.cancel();
    routeSubscription = null;
    super.onClose();
  }

  void _initData() {
    pageNum = 1;
    reelItems.clear();
    forYouList.clear();
    getData();
    everListener?.dispose();
    everListener = ever(bottomNavController.pageIndex, callback);
  }

  void onRouterListener(RouteObserverCustomEvent event) {
    if (bottomNavController.pageIndex.value == 1 &&
        bottomNavController.currentRoute.value == Routes.mainPage) {
      goodReviewService.onStartFeed();
      onCurrentVideoPlay();
    } else {
      goodReviewService.onEndFeed();
      onCurrentVideoPause();
    }
  }

  void callback(int value) async {
    if (value == 1 &&
        bottomNavController.currentRoute.value == Routes.mainPage) {
      onCurrentVideoPlay();
      goodReviewService.onStartFeed();
      await ScreenCaptureRestrictions.enableSecure();
      WakelockPlus.enable();
    } else {
      goodReviewService.onEndFeed();
      onCurrentVideoPause();
      await ScreenCaptureRestrictions.disableSecure();
      WakelockPlus.disable();
    }
  }

  /// 在数据不足的情况下，保证feel可以不断的滑动，无限循环列表
  Future<void> getOldData() async {
    // 这里取原有的数据
    if (forYouList.isNotEmpty) {
      updateList(forYouList);
      loadingStatus.value = LoadingStatus.success;
    }
  }

  Future<void> getData() async {
    if (pageNum == 1) {
      loadingStatus.value = LoadingStatus.loading;
    }
    final result = await ApiReels.getForYouListPage(
      pageNum,
      pageSize,
      isColdBoot: pageNum == 1 ? 1 : 2,
      lastShortPlayId: lastShortPlayId,
      consecutiveTimes: consecutiveTimes,
      realLanguageCode: realLanguageCode,
    );
    if (result != null && result.forYouList != null) {
      forYouList.addAll(result.forYouList!);
      updateList(result.forYouList!);
      pushNotificationJumpToShort();
      _setPrecacheList();
      if (result.consecutiveTimes != null) {
        consecutiveTimes = result.consecutiveTimes;
      }
      if (result.realLanguageCode != null) {
        realLanguageCode = result.realLanguageCode;
      }
      if (result.forYouList != null && result.forYouList!.isNotEmpty) {
        final lastShort = result.forYouList!.last;
        if (lastShort.shortPlayId != null) {
          lastShortPlayId = lastShort.shortPlayId;
        }
      }
      loadingStatus.value = LoadingStatus.success;
      if (pageNum == 1 && result.forYouList!.isNotEmpty) {
        _handleTrackReelRequestClick(0);
      }
      if (result.hasMore == false) {
        enableInfiniteScroll.value = true;
        isFinish = true;
      } else {
        enableInfiniteScroll.value = false;
        isFinish = false;
      }
    } else if (pageNum == 1) {
      loadingStatus.value = LoadingStatus.failed;
    }
  }

  Future<void> updateList(List<ForYouShortInfo> list) async {
    bool isFirstPage = reelItems.isEmpty;

    // 第一个插入资源位
    if (isFirstPage) {
      final resourceBitReelItem = await _getResourceBitReelItem();
      if (resourceBitReelItem != null) {
        reelItems.add(resourceBitReelItem);
      }
    }

    List<ReelItem> newList = [];

    for (var i = 0; i < list.length; i++) {
      try {
        ForYouShortInfo info = list[i];

        /// 处理收藏
        if (info.isCollect != null && info.isCollect == 1) {
          Get.find<ShortsService>().confirmCollection(info.shortPlayCode);
        }

        Get.find<ShortsService>().setShortsCollectionNumberBy(
          info.shortPlayCode,
          info.collectNum,
        );

        final videoUrl = getVideoUrl(info.videoUrl!);
        if (i % 5 == 0 && i != 0) {
          if (AdManager().shouldShowAd.value == true) {
            newList.add(ReelItem(type: ReelType.ad));
          }
        }
        Reel reel = Reel(
          info: info,
          videoUrl: videoUrl!,
        );
        if (isFirstPage && i == 0) {
          // 第一个视频要读取本地观看进度
          final position = _readPlayDurationFromLocal(reel);
          reel.setPlayPosition(position);
        }
        newList.add(
          ReelItem(
            type: ReelType.shorts,
            reel: reel,
          ));
      } catch (e) {
        FFLog.error("$tag 处理视频项时出错: $e");
      }
    }
    reelItems.addAll(newList);
    _setPrecacheList();
  }

  void _writePlayDurationToLocal(Reel reel, Duration duration) {
    final episodeId = reel.info.id;
    if (episodeId == null) {
      return;
    }
    final mmkvService = Get.find<MMKVService>();
    mmkvService.setPlayDuration(episodeId, duration);
  }

  Duration _readPlayDurationFromLocal(Reel reel) {
    final episodeId = reel.info.id;
    if (episodeId == null) {
      return Duration.zero;
    }
    final videoDuration = reel.info.videoDuration;
    final mmkvService = Get.find<MMKVService>();
    final duration = mmkvService.getPlayDuration(episodeId);
    if (_isPlayDurationShouldReset(videoDuration, duration.inSeconds)) {
      _resetPlayDurationToLocal(reel);
      return Duration.zero;
    }
    return duration;
  }

  void _resetPlayDurationToLocal(Reel reel) {
    final episodeId = reel.info.id;
    if (episodeId == null) {
      return;
    }
    final mmkvService = Get.find<MMKVService>();
    mmkvService.removePlayDuration(episodeId);
  }

  /// 是否需要重置播放时间
  bool _isPlayDurationShouldReset(int? videoDuration, int? currentDuration) {
    if (videoDuration == null) {
      return false;
    }
    if (currentDuration == null) {
      return false;
    }
    if ((videoDuration <= 2) || currentDuration >= (videoDuration - 2)) {
      // 此处是临时特殊处理，因为规范的解决方法需要在播放器播放结束时不再回调onTimeChanged，影响范围太大。
      // 当剩余时间小于2秒时直接从头开始播放，避免剩余时间太短导致自动往下滑的问题。
      return true;
    }
    return false;
  }

  void _setPrecacheList() {
    try {
      FFLog.debug("$tag 设置预加载列表: ${reelItems.length}个新视频");
      // 记录所有新视频URL用于预加载
      List<String> videoUrls = [];
      for (var element in reelItems) {
        if (element.reel != null && element.reel!.videoUrl.isNotEmpty) {
          videoUrls.add(element.reel!.videoUrl);
        }
      }

      if (pageNum == 1) {
        // 第一页时，重置预加载列表
        playerService.setPreloadList(videoUrls);
      } else {
        // 加载更多时，添加到预加载列表
        playerService.addPreloadList(videoUrls);
      }
      FFLog.debug("$tag 预加载列表更新完成: ${videoUrls.length}个视频");
    } catch (e) {
      FFLog.error("$tag 设置预加载列表失败: $e");
    }
  }

  void onCurrentVideoPlay() {
    if (reelItems.isEmpty) return;
    final ReelItem reelItem = reelItems[currentIndex.value];
    if (reelItem.type != ReelType.shorts) return;
    if (reelItem.reel == null) return;
    final currentPlayerView = reelItem.reel!.playerKey;
    currentPlayerView.currentState?.onPlay();
  }

  void onCurrentVideoPause() {
    if (reelItems.isEmpty) return;
    final ReelItem reelItem = reelItems[currentIndex.value];
    if (reelItem.type != ReelType.shorts) return;
    if (reelItem.reel == null) return;
    final currentPlayerView = reelItem.reel!.playerKey;
    currentPlayerView.currentState?.onPause();
  }

  void onPageViewChanged(int index, CarouselPageChangedReason _) {
    addAdModel(index);
    onAddWatchHistory(currentIndex.value);
    onCurrentVideoPause();
    currentIndex.value = index;
    onCurrentVideoPlay();
    _handleTrackReelRequestClick(index);

    // 更新预加载列表，确保当前位置附近的视频被预加载
    _updatePreloadForCurrentPosition(index);

    if (currentIndex.value < reelItems.length - 2 && !isFinish) {
      pageNum = pageNum + 1;
      getData();
    } else if (isFinish && currentIndex.value == reelItems.length - 2) {
      // 滑动到倒数第二个时，如果没有下一页的，加载之前的数据
      getOldData();
    }
  }

  // 根据当前位置更新预加载列表
  void _updatePreloadForCurrentPosition(int index) {
    try {
      // 计算需要预加载的视频范围（当前位置及其后面的几个视频）
      int startIndex = index;
      int endIndex = index + 5; // 预加载当前及后面的5个视频
      if (endIndex >= reelItems.length) {
        endIndex = reelItems.length - 1;
      }

      List<String> videoUrls = [];
      for (int i = startIndex; i <= endIndex; i++) {
        if (i < reelItems.length) {
          final item = reelItems[i];
          if (item.type == ReelType.shorts && item.reel != null && item.reel!.videoUrl.isNotEmpty) {
            videoUrls.add(item.reel!.videoUrl);
          }
        }
      }

      if (videoUrls.isNotEmpty) {
        FFLog.debug("$tag 更新当前位置预加载列表: ${videoUrls.length}个视频");
        playerService.addPreloadList(videoUrls);
      }
    } catch (e) {
      FFLog.error("$tag 更新当前位置预加载列表失败: $e");
    }
  }

  // 请求剧埋点
  void _handleTrackReelRequestClick(int index) {
    if (index < reelItems.length) {
      ReelItem reelItem = reelItems[index];
      if (reelItem.type == ReelType.shorts) {
        useTrackEvent(TrackEvent.reel_request, extra: {
          TrackEvent.status: "success",
          TrackEvent.reel_id:
              reelItem.reel?.info.shortPlayCode?.toString() ?? "",
          TrackEvent.episode: reelItem.reel?.info.episodeNum?.toString() ?? "",
          TrackEvent.scene: "shorts",
        });
      }
    }
  }

  // 移除原有的_updatePreloadListForCurrentPosition方法

  /// 播放结束
  @Deprecated('请使用 onEndToDetail()')
  void onEnded() {
    if (currentIndex < reelItems.length - 1) {
      onNext();
    }
  }

  void onEndToDetail() {
    ReelItem reelItem = reelItems[currentIndex.value];
    if (reelItem.type != ReelType.shorts || reelItem.reel == null) return;
    Reel reel = reelItem.reel!;
    ForYouShortInfo info = reel.info;
    AppNavigator.startDetailsPage(DetailsOptions(
      // businessId: widget.info.info.shortPlayId!,
      businessId: info.shortPlayId!,
      scene: "recently",
      from: EventValue.fromShorts,
      // 下一集
      playerEpisodeIndex: info.episodeNum ?? 1,
    ));
  }

  void onNext() {
    _safeNextPage();
  }

  String? getVideoUrl(VideoInfoMap videoInfoMap) {
    if (videoInfoMap.video720 != null && videoInfoMap.video720!.isNotEmpty) {
      return videoInfoMap.video720;
    }
    if (videoInfoMap.video480 != null && videoInfoMap.video480!.isNotEmpty) {
      return videoInfoMap.video480;
    }
    return videoInfoMap.video1080;
  }

  void onVideoPlayTimeChange(int index, Duration time) {
    final ReelItem reelItem = reelItems[currentIndex.value];
    final reel = reelItem.reel;
    if (reel != null) {
      reel.setPlayPosition(time);
      _writePlayDurationToLocal(reel, time);
    }
  }

  Future<void> onAddWatchHistory(int index) async {
    if (index >= reelItems.length) {
      return;
    }
    final ReelItem reelItem = reelItems[currentIndex.value];
    if (reelItem.reel == null) return;
    if (reelItem.type != ReelType.shorts) return;
    final dramaId = reelItem.reel?.info.id;
    if (dramaId == null) {
      return;
    }
    int duration = reelItem.reel!.playPosition.inSeconds;
    if (duration < 5) {
      return;
    }
    bool success = await ApiDetails.saveWatchHistory(dramaId, duration);
    if (success) {
      eventBus.fire(RefreshHomeEvent());
    }
  }

  void onTapViews() {
    onCurrentVideoPause();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    FFLog.debug("$tag App lifecycle state changed to: $state");

    switch (state) {
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
        isForeground = false;
        onCurrentVideoPause();
        break;
      case AppLifecycleState.resumed:
        isForeground = true;
        //不是用户暂停的，则播放
        if (!isUserPaused) {
          onCurrentVideoPlay();
        }
        // 页面恢复时，重新设置预加载列表
        updatePreloadList();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // 不需要处理这些状态
        break;
    }
  }

  // 更新所有预加载列表的方法
  void updatePreloadList() {
    try {
      // 获取所有视频URL
      List<String> allVideoUrls = [];
      for (var item in reelItems) {
        if (item.type == ReelType.shorts && item.reel != null) {
          final videoUrl = getVideoUrl(item.reel!.info.videoUrl!);
          if (videoUrl != null && videoUrl.isNotEmpty) {
            allVideoUrls.add(videoUrl);
          }
        }
      }

      FFLog.debug("$tag 更新全部预加载列表: ${allVideoUrls.length}个视频");

      if (allVideoUrls.isNotEmpty) {
        // 重置预加载列表
        playerService.setPreloadList(allVideoUrls);

        // 同时更新当前位置的预加载优先级
        if (currentIndex.value < reelItems.length) {
          _updatePreloadForCurrentPosition(currentIndex.value);
        }
      }
    } catch (e) {
      FFLog.error("$tag 更新预加载列表失败: $e");
    }
  }

  void addAdModel(int index) {
    if (AdManager().shouldShowAd.value == false) {
      return;
    }
    int nextAdIndex = -1;
    for (var i = 0; i < reelItems.length; i++) {
      if (index < i && reelItems[i].type == ReelType.ad) {
        nextAdIndex = i;
        break;
      }
    }
    if (nextAdIndex != -1) {
      if (reelItems[nextAdIndex].adModel == null) {
        if (isAdLoading == false) {
          isAdLoading == true;
          AdManager().getAdModel(AdScene.shortsNative).then((model) {
            if (model != null && (model is AdAmNativeModel)) {
              reelItems[nextAdIndex].adModel = model;
            }
            isAdLoading = false;
          });
        }
      }
    }
  }

  Future<void> pushNotificationJumpToShort() async {
    if (pushNotificationToReelShortEventData != null) {
      PushNotificationToReelShortEventData eventData =
          PushNotificationToReelShortEventData(
        shortPlayId: pushNotificationToReelShortEventData!.shortPlayId,
        dramaId: pushNotificationToReelShortEventData!.dramaId,
        reelType: pushNotificationToReelShortEventData!.reelType,
        callback: pushNotificationToReelShortEventData!.callback,
      );
      pushNotificationToReelShortEventData = null;

      onCurrentVideoPause();
      int? index;
      bool shouldUpdate = false;
      if (eventData.reelType == ReelType.resourceBit) {
        // 资源位
        final resourceBitIndex = reelItems.indexWhere((item) => item.type == eventData.reelType);
        if (resourceBitIndex >= 0) {
          index = resourceBitIndex;
          shouldUpdate = true;
        }
      } else if (eventData.reelType == ReelType.shorts && reelItems.isNotEmpty) {
        for (var i = 0; i < reelItems.length; i++) {
          ReelItem item = reelItems[i];
          if (item.reel?.info.id != null &&
              item.reel?.info.id.toString() == eventData.dramaId) {
            index = i;
            String? videoUrl = item.reel?.videoUrl;
            if (videoUrl == null || videoUrl.isEmpty) {
              // 没有播放地址，也要请求剧集详情更新信息
              shouldUpdate = true;
            }
            break;
          }
        }
      }
      if (index != null) {
        if (shouldUpdate) {
          await _getPushNotificationShortInfo(eventData, index: index);
        } else {
          _safeJumpToPage(index);
        }
      } else {
        await _getPushNotificationShortInfo(eventData);
      }

      onCurrentVideoPlay();
    }
  }

  Future<void> _getPushNotificationShortInfo(
      PushNotificationToReelShortEventData eventData,
      {int? index}) async {
    int shortId = int.parse(eventData.dramaId);
    ForYouShortInfo? infoData = await ApiReels.getShortInfoById(shortId);
    if (infoData != null) {
      /// 处理收藏
      if (infoData.isCollect != null && infoData.isCollect == 1) {
        Get.find<ShortsService>().confirmCollection(infoData.shortPlayCode);
      }

      Get.find<ShortsService>().setShortsCollectionNumberBy(
        infoData.shortPlayCode,
        infoData.collectNum,
      );
      final videoUrlInfo = infoData.videoUrl;
      if (videoUrlInfo == null) {
        eventData.callback?.call(false);
        return;
      }
      final String? videoUrl = getVideoUrl(videoUrlInfo);
      ReelItem reelItem;
      if (index != null && index < reelItems.length) {
        reelItem = reelItems[index];
        reelItem.type = ReelType.shorts;
        reelItem.reel?.info = infoData;
        reelItem.reel?.videoUrl = videoUrl ?? '';
        onPageViewChanged(index, CarouselPageChangedReason.manual);
      } else {
        reelItem = ReelItem(
          type: ReelType.shorts,
          reel: Reel(
            info: infoData,
            videoUrl: videoUrl ?? '',
          ),
        );
        reelItems.insert(0, reelItem);
        /// 安全调用 jumpToPage，避免List.single 抛异常
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _safeJumpToPage(0);
        });
      }
      eventData.callback?.call(true);
    } else {
      eventData.callback?.call(false);
    }
  }

  /// 安全调用 jumpToPage 方法，避免空值检查错误
  void _safeJumpToPage(int page) {
    try {
      // 检查 pageViewController 是否已初始化
      if (pageViewController.ready) {
        pageViewController.jumpToPage(page);
        FFLog.debug("$tag _safeJumpToPage -> 成功跳转到页面: $page");
      } else {
        // 如果控制器还没准备好，等待其准备完成
        pageViewController.onReady.then((_) {
          pageViewController.jumpToPage(page);
          FFLog.debug("$tag _safeJumpToPage -> 延迟跳转到页面: $page");
        }).catchError((error) {
          FFLog.error("$tag _safeJumpToPage -> 延迟跳转失败: $error");
        });
      }
    } catch (e) {
      FFLog.error("$tag _safeJumpToPage -> 跳转到页面 $page 失败: $e");
    }
  }

  /// 安全调用 nextPage 方法，避免空值检查错误
  void _safeNextPage() {
    try {
      // 检查 pageViewController 是否已初始化
      if (pageViewController.ready) {
        pageViewController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        FFLog.debug("$tag _safeNextPage -> 成功跳转到下一页");
      } else {
        // 如果控制器还没准备好，等待其准备完成
        pageViewController.onReady.then((_) {
          pageViewController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          FFLog.debug("$tag _safeNextPage -> 延迟跳转到下一页");
        }).catchError((error) {
          FFLog.error("$tag _safeNextPage -> 延迟跳转失败: $error");
        });
      }
    } catch (e) {
      FFLog.error("$tag _safeNextPage -> 跳转到下一页失败: $e");
    }
  }
}

extension ReelsControllerResourceBitExtension on ReelsController {
  Future<ReelItem?> _getResourceBitReelItem() async {
    final service = ResourceBitManager.getInstance().getService(ResourceBitScene.feed);
    if (service == null) {
      return null;
    }
    final model = await service.getResourceModel();
    if (model == null) {
      return null;
    }

    ResourceBitSkipType? skipType = model.skipType;
    if (skipType == null) {
      return null;
    }

    try {
      // 资源位先占位
      Reel? reel;
      if (skipType == ResourceBitSkipType.shorts) {
        int? shortPlayId = model.shortPlayId;
        if (shortPlayId == null) {
          return null;
        }
        reel = Reel(
          info: ForYouShortInfo(
            shortPlayId: shortPlayId,
          ),
          videoUrl: "",
        );
      }
      return ReelItem(
        type: ReelType.resourceBit,
        reel: reel,
        resourceBitModel: model,
      );
    } catch (e) {
      FFLog.error('获取feed流资源位数据失败：$e');
      return null;
    }
  }
}

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/reels/reel_item_widget.dart';
import 'package:playlet/components/shimmer/reel_shimmer.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/modules/reels/reels_controller.dart';
import 'package:playlet/modules/reels/widgets/feed_item_ad.dart';
import 'package:playlet/modules/resource_bit/feed/feed_resource_bit_view.dart';

class ReelsPage extends GetView<ReelsController> with WidgetsBindingObserver {
  const ReelsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () {
          if (controller.loadingStatus.value == LoadingStatus.loading) {
            // 加载中
            return const ReelShimmer();
          }
          if (controller.loadingStatus.value == LoadingStatus.failed) {
            // 加载失败
            return EmptyWidget(
              pageFrom: EmptyPageFrom.shorts,
              type: EmptyType.noNetwork,
              onRefresh: () {
                controller.getData();
              },
            );
          }
          if (controller.reelItems.isEmpty) {
            return const EmptyWidget(
                pageFrom: EmptyPageFrom.shorts, type: EmptyType.noContent);
          }
          return Obx(() => CarouselSlider.builder(
                options: CarouselOptions(
                  onPageChanged: controller.onPageViewChanged,
                  height: Get.height,
                  viewportFraction: 1,
                  scrollDirection: Axis.vertical,
                  enableInfiniteScroll: controller.enableInfiniteScroll.value,
                  padEnds: true,
                  pageViewKey: const PageStorageKey('reels_page_view'),
                ),
                carouselController: controller.pageViewController,
                itemCount: controller.reelItems.length,
                itemBuilder: (context, index, realIndex) {
                  final info = controller.reelItems[index];
                  if (info.type == ReelType.resourceBit) {
                    return FeedResourceBitView(info.resourceBitModel);
                  } else if (info.type == ReelType.ad) {
                    return FeedItemAd(item: info);
                  } else {
                    return info.reel == null
                        ? const SizedBox.shrink()
                        : ReelItemWidget(
                            key: ValueKey(index),
                            index: index,
                            shortPlayCode: info.reel!.info.shortPlayCode,
                            shortPlayType: info.reel!.info.shortPlayType,
                            info: info.reel!,
                            onEnded: controller.onEndToDetail,
                            onTimeChange: (tiem) =>
                                controller.onVideoPlayTimeChange(index, tiem),
                          );
                  }
                },
              ));
        },
      ),
    );
  }
}

import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:playlet/components/nav_bar/ffnav_bar.dart';
import 'package:playlet/components/ui/login_buttom.dart';
import 'package:playlet/components/ui/screen_view.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/login/login_controller.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/index.dart';



class LoginPage extends GetView<LoginController> {
  const LoginPage({super.key});
  @override
  Widget build(BuildContext context) {
    return ScreenView(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: true,
        body: Stack(
          children: [
            Assets.login.loginHeadBg.image(
              width: Get.width,
              height: 400.sp,
              fit: BoxFit.cover,
            ),
            SizedBox(
              width: Get.width,
              height: Get.height,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _buildTop(),
                  _buildBottom(),
                ],
              ),
            ),
            _buildBack(),
          ],
        ),
      ),
    );
  }

  Widget _buildBack() {
     return const FFNavBar(
      showBackIcon: true, 
      alpha: 0.0,
    );
  }

  Widget _buildBottom() {
    return SafeArea(child: Column(
      children: [
        _buildAgreement(),
      ],
    ));
  }

  Widget _buildAgreement() {
    return Container(
      width: 313.sp,
      padding: EdgeInsets.only(bottom: 22.sp, top: 50.sp),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          children: [
            TextSpan(
              text: AppTrans.signInTips(),
              style: TextStyle(
                color: const Color.fromRGBO(130, 130, 130, 1),
                fontSize: 12.sp,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
                height: 1.5,
              ),
            ),
            TextSpan(
              text: ' ',
              style: TextStyle(
                fontSize: 12.sp,
              ),
            ),
            TextSpan(
              text: AppTrans.userAgreement(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
                height: 1.5,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Utils.openUserAgreement();
                },
            ),
            TextSpan(
              text: ' ',
              style: TextStyle(
                fontSize: 12.sp,
              ),
            ),
            TextSpan(
              text: AppTrans.signInAnd(),
              style: TextStyle(
                color: const Color.fromRGBO(130, 130, 130, 1),
                fontSize: 12.sp,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
                height: 1.5,
              ),
            ),
            TextSpan(
              text: ' ',
              style: TextStyle(
                fontSize: 12.sp,
              ),
            ),
            TextSpan(
              text: AppTrans.privacyPolicy(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
                height: 1.5,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Utils.openPrivacyPolicy();
                },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogins() {
    if (Platform.isIOS) {
      return Column(
        children: [
          _buildAppleLoginButton(),
          SizedBox(height: 18.sp),
          _buildGoogleLoginButton(),
          SizedBox(height: 12.sp),
          _buildLoginGift(_buildFacebookLoginButton()),
        ],
      );
    }
    return Column(
      children: [
        _buildLoginGift(_buildFacebookLoginButton()),
        SizedBox(height: 18.sp),
        _buildGoogleLoginButton(),
      ],
    );
  }

  Widget _buildLoginGift(Widget child) {
    return Stack(
      children: [
        Column(
          children: [
            SizedBox(height: 19.sp),
            child,
          ],
        ),
        Positioned(
          top: 0,
          right: 0,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 5.sp),
            height: 26.sp,
            decoration: BoxDecoration(
              color: const Color.fromRGBO(255, 69, 0, 1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(20.r),
                bottomLeft: Radius.circular(1.r),
                bottomRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 添加图标
                Assets.login.iconBonus.image(
                  width: 16.sp,
                  height: 16.sp,
                ),
                SizedBox(width: 4.sp), // 添加图标和文本之间的间距
                Text(
                  AppTrans.signInReward(Get.find<UserService>().metaLoginBonus),
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 12.sp,
                    fontFamily: 'Poppins',
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAppleLoginButton() {
    return LoginButtom(
      width: 312.sp,
      title: AppTrans.signInApple(),
      image: Assets.login.appleBlack.path,
      onTap: controller.onAppleLogin,
      backgroundColor: Colors.white,
      textColor: Colors.black,
      border: false,
    );
  }

  Widget _buildFacebookLoginButton() {
    return LoginButtom(
      width: 312.sp,
      title: AppTrans.signInFacebook(),
      image: Assets.login.facebookWhiteIcon.path,
      backgroundColor: const Color.fromRGBO(62, 103, 181, 1),
      onTap: controller.onFacebookLogin,
    );
  }

  Widget _buildGoogleLoginButton() {
    return LoginButtom(
      width: 312.sp,
      title: AppTrans.signInGoogle(),
      image: Assets.login.googleIcon.path,
      backgroundColor: const Color.fromRGBO(85, 85, 85, 1),
      onTap: controller.onGoogleLogin,
    );
  }

  Widget _buildTop() {
    return Column(
      children: [
        SizedBox(height: 198.sp),
        Assets.logo.image(
          width: 90.sp,
          height: 90.sp,
        ),
        SizedBox(height: 10.sp),
        Text(
          Config.appName,
          style: TextStyle(
            fontSize: 34.sp,
            color: Colors.white,
            fontWeight: FontWeight.w600,
            fontFamily: "Poppins",
          ),
        ),
        SizedBox(height: 6.sp),
        Text(
          AppTrans.signInWelcome(Config.appName),
          style: TextStyle(
            color: Colors.white,
            fontSize: 18.sp,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 52.sp),
        _buildLogins(),
      ],
    );
  }
}

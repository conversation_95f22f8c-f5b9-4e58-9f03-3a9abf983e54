import 'package:get/get.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/utils/auth.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/track_event.dart';

class LoginPageParams {
  LoginPageParams({
    this.trackFrom,
    this.trackScene,
    this.fbRewardsTrackFrom,
  });
  final String? trackFrom;
  final String? trackScene;
  final String? fbRewardsTrackFrom;
}

class LoginController extends GetxController {
  late final LoginPageParams? params;

  @override
  void onInit() {
    params = Get.arguments;
    super.onInit();
    useTrackEvent(
      EventName.login_windows_show,
      extra: {
        EventKey.from: params?.trackFrom ?? '',
      },
    );
  }

  void onGoogleLogin() async {
    _trackLoginClickEvent(EventValue.google);
    Get.loading();
    bool isSuccess = await Auth.signInWithGoogle(params?.trackScene ?? '');
    Get.dismiss();
    if (isSuccess) {
      Get.back();
    }
  }

  void _trackLoginClickEvent(String type) {
    useTrackEvent(
      EventName.login_click,
      extra: {
        EventKey.scene: params?.trackScene ?? '',
        EventKey.type: type,
      },
    );
  }

  void onAppleLogin() async {
    _trackLoginClickEvent(EventValue.apple);
    Get.loading();
    bool isSuccess = await Auth.signInWithApple(params?.trackScene ?? '');
    Get.dismiss();
    if (isSuccess) {
      Get.back();
    }
  }

  void onFacebookLogin() async {
    _trackLoginClickEvent(EventValue.facebook);
    Get.loading();
    bool isSuccess = await Auth.signInWithFacebook(
      params?.trackScene ?? '',
      fbRewardsTrackFrom: params?.fbRewardsTrackFrom ?? '',
    );
    Get.dismiss();
    if (isSuccess) {
      Get.back();
    }
  }
}

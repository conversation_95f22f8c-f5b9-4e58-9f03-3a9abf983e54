import 'dart:io';

import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/api/recommend.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/recommend.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/model/store/store_product_result.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/payment/payment_events.dart';
import 'package:playlet/service/payment/payment_service.dart';
import 'package:playlet/service/recommend_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/track_event.dart';

enum LoadingStatus {
  loading,
  success,
  failed,
}

class RecommendController extends GetxController {
  final ExpandableController expandableController = ExpandableController();

  final ScrollController scrollController = ScrollController();

  final RecommendService recommendService = Get.find<RecommendService>();

  final PaymentService paymentService = Get.find<PaymentService>();
  final UserService userService = Get.find<UserService>();

  // 加载状态
  late Rx<LoadingStatus> loadingStatus = LoadingStatus.loading.obs;

  RxBool isExpand = false.obs;

  RxBool isExpandable = false.obs;

  Rx<RecommendData?> data = RecommendData().obs;

  RxList<SkuInfoResponses> topSkuInfos = <SkuInfoResponses>[].obs;

  RxInt playIndex = 0.obs;
  RxBool isShowStore = true.obs;

  late String from;

  @override
  void onInit() {
    recommendService.getRemainingTime();
    from = Get.arguments;
    isShowStore.value = recommendService.getPaySuccess() == true ? false : true;
    recommendService.endTime.obs.listen((event) {});
    getData();
    scrollController.addListener(scrollListener);
    PaymentEvent.submitRechargeShow(
      strScene: "new_user_iap,new_user_subs",
      reelId: "",
      episode: "",
      action: "other",
      lockBegin: "",
      playDirection: EventValue.vertical,
    );
    super.onInit();
  }

  @override
  void onClose() {
    scrollController.dispose();
    PaymentEvent.submitRechargeShowEnd(
      strScene: "new_user_iap,new_user_subs",
      reelId: "",
      episode: "",
      action: "other",
      lockBegin: "",
      resourceBitId: "",
      playDirection: EventValue.vertical,
    );
    super.onClose();
  }

  void scrollListener() {
    if (scrollController.position.pixels > 30) {
      isExpand.value = true;
    } else {
      isExpand.value = false;
    }
  }

  Future<void> getData() async {
    final result = await ApiRecommend.getData();
    if (result != null) {
      data.value = result;
      //开始收集商品id
      Set<String> skuIds = {};
      final isSkuList = data.value?.skuInfoList != null &&
          data.value!.skuInfoList!.isNotEmpty;
      final isSubList =
          data.value?.subscribeList != null && result.subscribeList!.isNotEmpty;
      if (isSubList) {
        for (SubscribeSkuResponses element in result.subscribeList!) {
          skuIds.add(element.skuId);
        }
      }
      if (isSkuList) {
        for (SkuInfoResponses element in result.skuInfoList ?? []) {
          final skuId = Platform.isIOS ? element.iosSkuId : element.gpSkuId;
          skuIds.add(skuId);
        }
      }
      await paymentService.loadProductsBySkuId(skuIds);
      //开始初始化本地化
      if (isSkuList) {
        for (SkuInfoResponses element in result.skuInfoList ?? []) {
          final skuId = Platform.isIOS ? element.iosSkuId : element.gpSkuId;
          final productDetails = paymentService.getProduct(skuId);
          if (productDetails != null) {
            element.recharge = productDetails.rawPrice.toString();
          }
        }
      }
      if (isSubList) {
        for (SubscribeSkuResponses element in result.subscribeList!) {
          final productDetails = paymentService.getProduct(element.skuId);
          if (productDetails != null) {
            element.payAmount = productDetails.rawPrice.toString();
            element.firstAmount =
                paymentService.getIntroductoryPrice(productDetails);
          }
        }
      }
      if (result.skuInfoList != null && result.skuInfoList!.isNotEmpty) {
        topSkuInfos.add(result.skuInfoList![0]);
        if (result.skuInfoList!.length > 1) {
          topSkuInfos.add(result.skuInfoList![1]);
        }
      }
      onRefreshCoinStore();
      loadingStatus.value = LoadingStatus.success;
    } else {
      loadingStatus.value = LoadingStatus.failed;
    }
  }

  void onExpandableTap() {
    isExpandable.value = !isExpandable.value;
    expandableController.toggle();
    useTrackEvent(TrackEvent.newUserDramaClick, extra: {
      "from": from,
      "action": "more",
      "sku": "",
      "reel_id": "",
      "episode": ""
    });
  }

  Future<void> onSubPay(SubscribeSkuResponses item) async {
    Get.loading();
    useTrackEvent(TrackEvent.newUserDramaClick, extra: {
      "from": from,
      "action": "sub",
      "sku": item.skuId,
      "reel_id": "",
      "episode": ""
    });
    bool val = await paymentService.startSubscription(
      skuId: item.skuId,
      productId: item.productId.toString(),
      amount: item.isFirstBuy ? item.firstAmount : item.payAmount,
      strSource: "new_user_subs",
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );
    if (val) {
      Get.log("订阅成功");
    }
    Get.dismiss();
  }

  // void useTrackEventReelPlay(RecommendList item) {
  //   useTrackEvent(TrackEvent.reelPlay, extra: {
  //     // 剧id
  //     EventKey.reelId: item.shortPlayId ?? "",
  //     //剧集数值
  //     EventKey.episode:
  //         item.episodeNum == null ? '' : item.episodeNum!.toString(),
  //     EventKey.scene: EventValue.newUserPage,
  //     //前向来源
  //     EventKey.from: from,
  //     //是否免费：1 付费，0 免费
  //     EventKey.isFree: '0',
  //     //进剧逻辑：nature/random
  //     EventKey.logic: 'nature',
  //     //播放速度：[]
  //     EventKey.speedLevel: '1',
  //     //解锁卡点
  //     EventKey.lockBegin: '',
  //     EventKey.playDirection: ScreenUtils.isLandscape(Get.context!)
  //         ? EventValue.horizontal
  //         : EventValue.vertical
  //   });
  // }

  void useTrackEventReelShow(RecommendList item) {
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: item.shortPlayId ?? "",
      TrackEvent.scene: EventValue.newUserPage,
    });
  }

  void onToDetail(RecommendList item) async {
    useTrackEvent(TrackEvent.newUserDramaClick, extra: {
      "from": from,
      "action": "reel",
      "sku": "",
      "reel_id": item.shortPlayId.toString(),
      "episode": item.dramaId.toString(),
    });
    AppNavigator.startDetailsPage(DetailsOptions(
      businessId: int.parse(item.shortPlayId!),
      scene: EventValue.newUserPage,
      from: EventValue.newUserPage,
    ));
  }

  Future<void> onCoinsPay(SkuInfoResponses item) async {
    final skuId = Platform.isAndroid ? item.gpSkuId : item.iosSkuId;
    if (item.purchasedSku == true) {
      FFLog.info("已经购买过了则不触发购买 $skuId", tag: "RecommendController");
      return;
    }
    Get.loading();
    final productId = item.skuProductId;
    final skuModelConfigId = item.skuModelConfigId;
    final skuType = item.skuType;
    useTrackEvent(TrackEvent.newUserDramaClick, extra: {
      "from": from,
      "action": "sku",
      "sku": skuId,
      "reel_id": "",
      "episode": ""
    });
    bool val = await paymentService.startPurchase(
      skuId: skuId,
      productId: productId,
      skuType: skuType,
      strSource: "new_user_iap",
      skuModelConfigId: skuModelConfigId,
      amount: item.recharge,
      coins: item.coins.toString(),
      bonus: item.productGiveCoins.toString(),
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );
    if (val) {
      for (SkuInfoResponses element in topSkuInfos) {
        if (element.skuProductId == productId) {
          element.purchasedSku = true;
        }
      }
      for (SkuInfoResponses element in data.value?.skuInfoList ?? []) {
        if (element.skuProductId == productId) {
          element.purchasedSku = true;
        }
      }
      bool isAllPurchased = true;
      for (SkuInfoResponses element in data.value?.skuInfoList ?? []) {
        if (element.purchasedSku == false) {
          isAllPurchased = false;
          break;
        }
      }
      if (isAllPurchased) {
        isShowStore.value = false;
        FFLog.info("购买完了所有金币商品", tag: "RecommendController");
      }
      sortSkuInfosByPurchasedStatus(topSkuInfos);
      if (data.value?.skuInfoList != null &&
          data.value!.skuInfoList!.isNotEmpty) {
        sortSkuInfosByPurchasedStatus(data.value!.skuInfoList!);
      }
      onRefreshCoinStore();
      Get.log("支付成功");
    }
    Get.dismiss();
  }

  void onRefreshCoinStore() {
    bool isAllPurchased = true;
    for (SkuInfoResponses element in data.value?.skuInfoList ?? []) {
      if (element.purchasedSku == false) {
        isAllPurchased = false;
        break;
      }
    }
    if (isAllPurchased) {
      isShowStore.value = false;
      FFLog.info("购买完了所有金币商品", tag: "RecommendController");
    }
    topSkuInfos.refresh();
    data.refresh();
  }

  void sortSkuInfosByPurchasedStatus(List<SkuInfoResponses> skuInfos) {
    skuInfos.sort((a, b) {
      // false 在前，true 在后
      if (!a.purchasedSku && b.purchasedSku) return -1;
      if (a.purchasedSku && !b.purchasedSku) return 1;
      return 0; // 相等时保持原有顺序
    });
  }

  String getVideoUrlByResolution(VideoInfoMap videoInfoMap, String resolution) {
    switch (resolution) {
      case '1080p':
        return videoInfoMap.video1080 ??
            videoInfoMap.video720 ??
            videoInfoMap.video480!;
      case '720p':
        return videoInfoMap.video720 ??
            videoInfoMap.video480 ??
            videoInfoMap.video1080!;
      case '480p':
        return videoInfoMap.video480 ??
            videoInfoMap.video720 ??
            videoInfoMap.video1080!;
      default:
        return videoInfoMap.video720 ??
            videoInfoMap.video480 ??
            videoInfoMap.video1080!;
    }
  }

  void onPlayEnd(int index) {
    playIndex.value = index + 1;
  }
}

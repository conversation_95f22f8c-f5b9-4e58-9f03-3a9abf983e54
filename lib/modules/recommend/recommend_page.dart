import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/loading/ffloading.dart';
import 'package:playlet/components/recommend/product_item.dart';
import 'package:playlet/components/recommend/recommend_item.dart';
import 'package:playlet/components/store/subscribe_item.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/subscription/widgets/subscription_detail_body.dart';
import 'package:playlet/utils/track_event.dart';
import 'recommend_controller.dart';

class RecommendPage extends StatefulWidget {
  const RecommendPage({super.key});

  @override
  State<RecommendPage> createState() => _RecommendPageState();
}

class _RecommendPageState extends State<RecommendPage> {
  @override
  Widget build(BuildContext context) {
    RecommendController controller;
    if (Get.isRegistered<RecommendController>() == true) {
      controller = Get.find<RecommendController>();
    } else {
      controller = Get.put(RecommendController());
    }
    return Scaffold(
      body: Stack(
        children: [
          Positioned(
              child: Assets.recommend.topBack.image(
            width: Get.width,
            height: 402.sp,
          )),
          Obx(() {
            if (controller.loadingStatus.value == LoadingStatus.loading) {
              // 加载中
              return const Center(child: FFLoadingWidget());
            }
            if (controller.loadingStatus.value == LoadingStatus.failed) {
              // 加载失败
              return EmptyWidget(
                pageFrom: EmptyPageFrom.shorts,
                type: EmptyType.noNetwork,
                onRefresh: () {
                  controller.getData();
                },
              );
            }
            return Container(
              width: Get.width,
              height: Get.height,
              decoration: BoxDecoration(
                color: controller.isExpand.value
                    ? Colors.black
                    : Colors.transparent,
              ),
              child: SingleChildScrollView(
                controller: controller.scrollController,
                child: Column(
                  children: [
                    SizedBox(height: Get.mediaQuery.padding.top + 87.sp),
                    _buildTopInfo(controller),
                    SizedBox(height: 49.sp),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.sp),
                      child: _buildSub(controller),
                    ),
                    const RecommedStoreList(),
                    const RecommedPlayletList(),
                    SizedBox(height: 26.sp),
                  ],
                ),
              ),
            );
          }),
          _buildTopNav(controller),
        ],
      ),
    );
  }

  Widget _buildSub(RecommendController controller) {
    return Obx(() {
      if (controller.userService.userInfo.value?.isSubscription == true &&
          controller.paymentService.subProductEd.value.skuId.isNotEmpty) {
        return SubscriptionDetailBaseInfoWidget(
          subscriptionType:
              controller.userService.userInfo.value!.subscriptionType,
        );
      }
      if (controller.data.value?.subscribeList != null &&
          controller.data.value?.subscribeList!.isNotEmpty == true) {
        final item = controller.data.value!.subscribeList!.first;
        return SubscribeItem(
          product: item,
          currency: controller.paymentService.currency.value,
          onTap: () => controller.onSubPay(item),
        );
      }
      return const SizedBox();
    });
  }

  Widget _buildTopNav(RecommendController controller) {
    return Positioned(
      top: 0,
      child: Obx(
        () => Container(
          width: Get.width,
          padding: EdgeInsets.only(
            top: Get.mediaQuery.padding.top,
            left: 16.sp,
          ),
          decoration: BoxDecoration(
            color:
                controller.isExpand.value ? Colors.black : Colors.transparent,
          ),
          child: Row(
            children: [
              IconButton(
                onPressed: () {
                  useTrackEvent(TrackEvent.newUserDramaClick, extra: {
                    "from": controller.from,
                    "action": "close",
                    "sku": "",
                    "reel_id": "",
                    "episode": ""
                  });
                  Get.back();
                },
                icon: const Icon(Icons.arrow_back_ios_new_rounded),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopInfo(RecommendController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp),
          alignment: Alignment.center,
          child: ShaderMask(
            shaderCallback: (bounds) {
              return const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFFFEAA3),
                  Color(0xFFFFD549),
                ],
              ).createShader(bounds);
            },
            child: Text(
              AppTrans.recommendTitle(),
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 26.sp,
                  fontWeight: FontWeight.w600,
                  shadows: [
                    Shadow(
                      color: Colors.black.withValues(alpha: 0.5),
                      offset: const Offset(0, 2),
                      blurRadius: 2,
                    ),
                  ]),
            ),
          ),
        ),
        SizedBox(height: 26.sp),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 34.sp,
              constraints: BoxConstraints(
                minWidth: 221.sp,
              ),
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.recommend.limitedBack.provider(),
                  fit: BoxFit.fill,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.recommend.timeEnd.image(
                    width: 28.sp,
                    height: 28.sp,
                  ),
                  SizedBox(width: 8.sp),
                  Text(
                    AppTrans.recommendTitleLabel(),
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: const Color(0XFFE3BE6C),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 33.sp),
        _buildTimeWidget(controller),
      ],
    );
  }

  Widget _buildTimeWidget(RecommendController controller) {
    return Obx(
      () => controller.recommendService.isShow.value == true
          ? _buildDownTimeWidget(controller)
          : const SizedBox.shrink(),
    );
  }

  Widget _buildDownTimeWidget(RecommendController controller) {
    return FutureBuilder(
      future: Future.delayed(const Duration(milliseconds: 60)),
      builder: (context, snapshot) {
        final textStyle = TextStyle(
          fontSize: 28.sp,
          fontWeight: FontWeight.w700,
          color: Colors.white,
        );
        return Obx(
          () => Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 60.sp,
                height: 50.sp,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Assets.recommend.timeBack.path),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      controller.recommendService.timeRemaining.value.hours
                          .split("")[0],
                      style: textStyle,
                    ),
                    Text(
                      controller.recommendService.timeRemaining.value.hours
                          .split("")[1],
                      style: textStyle,
                    ),
                  ],
                ),
              ),
              Container(
                width: 28.sp,
                alignment: Alignment.center,
                child: Text(":", style: textStyle),
              ),
              Container(
                width: 60.sp,
                height: 50.sp,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Assets.recommend.timeBack.path),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      controller.recommendService.timeRemaining.value.minutes
                          .split("")[0],
                      style: textStyle,
                    ),
                    Text(
                      controller.recommendService.timeRemaining.value.minutes
                          .split("")[1],
                      style: textStyle,
                    ),
                  ],
                ),
              ),
              Container(
                width: 28.sp,
                alignment: Alignment.center,
                child: Text(":", style: textStyle),
              ),
              Container(
                width: 60.sp,
                height: 50.sp,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Assets.recommend.timeBack.path),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      controller.recommendService.timeRemaining.value.seconds
                          .split("")[0],
                      style: textStyle,
                    ),
                    Text(
                      controller.recommendService.timeRemaining.value.seconds
                          .split("")[1],
                      style: textStyle,
                    ),
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

class RecommedStoreList extends StatefulWidget {
  const RecommedStoreList({super.key});

  @override
  State<RecommedStoreList> createState() => _RecommedStoreListState();
}

class _RecommedStoreListState extends State<RecommedStoreList> {
  final RecommendController _controller = Get.find<RecommendController>();

  @override
  void initState() {
    useTrackEvent(TrackEvent.newUserDramaShow);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (_controller.isShowStore.value) {
        return Column(
          children: [
            SizedBox(height: 16.sp),
            _buildCoinsStore(),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.sp),
              child: Obx(
                () => _controller.isExpandable.value
                    ? const SizedBox()
                    : Padding(
                        padding: EdgeInsets.symmetric(vertical: 20.sp),
                        child: GestureDetector(
                          onTap: _controller.onExpandableTap,
                          child: Container(
                            height: 34.sp,
                            decoration: BoxDecoration(
                              color: const Color.fromRGBO(30, 30, 30, 1),
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.keyboard_arrow_down_outlined),
                                Text(AppTrans.recommendMore()),
                              ],
                            ),
                          ),
                        ),
                      ),
              ),
            ),
          ],
        );
      }
      return const SizedBox();
    });
  }

  Widget _buildCollapsed() {
    return Obx(() => GridView.builder(
          shrinkWrap: true,
          padding: EdgeInsets.symmetric(horizontal: 16.sp),
          physics: const NeverScrollableScrollPhysics(),
          clipBehavior: Clip.none,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 1.95,
            mainAxisSpacing: 16.sp,
            crossAxisSpacing: 16.sp,
          ),
          itemCount: _controller.topSkuInfos.length,
          itemBuilder: (context, index) {
            final item = _controller.topSkuInfos[index];
            return GestureDetector(
              onTap: () => _controller.onCoinsPay(item),
              child: ProductItem(
                item: item,
                currency: _controller.paymentService.currency,
                disabled: item.purchasedSku,
                showOriginPrice:
                    _controller.data.value?.showOriginPrice == true,
              ),
            );
          },
        ));
  }

  Widget _buildCoinsStore() {
    return Obx(() {
      if (_controller.data.value?.skuInfoList != null &&
          _controller.data.value!.skuInfoList!.isNotEmpty == true) {
        return ExpandablePanel(
          controller: _controller.expandableController,
          collapsed: _buildCollapsed(),
          expanded: _buildExpanded(),
        );
      }
      return const SizedBox();
    });
  }

  Widget _buildExpanded() {
    return Obx(
      () => GridView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.symmetric(horizontal: 16.sp),
        physics: const NeverScrollableScrollPhysics(),
        clipBehavior: Clip.none,
        itemCount: _controller.data.value?.skuInfoList?.length ?? 0,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.95,
          mainAxisSpacing: 16.sp,
          crossAxisSpacing: 16.sp,
        ),
        itemBuilder: (context, index) {
          final item = _controller.data.value!.skuInfoList![index];
          return GestureDetector(
            onTap: () => _controller.onCoinsPay(item),
            child: ProductItem(
              item: item,
              currency: _controller.paymentService.currency,
              disabled: item.purchasedSku,
              showOriginPrice: _controller.data.value?.showOriginPrice == true,
            ),
          );
        },
      ),
    );
  }
}

class RecommedPlayletList extends StatefulWidget {
  const RecommedPlayletList({super.key});

  @override
  State<RecommedPlayletList> createState() => _RecommedPlayletListState();
}

class _RecommedPlayletListState extends State<RecommedPlayletList> {
  final RecommendController contrller = Get.find<RecommendController>();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (contrller.data.value == null ||
          contrller.data.value!.recommendList == null ||
          contrller.data.value!.recommendList!.isEmpty == true) {
        return const SizedBox();
      }
      final list = contrller.data.value!.recommendList!;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 20.sp),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.sp),
            child: Text(
              "${AppTrans.recommendNewcomers()} :",
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          SizedBox(height: 20.sp),
          //动态高度
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: 16.sp),
            itemCount: (list.length + 1) ~/ 3,
            itemBuilder: (context, rowIndex) {
              int startIndex = rowIndex * 3;
              final rowItems = list.sublist(
                startIndex,
                startIndex + 3 > list.length ? list.length : startIndex + 3,
              );
              return IntrinsicHeight(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: rowItems.asMap().entries.map((entry) {
                    int index = startIndex + entry.key;
                    return RecommendItem(item: entry.value, index: index);
                  }).toList(),
                ),
              );
            },
          ),
        ],
      );
    });
  }
}

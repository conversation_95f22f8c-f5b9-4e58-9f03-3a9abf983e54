import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/api/rewards_bind_api.dart';
import 'package:playlet/components/alert/rewards_bind_alert.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../common/event/track_event.dart';
import '../../common/log/ff_log.dart';
import '../../components/alert/index.dart';
import '../../model/rewards_bind/bind_event.dart';
import '../../model/rewards_bind/bind_options.dart';
import '../../utils/events.dart';
import '../../utils/rewards_strong.dart';

class RewardsVerificationController extends GetxController {

  var optionsModel = BindOptionsModel(
          type: RewardsBindType.Email,
          sourceBindType: SourceBindType.AccountInfo)
      .obs;

  var countDown = 0.obs;
  Timer? _timer;

  TextEditingController textEditingController = TextEditingController();


  @override
  void onReady() {
    var arguments = Get.arguments;
    if (arguments is BindOptionsModel) {
      optionsModel.value = arguments;
    }
    FFLog.info("optionsModel.value  ${optionsModel.value}");
    startCountdown();
    super.onReady();
  }


  @override
  void onClose() {
    cancelTimer();
    super.onClose();
  }

  void startCountdown() {
    countDown.value = RewardsBindStrong.getLeftTime();
    FFLog.info("开始倒计时 ${countDown.value}");
    _timer = Timer.periodic(const Duration(seconds: 1), (Timer timer) async {
      if (countDown.value > 0) {
        countDown.value = countDown.value - 1;
        FFLog.info("开始倒计时  ${countDown.value}");
      } else {
        cancelTimer(); // 停止计时器
      }
    });
  }

  void cancelTimer() {
    if (_timer != null && _timer!.isActive == true) {
      _timer!.cancel();
      _timer = null;
    }
  }


  Future<void> onCompletedInput(String verificationCode) async {
    // todo 添加实际验证逻辑
    if (optionsModel.value.type == RewardsBindType.Phone) {
      List<String> phones = optionsModel.value.text.split(' ');
      if (phones.length == 2) {
        Get.loading();
        String areaCode = phones.first;
        String phoneNumber = phones.last;
        int? result = await ApiRewardsBind.bindByPhone(
          verificationCode: verificationCode,
          phone: phoneNumber,
          areaCode: areaCode,
        );
        Get.dismiss();
        if (result != null) {
          FFAlert.show(
            onDismiss: () async {
              ApiRewardsBind.clearPhoneSendTimes();
              AppNavigator.offNamedUntilRewardsPage(from: TrackEvent.binding);
            },
            container: RewardsBindAlert(
              bonus: result,
              onClick: () async {
                ApiRewardsBind.clearPhoneSendTimes();
                AppNavigator.offNamedUntilRewardsPage(from: TrackEvent.binding);
              },
              type: optionsModel.value.type,
            ),
          );
          eventBus.fire(BindStateEvent());
        }
      }
    } else {
      Get.loading();

      var result = await ApiRewardsBind.bindByEmail(
          optionsModel.value.text, verificationCode);
      Get.dismiss();

      if (result != null) {
        FFAlert.show(
          onDismiss: () async {
            ApiRewardsBind.clearPhoneSendTimes();
            AppNavigator.offNamedUntilRewardsPage(from: TrackEvent.binding);
          },
          container: RewardsBindAlert(
            bonus: result,
            onClick: () async {
              ApiRewardsBind.clearPhoneSendTimes();
              AppNavigator.offNamedUntilRewardsPage(from: TrackEvent.binding);
            },
            type: optionsModel.value.type,
          ),
        );
        eventBus.fire(BindStateEvent());
      }
    }
  }

  Future<void> sendVerification() async {
    // 发送验证码 判断倒计时是否归零
    if (RewardsBindStrong.getLeftTime() == 0) {
      textEditingController.clear();
      Get.loading();
      var isSendSuccess = false;
      if (optionsModel.value.type == RewardsBindType.Phone) {
        //   发送短信验证码
        List<String> phones = optionsModel.value.text.split(' ');
        if (phones.length == 2) {
          String areaCode = phones.first;
          String phoneNumber = phones.last;
          ApiRewardsBind.increasePhoneSendTimes();
          isSendSuccess = await ApiRewardsBind.sendPhoneOtp(
            phone: phoneNumber,
            areaCode: areaCode,
            sendTimes: ApiRewardsBind.getPhoneSendTimes(),
          );
        }
      } else {
        //   发送邮箱验证码
        isSendSuccess = await ApiRewardsBind.sendEmail(optionsModel.value.text);
      }

      if (isSendSuccess) {
        FFLog.info("发送验证码成功");
        RewardsBindStrong.startCountdown();
        startCountdown();
      }
      Get.dismiss();
    }
  }
}

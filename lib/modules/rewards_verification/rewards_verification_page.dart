import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:playlet/common/log/ff_log.dart';

import '../../components/nav_bar/ffnav_bar.dart';
import '../../components/rewards_bind/bind_progress_widget.dart';
import '../../i18n/trans.dart';
import '../../model/rewards_bind/bind_options.dart';
import 'rewards_verification_controller.dart';

class RewardsVerificationPage extends GetView<RewardsVerificationController> {
  const RewardsVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: FFNavBar(
          showBackIcon: true,
          title: controller.optionsModel.value.type == RewardsBindType.Phone
              ? AppTrans.mobileLogin()
              : AppTrans.emailBinding(),
        ),
        body: Column(
          children: [
            SizedBox(
              height: 46.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: const BindProgressWidget(
                  currentStep: BindProgressWidget.verificationState),
            ),
            SizedBox(
              height: 46.h,
            ),
            controller.optionsModel.value.type == RewardsBindType.Phone
                ? buildPhoneVerificationWidget(context)
                : buildEmailVerificationWidget(context),
            SizedBox(
              height: 20.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 18.w),
              child: buildCountDown(),
            ),
          ],
        ),
      );
    });
  }

  Row buildCountDown() {
    return Row(
      mainAxisAlignment: controller.countDown.value == 0
          ? MainAxisAlignment.start
          : MainAxisAlignment.end,
      children: [
        controller.countDown.value == 0
            ? InkWell(
          onTap: () {
            controller.sendVerification();
          },
          child: Text(
            AppTrans.notReceiveTryAgain(),
            style: TextStyle(
                fontSize: 12.sp, color: const Color(0xFFFFCD00)),
          ),
        )
            : Text(AppTrans.resendAfterCountDown(controller.countDown.value),
            style: TextStyle(fontSize: 12.sp, color: Colors.white)),
      ],
    );
  }

  buildPhoneVerificationWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          AppTrans.verificationCodeSentToPhone(),
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: const Color(0xFFE0E0E0),
          ),
          textAlign: TextAlign.center,
        ),
        Padding(
          padding: EdgeInsets.only(top: 18.h, bottom: 40.h),
          child: Text(
            controller.optionsModel.value.text,
            style: TextStyle(fontSize: 16.sp),
          ),
        ),
        PinCodeTextField(
          appContext: context,
          mainAxisAlignment: MainAxisAlignment.center,
          pastedTextStyle: TextStyle(
            color: const Color(0xFFFFCD00),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
          controller: controller.textEditingController,
          textStyle: TextStyle(
            color: const Color(0xFFFFCD00),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
          length: 6,
          animationType: AnimationType.fade,
          pinTheme: PinTheme(
              shape: PinCodeFieldShape.box,
              fieldHeight: 56.h,
              fieldWidth: 44.w,
              borderRadius: BorderRadius.circular(4.r),
              activeFillColor: const Color(0xFF424242),
              inactiveColor: const Color(0xFF424242),
              inactiveFillColor: const Color(0xFF424242),
              activeColor: const Color(0xFFFFCD00),
              selectedColor: const Color(0xFFFFCD00),
              selectedFillColor: const Color(0xFF424242),
              fieldOuterPadding: EdgeInsets.symmetric(horizontal: 6.w)),
          cursorColor: const Color(0xFFFFCD00),
          animationDuration: const Duration(milliseconds: 300),
          enableActiveFill: true,
          keyboardType: TextInputType.number,
          autoFocus: true,
          boxShadows: const [
            BoxShadow(
              offset: Offset(0, 1),
              color: Colors.black12,
              blurRadius: 10,
            )
          ],
          onCompleted: (v) {
            FFLog.info("输入完毕。 $v");
            controller.onCompletedInput(v);
          },
          onChanged: (value) {
            FFLog.info(value);
          },
          beforeTextPaste: (text) {
            FFLog.info("Allowing to paste $text");
            return true;
          },
        ),
      ],
    );
  }

  buildEmailVerificationWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          AppTrans.verificationCodeSentToEmail(),
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: const Color(0xFFE0E0E0),
          ),
          textAlign: TextAlign.center,
        ),
        Padding(
          padding: EdgeInsets.only(top: 18.h, bottom: 40.h),
          child: Text(
            controller.optionsModel.value.text,
            style: TextStyle(fontSize: 16.sp),
          ),
        ),
        PinCodeTextField(
          appContext: context,
          mainAxisAlignment: MainAxisAlignment.center,
          pastedTextStyle: const TextStyle(
            color: Color(0xFFFFCD00),
            fontWeight: FontWeight.bold,
          ),
          controller: controller.textEditingController,
          textStyle: const TextStyle(
            color: Color(0xFFFFCD00),
            fontWeight: FontWeight.bold,
          ),
          length: 6,
          animationType: AnimationType.fade,
          pinTheme: PinTheme(
              shape: PinCodeFieldShape.box,
              fieldHeight: 56.h,
              fieldWidth: 44.w,
              borderRadius: BorderRadius.circular(4.r),
              activeFillColor: const Color(0xFF424242),
              inactiveColor: const Color(0xFF424242),
              inactiveFillColor: const Color(0xFF424242),
              activeColor: const Color(0xFFFFCD00),
              selectedColor: const Color(0xFFFFCD00),
              selectedFillColor: const Color(0xFF424242),
              fieldOuterPadding: const EdgeInsets.symmetric(horizontal: 6)),
          cursorColor: const Color(0xFFFFCD00),
          animationDuration: const Duration(milliseconds: 300),
          enableActiveFill: true,
          autoFocus: true,
          keyboardType: TextInputType.number,
          boxShadows: const [
            BoxShadow(
              offset: Offset(0, 1),
              color: Colors.black12,
              blurRadius: 10,
            )
          ],
          onCompleted: (v) {
            FFLog.info("输入完毕。 $v");
            controller.onCompletedInput(v);
          },
          onChanged: (value) {
            FFLog.info(value);
          },
          beforeTextPaste: (text) {
            FFLog.info("Allowing to paste $text");
            return true;
          },
        ),
      ],
    );
  }
}

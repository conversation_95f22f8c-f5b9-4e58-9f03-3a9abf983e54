import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:playlet/model/webview.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebviewController extends GetxController {
  WebviewModel? webviewModel;

  late final WebViewController webViewController;

  @override
  void onInit() {
    WebviewModel? val = Get.arguments;
    if (val != null) {
      webviewModel = val;
    } else {
      Get.back();
    }
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted);
    if (webviewModel!.url.contains("http")) {
      _loadUrl();
    } else {
      _loadHtmlFromAssets();
    }
    super.onInit();
  }

  void _loadUrl() async {
    webViewController.loadRequest(Uri.parse(webviewModel!.url));
  }

  void _loadHtmlFromAssets() async {
    String fileText = await rootBundle.loadString(webviewModel!.url);
    webViewController.loadHtmlString(fileText);
  }
}

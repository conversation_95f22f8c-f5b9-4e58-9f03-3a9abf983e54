import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/components/nav_bar/ffnav_bar.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'webview_controller.dart';

class WebviewPage extends GetView<WebviewController> {
  const WebviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: FFNavBar(
        title: controller.webviewModel!.title,
        showBackIcon: true,
      ),
      body: WebViewWidget(
        controller: controller.webViewController,
      ),
    );
  }
}

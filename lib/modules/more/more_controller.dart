import 'dart:async';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:get/get.dart';
import 'package:playlet/api/more_api.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/ad/ad_track_event.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_native_model.dart';
import 'package:playlet/utils/events.dart';

class MoreController extends GetxController {
  RxBool loading = true.obs;
  final shortPlayResponseList = Rx<List<ShortPlayResponseList>?>(null);
  HomeModuleStyle? style;
  BannerResponseList? bannerData;
  StreamSubscription? shortWatchHistoryEventShortSubscription;

  EasyRefreshController refreshController = EasyRefreshController(
    controlFinishRefresh: true,
  );

  Future<void> loadData({bool isRefresh = false}) async {
    int bannerId = bannerData?.bannerId ?? -1;
    if (bannerId != -1) {
      if (isRefresh) {
        var result = await ApiMore.getBannerMore(bannerId);
        var moreList = result?.shortPlayResponseList;
        if (moreList != null) {
          shortPlayResponseList.value = moreList;
        }
        refreshController.finishRefresh(moreList != null ? IndicatorResult.success : IndicatorResult.fail);
      } else {
        loading.value = true;
        style = bannerData?.style;
        var result = await ApiMore.getBannerMore(bannerId);
        shortPlayResponseList.value = result?.shortPlayResponseList;
        loading.value = false;
      }
    }
  }

  @override
  void onInit() {
    super.onInit();
    bannerData = Get.arguments;
    if (bannerData == null) {
      Get.back();
    }

    shortWatchHistoryEventShortSubscription = eventBus.on<ShortWatchHistoryEvent>().listen((event) {
      var short = shortPlayResponseList.value?.firstWhereOrNull((item)=>item.id == event.shortPlayId);
      short?.episodeNum = event.episodeNum;
    });
  }

  @override
  void onReady() {
    super.onReady();
    AdTrackEvent.adPlacementShow(AdType.native, AdScene.homeFeed);
    loadData();
  }

  void goToDetailPage(ShortPlayResponseList shortPlayResponseList) {
    int? id = shortPlayResponseList.id;
    if (id == null) return;
    int? episodeNum = shortPlayResponseList.episodeNum ?? 1;
    AppNavigator.startDetailsPage(DetailsOptions(
      businessId: id,
      playerEpisodeIndex: episodeNum - 1,
      scene: "collections",
      from: EventValue.fromDiscoverMore,
    ));
  }

  @override
  void onClose() {
    shortWatchHistoryEventShortSubscription?.cancel();
    refreshController.dispose();

        // 释放所有模块资源
    for (var item in shortPlayResponseList.value!) {
      if (item.adModel != null && item.adModel is AdAmNativeModel) {
        (item.adModel as AdAmNativeModel).safeDispose();
        item.adModel = null;
      }
    }

    super.onClose();
  }
}

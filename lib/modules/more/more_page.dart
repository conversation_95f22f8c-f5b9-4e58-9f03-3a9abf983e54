import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/loading/ffloading.dart';
import 'package:playlet/components/nav_bar/ffnav_bar.dart';
import 'package:playlet/components/ui/short_tag_view.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/more/more_controller.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_native_model.dart';
import 'package:playlet/utils/track_event.dart';

class MorePage extends GetView<MoreController> {
  const MorePage({super.key});

  Widget _buildTags(List<String> tags) {
    return tags.isEmpty ? Container() : ShortTagView(tags: tags);
  }

  Widget _buildListItem(ShortPlayResponseList shortPlayResponseList) {
    return GestureDetector(
      onTap: () {
        controller.goToDetailPage(shortPlayResponseList);
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: 16.sp),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 16.sp),
            _buildShortImage(shortPlayResponseList),
            SizedBox(width: 10.sp),
            _buildShortContent(shortPlayResponseList),
            SizedBox(width: 16.sp),
          ],
        ),
      ),
    );
  }

  Widget _buildRankListItem(ShortPlayResponseList shortPlayResponseList, int index) {
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: shortPlayResponseList.id?.toString() ?? "",
      TrackEvent.scene: EventValue.fromDiscoverMore,
      TrackEvent.module_name: controller.bannerData?.title ?? "",
      TrackEvent.module_id: controller.bannerData?.bannerId?.toString() ?? "",
    });

    return GestureDetector(
      onTap: () {
        controller.goToDetailPage(shortPlayResponseList);
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: 16.sp),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 51.sp,
              child: Center(
                child: Text(index <= 99 ? "$index" : "",
                    style: TextStyle(
                      fontSize: index <= 9 ? 22.sp : 21.sp,
                      fontWeight: FontWeight.w700,
                      color: index <= 3 ? const Color(0xFFFFC001) : const Color(0xFF999999),
                    )),
              ),
            ),
            _buildShortImage(shortPlayResponseList),
            SizedBox(width: 10.sp),
            _buildShortContent(shortPlayResponseList),
            SizedBox(width: 16.sp),
          ],
        ),
      ),
    );
  }

  Widget _buildShortImage(ShortPlayResponseList shortPlayResponseList) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(4.sp),
      child: CoverWidget(
        width: 86.sp,
        height: 114.sp,
        fit: BoxFit.cover,
        imageUrl: "${shortPlayResponseList.picUrl}",
        compress: false,
      ),
    );
  }

  Widget _buildShortContent(ShortPlayResponseList shortPlayResponseList) {
    return Expanded(
      child: SizedBox(
        height: 114.sp,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "${shortPlayResponseList.shortPlayName}",
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 6.sp),
            _buildTags(shortPlayResponseList.displayTags),
            SizedBox(height: 6.sp),
            Text(
              "${shortPlayResponseList.recommendContent}",
              style: TextStyle(
                fontSize: 11.sp,
                fontWeight: FontWeight.w400,
                height: 1.5,
                color: const Color(0xFF9F9FA2),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 5.sp),
            Text(
              formatUpdateEpisodeText(shortPlayResponseList),
              style: TextStyle(
                fontSize: 11.sp,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF9F9FA2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String formatUpdateEpisodeText(shortData) {
    int updateEpisode = shortData.updateEpisode ?? 1;
    int totalEpisodes = shortData.totalEpisodes ?? 1;
    if (updateEpisode >= totalEpisodes) {
      return AppTrans.videoEpisodeAll(totalEpisodes);
    }
    return AppTrans.videoEpisodeUpdateProgress(updateEpisode, totalEpisodes);
  }

  Widget _buildAdItem(ShortPlayResponseList? item) {
    return FutureBuilder<Widget?>(
      future: _loadAdView(item),
      builder: (context, snapshot) {
        final adView = snapshot.data;
        if (snapshot.hasData && adView != null) {
          // 成功加载广告Widget且不为null时才展示
          return Container(
            width: double.infinity,  // 确保宽度占满父容器
            padding: EdgeInsets.only(left: 16.sp, right: 16.sp, bottom: 16.sp),
            child: Center(child: adView),
          );
        } else {
          // 加载中、加载失败或返回null时不显示任何内容
          return const SizedBox(height: 0.0);
        }
      },
    );
  }

    Future<Widget?> _loadAdView(ShortPlayResponseList? item) async {

    if (item?.adModel != null && (item?.adModel is AdAmNativeModel)) {
      return await AdManager().getNativeAdViewForAdModel(AdScene.listFeed, (item?.adModel as AdAmNativeModel));
    }
    // 先获取广告模型
    final adModel = await AdManager().getAdModel(AdScene.listFeed);
    if (adModel == null) {
      return null;
    }
    // 将广告模型转换为原生广告模型并存储
    if (adModel is AdAmNativeModel) {
      item?.adModel = adModel;
      // 使用存储的模型获取广告视图
      return await AdManager().getNativeAdViewForAdModel(AdScene.listFeed, adModel);
    }
    return null;
  } 


  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black,
        appBar: FFNavBar(
          showBackIcon: true,
          title: controller.bannerData?.title ?? "",
        ),
        body: Obx(() => _buildContent()));
  }

  Widget _buildContent() {
    if (controller.loading.value) {
      return const FFLoadingWidget();
    } else if (controller.shortPlayResponseList.value == null) {
      return EmptyWidget(pageFrom: EmptyPageFrom.more, type: EmptyType.noNetwork, onRefresh: () => {controller.loadData()});
    } else {
      return EasyRefresh.builder(
          refreshOnStart: false,
          controller: controller.refreshController,
          header: BuilderHeader(
            builder: (context, state) {
              if (state.mode == IndicatorMode.inactive || state.mode == IndicatorMode.done) {
                return const SizedBox();
              }
              return Assets.loading.dotLoading.image(
                width: 50.sp,
                height: 50.sp,
              );
            },
            triggerOffset: 50.sp,
            clamping: false,
            position: IndicatorPosition.above,
          ),
          onRefresh: () {
            controller.loadData(isRefresh: true);
          },
          triggerAxis: Axis.vertical,
          childBuilder: (context, physics) {
            return ListView.builder(
              physics: physics,
              padding: EdgeInsets.symmetric(vertical: 16.sp),
              itemCount: controller.shortPlayResponseList.value?.length ?? 0,
              itemBuilder: (context, index) {
                List<Widget> itemChildren = [];
                final item = controller.shortPlayResponseList.value?[index];
                if (item == null) {
                  return const SizedBox();
                }
                if (index == 3 || (index > 3 && (index - 3) % 5 == 0)) {
                  itemChildren.add(_buildAdItem(item));
                }
                itemChildren.add(controller.style == HomeModuleStyle.rankingList ? _buildRankListItem(item!, index + 1) : _buildListItem(item!));
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: itemChildren,
                );
              },
            );
          });
    }
  }
}

import 'package:intl/intl.dart' as intl;
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/service/app_device_service.dart';

class SubscriptionUtils {
  SubscriptionUtils._();

  static String getSubscriptionTypeDescription(
    int? type,
  ) {
    /// 周卡
    if (type == 5) return AppTrans.subscriptionWeekly();

    /// 月卡
    if (type == 6) return AppTrans.monthlySubscription();

    /// 年卡
    if (type == 7) return AppTrans.annualSubscription();

    return '';
  }

  static String getSubscriptionTypeShortDescription(
    int? type,
  ) {
    /// 周卡
    if (type == 5) return AppTrans.week();

    /// 月卡
    if (type == 6) return AppTrans.month();

    /// 年卡
    if (type == 7) return AppTrans.year();
    return '';
  }

  static String getSubscriptionDetailDescription(
    UserResponse? userInfo,
  ) {
    if (userInfo?.isSubscription == true) {
      String endDate = formatSubcriptionEndDate(userInfo?.subscriptionEndTime);
      return '${AppTrans.expirationDate()}: $endDate';
    }
    return '';
  }

  static String getSubscriptionButtonDescription(
    UserResponse? userInfo,
  ) {
    if (userInfo?.isSubscription == true) {
      return AppTrans.subscribed();
    } else {
      return AppTrans.go();
    }
  }

  static bool getSubscriptionButtonIsDisable(
    UserResponse? userInfo,
  ) {
    if (userInfo?.isSubscription == true) {
      return false;
    } else {
      return true;
    }
  }

  static bool isExpiration(int? expirationDate) {
    if (expirationDate == null) return true;
    if (expirationDate < DateTime.now().millisecondsSinceEpoch) {
      return true;
    }
    return false;
  }

  static String formatSubcriptionEndDate(int? endDate) {
    if (endDate == null) return '';
    DateTime date = DateTime.fromMillisecondsSinceEpoch(endDate);

    try {
      final dateFormatter = intl.DateFormat(
        'yyyy/MM/dd HH:mm',
        AppDeviceService.instance.getSystemLanguage(),
      );
      return dateFormatter.format(date);
    } catch (e) {
      // 捕捉到异常，使用en_US格式代替
      // 因为有些设备的系统语言可能不支持yyyy/MM/dd HH:mm格式
      final dateFormatter = intl.DateFormat(
        'yyyy/MM/dd HH:mm',
        "en_US",
      );
      return dateFormatter.format(date);
    }
  }
}

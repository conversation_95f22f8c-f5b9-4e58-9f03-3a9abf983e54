import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:playlet/config/index.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/profile/profile_controller.dart';
import 'package:playlet/modules/profile/widget/header.dart';
import 'package:playlet/modules/profile/widget/item.dart';
import 'package:playlet/modules/profile/widget/subscription.dart';
import 'package:playlet/modules/resource_bit/banner/banner_view.dart';

class ProfilePage extends GetView<ProfileController> {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: ListView(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 30, 16, 0),
              child: Obx(
                () => ProfileHeader(
                  isLogin: controller.userService.isLogin,
                  userInfo: controller.userService.userInfo.value,
                  metaLoginBonus:
                      controller.userService.metaLoginBonus.value ?? '',
                  onLogin: controller.toLogin,
                  onTopUp: controller.toTopUp,
                  onTapUid: controller.onTapUid,
                ),
              ),
            ),
            Obx(() {
              if (controller.bannerController.bannerList.isNotEmpty) {
                return Padding(
                  padding: EdgeInsets.only(top: 14.sp, left: 16.sp, right: 16.sp),
                  child: ResourceBitBannerView(
                    tag: controller.bannerControllerTag,
                    height: 75.sp,
                  ),
                );
              } else {
                return const SizedBox(width: 0, height: 0,);
              }
            }),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Obx(
                () => (controller.userService.userInfo.value?.hasSubscription ?? false) ==
                        true
                    ? ProfileSubscription(
                        userInfo: controller.userService.userInfo.value,
                  onSubscript: controller.toSubscription,
                      )
                    : const SizedBox(),
              ),
            ),
            Column(
              children: [
                Obx(() {
                  return ProfileItem(
                    icon: Assets.profile.rewards.path,
                    title: AppTrans.rewards(),
                    onTap: controller.toRewards,
                    rewardsBonus: controller.userService.rewardsTotalBonus.value,
                  );
                }),
                ProfileItem(
                  icon: Assets.profile.language.path,
                  title: AppTrans.language(),
                  onTap: controller.toLanguage,
                ),
                ProfileItem(
                  icon: Assets.profile.feedback.path,
                  title: AppTrans.feedback(),
                  onTap: controller.toFeedback,
                ),
                ProfileItem(
                  icon: Assets.profile.setting.path,
                  title: AppTrans.settings(),
                  onTap: controller.toSetting,
                ),
                // 调试页面入口，仅在测试阶段开启
                if (!Config.isProduction)
                  ProfileItem(
                    icon: Assets.profile.setting.path,
                    title: "调试",
                    onTap: controller.toDebug,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/log/upload/ff_log_upload.dart';
import 'package:playlet/components/alert/subscription.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/profile/utils/subscription.dart';
import 'package:playlet/modules/resource_bit/banner/banner_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/resource_bit_manager.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../common/event/event_value.dart';
import '../../common/event/track_event.dart';
import '../../common/utils/screen_utils.dart';
import '../../service/payment/payment_events.dart';
import '../../service/user_service.dart';

class ProfileController extends GetxController {
  static String get _tag => 'ProfileController';
  String get bannerControllerTag => 'profile';

  final UserService userService = Get.find<UserService>();

  late DateTime tapTime = DateTime.now();

  ResourceBitBannerController get bannerController {
    if (!Get.isRegistered<ResourceBitBannerController>(
        tag: bannerControllerTag)) {
      return Get.put(ResourceBitBannerController(), tag: bannerControllerTag);
    }
    return Get.find<ResourceBitBannerController>(tag: bannerControllerTag);
  }

  StreamSubscription<List<ResourceBitModel>?>? _resourceBitStreamSubscription;

  @override
  void onInit() {
    super.onInit();
    _addResourceBitChangedListener();
    // 加载数据
    unawaited(_loadBannerData());
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  @override
  void onClose() {
    _removeResourceBitChangedListener();
    super.onClose();
  }

  Future<void> onTapUid() async {
    if (DateTime.now().difference(tapTime).inSeconds > 2) {
      if (userService.userInfo.value?.userId != null) {
        await Clipboard.setData(
          ClipboardData(
            text: userService.userInfo.value!.userId!,
          ),
        );
        Get.toast(AppTrans.succeeded());
      }
      tapTime = DateTime.now();
    }
  }

  Future<void> _loadBannerData() async {
    try {
      bannerController.bannerList.value = [];
      bannerController.currentIndex.value = 0;

      final service = _getResourceBitService();
      if (service == null) {
        return;
      }
      final models = service.resourceBitModels;
      if (models == null || models.isEmpty) {
        return;
      }
      FFLog.debug('开始获取我的页面横幅资源位数据', tag: _tag);
      final initializedModel = await service.getResourceModel();
      final placeholderImage = Assets.resourcebit.bannerPlaceholder.image();

      List<ResourceBitBannerItem> bannerItems = [];
      int initializedIndex = -1;
      for (var model in models) {
        String? imageUrl = service.getResourceImageUrl(model);
        if (imageUrl == null || imageUrl.isEmpty) {
          continue;
        }
        if (model == initializedModel) {
          initializedIndex = bannerItems.length;
        }

        final item = ResourceBitBannerItem(
          model: model,
          widget: CoverWidget(
            imageUrl: imageUrl,
            width: 361.sp,
            height: 75.sp,
            fit: BoxFit.cover,
            errorWidget: (context, url, error) => placeholderImage,
          ),
          onTap: () {
            _onProfileResourceBitBannerClick(model);
          },
        );
        bannerItems.add(item);
      }
      bannerController.bannerList.value = bannerItems;
      if (initializedIndex >= 0) {
        bannerController.currentIndex.value = initializedIndex;

        if (initializedModel != null) {
          await service.onShown(initializedModel);
        }
      }
    } catch (e) {
      FFLog.debug('获取我的页面横幅资源位数据源失败: $e', tag: _tag);
    }
  }

  ResourceBitBaseService? _getResourceBitService() {
    final service = ResourceBitManager.getInstance()
        .getService(ResourceBitScene.myProfileBanner);
    return service;
  }

  void _addResourceBitChangedListener() {
    final service = _getResourceBitService();
    if (service == null) {
      return;
    }
    _removeResourceBitChangedListener();
    _resourceBitStreamSubscription = service.modelsStream.listen((models) {
      unawaited(_loadBannerData());
    });
  }

  void _removeResourceBitChangedListener() {
    _resourceBitStreamSubscription?.cancel();
    _resourceBitStreamSubscription = null;
  }

  void _onProfileResourceBitBannerClick(ResourceBitModel model) {
    model.onResourceBitClick(ResourceBitScene.myProfileBanner);
  }

  void toLogin() {
    Get.loginDialog(
      from: EventValue.login_page,
      fbRewardsTrackFrom: EventValue.profileLoginButton,
    );
  }

  void toTopUp() {
    AppNavigator.startStorePage();
  }

  Future<void> toRewards() async {
    AppNavigator.startRewardsPage(from: TrackEvent.profileReward);
  }

  void toLanguage() {
    AppNavigator.startLanguagePage();
  }

  Future<void> toSubscription() async {
    ///未登录=> 订阅页面
    ///登录/未订阅=> 订阅页面
    ///登录/已订阅=> 订阅详情
    ///登录/订阅黑名单=> 黑名单弹窗
    ///登录/订阅已过期=> 过期弹窗
    // await Get.find<UserService>().fetchUserInfo();

    if (userService.userInfo.value?.isSubscription == null) {
      ///展示订阅页面
      AppNavigator.startSubscriptionPage(from: "mine");
      return;
    }

    if (userService.userInfo.value?.isSubscription == false) {
      ///展示订阅页面
      AppNavigator.startSubscriptionPage(from: "mine");
      return;
    }

    if (userService.userInfo.value?.isSubscriptionBlack == true) {
      /// 展示黑名单弹窗
      showSubscriptionBlockAlert();
      return;
    }

    if (SubscriptionUtils.isExpiration(
            userService.userInfo.value?.subscriptionEndTime) ==
        true) {
      /// 展示过期弹窗
      String subscriptionDesc =
          SubscriptionUtils.getSubscriptionTypeDescription(
        userService.userInfo.value?.subscriptionType,
      );

      PaymentEvent.submitRechargeShow(
        strScene: EventValue.subscribeExpirePopup,
        reelId: "",
        episode: "",
        action: "",
        lockBegin: "",
        playDirection: ScreenUtils.isLandscape(Get.context!)
            ? EventValue.horizontal
            : EventValue.vertical,
      );
      showSubscriptionExpiredAlert(subscriptionDesc: subscriptionDesc);

      return;
    }

    ///展示订阅页面
    AppNavigator.startSubscriptionPage(from: "mine");
  }

  void toFeedback() {
    FFLogUploader().uploadLogs();
    AppNavigator.startFeedbackPage();
  }

  void toSetting() {
    AppNavigator.startSettingPage();
  }

  void toDebug() {
    AppNavigator.startDebugPage();
  }
}

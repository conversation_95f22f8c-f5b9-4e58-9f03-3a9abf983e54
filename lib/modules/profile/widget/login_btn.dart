import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/base/button.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/date_extension.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:super_tooltip/super_tooltip.dart';

const String _kLoginTipShowTimestamp = 'kLoginTipShowTimestamp';

class ProfileLoginButton extends StatefulWidget {
  const ProfileLoginButton({
    super.key,
    required this.onLogin,
    required this.bonus,
  });
  final VoidCallback onLogin;
  final String bonus;

  @override
  State<ProfileLoginButton> createState() => _ProfileLoginButtonState();
}

class _ProfileLoginButtonState extends State<ProfileLoginButton> {
  final SuperTooltipController tipController = SuperTooltipController();
  Timer? _timer;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((value) {
      if (_checkShouldShowLoginTips() == true) {
        _showLoginTips();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    tipController.dispose();
    _cancelTimer();
    super.dispose();
  }

  bool _checkShouldShowLoginTips() {
    UserService userService = Get.find<UserService>();

    /// 判断：已登录不展示
    if (userService.isLogin == true) {
      return false;
    }

    /// 判断：今天已展示过就不展示
    int? lastTimestamp = SafeStorage().read<int?>(_kLoginTipShowTimestamp);
    if (lastTimestamp != null) {
      DateTime lastDate = DateTime.fromMillisecondsSinceEpoch(lastTimestamp);
      if (lastDate.isToday() == true) {
        return false;
      }
    }
    return true;
  }

  Future<void> _showLoginTips() async {
    await SafeStorage()
        .write(_kLoginTipShowTimestamp, DateTime.now().millisecondsSinceEpoch);
    tipController.showTooltip();
    _startTimer();
  }

  void _hideLoginTips() {
    tipController.hideTooltip();
  }

  void _startTimer() {
    _cancelTimer();
    _timer = Timer(const Duration(seconds: 4), () {
      _hideLoginTips();
      _cancelTimer();
    });
  }

  void _cancelTimer() {
    if (_timer != null) {
      if (_timer!.isActive == true) {
        _timer!.cancel();
      }
      _timer = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SuperTooltip(
      popupDirection: TooltipDirection.up,
      controller: tipController,
      showBarrier: false,
      showOnTap: false,
      hasShadow: false,
      arrowLength: 0.0,
      arrowBaseWidth: 0.0,
      arrowTipDistance: 0.0,
      minimumOutsideMargin: 33.sp,
      overlayDimensions: EdgeInsets.zero,
      bubbleDimensions: EdgeInsets.zero,
      hideTooltipOnBarrierTap: false,
      backgroundColor: Colors.transparent,
      shadowColor: Colors.transparent,
      fadeInDuration: const Duration(seconds: 1),
      fadeOutDuration: const Duration(seconds: 1),
      sigmaX: 0.0,
      sigmaY: 0.0,
      borderRadius: 0.0,
      borderWidth: 0.0,
      borderColor: Colors.transparent,
      content: Padding(
        padding: EdgeInsets.only(bottom: 14.sp),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 7.sp, vertical: 2.sp),
              margin: EdgeInsets.zero,
              decoration: const BoxDecoration(
                color: Color(0xFFFF4500),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Assets.login.fbIconBlue.image(
                    width: 13.sp,
                    height: 13.sp,
                  ),
                  SizedBox(width: 4.sp),
                  Text(
                    /// TODO: 需替换成多语言
                    // '+${widget.bonus} bonus for first login',
                    '+${widget.bonus} ${AppTrans.bonusForFirstLogin()}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 11.sp,
                      fontWeight: FontWeight.w400,
                      height: 1.35,
                    ),
                  ),
                ],
              ),
            ),
            Assets.triangleSmall.image(
              width: 4,
              height: 5,
            ),
          ],
        ),
      ),
      child: FFButton.text(
        isMaxWidth: false,
        text: AppTrans.login(),
        textStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
        backgroundColor: const Color(0xFF1A1A1A),
        shape: StadiumBorder(
          side: BorderSide(
            color: const Color(0xFF444444),
            width: 0.88.w,
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 17.5.sp, vertical: 6.sp),
        onPressed: widget.onLogin,
      ),
    );
  }
}

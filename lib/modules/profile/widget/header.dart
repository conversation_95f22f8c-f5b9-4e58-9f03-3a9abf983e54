import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/base/button.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/modules/profile/widget/login_btn.dart';

class ProfileHeader extends StatelessWidget {
  const ProfileHeader({
    super.key,
    this.userInfo,
    required this.metaLoginBonus,
    required this.isLogin,
    required this.onLogin,
    required this.onTopUp,
    required this.onTapUid,
  });

  final UserResponse? userInfo;
  final String metaLoginBonus;
  final bool isLogin;
  final VoidCallback onLogin;
  final VoidCallback onTopUp;
  final VoidCallback onTapUid;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1B1B1B),
        borderRadius: BorderRadius.circular(12.0.r),
      ),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF1E1E1E),
              borderRadius: BorderRadius.circular(12.r),
            ),
            padding: EdgeInsets.fromLTRB(14.sp, 20.sp, 16.sp, 10.sp),
            child: Column(
              children: [
                Row(
                  children: [
                    _buildAvatar(),
                    SizedBox(width: 6.w),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildName(),
                          _buildUID(),
                        ],
                      ),
                    ),
                    _buildLogin(),
                  ],
                ),
                SizedBox(height: 26.sp),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildMyWallet(),
                    _buildTopUp(),
                  ],
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.sp, vertical: 10.sp),
            child: Row(
              children: [
                Expanded(
                  child: _buildCoinsOrBonus(
                    icon: Assets.profile.coins.path,
                    name: AppTrans.coins(),
                    number: userInfo?.coins ?? 0,
                  ),
                ),
                SizedBox(width: 40.sp),
                Expanded(
                  child: _buildCoinsOrBonus(
                    icon: Assets.profile.bonus.path,
                    name: AppTrans.bonus(),
                    number: userInfo?.bonus ?? 0,
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 64.sp,
      height: 64.sp,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        border: Border.all(
          color: const Color(0xFF434343),
          width: 1.45.sp,
        ),
      ),
      child: userInfo?.headPic == null || userInfo!.headPic!.isEmpty
          ? Assets.profile.avatar.image()
          : ClipOval(
              child: CachedNetworkImage(
                imageUrl: userInfo!.headPic!,
                placeholder: (context, url) => Assets.profile.avatar.image(),
                errorListener: (value) {
                  FFLog.error(value, tag: "ProfileHeaderWidget");
                },
                fit: BoxFit.cover,
              ),
            ),
    );
  }

  Widget _buildName() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 6.w),
      child: Text(
        (userInfo?.nickName != null && userInfo!.nickName!.isNotEmpty)
            ? userInfo!.nickName!
            : userInfo?.firebaseSource != null && userInfo?.firebaseSource == 80
                ? "${AppTrans.user()}_${userInfo?.userId}"
                : AppTrans.guest(),
        style: TextStyle(
          fontSize: 19.sp,
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildUID() {
    return SizedBox(
      height: 20.h,
      child: ElevatedButton(
        onPressed: onTapUid,
        style: ButtonStyle(
          padding: WidgetStatePropertyAll(
              EdgeInsets.symmetric(horizontal: 6.w, vertical: 0.0)),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          visualDensity: VisualDensity.compact,
          shape: WidgetStatePropertyAll(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.r))),
          alignment: Alignment.centerLeft,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Flexible(
              child: Text(
                'Uid: ${userInfo?.userId ?? ''}',
                style: const TextStyle(
                  fontSize: 10.0,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ),
            userInfo?.userId == null
                ? const SizedBox.shrink()
                : SizedBox(width: 4.sp),
            userInfo?.userId == null
                ? const SizedBox.shrink()
                : Assets.profile.copy.image(width: 14.sp, height: 14.sp),
          ],
        ),
      ),
    );
  }

  Widget _buildLogin() {
    return isLogin == true
        ? const SizedBox.shrink()
        : ProfileLoginButton(
            onLogin: onLogin,
            bonus: metaLoginBonus,
          );
  }

  Widget _buildMyWallet() {
    return Text(
      AppTrans.myWallet(),
      style: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: Colors.white,
      ),
    );
  }

  Widget _buildTopUp() {
    return FFButton.text(
      text: AppTrans.topUp(),
      textStyle: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: const Color(0xFF171717),
      ),
      isMaxWidth: false,
      padding: EdgeInsets.symmetric(horizontal: 18.82.w, vertical: 6.h),
      onPressed: onTopUp,
    );
  }

  Widget _buildCoinsOrBonus({
    required final String icon,
    required final String name,
    required final int number,
  }) {
    return Row(
      children: [
        Image.asset(
          icon,
          width: 14.w,
          height: 14.w,
        ),
        SizedBox(width: 4.w),
        Text(
          name,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: Colors.white,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Align(
            alignment: Alignment.center,
            child: Text(
              number.abs().toString(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

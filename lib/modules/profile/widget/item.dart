import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/i18n/trans.dart';

import '../../../gen/assets.gen.dart';

class ProfileItem extends StatelessWidget {
  const ProfileItem({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.rewardsBonus,
    this.paddingBotton = 4.0,
  });

  final double paddingBotton;
  final String icon;
  final String title;
  final int? rewardsBonus;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: paddingBotton),
      child: FilledButton(
        onPressed: onTap,
        style: ButtonStyle(
          padding: WidgetStatePropertyAll(
            EdgeInsets.symmetric(horizontal: 18.sp, vertical: 16.sp),
          ),
          shape: const WidgetStatePropertyAll(RoundedRectangleBorder()),
          backgroundColor: const WidgetStatePropertyAll(Colors.transparent),
        ),
        child: Row(
          children: [
            Image.asset(icon, width: 24.sp, height: 24.sp),
            SizedBox(width: 14.0.sp),
            Expanded(
              child: Row(
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.0.sp,
                      fontWeight: FontWeight.w400,
                      color: Colors.white,
                    ),
                  ),
                  if (rewardsBonus != null && rewardsBonus! > 0)
                    buildRewardsTag(rewardsBonus)
                ],
              ),
            ),
            SizedBox(width: 14.0.sp),
            Icon(
              Icons.arrow_forward_ios_rounded,
              size: 20.sp,
              color: const Color(0xFF999999),
            ),
          ],
        ),
      ),
    );
  }

  Container buildRewardsTag(int? rewardsBonus) {
    return Container(
      margin: EdgeInsets.only(left: 10.w),
      padding: EdgeInsets.only(
        left: 6.w,
        right: 8.w,
        top: 2.h,
        bottom: 5.h,
      ),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.rewards.imgRewardsUserTagBg.path),
          fit: BoxFit.fill,
        ),
      ),
      child: Center(
        child: Text(
          AppTrans.getBonus(rewardsBonus.toString()),
          style: TextStyle(
            color: Colors.white,
            fontSize: 11.sp,
            fontFamily: "Poppins",
            fontStyle: FontStyle.italic,
            fontWeight: FontWeight.w500,
            height: 1.35,
            // 对应 line-height: 135%
            letterSpacing: 0, // 对应 letter-spacing: 0%
          ),
          textAlign: TextAlign.center, // 对应 text-align: center
        ),
      ),
    );
  }
}

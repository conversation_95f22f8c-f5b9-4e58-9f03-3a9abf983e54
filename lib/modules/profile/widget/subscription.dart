import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/components/base/gradient_border.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/modules/profile/utils/subscription.dart';

class ProfileSubscription extends StatelessWidget {
  const ProfileSubscription({
    super.key,
    required this.onSubscript,
    this.userInfo,
  });
  final UserResponse? userInfo;
  final VoidCallback onSubscript;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onSubscript,
      child: Container(
        height: 66.sp,
        decoration: BoxDecoration(
          color:
              (userInfo?.isSubscription ?? false) == true ? null : const Color(0xFF171713),
          borderRadius: BorderRadius.circular(12.r),
          border: _buildGradientBorder(),
          gradient: (userInfo?.isSubscription ?? false) == true
              ? const LinearGradient(
                  // begin: Alignment.centerLeft,
                  // end: Alignment.center,
                  colors: [
                    Color(0xFF4E3412),
                    Color(0xFF231815),
                  ],
                )
              : null,
        ),
        child: _buildLayout(),
      ),
    );
  }

  GradientBoxBorder _buildGradientBorder() {
    return GradientBoxBorder(
      width: 1.2.sp,
      gradient: const LinearGradient(
        colors: [
          Color(0xFFAA853D),
          Color(0xFFFFEBC2),
          Color(0xFF825B0A),
        ],
      ),
    );
  }

  Widget _buildLayout() {
    return Row(
      children: [
        _buildIcon(),
        SizedBox(width: 4.sp),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildTitle(),
              _buildDetail(),
            ],
          ),
        ),
        _buildAction(),
      ],
    );
  }

  Widget _buildIcon() {
    if ((userInfo?.isSubscription ?? false) == true) {
      return Padding(
        padding: EdgeInsets.only(left: 28.sp),
        child: Assets.store.imgSubscriptionVipBig.image(
          width: 66.sp,
          height: 66.sp,
        ),
      );
    } else {
      return Padding(
        padding: EdgeInsets.only(left: 10.sp),
        child: Assets.profile.vip.image(width: 36.sp, height: 36.sp),
      );
    }
  }

  Widget _buildTitle() {
    return ShaderMask(
      shaderCallback: (rect) => const LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.center,
        colors: [
          Color(0xFFFF9937),
          Color(0xFFFBE840),
        ],
      ).createShader(rect),
      child: (userInfo?.isSubscription ?? false) == true
          ? Padding(
              padding: EdgeInsets.only(left: 20.sp),
              child: Text(
                SubscriptionUtils.getSubscriptionTypeDescription(
                  userInfo?.subscriptionType,
                ),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          : Padding(
              padding: EdgeInsets.only(left: 4.sp),
              child: Text(
                AppTrans.joinVipTitleDesc(),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
    );
  }

  Widget _buildDetail() {
    return (userInfo?.isSubscription ?? false) == true
        ? Padding(
            padding: EdgeInsets.only(left: 20.sp, top: 4.sp),
            child: Text(
              SubscriptionUtils.getSubscriptionDetailDescription(userInfo),
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                color: const Color(0xFFE8E8E8),
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          )
        : Padding(
            padding: EdgeInsets.only(left: 4.sp),
            child: Text(
              AppTrans.joinVipDetailDesc(),
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                color: const Color(0xFFE8E8E8),
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          );
  }

  Widget _buildAction() {
    return (userInfo?.isSubscription ?? false) == true
        ? const SizedBox.shrink()
        : Padding(
            padding: EdgeInsets.only(left: 12.sp, right: 16.sp),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 17.sp, vertical: 4.sp),
              decoration: ShapeDecoration(
                shape: StadiumBorder(
                  side: BorderSide(
                    color: const Color(0xFFFFFFFF).withValues(alpha: 0.6),
                    width: 0.7.sp,
                  ),
                ),
                color: const Color(0xFFFFCD00),
              ),
              child: Text(
                SubscriptionUtils.getSubscriptionButtonDescription(userInfo),
                style: TextStyle(
                  fontSize: 14.0.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF171717),
                ),
              ),
            ),
          );
  }
}

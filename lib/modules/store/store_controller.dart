import 'dart:async';
import 'dart:io';

import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/components/alert/remind.dart';
import 'package:playlet/model/store/store_product_result.dart';
import 'package:playlet/service/good_review_service.dart';
import 'package:playlet/service/payment/payment_service.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../common/log/ff_log.dart';
import '../../service/payment/payment_model.dart';
import '../../service/user_service.dart';
import '../../utils/limited_time.dart';

enum LoadingStatus {
  loading,
  success,
  failed,
}

class StoreController extends GetxController {
  // var isSelect = true.obs;
  var isShowTimeProduct = false.obs;
  var countDown = 0.obs;

  var productList = <SkuInfoResponses>[].obs;
  var subscribeProductList = <SubscribeSkuResponses>[].obs;
  final PaymentService paymentService = Get.find<PaymentService>();
  late Rx<LoadingStatus> loadingStatus = LoadingStatus.loading.obs;
  var userServices = Get.find<UserService>();
  Timer? _timer; // 声明 Timer 变量

  var firstSku = SkuInfoResponses().obs;

  final GoodReviewService goodReviewService = Get.find<GoodReviewService>();
  bool isPaySuc = false;

  @override
  void onInit() {
    super.onInit();
    initData();
  }

  @override
  void onReady() {
    super.onReady();
    paymentService.startRecover(from: EventValue.topUp);
  }

  initData() async {
    await paymentService.getProductList();
    await paymentService.refreshUserBalance();

    isShowTimeProduct.value = LimitedTime.getPopupShow();
    FFLog.debug("倒计时   ${isShowTimeProduct.value}");
    if (isShowTimeProduct.value) {
      countDown.value = LimitedTime.getLeftTime();
      FFLog.debug("倒计时   ${countDown.value}");
    }

    var skuList = paymentService.storeProductResult.value.skuInfoResponses;
    var retainSkuInfoResponses =
        paymentService.storeProductResult.value.retainSkuInfoResponses;

    if (skuList != null) {
      productList.clear();
      skuList.sort((a, b) => a.skuType.compareTo(b.skuType));
      productList.addAll(skuList);
      if (paymentService.storeProductResult.value.skuPositionType == 1 &&
          retainSkuInfoResponses != null &&
          isShowTimeProduct.value) {
        FFLog.debug("插入膨胀商品   ${retainSkuInfoResponses.toJson()}");
        productList.insert(1, retainSkuInfoResponses);
      }
    }

    var subList = paymentService.storeProductResult.value.subscribeSkuResponses;

    if (subList != null) {
      subscribeProductList.clear();
      subscribeProductList.addAll(subList);
    }

    if (paymentService.currency.value.isEmpty) {
      await paymentService.loadProducts();
    }

    if (productList.isNotEmpty == true) {
      loadingStatus.value = LoadingStatus.success;
    } else {
      loadingStatus.value = LoadingStatus.failed;
    }

    startCountDown(() async {
      Get.loading();
      await initData();
      Get.dismiss();
    });

    // isSelect.value = userServices.userInfo.value?.autoUnlock ?? true;
  }

  Future<bool> onStartPay({
    required SkuInfoResponses? currItem,
    required bool? isRetain,
    required String strSource,
    required String playDirection,
    int? episode,
    String? lockBegin,
    int? shortPlayCode,
    int? shortPlayId,
    String? resourceBitId,
  }) async {
    if (currItem != null) {
      String? skuId = Platform.isIOS ? currItem.iosSkuId : currItem.gpSkuId;
      String? productId = currItem.skuProductId;
      String? skuModelConfigId = currItem.skuModelConfigId;
      int? skuType = currItem.skuType;
      if (skuId.isNotEmpty && productId.isNotEmpty) {
        String bonus = '';
        if (currItem.skuType == 7) {
          bonus = currItem.keepGiveCoins == null
              ? ''
              : currItem.keepGiveCoins.toString();
        } else {
          bonus = currItem.productGiveCoins.toString();
        }

        final result = await startBuyProduct(
            skuId: skuId,
            productId: productId,
            source: strSource == EventValue.recharge
                ? SourceType.rechargePage
                : SourceType.unlockDialogPage,
            shortPlayCode: shortPlayCode,
            shortPlayId: shortPlayId,
            episode: episode,
            skuModelConfigId: skuModelConfigId,
            isRetain: isRetain,
            skuType: skuType,
            lockBegin: lockBegin,
            strSource: strSource,
            coins: currItem.coins.toString(),
            bonus: bonus,
            amount: currItem.recharge,
            resourceBitId: resourceBitId,
            playDirection: playDirection);
        if (result == true) {
          if (strSource == EventValue.recharge) {
            isPaySuc = true;
          }
          //资产保护提醒弹窗
          _handleProtectProperityRemindAlert();
        }
        return result == true;
      }
    }
    return false;
  }

  Future<bool> startBuyProduct({
    required String skuId,
    required String productId,
    required String skuModelConfigId,
    required String amount,
    required String strSource,
    required String coins,
    required String bonus,
    required String playDirection,
    int? source,
    int? skuType,
    bool? recover,
    int? shortPlayId,
    int? shortPlayCode,
    int? episode,
    bool? isRetain,
    String? lockBegin,
    String? resourceBitId,
  }) async {
    Get.loading();
    var isSuccess = await paymentService.startPurchase(
      skuId: skuId,
      productId: productId,
      source: source,
      recover: recover,
      shortPlayId: shortPlayId,
      shortPlayCode: shortPlayCode,
      episode: episode,
      skuModelConfigId: skuModelConfigId,
      isRetain: isRetain,
      skuType: skuType,
      lockBegin: lockBegin,
      amount: amount,
      strSource: strSource,
      coins: coins,
      bonus: bonus,
      resourceBitId: resourceBitId,
      playDirection: playDirection,
    );

    if (isSuccess && isRetain != null && isRetain) {
      FFLog.debug("关闭限时弹框。$isSuccess");
      LimitedTime.closeThatDayShow();
      await initData();
    }

    FFLog.debug("购买是否完成。$isSuccess");
    Get.dismiss();
    return isSuccess;
  }

  Future<bool> startBuySubscription({
    required String skuId,
    required String productId,
    required String amount,
    required String strSource,
    required String playDirection,
    int? source,
    bool? recover,
    int? shortPlayId,
    int? shortPlayCode,
    int? episode,
    int? skuType,
    String? lockBegin,
    String? resourceBitId,
  }) async {
    Get.loading();
    var isSuccess = await paymentService.startSubscription(
        skuId: skuId,
        productId: productId,
        source: source,
        recover: recover,
        shortPlayId: shortPlayId,
        shortPlayCode: shortPlayCode,
        episode: episode,
        skuType: skuType,
        amount: amount,
        strSource: strSource,
        resourceBitId: resourceBitId,
        playDirection: playDirection,
        lockBegin: lockBegin);

    FFLog.debug("购买是否完成。$isSuccess");

    if (isSuccess) {
      if (strSource == EventValue.recharge) {
        isPaySuc = true;
      }
      await initData();
      //资产保护提醒弹窗
      _handleProtectProperityRemindAlert();
    }
    Get.dismiss();
    return isSuccess;
  }

  void startCountDown(void Function() onCountDownEnd) {
    cancelTimer();
    if (paymentService.storeProductResult.value.skuPositionType == 1 &&
        isShowTimeProduct.value &&
        paymentService.storeProductResult.value.retainSkuInfoResponses !=
            null) {
      // productCount.value = productCount.value + 1;

      _timer = Timer.periodic(const Duration(seconds: 1), (Timer timer) async {
        if (countDown.value > 0) {
          countDown.value = countDown.value - 1;
        } else {
          cancelTimer(); // 停止计时器
          onCountDownEnd();
        }
      });
    }
  }

  void cancelTimer() {
    if (_timer != null) {
      if (_timer!.isActive == true) {
        _timer!.cancel();
      }
      _timer = null;
    }
  }

  @override
  void onClose() {
    cancelTimer();
    super.onClose();
  }

  onBackPressed() async {
    if (isPaySuc) {
      await goodReviewService.onShow();
    }
    Get.back();
  }

  onPopInvokedWithResult(didPop, _) async {
    if (didPop == true) {
      return;
    }
    if (isPaySuc) {
      await goodReviewService.onShow();
    }
    Get.back();
  }

  Future<void> _handleProtectProperityRemindAlert() async {
    //资产保护提醒弹窗
    if (checkShouldShowProtectProperityRemindAlert() == true) {
      bool? result = await showProtectProperityRemindAlert(inDetails: true);
      if (result != null && result == true) {
        await Get.loginDialog(
          from: EventValue.assetProtection,
          fbRewardsTrackFrom: EventValue.assetProtection,
        );
      }
    }
  }
}

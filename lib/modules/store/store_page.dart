import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/components/store/custom_row_widget.dart';
import 'package:playlet/components/store/product_item.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/service/payment/payment_events.dart';
import 'package:playlet/utils/get_extension.dart';

import '../../common/log/ff_log.dart';
import '../../common/utils/screen_utils.dart';
import '../../components/empty/empty.dart';
import '../../components/loading/ffloading.dart';
import '../../components/nav_bar/ffnav_bar.dart';
import '../../components/store/agreement_row_widget.dart';
import '../../components/store/subscribe_item.dart';
import '../../components/store/subscription_agreement_widget.dart';
import '../../gen/assets.gen.dart';
import '../../model/store/store_product_result.dart';
import '../../service/payment/payment_model.dart';
import '../../utils/index.dart';
import 'store_controller.dart';

class StorePage extends StatefulWidget {
  const StorePage({super.key});

  @override
  State<StorePage> createState() => _StorePageState();
}

class _StorePageState extends State<StorePage> {
  final StoreController controller = Get.put(StoreController());

  @override
  void initState() {
    super.initState();
    // 商店页面无数据
    PaymentEvent.submitRechargeShow(
      strScene: "${EventValue.recharge},${EventValue.rechargeSubscribe}",
      reelId: "",
      episode: "",
      action: "other",
      lockBegin: "",
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );
  }

  @override
  void dispose() {
    super.dispose();
    PaymentEvent.submitRechargeShowEnd(
      strScene: "${EventValue.recharge},${EventValue.rechargeSubscribe}",
      reelId: "",
      episode: "",
      action: "other",
      lockBegin: "",
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: controller.onPopInvokedWithResult,
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: FFNavBar(
          showBackIcon: true,
          title: AppTrans.store(),
          onBackPressed: controller.onBackPressed,
        ),
        body: Obx(() {
          if (controller.loadingStatus.value == LoadingStatus.loading) {
            // 加载中
            return const Center(child: FFLoadingWidget());
          }
          if (controller.loadingStatus.value == LoadingStatus.failed) {
            // 加载失败
            return EmptyWidget(
              pageFrom: EmptyPageFrom.topup,
              type: EmptyType.noNetwork,
              onRefresh: () async {
                Get.loading();
                await controller.initData();
                Get.dismiss();
              },
            );
          }
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Obx(() {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      height: 15.sp,
                    ),
                    _buildUserCoin(),
                    SizedBox(
                      height: 26.sp,
                    ),
                    _buildProduct(),
                    // SizedBox(height: 20.h),
                    controller.subscribeProductList.isNotEmpty
                        ? _buildSubscribeTitle()
                        : const SizedBox(),

                    controller.subscribeProductList.isNotEmpty
                        ? _buildSubscribeProduct()
                        : const SizedBox(),

                    // _buildSubscribeProduct(),

                    SizedBox(height: 16.h),

                    SubscriptionAgreementWidget(
                      title: AppTrans.storeSubscription(),
                      content: AppTrans.storeSubscriptionInfo(),
                    ),

                    SizedBox(height: 24.h),

                    AgreementRow(
                      onPrivacyAgreementTap: () {
                        Utils.openPrivacyPolicy();
                      },
                      onUserAgreementTap: () {
                        Utils.openUserAgreement();
                      },
                    ),

                    SizedBox(
                      height: 23.h,
                    ),
                  ],
                );
              }),
            ),
          );
        }),
      ),
    );
  }

  Container _buildSubscribeTitle() {
    return Container(
      padding: EdgeInsets.only(top: 28.h, bottom: 18.h),
      child: Text(
        AppTrans.subscription(),
        style: TextStyle(
            fontSize: 18.sp, color: Colors.white, fontWeight: FontWeight.w600),
      ),
    );
  }

  Obx _buildUserCoin() {
    return Obx(() {
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          CustomRow(
            imagePath: Assets.imgCoinF.path,
            numberText:
                controller.paymentService.userBalance.value.coins.toString(),
          ),
          SizedBox(
            width: 26.w,
          ),
          CustomRow(
            imagePath: Assets.imgCoinB.path,
            coinsText: AppTrans.bonus(),
            numberText:
                controller.paymentService.userBalance.value.bonus.toString(),
          ),
        ],
      );
    });
  }

  GridView _buildProduct() {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 12.sp,
        crossAxisSpacing: 16.sp,
        childAspectRatio: 172 / 86,
      ),
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: controller.productList.length,
      itemBuilder: (BuildContext context, int index) {
        var currItem = controller.productList[index];
        if (currItem.skuType == 0) {
          return ProductFirstItemWith(
            currItem,
            controller.paymentService.currency.value,
            currItem.subscript.isNotEmpty == true,
            onTap: () async {
              controller.onStartPay(
                currItem: currItem,
                isRetain: false,
                strSource: EventValue.recharge,
                playDirection: ScreenUtils.isLandscape(Get.context!)
                    ? EventValue.horizontal
                    : EventValue.vertical,
              );
            },
          );
        } else if ((currItem.skuType == 7) &&
            controller.isShowTimeProduct.value) {
          return ProductClockItem(
            currItem,
            controller.paymentService.currency.value,
            controller.countDown,
            onTap: () async {
              controller.onStartPay(
                currItem: currItem,
                isRetain: true,
                strSource: EventValue.payRetainCommodity,
                playDirection: ScreenUtils.isLandscape(Get.context!)
                    ? EventValue.horizontal
                    : EventValue.vertical,
              );
            },
          );
        } else {
          // FFLog.info("显示其余商品。${currItem.toJson()} ");
          try {
            return ProductItem(
                currItem, controller.paymentService.currency.value,
                showDiscountTag: currItem.subscript.isNotEmpty == true,
                onTap: () async {
              controller.onStartPay(
                currItem: currItem,
                isRetain: false,
                strSource: EventValue.recharge,
                playDirection: ScreenUtils.isLandscape(Get.context!)
                    ? EventValue.horizontal
                    : EventValue.vertical,
              );
            });
          } catch (e) {
            return const SizedBox();
          }
        }
      },
    );
  }

  ListView _buildSubscribeProduct() {
    return ListView.builder(
        itemCount: controller.subscribeProductList.length,
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          SubscribeSkuResponses product =
              controller.subscribeProductList[index];

          // 订阅展示埋点
          PaymentEvent.submitSubscribeThingShow(
              product.isFirstBuy ? product.firstAmount : product.payAmount,
              EventValue.recharge,
              product.productId.toString());

          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: SubscribeItem(
              product: product,
              currency: controller.paymentService.currency.value,
              onTap: () {
                // 在这里添加点击事件的逻辑
                String? skuId = product.skuId;
                int? skuType = product.skuType;
                String? productId = product.productId.toString();
                FFLog.debug('发起订阅。skuId $skuId   productId $productId ');
                if (skuId.isNotEmpty) {
                  controller.startBuySubscription(
                    skuId: skuId,
                    productId: productId,
                    source: SourceType.rechargePage,
                    skuType: skuType,
                    amount: product.isFirstBuy
                        ? product.firstAmount
                        : product.payAmount,
                    strSource: EventValue.rechargeSubscribe,
                    playDirection: ScreenUtils.isLandscape(Get.context!)
                        ? EventValue.horizontal
                        : EventValue.vertical,
                  );
                }
              },
            ),
          );
        });
  }
}

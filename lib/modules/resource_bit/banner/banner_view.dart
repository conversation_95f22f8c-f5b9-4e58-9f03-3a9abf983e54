import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/horizontal_banner/swiper.dart';
import 'package:playlet/modules/resource_bit/banner/banner_controller.dart';

class ResourceBitBannerView extends StatefulWidget {
  final String tag;
  final double height;
  
  const ResourceBitBannerView({
    super.key,
    required this.tag,
    this.height = 90,
  });

  @override
  State<ResourceBitBannerView> createState() => _ResourceBitBannerViewState();
}

class _ResourceBitBannerViewState extends State<ResourceBitBannerView> {

  final double _radius = 6.sp;
  final bool _showIndicator = true;
  final Color _indicatorColor = Colors.white.withValues(alpha: 0.4);
  final Color _activeIndicatorColor = const Color(0xFFFFCD00);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ResourceBitBannerController controller = Get.find<ResourceBitBannerController>(tag: widget.tag);

    return Obx(() {
      if (controller.bannerList.isEmpty) {
        return SizedBox(
          width: Get.width,
          height: widget.height,
        );
      }

      return Stack(
        children: [
          SizedBox(
            width: Get.width,
            height: widget.height,
            child: Swiper(
              controller: controller.swiperController,
              initialIndex: controller.currentIndex.value,
              height: widget.height,
              viewportFraction: 1.0,
              autoPlay: controller.isAutoPlay.value,
              loop: controller.bannerList.length > 1,
              autoPlayInterval: Duration(seconds: controller.autoPlayInterval.value),
              onPageChanged: (index) {
                controller.changePage(index);
              },
              onPageScrollStateChange: (state) {
                if (state == ScrollState.dragging) {
                  controller.isUserScrolling.value = true;
                } else {
                  controller.isUserScrolling.value = false;
                }
              },
              children: List.generate(
                controller.bannerList.length,
                (index) {
                  final item = controller.bannerList[index];
                  return GestureDetector(
                    onTap: item.onTap,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(_radius),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(_radius),
                        child: item.widget,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          if (_showIndicator && controller.bannerList.length > 1)
            Positioned(
              right: 16.sp,
              bottom: 10.sp,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: List.generate(
                  controller.bannerList.length,
                  (index) => Container(
                    width: controller.currentIndex.value == index ? 10.0.sp : 4.0.sp,
                    height: 4.0.sp,
                    margin: EdgeInsets.only(left: 4.0.sp),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(1.r),
                      color: controller.currentIndex.value == index
                          ? _activeIndicatorColor
                          : _indicatorColor,
                    ),
                  ),
                ),
              ),
            ),
        ],
      );
    });
  }
}
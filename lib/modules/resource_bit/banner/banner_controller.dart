import 'dart:async';

import 'package:get/get.dart';
import 'package:flutter/widgets.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/horizontal_banner/swiper.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/track_event/resource_bit_track_event.dart';

class ResourceBitBannerItem {
  final ResourceBitModel? model;
  final Widget widget;
  final Function()? onTap;
  
  ResourceBitBannerItem({required this.model, required this.widget, this.onTap});
}

class ResourceBitBannerController extends GetxController {
  final SwiperController swiperController = SwiperController();

  // 轮播图数据列表
  final bannerList = <ResourceBitBannerItem>[].obs;
  
  // 当前页面索引
  final currentIndex = 0.obs;
  
  // 是否自动播放
  final isAutoPlay = true.obs;
  
  // 自动播放间隔时间（秒）
  final autoPlayInterval = 3.obs;
  
  // 是否用户正在手动滑动
  final isUserScrolling = false.obs;

  StreamSubscription? _bannerListSubscription;
  StreamSubscription? _isAutoPlaySubscription;
  StreamSubscription? _isUserScrollingSubscription;

  @override
  void onInit() {
    super.onInit();

    _bannerListSubscription?.cancel();
    _bannerListSubscription = bannerList.listen((list) {
      if (list.length > 1) {
        isAutoPlay.value = true;
      } else {
        isAutoPlay.value = false;
      }
    });
    
    _isAutoPlaySubscription?.cancel();
    _isAutoPlaySubscription = isAutoPlay.listen((auto) {
      _updateAutoPlayStatus();
    });

    _isUserScrollingSubscription?.cancel();
    _isUserScrollingSubscription = isUserScrolling.listen((isUser) {
      _updateAutoPlayStatus();
    });

    _updateAutoPlayStatus();
  }
  
  @override
  void onClose() {
    _bannerListSubscription?.cancel();
    _bannerListSubscription = null;
    _isAutoPlaySubscription?.cancel();
    _isAutoPlaySubscription = null;
    _isUserScrollingSubscription?.cancel();
    _isUserScrollingSubscription = null;
    super.onClose();
    swiperController.stopAutoplay();
  }
  
  // 切换到指定页面
  void changePage(int index) {
    currentIndex.value = index;

    if (index >= bannerList.length) {
      return;
    }
    final item = bannerList[index];
    final model = item.model;
    if (model == null) {
      return;
    }
    ResourceBitTrackEvent.reelShow(model);
  }

  /// 更新自动播放状态
  void _updateAutoPlayStatus() {
    if (isUserScrolling.value) {
      // 如果用户正在手动滑动，则停止自动播放
      swiperController.stopAutoplay();
      return;
    }
    if (!isAutoPlay.value) {
      // 如果自动播放被禁用，则停止自动播放
      swiperController.stopAutoplay();
      return;
    }
    swiperController.startAutoplay();
  }
}
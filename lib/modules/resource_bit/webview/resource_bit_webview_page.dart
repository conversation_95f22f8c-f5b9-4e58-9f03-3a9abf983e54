import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/nav_bar/ffnav_bar.dart';
import 'package:playlet/modules/resource_bit/webview/resource_bit_webview_controller.dart';

class ResourceBitWebviewPage extends StatefulWidget {
  const ResourceBitWebviewPage({super.key});

  @override
  State<ResourceBitWebviewPage> createState() => _ResourceBitWebviewPageState();
}

class _ResourceBitWebviewPageState extends State<ResourceBitWebviewPage> {
  static const String _tag = 'ResourceBitWebviewPage';

  bool _isDisposed = false;
  @override
  void dispose() {
    super.dispose();
    _isDisposed = true;
  }

  @override
  Widget build(BuildContext context) {
    ResourceBitWebviewController controller = Get.find<ResourceBitWebviewController>();
    return Scaffold(
      appBar: FFNavBar(
        title: controller.context?.resourceBitModel?.recommendName ?? '',
        showBackIcon: true,
      ),
      body: Stack(
        children: [
          SafeArea(
            child: controller.context?.resourceBitModel?.skipValue == null
                ? const EmptyWidget(pageFrom: EmptyPageFrom.check_in, type: EmptyType.noNetwork)
                : InAppWebView(
                    initialUrlRequest: URLRequest(
                      url: WebUri(controller.context!.resourceBitModel!.skipValue!),
                    ),
                    initialSettings: InAppWebViewSettings(
                      javaScriptEnabled: true,
                      supportZoom: false,
                      isInspectable: true,
                    ),
                    onWebViewCreated: (InAppWebViewController webViewController) {
                      if (_isDisposed) {
                        return;
                      }
                      if (controller.webViewController == null) {
                        controller.onWebViewCreated(webViewController);
                      }
                    },
                    onLoadStart: (webViewController, url) {
                      if (_isDisposed) {
                        return;
                      }
                      controller.onBeginLoad();
                      FFLog.info('onLoadStart url: $url', tag: _tag);
                    },
                    onLoadStop: (webViewController, url) {
                      if (_isDisposed) {
                        return;
                      }
                      FFLog.info('onLoadStop url: $url', tag: _tag);
                    },
                    onReceivedError: (webViewController, request, error) {
                      if (_isDisposed) {
                        return;
                      }
                      FFLog.error('onReceivedError url: ${request.url.toString()} error: $error', tag: _tag);
                    },
                  ),
          ),
        ],
      ),
    );
  }
}

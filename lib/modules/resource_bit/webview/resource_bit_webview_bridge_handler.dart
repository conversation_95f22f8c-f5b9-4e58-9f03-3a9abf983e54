import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/track_event.dart';
import 'dart:core';

import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/webview.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/payment/payment_service.dart';
import 'package:playlet/utils/map_extension.dart';
import 'package:playlet/utils/track_event.dart';
import 'package:url_launcher/url_launcher.dart';

/// APP与H5交互接口文档：
/// https://scnd0fd9eth3.feishu.cn/wiki/NYGgwr62siod8XklMRCceWTHnTd

class ResourceBitWebviewBridgeHandler with WidgetsBindingObserver {

  String url = '';
  WeakReference<InAppWebViewController>? _webViewControllerRef;
  InAppWebViewController? get _webViewController => _webViewControllerRef?.target;
  late final List<String> _handlerNames = [];
  
  ResourceBitWebviewBridgeHandler() {
    WidgetsBinding.instance.addObserver(this);
  }

  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    unregisterAllJSBridge();
    _webViewControllerRef = null;
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      appH5Callback(true);
    } else if (state == AppLifecycleState.paused) {
      appH5Callback(false);
    }
  }

  void setWebViewController(InAppWebViewController controller) {
    _webViewControllerRef = WeakReference<InAppWebViewController>(controller);
  }

  void registerJsBridge() {
    // 跳转app原生页面
    _addJavaScriptHandler(
      handlerName: 'toNativePage',
      callback: (arg) {
        String? type = arg?.safeGet<String>('type');
        if (type == null) {
          return;
        }
        bool closeWeb = arg?.safeGet<bool>('closeWeb') ?? false;
        String? from = arg?.safeGet<String>('from');
        if (type == 'shorts') { // 沉浸页
          int? shortPlayId = arg?.safeGet<int>('shortPlayId');
          if (shortPlayId == null) {
            return;
          }
          AppNavigator.startDetailsPage(
            DetailsOptions(
              businessId: shortPlayId,
              scene: TrackEvent.discover,
              from: from ?? '',
            ),
            off: closeWeb,
          );
          return;
        }
        
        if (type == 'topUp') { // 充值页面
          AppNavigator.startStorePage(
            off: closeWeb,
          );
          return;
        }
        if (type == 'subscribe') { // 订阅详情
          AppNavigator.startSubscriptionPage(
            from: from ?? '',
            off: closeWeb,
          );
          return;
        }
      },
    );

    // 通过APP进行埋点调用
    _addJavaScriptHandler(
      handlerName: 'trackEvent',
      callback: (arg) {
        String? eventName = arg?.safeGet<String>('eventName');
        if (eventName == null) {
          return;
        }
        Map<String, String>? eventExtra = arg?.safeGet<Map<String, String>>('eventExtra');
        useTrackEvent(eventName, extra: eventExtra);
      },
    );

    // 退出webview
    _addJavaScriptHandler(
      handlerName: 'closeWebView',
      callback: (arg) {
        AppNavigator.back();
      },
    );

    // 跳转APP外部页面打开URL
    _addJavaScriptHandler(
      handlerName: 'openURL',
      callback: (arg) async {
        String? url = arg?.safeGet<String>('url');
        if (url == null) {
          FFLog.error('打开外部浏览器失败：url为空');
          return;
        }
         try {
          Uri uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(
              uri,
              mode: LaunchMode.externalApplication,
            );
          }
        } catch (e) {
          FFLog.error('打开外部浏览器失败：$e');
        }
      },
    );

    // 通知网页加载完成
    _addJavaScriptHandler(
      handlerName: 'onWebLoaded',
      callback: (arg) {
        FFLog.info('网页加载完成');
      },
    );

    // token失效等状态
    _addJavaScriptHandler(
      handlerName: 'tokenInvalid',
      callback: (arg) {
        // xjy TODO：刷新token 
      },
    );

    // 支持清除临时cookie
    _addJavaScriptHandler(
      handlerName: 'clearTempCookie',
      callback: (arg) {
        if (url.isEmpty) {
          return;
        }
        String? key = arg?.safeGet<String>('key');
        if (key == null) {
          return;
        }
        CookieManager.instance().deleteCookie(url: WebUri(url), name: key);
      },
    );

    // app内部打开h5链接
    _addJavaScriptHandler(
      handlerName: 'openNewWeb',
      callback: (arg) {
        String? url = arg?.safeGet<String>('url');
        if (url == null) {
          return;
        }
        String title = arg?.safeGet<String>('title') ?? '';
        bool closeWeb = arg?.safeGet<bool>('closeWeb') ?? false;

        AppNavigator.startWebViewPage(
          WebviewModel(
            title: title,
            url: url,
          ),
          off: closeWeb,
        );
      },
    );
  }

  void unregisterAllJSBridge() {
    InAppWebViewController? controller = _webViewController;
    if (controller == null) {
      _handlerNames.clear();
      return;
    }
    for (var handlerName in _handlerNames) {
      controller.removeJavaScriptHandler(handlerName: handlerName);
    }
    _handlerNames.clear();
  }

  /// 获取网络状态
  Future<void> networkH5Callback() async {
    // 初始检查网络状态
    var connectivity = Connectivity();
    var connectivityResult = await connectivity.checkConnectivity();

    int type = 0;
    int online = 1;
    if (connectivityResult.contains(ConnectivityResult.none)) {
      type = 0;
      online = 2;
    } else if (connectivityResult.contains(ConnectivityResult.mobile)) {
      type = 1;
    } else if (connectivityResult.contains(ConnectivityResult.wifi)) {
      type = 2;
    }  else {
      type = 3;
    }
    _webViewController?.evaluateJavascript(source: 'networkH5Callback(\'{"type":$type, "online": $online}\')');
  }

  /// 获取app状态
  void appH5Callback(bool isAppInForeground) {
    int type = isAppInForeground ? 1 : 2;
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    _webViewController?.evaluateJavascript(source: 'appH5Callback(\'{"type":$type, "timestamp": $timestamp}\')');
  }

  /// 通知H5更新Cookie
  void cookieH5Callback(Map<String, dynamic> cookies) {
    if (cookies.isEmpty) {
      return;
    }
    _webViewController?.evaluateJavascript(source: 'cookieH5Callback(\'${cookies.toString()}\')');
  }

  void _addJavaScriptHandler({
    required String handlerName,
    required void Function(Map<String, dynamic>?) callback}) {
      InAppWebViewController? controller = _webViewController;
      if (controller == null) {
        return;
      }
      if (!_handlerNames.contains(handlerName)) {
        _handlerNames.add(handlerName);
      }
      bool isAdded = controller.hasJavaScriptHandler(handlerName: handlerName);
      if (isAdded == true) {
        return;
      }
      _webViewController?.addJavaScriptHandler(
        handlerName: handlerName,
        callback: (args) {
          FFLog.info('JSBridge called method: $handlerName args: $args');
          Map<String, dynamic>? arg;
          if (args.isNotEmpty) {
            final first = args.first;
            if (first is Map<String, dynamic>) {
              arg = first;
            }
          }
          callback(arg);
        },
      );
  }
}
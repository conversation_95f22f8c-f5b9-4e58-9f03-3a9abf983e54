
import 'package:get/get.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/modules/resource_bit/webview/resource_bit_webview_bridge_handler.dart';
import 'package:playlet/modules/resource_bit/webview/resource_bit_webview_context.dart';

class ResourceBitWebviewController extends GetxController {
  ResourceBitWebViewContext? context;

  // WebView控制器
  InAppWebViewController? webViewController;
  ResourceBitWebviewBridgeHandler? _bridgeHandler;

  @override
  void onInit() {
    super.onInit();
    context = Get.arguments;
    _bridgeHandler = ResourceBitWebviewBridgeHandler();
    _bridgeHandler?.url = context?.resourceBitModel?.skipValue ?? '';
  }

  @override
  void onClose() {
    _bridgeHandler?.dispose();
    _bridgeHandler = null;
    
    if (webViewController != null) {
      try {
        webViewController?.dispose();
        webViewController = null;
      } catch (e) {
        FFLog.error('dispose webview error: $e');
      }
    }
    if (Get.isRegistered<ResourceBitWebviewBridgeHandler>()) {
      Get.delete<ResourceBitWebviewBridgeHandler>();
    }
    super.onClose();
  }

  void onWebViewCreated(InAppWebViewController controller) {
    webViewController = controller;
    _bridgeHandler?.unregisterAllJSBridge();
    _bridgeHandler?.setWebViewController(controller);
    _bridgeHandler?.registerJsBridge();
  }

  Future<void> onBeginLoad() async {
    final scene = context?.scene;
    if (scene == null) {
      return;
    }
    final model = context?.resourceBitModel;
    if (model == null) {
      return;
    }
    Map<String, dynamic> cookies = await model.getCookies(scene, isLoadSuccess: true);
    await setCookies(cookies);
  }

  // 设置Cookie
  Future<void> setCookies(Map<String, dynamic> cookies) async {
    final url = context?.resourceBitModel?.skipValue;
    if (url == null) return;
    
    final uri = WebUri(url);
    final cookieManager = CookieManager.instance();
    
    String script = "";
    for (var entry in cookies.entries) {
      final name = entry.key;
      final value = entry.value;
      
      if (value == null || value.toString().isEmpty) {
        // 设置过期的cookie
        script += "document.cookie = '$name=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;';";
        continue;
      }
      
      // URL编码cookie值
      final encodedValue = Uri.encodeComponent(value.toString());
      script += "document.cookie = '$name=$encodedValue; path=/;';";
      
      // 同时通过CookieManager设置
      await cookieManager.setCookie(
        url: uri,
        name: name,
        value: encodedValue,
        path: "/",
      );
    }
    
    if (script.isNotEmpty) {
      await webViewController?.evaluateJavascript(source: script);
    }
  }
}

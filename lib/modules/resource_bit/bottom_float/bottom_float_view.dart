import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'bottom_float_controller.dart';
import 'package:get/get.dart';

class HomeResourceBitBottomFloatView extends StatelessWidget {
  const HomeResourceBitBottomFloatView({
    super.key,
    this.closeButtonRight = 0,
  });

  final double closeButtonRight;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeResourceBitBottomFloatController>(
      init: HomeResourceBitBottomFloatController(),
      builder: (controller) {
        return Obx(() {
          if (controller.imagePath.isNotEmpty) {
            controller.onShownFloat();
            return Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: GestureDetector(
                    onTap: () {
                      unawaited(controller.onHomeBottomFloatClick());
                    },
                    child: Image.file(
                      File(controller.imagePath.value),
                      width: 110.sp,
                      height: 84.sp,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Positioned(
                  right: closeButtonRight,
                  top: 0,
                  child: GestureDetector(
                    onTap: controller.onCloseFloat,
                    child: Assets.resourcebit.homeBottomFloatClose.image(
                        width: 20.sp,
                        height: 20.sp,
                    ),
                  ),
                ),
              ],
            );
          } else {
            return const SizedBox.shrink();
          }
        });
    });
  }
}
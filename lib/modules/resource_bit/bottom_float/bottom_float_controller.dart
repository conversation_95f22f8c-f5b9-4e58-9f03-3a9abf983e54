import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/service/recommend_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/resource_bit_manager.dart';
import 'package:playlet/service/resource_bit/services/home_bottom_float_service.dart';
import 'package:playlet/service/resource_bit/track_event/resource_bit_track_event.dart';

class HomeResourceBitBottomFloatController extends GetxController {
  ResourceBitModel? resourceBitModel;
  RxString imagePath = ''.obs;
  StreamSubscription<List<ResourceBitModel>?>? _resourceBitStreamSubscription;
  StreamSubscription? _isShowListener;

  @override
  void onInit() {
    super.onInit();
    _addListener();
    unawaited(_loadBottomFloatImagePath());
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  Future<void> onHomeBottomFloatClick() async {
    final model = resourceBitModel;
    if (model == null) {
      return;
    }
    model.onResourceBitClick(ResourceBitScene.homeBottomFloat);
  }

  Future<void> onShownFloat() async {
    final service = _getBottomFloatService();
    if (service == null) {
      return;
    }
    final model = resourceBitModel;
    if (model == null) {
      return;
    }
    await service.onShown(model);
  }

  Future<void> onCloseFloat() async {
    final service = _getBottomFloatService();
    if (service != null) {
      service.onCloseFloat(); 
    }
    await _updateImagePath();
  }

  void _addListener() {
    _removeListener();

    final service = _getBottomFloatService();
    if (service != null) {
      _resourceBitStreamSubscription = service.modelsStream.listen((models) {
        unawaited(_loadBottomFloatImagePath());
      });
    }

    final RecommendService recommendService = Get.find<RecommendService>();
    _isShowListener?.cancel();
    _isShowListener = recommendService.isShow.listen((value) async {
      await _updateImagePath();
    });
  }

  void _removeListener() {
    _resourceBitStreamSubscription?.cancel();
    _resourceBitStreamSubscription = null;

    _isShowListener?.cancel();
    _isShowListener = null;
  }

  Future<void> _loadBottomFloatImagePath() async {
    final service = _getBottomFloatService();
    if (service == null) {
      resourceBitModel = null;
      await _updateImagePath();
      return;
    }
    final model = await service.getResourceModel();
    resourceBitModel = model;
    if (model == null) {
      await _updateImagePath();
      return;
    }
    await service.loadResource(model);
    await _updateImagePath();
  }

  Future<void> _updateImagePath() async {
    final RecommendService recommendService = Get.find<RecommendService>();
    if (recommendService.isShow.value) {
      // 展示推荐新人页，此时不能显示悬浮资源位
      imagePath.value = '';
      return;
    }
    final service = _getBottomFloatService();
    if (service == null) {
      imagePath.value = '';
      return;
    }
    if (service.isClosed) {
      imagePath.value = '';
      return;
    }
    final model = resourceBitModel;
    if (model == null) {
      imagePath.value = '';
      return;
    }
    String? path = await service.getResourceCachePath(model);
    path ??= '';
    imagePath.value = path;

    if (path.isEmpty) {
      return;
    }
    ResourceBitTrackEvent.reelShow(model);
  }

  ResourceBitHomeBottomFloatService? _getBottomFloatService() {
    final service = ResourceBitManager.getInstance().getService(ResourceBitScene.homeBottomFloat);
    if (service == null) {
      return null;
    }
    if (service is! ResourceBitHomeBottomFloatService) {
      return null;
    }
    return service;
  }
} 
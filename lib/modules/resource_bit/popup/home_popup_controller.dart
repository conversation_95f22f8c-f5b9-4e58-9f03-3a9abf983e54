import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/resource_bit_manager.dart';
import 'package:playlet/service/resource_bit/track_event/resource_bit_track_event.dart';

class HomePopupController extends GetxController {
  ResourceBitModel resourceBitModel;
  HomePopupController(this.resourceBitModel);

  @override
  void onInit() {
    super.onInit();

    ResourceBitTrackEvent.reelShow(resourceBitModel);
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onHomePopupClick() async {
    await closePopup();
    resourceBitModel.onResourceBitClick(ResourceBitScene.homePopup);
  }

  Future<void> closePopup() async {
    await SmartDialog.dismiss();
  }
}
import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/modules/resource_bit/popup/home_popup_controller.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';

class HomePopupView extends StatelessWidget {
  const HomePopupView({
    super.key,
    required this.resourceBitModel,
    required this.imagePath,
  });

  final ResourceBitModel resourceBitModel;
  final String imagePath;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.zero, // 移除Dialog默认的边距
      child: GetBuilder<HomePopupController>(
        init: HomePopupController(resourceBitModel),
        builder: (controller) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  // 图片
                  GestureDetector(
                    onTap: () {
                      controller.onHomePopupClick();
                    },
                    child: FutureBuilder<Size>(
                      future: _getImageSize(imagePath),
                      builder: (context, snapshot) {
                        if (!snapshot.hasData) {
                          return const CircularProgressIndicator();
                        }
                        
                        final imageSize = snapshot.data!;
                        final imageRatio = imageSize.width / imageSize.height;
                        const minRatio = 393 / 450;
                        
                        // 如果图片比例小于最小比例，则使用最小比例
                        final useRatio = imageRatio < minRatio ? minRatio : imageRatio;
                        final width = Get.width;
                        final height = width / useRatio;
                        
                        return Image.file(
                          File(imagePath),
                          width: width,
                          height: height,
                          fit: BoxFit.cover,
                        );
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.sp),
              // 关闭按钮
              IconButton(
                onPressed: () {
                  controller.closePopup();
                },
                icon: Assets.resourcebit.homePopupClose.image(
                  width: 30.sp,
                  height: 30.sp,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
  
  // 获取图片尺寸
  Future<Size> _getImageSize(String imagePath) async {
    final image = Image.file(File(imagePath));
    final completer = Completer<Size>();
    
    image.image.resolve(const ImageConfiguration()).addListener(
      ImageStreamListener((info, _) {
        completer.complete(Size(
          info.image.width.toDouble(),
          info.image.height.toDouble(),
        ));
      })
    );
    
    return await completer.future;
  }
}
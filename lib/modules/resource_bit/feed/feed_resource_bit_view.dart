import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/shimmer/reel_shimmer.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/modules/resource_bit/feed/feed_resource_bit_controller.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';

class FeedResourceBitView extends GetView {
  final ResourceBitModel? resourceBitModel;
  const FeedResourceBitView(this.resourceBitModel, {super.key});

  Widget _errorWidget(FeedResourceBitController controller) {
    return EmptyWidget(
      pageFrom: EmptyPageFrom.shorts,
      type: EmptyType.timeout,
      onRefresh: () {
        controller.refreshData();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<FeedResourceBitController>(
      init: FeedResourceBitController(resourceBitModel),
      builder: (controller) {
        return Obx(() {
          if (controller.loadingStatus.value == FeedResourceBitLoadingStatus.success) {
            if (controller.imageUrl.isNotEmpty) {
              return GestureDetector(
                onTap: () {
                  controller.onFeedClick();
                },
                child: SizedBox(
                  width: double.infinity,
                  height: double.infinity,
                  child: CoverWidget(
                    imageUrl: controller.imageUrl,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) => _errorWidget(controller),
                  ),
                ),
              );
            } else {
              return const SizedBox.shrink();
            }
          } else if (controller.loadingStatus.value == FeedResourceBitLoadingStatus.failed) {
            return _errorWidget(controller);
          } else {
            return const ReelShimmer();
          }
        });
      }
    );
  }
}
import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/api/reels.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/resource_bit_manager.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';
import 'package:playlet/service/resource_bit/track_event/resource_bit_track_event.dart';
import 'package:playlet/utils/events.dart';

enum FeedResourceBitLoadingStatus {
  idle,
  loading,
  success,
  failed,
}

class FeedResourceBitController extends GetxController {
  String imageUrl = '';
  Rx<FeedResourceBitLoadingStatus> loadingStatus = FeedResourceBitLoadingStatus.idle.obs;
  ResourceBitModel? resourceBitModel;

  FeedResourceBitController(this.resourceBitModel);

  @override
  void onInit() {
    super.onInit();
    unawaited(_loadData());
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> refreshData() async {
    await _loadData();
    update();
  }

  Future<void> onFeedClick() async {
    resourceBitModel?.onResourceBitClick(ResourceBitScene.feed);
  }

  Future<void> _loadData() async {
    imageUrl = '';

    ResourceBitBaseService? service = await _getService();
    if (service == null) {
      loadingStatus.value = FeedResourceBitLoadingStatus.failed;
      return;
    }
    final model = resourceBitModel;
    ResourceBitSkipType? skipType = model?.skipType;
    if (model == null || skipType == null) {
      loadingStatus.value = FeedResourceBitLoadingStatus.failed;
      return;
    }
    if (skipType == ResourceBitSkipType.h5) {
      imageUrl = service.getResourceImageUrl(model) ?? "";
      if (imageUrl.isEmpty) {
        loadingStatus.value = FeedResourceBitLoadingStatus.failed;
      } else {
        loadingStatus.value = FeedResourceBitLoadingStatus.success;
        await service.onShown(model);
        ResourceBitTrackEvent.reelShow(model);
      }
      return;
    }

    if (skipType == ResourceBitSkipType.shorts) {
      // 如果是短剧资源，通知feed流更新短剧信息
      int? shortPlayId = model.shortPlayId;
      if (shortPlayId == null) {
        loadingStatus.value = FeedResourceBitLoadingStatus.failed;
        return;
      }
      int? dramaId = await ApiReels.getDramaIdByShortPlayId(shortPlayId);
      if (dramaId == null) {
        loadingStatus.value = FeedResourceBitLoadingStatus.failed;
        return;
      }
      eventBus.fire(
        PushNotificationToReelShortEventData(
          shortPlayId: shortPlayId.toString(),
          dramaId: dramaId.toString(),
          reelType: ReelType.resourceBit,
          callback: (success) {
            if (success) {
              loadingStatus.value = FeedResourceBitLoadingStatus.success;
              service.onShown(model);
              ResourceBitTrackEvent.reelShow(model);
            } else {
              loadingStatus.value = FeedResourceBitLoadingStatus.failed;
            }
          },
        ),
      );
      return;
    }
    loadingStatus.value = FeedResourceBitLoadingStatus.failed;
  }

  Future<ResourceBitBaseService?> _getService() async {
    final service = ResourceBitManager.getInstance().getService(ResourceBitScene.feed);
    return service;
  }
}
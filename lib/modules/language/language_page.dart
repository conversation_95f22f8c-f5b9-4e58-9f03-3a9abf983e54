import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/nav_bar/ffnav_bar.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/i18n/translation_service.dart';

import 'language_controller.dart';

class LanguagePage extends GetView<LanguageController> {
  final TranslationService translationService = Get.find<TranslationService>();
  LanguagePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: FFNavBar(
        showBackIcon: true,
        title: AppTrans.languageSetting(),
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.only(
            bottom: Get.mediaQuery.viewPadding.bottom,
          ),
          child: GetBuilder<LanguageController>(
            id: "list",
            builder: (_) {
              return Column(
                children: [
                  Expanded(
                    child: controller.loading
                        ? const Center(child: SizedBox())
                        : Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.sp),
                            child: ListView.separated(
                              itemCount: controller.languages.length,
                              itemBuilder: (context, index) {
                                final currentLangCode = controller.languages[index];
                                return InkWell(
                                  onTap: () =>
                                      controller.selectLanguage(currentLangCode),
                                  child: SizedBox(
                                    height: 54.sp,
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            translationService.getLanguageName(
                                                    currentLangCode) ??
                                                "",
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                            width: 54.sp,
                                            height: 54.sp,
                                            child: currentLangCode ==
                                                    controller.language
                                                ? Center(
                                                    child: Assets
                                                        .language.icLanguageCheck
                                                        .image(
                                                            width: 20.sp,
                                                            height: 20.sp))
                                                : Center(
                                                    child: Assets
                                                        .language.icLanguageUncheck
                                                        .image(
                                                            width: 20.sp,
                                                            height: 20.sp))),
                                      ],
                                    ),
                                  ),
                                );
                              },
                              separatorBuilder: (context, index) {
                                return const SizedBox.shrink();
                              },
                            ),
                          ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(16.sp),
                    child: SizedBox(
                      width: Get.width,
                      height: 48.sp,
                      child: ElevatedButton(
                        onPressed: () =>
                            controller.saveLanguage(controller.language),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFFFCD00), // 按钮背景色
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(80.r),
                          ),
                        ),
                        child: Text(
                          AppTrans.save(),
                          style: TextStyle(
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF121212),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

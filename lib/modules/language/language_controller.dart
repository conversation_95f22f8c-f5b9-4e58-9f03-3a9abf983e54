import 'package:get/get.dart';
import 'package:playlet/api/language.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/translation_service.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/app_service.dart';
import 'package:playlet/utils/app_language.dart';
import 'package:playlet/utils/get_extension.dart';

class LanguageController extends GetxController {
  final TranslationService translationService = Get.find<TranslationService>();
  List<String> languages = [];
  String language = "";
  bool loading = false;
  final RxBool isSaving = false.obs;

  @override
  void onInit() {
    loading = true;
    getList();
    language = translationService.getCurrentLanguage();
    FFLog.info("language: $language");
    super.onInit();
  }

  void getList() {
    languages = translationService.getSupportedLanguages();
    update(["list"]);
    loading = false;
  }

  void selectLanguage(String langCode) async {
    language = langCode;
    update(["list"]);
  }

  void saveLanguage(String langCode) async {
    if (isSaving.value) return;
    isSaving.value = true;
    Get.loading();
    try {
      // 切换语言 - 直接使用语言代码
      await AppLanguage.setLanguage(langCode);
      FFLog.info("language save: $language");
      language = langCode;
      update(["list"]);
      await ApiLanguage.setUserLanguage();
      // 标记为非冷启动
      AppService appService = Get.find<AppService>();
      appService.isColdOpen = false;
    } catch (e) {
      FFLog.error("保存语言失败: $e");
      return;
    } finally {
      Get.dismiss();
      isSaving.value = false; // 保存完成，恢复状态
    }

    Get.back();
    AppNavigator.startMainPage();
  }
}

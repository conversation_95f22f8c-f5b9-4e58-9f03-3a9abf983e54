import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';

abstract class HomeTabProtocol {
  // tab是否已经初始化
  RxBool isInitialized = RxBool(false);

  // 触发刷新
  FutureOr Function()? onRefresh;

  // 获取tab唯一标识
  HomeTabFlag? get tabFlagName;

  // 获取 tab 唯一标识
  int get tabId => 0;

  // 获取 tab 标题
  String get tabTitle => '';

  // 获取 tab 对应的页面
  GetBuilder get tabPage;
  
  // 设置tab数据
  void setTabModel(HomeTabModel? model, TabPageResponse tabPageData);

  // 处理请求结果状态
  void onRequestResult(bool success);
}
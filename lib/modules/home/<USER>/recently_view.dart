import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/gen/fonts.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/home/<USER>/collapse_animation_view.dart';
import 'package:playlet/modules/home/<USER>/recently_controller.dart';

class HomeRecentlyView extends StatelessWidget {
  const HomeRecentlyView({super.key});

  Widget _buildNormalWidget(HomeRecentlyController controller) {
    controller.trackReelShow();

    final placeholderImage = Assets.home.homePlaceholder.image();
     
    return GestureDetector(
            onTap: () {
              controller.tpDetailsPage();
            },
            child: SizedBox(
              width: Get.width,
              height: 69.sp,
              child: Stack(
                children: [
                  Positioned(
                    top: 9.sp,
                    child: Assets.home.homeRecentlyBigBg.image(
                      width: Get.width,
                      height: 60.sp,
                    ),
                  ),
                  Positioned(
                    top: 9.sp,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(width: 8.sp),
                        CoverWidget(
                          imageUrl: controller.imageUrl.value,
                          width: 39.sp,
                          height: 52.sp,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) => placeholderImage,
                          placeholder: (context, url) => placeholderImage,
                        ),
                        SizedBox(width: 10.sp),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              controller.shortPlayName.value,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                                fontFamily: FontFamily.poppins,
                                fontStyle: FontStyle.normal,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 2.sp),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                _buildEPText(AppTrans.recentlyEp(controller.episodeNumber.value), Colors.white),
                                _buildEPText(" / ", const Color(0xFF9e9e9e)),
                                _buildEPText(AppTrans.recentlyAllEp(controller.allEpisodesNumber.value), const Color(0xFF9e9e9e)),
                              ],
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    top: 26.sp,
                    right: 26.sp,
                    height: 30.sp,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15.sp),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 10.sp, vertical: 7.sp),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Assets.home.homeRecentlyContinue.image(
                            width: 16.sp,
                            height: 16.sp,
                          ),
                          SizedBox(width: 4.sp),
                          Text(
                            AppTrans.recentlyContinue(),
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF111218),
                              fontFamily: FontFamily.poppins,
                              fontStyle: FontStyle.normal,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    right: 4.sp,
                    child: GestureDetector(
                        onTap: () {
                          controller.closeRecentlyView();
                        },
                        child: Assets.home.homeRecentlyClose.image(
                          width: 18.sp,
                          height: 18.sp,
                        ),
                    ),
                  )
                ]
              ),
            ),
          );
  }

  Text _buildEPText(String text, Color color) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w400,
        color: color,
        fontFamily: FontFamily.poppins,
        fontStyle: FontStyle.normal,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSmallWidget(HomeRecentlyController controller) {
    controller.trackReelShow();
    
    final placeholderImage = Assets.home.homePlaceholder.image();
    
    return GestureDetector(
      onTap: () {
        controller.tpDetailsPage();
      },
      child: SizedBox(
        width: 81.sp,
        height: 104.sp,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Positioned(
              top: 9.sp,
              right: 9.sp,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.6),
                    width: 1.sp,
                  ),
                  borderRadius: BorderRadius.circular(4.sp),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4.sp),
                  child: Stack(
                      children: [
                        CoverWidget(
                          imageUrl: controller.imageUrl.value,
                          width: 70.sp,
                          height: 93.sp,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) => placeholderImage,
                          placeholder: (context, url) => placeholderImage,
                        ),
                        Assets.home.homeRecentlyCover.image(
                            width: 70.sp,
                            height: 93.sp
                        ),
                        Positioned(
                          left: 0,
                          right: 0,
                          top: 0,
                          bottom: 0,
                          child: Center(
                            child: Assets.home.homeRecentlyPlay.image(
                              width: 20.sp,
                              height: 20.sp,
                            ),
                          ),
                        ),
                      ],
                  ),
                ),
              ),
            ),
            Positioned(
              left: 1.sp,
              top: 10.sp,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Assets.home.homeRecentlySmallBg.image(
                    height: 18.sp,
                    fit: BoxFit.fitHeight,
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 2.sp),
                    child: Text(
                      "EP.${controller.episodeNumber.value}",
                      style: const TextStyle(
                        fontSize: 9,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                        fontFamily: FontFamily.poppins,
                        fontStyle: FontStyle.normal,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              right: 0.sp,
              top: 0.sp,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  controller.closeRecentlyView();
                },
                child: Assets.home.homeRecentlyClose.image(
                      width: 18.sp,
                      height: 18.sp,
                      fit: BoxFit.cover
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: HomeRecentlyController(),
      builder: (controller) {
        return Obx(() {
          if (controller.isShowNormal) {
            return CollapseAnimationView(
              normalWidget: _buildNormalWidget(controller),
              normalWidgetWidth: Get.width,
              smallWidget: Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: _buildSmallWidget(controller),
              ),
              smallWidgetWidth: 70.sp,
              onAnimationComplete: () {
                controller.isShowNormal = false;
              },
            );
          } else {
            return _buildSmallWidget(controller);
          }
        });
    });
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
// ignore: implementation_imports
import 'package:flutter_smart_dialog/src/data/animation_param.dart';
// ignore: implementation_imports
import 'package:flutter_smart_dialog/src/kit/typedef.dart';

import 'dialog_chain_manager.dart';

class <PERSON><PERSON><PERSON><PERSON><PERSON> extends Chain {
  final VoidCallback? onShow;
  final Widget dialogWidget;
  final DialogOptions? dialogOptions;

  DialogChain(
    this.dialogWidget,
    int priority, {
    this.dialogOptions,
    this.onShow,
    super.markTag,
  }) : super((chain, data) async {
          onShow?.call();
          // 显示对话框
          await SmartDialog.show(
            builder: (context) => dialogWidget,
            controller: dialogOptions?.controller,
            alignment: dialogOptions?.alignment,
            clickMaskDismiss: dialogOptions?.clickMaskDismiss ?? false,
            usePenetrate: dialogOptions?.usePenetrate,
            useAnimation: dialogOptions?.useAnimation,
            animationType: dialogOptions?.animationType,
            nonAnimationTypes: dialogOptions?.nonAnimationTypes,
            animationBuilder: dialogOptions?.animationBuilder,
            animationTime: dialogOptions?.animationTime,
            maskColor: dialogOptions?.maskColor,
            maskWidget: dialogOptions?.maskWidget,
            debounce: dialogOptions?.debounce,
            onDismiss: dialogOptions?.onDismiss,
            onMask: dialogOptions?.onMask,
            displayTime: dialogOptions?.displayTime,
            tag: dialogOptions?.tag,
            keepSingle: dialogOptions?.keepSingle,
            permanent: dialogOptions?.permanent,
            useSystem: dialogOptions?.useSystem,
            bindPage: dialogOptions?.bindPage,
            bindWidget: dialogOptions?.bindWidget,
            ignoreArea: dialogOptions?.ignoreArea,
            backType: dialogOptions?.backType,
            onBack: dialogOptions?.onBack,
          );

          // await Get.dialog(
          //   dialogWidget,
          //   barrierDismissible: false,
          //   useSafeArea: true,
          // );
          chain.finish();
        }, priority: priority);
}

class DialogOptions {
  SmartDialogController? controller;
  Alignment? alignment;
  bool? clickMaskDismiss;
  bool? usePenetrate;
  bool? useAnimation;
  SmartAnimationType? animationType;
  List<SmartNonAnimationType>? nonAnimationTypes;
  AnimationBuilder? animationBuilder;
  Duration? animationTime;
  Color? maskColor;
  Widget? maskWidget;
  bool? debounce;
  VoidCallback? onDismiss;
  VoidCallback? onMask;
  Duration? displayTime;
  String? tag;
  bool? keepSingle;
  bool? permanent;
  bool? useSystem;
  bool? bindPage;
  BuildContext? bindWidget;
  Rect? ignoreArea;
  SmartBackType? backType;
  SmartOnBack? onBack;

  DialogOptions({
    this.controller,
    this.alignment,
    this.clickMaskDismiss,
    this.usePenetrate,
    this.useAnimation,
    this.animationType,
    this.nonAnimationTypes,
    this.animationBuilder,
    this.animationTime,
    this.maskColor,
    this.maskWidget,
    this.debounce,
    this.onDismiss,
    this.onMask,
    this.displayTime,
    this.tag,
    this.keepSingle,
    this.permanent,
    this.useSystem,
    this.bindPage,
    this.bindWidget,
    this.ignoreArea,
    this.backType,
    this.onBack,
  });
}

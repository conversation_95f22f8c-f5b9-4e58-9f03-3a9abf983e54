import 'dart:ui';

/// home页面的dialog链式调用
class DialogChainManager {
  DialogChainManager.internal();

  factory DialogChainManager() => instance;
  static final DialogChainManager instance = DialogChainManager.internal();

  /// 对话框关闭的回调列表
  final List<VoidCallback> _dialogCloseCallbacks = [];

  // 应用更新提醒-强制更新/推荐更新
  static const appUpdate = 1;
  // 通知自定义弹窗
  static const customNotification = 2;
  // 实时活动的通知弹窗
  static const liveActivityNotification = 3;
  // 新人剧集推荐
  static const dramaRecommended = 4;
  //  FSI权限引导弹窗
  static const fsiPermission = 5;
  // 账号登录引导
  static const accountLoginGuide = 6;
  // 后台配置-首页弹窗资源位
  static const homePageResource = 7;
  // 好评引导
  static const goodReviewGuide = 8;

  ///是否开启了执行线程
  bool _isOpen = false;

  bool get isOpen => _isOpen;

  ///是否暂停
  bool _isPaused = false;

  bool get isPaused => _isPaused;

  ///执行队列
  final List<Chain> _queueChain = [];
  List<Chain> _opportunityChains = [];

  VoidCallback? _closeCallBack;
  VoidCallback? _openCallBack;

  Chain? _tempChain;
  dynamic _tempData;

  ///开启执行线程
  onOpen(VoidCallback openCallBack) {
    _openCallBack = openCallBack;
  }

  ///所有任务执行完成关闭执行线程
  onClose(VoidCallback closeCallBack) {
    _closeCallBack = closeCallBack;
  }

  ///添加需要执行的任务到执行线程
  add(Chain chain) {
    // 按优先级插入任务
    _insertWithPriority(chain);

    // 如果当前没有运行中的任务或是没有暂停，则启动执行
    if (!_isOpen || !_isPaused) {
      _execute();
    }
  }

  void addOpportunityChain(Chain chain) {
    bool exit = false;
    if (_opportunityChains.isNotEmpty) {
      for (var element in _opportunityChains) {
        if (element.markTag != null && chain.markTag != null) {
          if (element.markTag == chain.markTag) {
            exit = true;
            break;
          }
        }
      }
    }
    if (_queueChain.isNotEmpty) {
      for (var element in _queueChain) {
        if (element.markTag != null && chain.markTag != null) {
          if (element.markTag == chain.markTag) {
            exit = true;
            break;
          }
        }
      }
    }
    if (_isOpen == true || isPaused == true) {
      if (_tempChain != null) {
        if (_tempChain!.markTag != null && chain.markTag != null) {
          if (_tempChain!.markTag == chain.markTag) {
            exit = true;
          }
        }
      }
    }

    if (exit == false) {
      _opportunityChains.add(chain);
    }
    _opportunityChains.sort((a, b) => a.priority.compareTo(b.priority));
  }

  void startOpportunityChain() {
    if (_opportunityChains.isNotEmpty) {
      Chain chain = _opportunityChains.first;
      add(chain);
      List<Chain> opportunitys = [];
      for (var i = 0; i < _opportunityChains.length; i++) {
        if (i > 0) {
          opportunitys.add(_opportunityChains[i]);
        }
      }
      _opportunityChains = opportunitys;
    }
  }

  /// 按优先级插入任务
  void _insertWithPriority(Chain chain) {
    // 如果队列为空，直接添加
    if (_queueChain.isEmpty) {
      _queueChain.add(chain);
      return;
    }
    _queueChain.add(chain);
    _queueChain.sort((a, b) => a.priority.compareTo(b.priority));
  }

  ///开始执行
  _execute() {
    // 如果已经在执行中，直接返回
    if (_isOpen) return;

    _isOpen = true;
    _openCallBack?.call();
    _executor().then((value) {
      if (value) {
        _closeCallBack?.call();
      }
    });
  }

  ///主动停止
  ///终止执行并清空任务队列
  ///界面退出时需要调用该方法，否则会内存溢出
  stop() {
    _isOpen = false;
    _queueChain.clear(); // 清空剩余任务队列
    _tempChain = null; // 清空当前任务
    _tempData = null; // 清空临时数据
    _closeCallBack?.call(); // 调用关闭回调
    _opportunityChains.clear();
  }

  ///暂停执行
  pause() {
    _isPaused = true;
  }

  ///恢复执行
  resume() {
    _isPaused = false;
    _execute();
  }

  /// 添加对话框关闭的监听
  void addDialogCloseListener(VoidCallback callback) {
    if (!_dialogCloseCallbacks.contains(callback)) {
      _dialogCloseCallbacks.add(callback);
    }
  }

  /// 移除对话框关闭的监听
  void removeDialogCloseListener(VoidCallback callback) {
    _dialogCloseCallbacks.remove(callback);
  }

  Future<bool> _executor() async {
    return Future(() async {
      while (_isOpen) {
        if (_isOpen && !_isPaused) {
          // 添加暂停检查
          if (_queueChain.isNotEmpty &&
              (_tempChain == null || _tempChain!.isFinished())) {
            _tempChain = _queueChain.removeAt(0);
            if (!_tempChain!.isFinished()) {
              _tempChain!.execute(_tempData);
              _tempData = _tempChain!.data;
            }
          }
        }
        //退出条件：手动停止或任务执行完毕
        if ((!_isOpen) ||
            (_isOpen &&
                !_isPaused && // 添加暂停检查
                _queueChain.isEmpty &&
                (_tempChain == null || _tempChain!.isFinished()))) {
          _isOpen = false;
          break;
        }
        await Future.delayed(const Duration(milliseconds: 300));
      }
      return true;
    });
  }
}

///执行一些代码，不管是异步还是同步，执行完成后调用[Chain]的[finish]方法才会执行下一个[Chain]
///可以往下一个Chain传递参数调用[Chain]中的[sendData]
///或者可以接收上一个Chain传递过来的参数调用[Chain]中的[receiveData]
class Chain {
  dynamic data;
  // 任务优先级，数字越大优先级越低
  final int priority;
  // 标记
  final String? markTag;
  final dynamic Function(Chain chain, dynamic data) callback;

  Chain(this.callback, {this.priority = 0, this.markTag});

  ///任务是否执行完毕
  ///创建[Chain]相当于就开启了一个无限循环的任务一直等待该任务执行完毕
  ///用户需要在任务完成时主动调用[finish]结束任务
  bool _finished = false;

  ///用户任务实际执行的方法
  execute(dynamic data) {
    this.data = callback.call(this, data);
  }

  ///用户主动结束任务执行
  finish() {
    _finished = true;

    // 创建回调列表的副本，避免迭代过程中修改原列表导致的并发修改异常
    final callbacksCopy = List<Function>.from(DialogChainManager.instance._dialogCloseCallbacks);
    // 使用副本触发对话框关闭回调
    for (var callback in callbacksCopy) {
      callback();
    }
  }

  isFinished() {
    return _finished;
  }
}

/*
ChainExecutor._instance
      ..onOpen(() {
        ///执行前做什么事情
      })
      ..onClose(() {
        ///执行完毕做什么事情
      })
      ..add(Chain((chain,data) {
        ///do some thing
        ///执行一些代码，不管是异步还是同步，执行完成后调用[Chain]的[finish]方法才会执行下一个[Chain]
        ///可以往下一个Chain传递参数调用[Chain]中的[sendData]
        ///或者可以接收上一个Chain传递过来的参数调用[Chain]中的[receiveData]
      }))
      ..add(Chain((chain,data) {
        ///do some thing
      }))
      ..execute();
*/

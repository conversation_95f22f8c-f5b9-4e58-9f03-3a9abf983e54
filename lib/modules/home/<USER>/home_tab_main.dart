import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_main_controller.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_main_page.dart';

import 'home_tab_protocol.dart';

// 主页
class HomeTabMain implements HomeTabProtocol {
  @override
  RxBool isInitialized = RxBool(false);

  @override
  FutureOr Function()? onRefresh;

  @override
  int get tabId => _homeTabModel?.tabId ?? (_pageModel?.tabId ?? 0);

  @override
  HomeTabFlag? get tabFlagName => _homeTabModel?.tabFlagName;

  @override
  String get tabTitle => _homeTabModel?.tabDisplayName ?? "";

  @override
  GetBuilder<GetxController> get tabPage {
    // 确保 controller 已注册
    _registerControllerIfNeeded();
    return GetBuilder<HomeMainController>(
      builder: (controller) {
        if (_pageModel == null) {
          return const SizedBox();
        }
        return HomeMainPage(onRefresh: onRefresh,);
      },
    );
  }

  HomeTabModel? _homeTabModel;
  TabPageResponse? _pageModel;

  HomeTabMain({this.onRefresh});

  @override
  void setTabModel(HomeTabModel? model, TabPageResponse tabPageData) {
    _homeTabModel = model;
    _pageModel = tabPageData;
    
    _registerControllerIfNeeded();

    HomeMainController homeMainController = Get.find<HomeMainController>();
    homeMainController.updateModel(_pageModel);
  }

  @override
  void onRequestResult(bool success) {
    _registerControllerIfNeeded();
    HomeMainController homeMainController = Get.find<HomeMainController>();
    homeMainController.onRequestResult(success);
  }

  void _registerControllerIfNeeded() {
    if (Get.isRegistered<HomeMainController>()) {
      return;
    }
    Get.lazyPut<HomeMainController>(
      () => HomeMainController(),
      fenix: true,
    );
  }
}
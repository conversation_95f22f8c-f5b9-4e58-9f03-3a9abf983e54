import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/horizontal_banner/horizontal_banner_controller.dart';

class HorizontalBannerView extends StatelessWidget {
  final int index;
  final HorizontalBannerController controller;
  final double itemWidth;
  final ContentResponse contentResponse;
  final String moduleName;
  final String moduleId;

  const HorizontalBannerView({
    super.key,
    required this.controller,
    required this.itemWidth,
    required this.contentResponse,
    required this.index,
    required this.moduleName,
    required this.moduleId,
  });

  @override
  Widget build(BuildContext context) {
    var itemHeight = itemWidth * 354.sp / 266.sp;

    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          controller.goToVideoDetail(contentResponse.shortPlayResponse);
        },
        child: Stack(
          alignment: Alignment.bottomCenter, // 添加居中对齐
          children: [
            ClipRRect(
                borderRadius: BorderRadius.all(Radius.circular(6.r)),
                child: CoverWidget(
                  width: itemWidth,
                  height: itemHeight,
                  fit: BoxFit.cover,
                  imageUrl: "${contentResponse.coverId}",
                  compress: false,
                )),
            Obx(() => controller.currentIndex.value == index
                ? Container(
                      height: 63.sp,
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Color(0x000F1016),
                            Color(0x990F1016),
                            Color(0xE60F1016),
                          ],
                          stops: [0.0, 0.3507, 1.0],
                        ),
                      ),
                    )
                :  Container(
                      color: Colors.black.withValues(alpha: 0.4),
                    ),
                  ),
          ],
        ));
  }
}

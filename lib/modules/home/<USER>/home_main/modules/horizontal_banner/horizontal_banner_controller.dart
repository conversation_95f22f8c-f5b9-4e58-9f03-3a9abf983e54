import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/horizontal_banner/swiper.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/events.dart';

class HorizontalBannerController extends GetxController {
  var currentIndex = 0.obs;
  var showBottomLayout = true.obs;
  var bannerAutoPlay = true.obs;
  final HomeModuleContext homeModuleContext;
  final int positionId;
  List<ContentResponse>? contentResponseList;
  StreamSubscription? _bannerAutoPlaySubscription;

  HorizontalBannerController(this.homeModuleContext, this.positionId);

  // 添加一个SwiperController
  final swiperController = SwiperController();
  StreamSubscription? shortWatchHistoryEventShortSubscription;

  @override
  void onInit() {
    super.onInit();
    // 监听bannerAutoPlay的变化
    _bannerAutoPlaySubscription?.cancel();
    _bannerAutoPlaySubscription = bannerAutoPlay.listen((value) {
      if (value) {
        swiperController.startAutoplay();
      } else {
        swiperController.stopAutoplay();
      }
    });

    shortWatchHistoryEventShortSubscription = eventBus.on<ShortWatchHistoryEvent>().listen((event) {
      var item = contentResponseList?.firstWhereOrNull((item)=>item.shortPlayResponse?.id == event.shortPlayId);
      item?.shortPlayResponse?.episodeNum = event.episodeNum;
    });
  }

  void updateShowBottomLayout(scrollState) {
    showBottomLayout.value = scrollState == ScrollState.idle;
  }

  void updateCurrentIndex(int index) {
    currentIndex.value = index;
  }

  void goToVideoDetail(ShortPlayResponseList? shortPlayResponseList) {
    int? id = shortPlayResponseList?.id;
    if (id == null) return;
    int? episodeNum = shortPlayResponseList?.episodeNum ?? 1;
    AppNavigator.startDetailsPage(DetailsOptions(
      businessId: id,
      playerEpisodeIndex: episodeNum - 1,
      scene: TrackEvent.discover,
      //埋点来源：传入discover_后台模块id_后台模块title 例如：discover_1_list
      from: EventValue.discoverBanner,
      //后台模块id
      moduleId: homeModuleContext.moduleId ?? "",
      //传入模块的位置id的排序值。例如：每个模块中剧：1、2、3；
      moduleName: homeModuleContext.moduleName ?? "",
      //传入模块的位置id的排序值。例如：每个模块中剧：1、2、3；
      positionId: positionId.toString(),
      moduleStyle: homeModuleContext.moduleStyle,
    ));
  }

  /// 更新自动播放控制
  void updateBannerAutoPlay(bool isAuto) {
    if (bannerAutoPlay.value != isAuto) {
      bannerAutoPlay.value = isAuto;
    }
  }

  @override
  void onClose() {
    _bannerAutoPlaySubscription?.cancel();
    _bannerAutoPlaySubscription = null;
    shortWatchHistoryEventShortSubscription?.cancel();
    super.onClose();
  }
}


import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:get/get.dart' hide Trans;
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_main_module_protocol.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/horizontal_banner/horizontal_banner_controller.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/horizontal_banner/horizontal_banner_view.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/horizontal_banner/swiper.dart';
import 'package:playlet/utils/track_event.dart';
import 'package:visibility_detector/visibility_detector.dart';

class HorizontalBannerImpl with HomeMainModuleDefault {
  @override
  bool get isValid {
    final contentResponseList = moduleData?.contentResponseList;
    if (contentResponseList == null || contentResponseList.isEmpty) {
      return false;
    }
    return true;
  }

  @override
  String get title => "";

  @override
  EdgeInsetsGeometry get padding => EdgeInsets.only(bottom: 30.sp);

  @override
  bool get isShowMore => false;

  @override
  int getRowsNumber() {
    return 1;
  }

  @override
  int getColumsNumber() {
    return 1;
  }

  @override
  Widget getWidget(int row, int colum) {
    var data = moduleData?.contentResponseList ?? List.empty();
    final id = moduleData?.bannerId;
    if (data.isEmpty || id == null) {
      return Container();
    } else {
      var viewportFraction = 266.sp / 393.sp;
      var itemWidth = viewportFraction * Get.width;
      final tag = id.toString();
      final controller = Get.put(HorizontalBannerController(HomeModuleContext(moduleId: moduleId, moduleName: moduleData?.title), colum), tag: tag);
      controller.contentResponseList = data;
      return GetBuilder<HorizontalBannerController>(
          init: controller,
          tag: tag,
          builder: (_) => Column(
            children: [
              SizedBox(height: 14.sp,),
              VisibilityDetector(
                key: Key(id.toString()),
                onVisibilityChanged: (VisibilityInfo info) {
                  FFLog.info("${info.visibleFraction}%",tag: "VisibilityDetector");
                  controller.updateBannerAutoPlay(info.visibleFraction>0);
                 },
                child: SizedBox(
                      height: 372.sp,
                      child: Stack(
                        children: [
                          Obx(() => Swiper(
                                controller: controller.swiperController,
                                initialIndex: controller.currentIndex.value,
                                height: 354.sp,
                                viewportFraction: viewportFraction,
                                autoPlay: controller.bannerAutoPlay.value,
                                loop: true,
                                itemVerticalOffset: 10.sp,
                                animationDuration: const Duration(milliseconds: 500),
                                autoPlayInterval: const Duration(seconds: 3),
                                onPageScrollStateChange: (scrollState) {
                                  controller.updateShowBottomLayout(scrollState);
                                },
                                onPageChanged: (index) {
                                  controller.updateCurrentIndex(index);
                                  useTrackEvent(TrackEvent.reel_show, extra: {
                                    TrackEvent.reel_id: data[index].shortPlayResponse?.id?.toString() ?? "",
                                    TrackEvent.scene: EventValue.discoverBanner,
                                    TrackEvent.module_name: title,
                                    TrackEvent.module_id: moduleId,
                                  });
                                },
                                children: data
                                    .asMap()
                                    .entries
                                    .map((entry) => Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 6.sp),
                                      child: HorizontalBannerView(
                                        controller: controller,
                                        itemWidth: itemWidth,
                                        contentResponse: entry.value,
                                        index: entry.key,
                                        moduleName: title,
                                        moduleId: moduleId,
                                      ),
                                    ))
                                    .toList(),
                              )),
                          _buildBannerBottomLayout(itemWidth, controller)
                        ],
                      ),
                    ),
              ),
            ],
          ));
    }
  }

  // 底部布局
  Widget _buildBannerBottomLayout(itemWidth, controller) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Stack(
        alignment: Alignment.center, // 添加居中对齐
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center, // 添加居中对齐
            children: [
              SizedBox(height: 35.sp),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  controller.goToVideoDetail(moduleData?.contentResponseList?[controller.currentIndex.value].shortPlayResponse);
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 8.sp),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                    border: Border.all(color: Colors.white),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Assets.home.homeBannerPlay.image(
                        width: 20.sp,
                        height: 20.sp,
                      ),
                      SizedBox(width: 7.sp),
                      Text(
                        AppTrans.discoverFragmentPlayNow(),
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}

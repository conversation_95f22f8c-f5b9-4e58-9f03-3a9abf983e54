import 'dart:async';
import 'package:flutter/material.dart';

/// 滚动状态枚举
enum ScrollState {
  /// 空闲状态，没有滚动
  idle,

  /// 正在拖动
  dragging,

  /// 正在自动滚动到目标位置
  settling
}

/// 一个功能丰富的轮播组件，支持自动轮播、无限循环、自定义动画和指示器。
///
/// Swiper组件基于Flutter的PageView构建，提供了以下主要功能：
/// - 自动轮播：可设置是否自动播放和播放间隔时间
/// - 无限循环：支持无限滚动模式
/// - 自定义动画：可自定义切换动画的持续时间和曲线效果
/// - 视觉效果：当前项可突出显示，两侧项可部分可见
/// - 指示器：自定义底部指示器的样式和颜色
/// - 状态回调：提供页面变化和滚动状态变化的回调函数
///
/// 使用示例：
/// ```dart
/// Swiper(
///   height: 300,
///   viewportFraction: 0.8,
///   autoPlay: true,
///   loop: true,
///   children: [
///     // 轮播项列表
///     Container(color: Colors.red),
///     Container(color: Colors.blue),
///     Container(color: Colors.green),
///   ],
///   onPageChanged: (index) {
///     print('Current page: $index');
///   },
/// )
/// ```
///
/// 该组件适用于展示图片轮播、产品展示、广告轮播等场景，可根据需求灵活配置各项参数。
// 首先添加一个控制器类
class SwiperController {
  _SwiperState? _state;

  // 内部方法，由Swiper组件调用来关联状态
  void _attach(_SwiperState state) {
    _state = state;
  }

  // 内部方法，由Swiper组件调用来解除关联
  void _detach() {
    _state = null;
  }

  // 开始自动播放
  void startAutoplay() {
    _state?._startAutoSlide();
  }

  // 停止自动播放
  void stopAutoplay() {
    _state?._stopAutoSlide();
  }

  // 跳转到指定页面
  void jumpToPage(int page) {
    _state?._pageController.jumpToPage(page);
  }

  // 动画滚动到指定页面
  void animateToPage(int page, {Duration? duration, Curve? curve}) {
    _state?._pageController.animateToPage(
      page,
      duration: duration ?? const Duration(milliseconds: 500),
      curve: curve ?? Curves.easeOutCubic,
    );
  }

  // 获取当前页面
  int? get currentPage => _state?._currentPage;
}

class Swiper extends StatefulWidget {
  /// 轮播的子组件列表，每个子组件将作为一个轮播项
  final List<Widget> children;

  /// 轮播组件的高度
  final double height;

  /// 视口占比，控制每个轮播项在视口中的宽度比例，值越小，两侧可见的内容越多
  final double viewportFraction;

  /// 自动播放的时间间隔
  final Duration autoPlayInterval;

  /// 切换动画的持续时间
  final Duration animationDuration;

  /// 切换动画的曲线效果
  final Curve animationCurve;

  /// 是否自动播放
  final bool autoPlay;

  /// 是否循环播放，设为true时可以无限滚动
  final bool loop;

  /// 初始选中的索引，默认为0
  final int initialIndex;

  /// 页面变化时的回调函数，参数为当前页面的索引
  final Function(int)? onPageChanged;

  /// 页面滚动状态变化时的回调函数
  final Function(ScrollState)? onPageScrollStateChange;

  /// 当前页指示器的宽度
  final double activeDotWidth;

  /// 非当前页指示器的宽度
  final double inactiveDotWidth;

  /// 指示器的高度
  final double dotHeight;

  /// 当前页指示器的颜色
  final Color? activeDotColor;

  /// 非当前页指示器的颜色
  final Color? inactiveDotColor;

  /// 轮播项的垂直偏移量，用于创建当前项突出的效果
  final double itemVerticalOffset;

  /// 外部控制器，用于控制轮播图的播放状态
  final SwiperController? controller;

  const Swiper({
    super.key,
    required this.children,
    this.height = 300,
    this.viewportFraction = 0.75,
    this.autoPlayInterval = const Duration(seconds: 5),
    this.animationDuration = const Duration(milliseconds: 500),
    this.animationCurve = Curves.easeOutCubic,
    this.autoPlay = true,
    this.loop = true,
    this.initialIndex = 0,
    this.onPageChanged,
    this.onPageScrollStateChange,
    this.activeDotWidth = 16,
    this.inactiveDotWidth = 8,
    this.dotHeight = 8,
    this.activeDotColor,
    this.inactiveDotColor,
    this.itemVerticalOffset = 20,
    this.controller,
  });

  @override
  State<Swiper> createState() => _SwiperState();
}

class _SwiperState extends State<Swiper> {
  late PageController _pageController;
  late int _currentPage;
  Timer? _timer;
  bool _isUserInteracting = false;
  ScrollState _currentScrollState = ScrollState.idle;
  bool _autoPlayEnabled = false;

  @override
  void initState() {
    super.initState();

    // 关联控制器
    widget.controller?._attach(this);

    // 初始化自动播放状态
    _autoPlayEnabled = widget.autoPlay;

    // 计算初始页面位置，考虑initialIndex
    int initialPage;
    if (widget.loop) {
      // 在无限循环模式下，设置一个足够大的初始位置
      // 确保可以向左滚动足够多的页面
      initialPage = widget.children.length * 1000 + widget.initialIndex;
    } else {
      initialPage = widget.initialIndex;
    }

    _currentPage = initialPage;

    _pageController = PageController(
      viewportFraction: widget.viewportFraction,
      initialPage: initialPage,
    );

    // 确保在初始化后立即通知页面变化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _pageController.jumpToPage(initialPage);
      if (_autoPlayEnabled) {
        _startAutoSlide();
      }
    });
  }

  @override
  void didUpdateWidget(Swiper oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果autoPlay属性发生变化，更新自动播放状态
    if (widget.autoPlay != oldWidget.autoPlay) {
      _autoPlayEnabled = widget.autoPlay;
      if (_autoPlayEnabled && !_isUserInteracting) {
        _startAutoSlide();
      } else {
        _stopAutoSlide();
      }
    }

    // 如果控制器发生变化，更新控制器关联
    if (widget.controller != oldWidget.controller) {
      oldWidget.controller?._detach();
      widget.controller?._attach(this);
    }
  }

  @override
  void dispose() {
    _stopAutoSlide();
    widget.controller?._detach();
    _pageController.dispose();
    super.dispose();
  }

  bool _isAutoScrolling = false;

    void _startAutoSlide() {
      // 如果用户正在交互或自动播放被禁用，不启动自动滚动
      if (_isUserInteracting || !_autoPlayEnabled || _timer != null) return;
  
      _stopAutoSlide();
  
      _timer = Timer.periodic(widget.autoPlayInterval, (timer) {
        if (widget.children.isEmpty) return;
  
        // 标记开始自动滚动
        _isAutoScrolling = true;
        
        // 无限滚动模式下，直接增加页码即可
        _currentPage++;
  
        if (_pageController.hasClients) {
          _pageController.animateToPage(
            _currentPage,
            duration: widget.animationDuration,
            curve: widget.animationCurve,
          ).then((_) {
            // 标记自动滚动结束
            _isAutoScrolling = false;
          });
        } else {
          _isAutoScrolling = false;
        }
      });
    }

  void _stopAutoSlide() {
    _timer?.cancel();
    _timer = null;
  }

  // 处理滚动状态变化
  void _handleScrollStateChange(ScrollState newState) {
    if (_currentScrollState == newState) return;

    _currentScrollState = newState;

    // 调用回调函数
    if (widget.onPageScrollStateChange != null) {
      widget.onPageScrollStateChange!(newState);
    }

    // 根据滚动状态控制自动播放
    if (newState == ScrollState.dragging) {
      setState(() {
        _isUserInteracting = true;
      });
      _stopAutoSlide();
    } else if (newState == ScrollState.idle && _isUserInteracting) {
      setState(() {
        _isUserInteracting = false;
      });
      if (_autoPlayEnabled) {
        _startAutoSlide();
      }
    }
  }

  // 处理滚动通知
  bool _handleScrollNotification(ScrollNotification notification) {
    // 如果是自动滚动，忽略这个通知
    if (_isAutoScrolling) return true;
    
    if (notification is ScrollStartNotification && notification.dragDetails != null) {
      _handleScrollStateChange(ScrollState.dragging);
    } else if (notification is ScrollUpdateNotification) {
      if (notification.dragDetails == null && _currentScrollState == ScrollState.dragging) {
        _handleScrollStateChange(ScrollState.settling);
      }
    } else if (notification is ScrollEndNotification) {
      _handleScrollStateChange(ScrollState.idle);
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.children.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        SizedBox(
            height: widget.height,
            child: NotificationListener<ScrollNotification>(
              onNotification: _handleScrollNotification,
              child: PageView.builder(
                controller: _pageController,
                itemCount: widget.loop ? null : widget.children.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                  if (widget.onPageChanged != null) {
                    // 计算实际索引，用于回调
                    final actualIndex =
                        widget.loop ? index % widget.children.length : index;
                    widget.onPageChanged!(actualIndex);
                  }
                },
                pageSnapping: true,
                itemBuilder: (context, index) {
                  // 计算实际索引，用于构建item
                  final itemIndex =
                      widget.loop ? index % widget.children.length : index;
                  return _buildSwiperItem(widget.children[itemIndex], index);
                },
              ),
            )),
        // const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildSwiperItem(Widget child, int index) {
    final bool isCurrentPage = index == _currentPage;
    var alignment = Alignment.center;
    if(index<_currentPage){
      alignment = Alignment.centerRight;
    }else{
      alignment = Alignment.centerLeft;
    }
    return AnimatedScale(
      scale: isCurrentPage ? 1.0 : 0.82,
      duration: widget.animationDuration,
      curve: widget.animationCurve,
      alignment:alignment,
      child: child,
    );
  }
}

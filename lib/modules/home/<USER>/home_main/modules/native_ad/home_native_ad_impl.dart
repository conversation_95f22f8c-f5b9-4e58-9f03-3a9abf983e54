import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_main_module_protocol.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_native_model.dart';

class HomeNativeAdImpl with HomeMainModuleDefault {

  // 添加一个变量来存储广告模型
  AdAmNativeModel? _adModel;

  @override
  bool get isValid {
    return true;
  }

  @override
  int getColumsNumber() {
    return 1;
  }

  @override
  int getRowsNumber() {
    return 1;
  }

  @override
  EdgeInsetsGeometry get padding => EdgeInsets.only(left: 16.sp, right: 16.sp);

  @override
  bool get isShowMore => false;

  @override
  Widget getWidget(int row, int colum) {
    return FutureBuilder<Widget?>(
      future: _loadAdView(),
      builder: (context, snapshot) {
        final adView = snapshot.data;
        if (snapshot.hasData && adView != null) {
          // 成功加载广告Widget且不为null时才展示
          return Container(
            width: double.infinity,
            margin: EdgeInsets.only(bottom: 16.sp),
            child: Center(child: adView),
          );
        } else {
          // 加载中、加载失败或返回null时不显示任何内容
          return const SizedBox();
        }
      },
    );
  }


  Future<Widget?> _loadAdView() async {

    if (_adModel != null) {
      return await AdManager().getNativeAdViewForAdModel(AdScene.homeFeed, _adModel!);
    }
    // 先获取广告模型
    final adModel = await AdManager().getAdModel(AdScene.homeFeed);
    if (adModel == null) {
      return null;
    }
    // 将广告模型转换为原生广告模型并存储
    if (adModel is AdAmNativeModel) {
      _adModel = adModel;
      // 使用存储的模型获取广告视图
      return await AdManager().getNativeAdViewForAdModel(AdScene.homeFeed, _adModel!);
    }
    return null;
  } 

    // 添加dispose方法
  void dispose() {
    if (_adModel != null) {
      _adModel?.safeDispose();
      _adModel = null;
    }
  }
  
}
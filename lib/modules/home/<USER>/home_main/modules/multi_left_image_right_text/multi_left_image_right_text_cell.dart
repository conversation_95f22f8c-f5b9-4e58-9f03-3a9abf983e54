import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/components/ui/short_tag_view.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/gen/fonts.gen.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';

class MultiLeftImageRightTextCell extends StatelessWidget {
  final ShortPlayResponseList shortPlayData;

  const MultiLeftImageRightTextCell({
    super.key,
    required this.shortPlayData,
  });

  @override
  Widget build(BuildContext context) {
    final placeholderImage = Assets.home.homePlaceholder.image();
    final displayTags = shortPlayData.displayTags;
    return SizedBox(
      width: 273.sp,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: CoverWidget(
              imageUrl: shortPlayData.picUrl ?? '',
              width: 80.sp,  // 修改为80
              height: 106.sp,  // 修改为106
              fit: BoxFit.cover,
              compress: false,
              placeholder: (context, url) => placeholderImage,
              errorWidget: (context, url, error) => placeholderImage,
            ),
          ),
          SizedBox(width: 10.sp),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 3.sp),
                if (shortPlayData.shortPlayName?.isNotEmpty ?? false)
                  Text(
                    shortPlayData.shortPlayName ?? '',
                    style:  TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                      fontFamily: FontFamily.poppins,
                      fontStyle: FontStyle.normal,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                if (shortPlayData.shortPlayName?.isNotEmpty ?? false)
                  SizedBox(height: 5.sp),
                if (displayTags.isNotEmpty)
                  ShortTagView(tags: displayTags),
                if (displayTags.isNotEmpty)
                  SizedBox(height: 4.sp),
                Text(
                  shortPlayData.summary ?? '',
                  style:  TextStyle(
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF9F9FA2),
                    fontFamily: FontFamily.poppins,
                    fontStyle: FontStyle.normal,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
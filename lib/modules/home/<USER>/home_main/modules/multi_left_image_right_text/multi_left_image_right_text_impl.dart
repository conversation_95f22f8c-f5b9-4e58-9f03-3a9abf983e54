import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/multi_left_image_right_text/multi_left_image_right_text_controller.dart';
import 'package:playlet/routers/app_navigator.dart';

import '../../home_main_module_protocol.dart';
import 'multi_left_image_right_text_view.dart';

// 瀑布流
class MultiLeftImageRightTextImpl with HomeMainModuleDefault {

  @override
  EdgeInsetsGeometry get padding => EdgeInsets.only(left: 16.sp, bottom: 30.sp);

  @override
  int getRowsNumber() {
    return 1;
  }

  @override
  int getColumsNumber() {
    return 1;
  }

  @override
  Widget getWidget(int row, int colum) {
    final controller = MultiLeftImageRightTextController();
    controller.shortPlayResponseList = moduleData?.shortPlayResponseList;
    return GetBuilder<MultiLeftImageRightTextController>(
        init: controller, builder: (_) =>  MultiLeftImageRightTextView(moduleContext: HomeModuleContext(
          moduleId: moduleId, 
          moduleName: title,
          moduleStyle: moduleData?.style,
        )));
  }

  @override
  void clickMoreEvent() {
    if (moduleData == null) {
      return;
    }
    AppNavigator.startMorePage(moduleData!);
  }
}

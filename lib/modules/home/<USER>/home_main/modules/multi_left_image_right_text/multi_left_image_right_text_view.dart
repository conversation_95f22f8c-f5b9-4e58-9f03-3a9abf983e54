import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/multi_left_image_right_text/multi_left_image_right_text_controller.dart';
import 'package:playlet/utils/track_event.dart';
import 'multi_left_image_right_text_cell.dart';

class MultiLeftImageRightTextView
    extends GetView<MultiLeftImageRightTextController> {
  final HomeModuleContext moduleContext;

  const MultiLeftImageRightTextView({super.key, required this.moduleContext});

  @override
  Widget build(BuildContext context) {
    final shortPlayList = controller.shortPlayResponseList;
    if (shortPlayList == null || shortPlayList.isEmpty) {
      return const SizedBox();
    }

    double crossAxisSpacing = 12.sp;
    int rowNumber = shortPlayList.length < 3 ? shortPlayList.length : 3;
    double height = rowNumber * 106.sp + (rowNumber - 1) * crossAxisSpacing;

    return SizedBox(
      height: height,
      child: CustomScrollView(
        scrollDirection: Axis.horizontal,
        slivers: [
          SliverMasonryGrid.count(
            crossAxisCount: rowNumber,
            mainAxisSpacing: 18.sp,
            crossAxisSpacing: crossAxisSpacing,
            itemBuilder: (context, index) {
              final shortPlayData = shortPlayList[index];
              useTrackEvent(TrackEvent.reel_show, extra: {
                TrackEvent.reel_id: shortPlayData.id?.toString() ?? "",
                TrackEvent.scene: TrackEvent.discover,
                TrackEvent.module_name: moduleContext.moduleName ?? "",
                TrackEvent.module_id: moduleContext.moduleId ?? "",
              });
              return GestureDetector(
                  onTap: () {
                    controller.goToVideoDetail(
                        shortPlayData, moduleContext, index);
                  },
                  child: MultiLeftImageRightTextCell(
                    shortPlayData: shortPlayData,
                  ));
            },
            childCount: shortPlayList.length,
          ),
        ],
      ),
    );
  }
}

import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/events.dart';

class MultiLeftImageRightTextController extends GetxController {
  late List<ShortPlayResponseList>? shortPlayResponseList;
  late final BannerResponseList? bannerResponseList;
  StreamSubscription? shortWatchHistoryEventShortSubscription;

  @override
  void onInit() {
    super.onInit();
    shortWatchHistoryEventShortSubscription = eventBus.on<ShortWatchHistoryEvent>().listen((event) {
      var item = shortPlayResponseList?.firstWhereOrNull((item)=>item.id == event.shortPlayId);
      item?.episodeNum = event.episodeNum;
    });
  }

  @override
  void onClose() {
    shortWatchHistoryEventShortSubscription?.cancel();
    super.onClose();
  }

  void goToVideoDetail(ShortPlayResponseList shortPlayData, HomeModuleContext? moduleContext, int? positionId) {
    int? id = shortPlayData.id;
    if (id == null) return;
    int? episodeNum = shortPlayData.episodeNum ?? 1;
    AppNavigator.startDetailsPage(DetailsOptions(
        businessId: id,
        playerEpisodeIndex: episodeNum - 1,
        scene: TrackEvent.discover,
        from: EventValue.fromDiscover,
        moduleName: moduleContext?.moduleName ?? "",
        moduleId: moduleContext?.moduleId ?? "",
        moduleStyle: moduleContext?.moduleStyle,
        positionId: positionId?.toString() ?? "",
        ),
    );
  }
}

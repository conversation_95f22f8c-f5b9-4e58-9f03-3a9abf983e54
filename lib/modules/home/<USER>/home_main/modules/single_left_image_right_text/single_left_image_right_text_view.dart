import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/components/ui/short_tag_view.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/gen/fonts.gen.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/single_left_image_right_text/single_left_image_right_text_controller.dart';
import 'package:playlet/utils/track_event.dart';

class SingleLeftImageRightTextView extends StatelessWidget {
  final String tag;

  const SingleLeftImageRightTextView({super.key, required this.tag});

  @override
  Widget build(BuildContext context) {
    // 使用 tag 获取对应的控制器
    final controller = Get.find<SingleLeftImageRightTextController>(tag: tag);

    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: controller.shortData.id?.toString() ?? "",
      TrackEvent.scene: TrackEvent.discover,
      TrackEvent.module_name: controller.homeModuleContext?.moduleName ?? "",
      TrackEvent.module_id: controller.homeModuleContext?.moduleId ?? "",
    });

    final displayTags = controller.displayTags;
    final updateEpisodeText = controller.updateEpisodeText;

    final placeholderImage = Assets.home.homePlaceholder.image();
    return GestureDetector(
      onTap: () {
        controller.goToVideoDetail();
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧图片
          SizedBox(
            width: 126.sp,
            height: 168.sp,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: CoverWidget(
                imageUrl: controller.pictureUrl,
                fit: BoxFit.cover,
                compress: false,
                width: 126.sp,
                height: 168.sp,
                placeholder: (context, url) => placeholderImage,
                errorWidget: (context, url, error) => placeholderImage,
              ),
            ),
          ),
          SizedBox(width: 12.sp),
          // 右侧文本内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 10.sp),
                if (controller.shortPlayName.isNotEmpty)
                  Text(
                    controller.shortPlayName,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                      fontFamily: FontFamily.poppins,
                      fontStyle: FontStyle.normal,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                if (controller.shortPlayName.isNotEmpty)
                  SizedBox(height: 10.sp),

                // 标签
                if (displayTags.isNotEmpty) ShortTagView(tags: displayTags),
                if (displayTags.isNotEmpty) SizedBox(height: 10.sp),

                // 描述文本
                if (controller.summary.isNotEmpty)
                  Text(
                    controller.summary,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF9F9FA2),
                      fontFamily: FontFamily.poppins,
                      fontStyle: FontStyle.normal,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                if (controller.summary.isNotEmpty) SizedBox(height: 10.sp),
                // 更新进度
                if (updateEpisodeText.isNotEmpty)
                  Text(
                    updateEpisodeText,
                    style: const TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF9F9FA2),
                      fontFamily: FontFamily.poppins,
                      fontStyle: FontStyle.normal,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

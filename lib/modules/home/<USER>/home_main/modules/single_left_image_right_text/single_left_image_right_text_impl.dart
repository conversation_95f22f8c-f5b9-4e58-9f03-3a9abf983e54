import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/single_left_image_right_text/single_left_image_right_text_controller.dart';
import 'package:playlet/routers/app_navigator.dart';

import '../../home_main_module_protocol.dart';
import 'single_left_image_right_text_view.dart';

// 瀑布流
class SingleLeftImageRightTextImpl with HomeMainModuleDefault {

  @override
  double get verticalSpacing => 12.sp;

  @override
  int getRowsNumber() {
    return moduleData?.shortPlayResponseList?.length ?? 0;
  }

  @override
  int getColumsNumber() {
    return 1;
  }

  @override
  Widget getWidget(int row, int colum) {
    final shortData = getShortData(row, colum);
    if (shortData == null) {
      return const SizedBox();
    }
    final id = shortData.id;
    if (id == null) {
      return const SizedBox();
    }

    final String uniqueTag = id.toString();
    
    final controller = Get.put(
      SingleLeftImageRightTextController(
        HomeModuleContext(
          moduleId: moduleId, 
          moduleName: title,
          moduleStyle: moduleData?.style,
        ), 
        row),
      tag: uniqueTag,
    );
    controller.shortData = shortData;
    
    return GetBuilder<SingleLeftImageRightTextController>(
      tag: uniqueTag,
      builder: (_) => SingleLeftImageRightTextView(tag: uniqueTag),
    );
  }

  @override
  void clickMoreEvent() {
    final data = moduleData;
    if (data == null) {
      return;
    }
    AppNavigator.startMorePage(data);
  }

  // 获取行列对应的数据
  ShortPlayResponseList? getShortData(int row, int colum) {
    final list = moduleData?.shortPlayResponseList;
    if (list == null) {
      return null;
    }
    final index = row;
    if (index >= list.length) {
      return null;
    }
    return list[index];
  }
}
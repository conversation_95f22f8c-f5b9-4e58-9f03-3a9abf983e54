import 'dart:async';

import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:get/get.dart' hide Trans;
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/events.dart';

class SingleLeftImageRightTextController extends GetxController {
  late ShortPlayResponseList shortData;
  late int positionId; // 用于埋点, 用于判断是哪个位置的
  late HomeModuleContext? homeModuleContext;
  String get pictureUrl => shortData.picUrl ?? '';

  String get shortPlayName => shortData.shortPlayName ?? '';

  String get summary => shortData.summary ?? '';
  StreamSubscription? shortWatchHistoryEventShortSubscription;

  String get updateEpisodeText {
    int updateEpisode = shortData.updateEpisode ?? 1;
    int totalEpisodes = shortData.totalEpisodes ?? 1;
    if (updateEpisode >= totalEpisodes) {
      return AppTrans.videoEpisodeAll(totalEpisodes);
    }
    return AppTrans.videoEpisodeUpdateProgress(updateEpisode, totalEpisodes);
  }

  List<String> get displayTags => shortData.displayTags;

  SingleLeftImageRightTextController(this.homeModuleContext, this.positionId);

  @override
  void onInit() {
    super.onInit();
    shortWatchHistoryEventShortSubscription = eventBus.on<ShortWatchHistoryEvent>().listen((event) {
      if(shortData.id == event.shortPlayId){
        shortData.episodeNum = event.episodeNum;
      }
    });
  }

  @override
  void onClose() {
    shortWatchHistoryEventShortSubscription?.cancel();
    super.onClose();
  }

  void goToVideoDetail() {
    int? id = shortData.id;
    if (id == null) return;
    int? episodeNum = shortData.episodeNum ?? 1;
    AppNavigator.startDetailsPage(DetailsOptions(
        businessId: id,
        playerEpisodeIndex: episodeNum - 1,
        scene: TrackEvent.discover,
        from: EventValue.fromDiscover,
        moduleId: homeModuleContext?.moduleId ?? "",
        moduleName: homeModuleContext?.moduleName ?? "",
        moduleStyle: homeModuleContext?.moduleStyle,
        positionId: positionId.toString()
        )
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/single_row_rank/single_row_rank_controller.dart';
import 'package:get/get.dart';
import 'package:playlet/utils/index.dart';
import 'package:playlet/utils/track_event.dart';

class SingleRowRankView extends StatefulWidget {
  final String tag;
  final int index;
  final ShortPlayResponseList shortPlayResponseList;
  final HomeModuleContext homeModuleContext;

  const SingleRowRankView({super.key, required this.index, required this.shortPlayResponseList, required this.tag, required this.homeModuleContext});

  @override
  State<SingleRowRankView> createState() => _SingleRowRankViewState();
}

class _SingleRowRankViewState extends State<SingleRowRankView> {

  late SingleRowRankController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<SingleRowRankController>(tag: widget.tag);
    final imageUrl = widget.shortPlayResponseList.picUrl ?? "";
    final url = Utils.getImageRatioScale(
      imageUrl,
      width: 144.sp,
      height: 192.sp,
    );
    controller.loadImageWithDio(imageUrl, url);
  }

  @override
  Widget build(BuildContext context) {
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: widget.shortPlayResponseList.id?.toString() ?? "",
      TrackEvent.scene: TrackEvent.discover,
      TrackEvent.module_name: widget.homeModuleContext.moduleName ?? "",
      TrackEvent.module_id: widget.homeModuleContext.moduleId ?? "",
    });

    return GestureDetector(
      onTap: () {
        controller.goToVideoDetail(widget.shortPlayResponseList, widget.index, widget.homeModuleContext);
      },
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(4.r)),
            child: CoverWidget(
              imageUrl: widget.shortPlayResponseList.picUrl ?? "",
              width: 144.sp,
              height: 192.sp,
              fit: BoxFit.cover,
              compress: false,
            ),
          ),
          Positioned(
            bottom: 0,
            child: GetBuilder<SingleRowRankController>(
                id: 'gradient-${widget.shortPlayResponseList.picUrl}',
                init: controller,
                tag: widget.tag,
                builder: (context) {
                  return Container(
                      width: 144.sp,
                      height: 68.sp,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(4.r), bottomRight: Radius.circular(4.r)),
                        color: controller.getColor(widget.shortPlayResponseList.picUrl ?? ""),
                      ));
                }),
          ),
          Positioned(
              bottom: 0,
              child: Container(
                  width: 144.sp,
                  height: 68.sp,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(bottomLeft: Radius.circular(4.r), bottomRight: Radius.circular(4.r)),
                    color: const Color(0x66000000),
                  ))),
          Positioned(
            left: 6.sp,
            bottom: 12.sp,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "TOP ${widget.index + 1}",
                  style: TextStyle(fontSize: 20.sp, color: Colors.white, fontWeight: FontWeight.w800, shadows: const [
                    Shadow(
                      offset: Offset(0, 0),
                      blurRadius: 3.0,
                      color: Color.fromRGBO(0, 0, 0, 0.20),
                    ),
                  ]),
                ),
                SizedBox(height: 4.sp),
                SizedBox(
                  width: 132.sp,
                  child: Text(
                    widget.shortPlayResponseList.shortPlayName ?? "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      shadows: const [
                        Shadow(
                          offset: Offset(0, 0),
                          blurRadius: 3.0,
                          color: Color.fromRGBO(0, 0, 0, 0.20),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

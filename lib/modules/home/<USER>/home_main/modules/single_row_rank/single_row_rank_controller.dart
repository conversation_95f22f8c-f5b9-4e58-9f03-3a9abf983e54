import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/common/color/color_dominan_util.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/events.dart';

class SingleRowRankController extends GetxController {

  late List<ShortPlayResponseList>? shortPlayResponseList;
  StreamSubscription? shortWatchHistoryEventShortSubscription;

  final Map<String, Color> colorCache = {};

  @override
  void onInit() {
    super.onInit();
    shortWatchHistoryEventShortSubscription = eventBus.on<ShortWatchHistoryEvent>().listen((event) {
      var item = shortPlayResponseList?.firstWhereOrNull((item)=>item.id == event.shortPlayId);
      item?.episodeNum = event.episodeNum;
    });
  }

  @override
  void onClose() {
    shortWatchHistoryEventShortSubscription?.cancel();
    super.onClose();
  }

  setColor(String imageUrl, Color color) {
    colorCache[imageUrl] = color;
    update(['gradient-$imageUrl']);
  }

  Color getColor(String imageUrl) {
    return colorCache[imageUrl] ?? Colors.black;
  }

  bool hasColor(String imageUrl) {
    return colorCache.containsKey(imageUrl);
  }

  /// 通过 Dio 加载图片
  Future<void> loadImageWithDio(String imageUrl, String scaleUrl) async {
    try {
      if (hasColor(imageUrl)) {
        return;
      }
      // 加载中，先设置默认颜色
      setColor(imageUrl, Colors.black);
      Color? color = await ColoDominantUtil.fetchDominantColor(scaleUrl);
      if (color != null) {
        setColor(imageUrl, color);
      } else {
        // 只架加载一次
        FFLog.debug('主色加载失败: $imageUrl', tag: "loadImageWithDio");
      }
    } catch (e) {
      FFLog.debug('主色加载失败: $e', tag: "loadImageWithDio");
      colorCache.remove(imageUrl);
    }
  }

  void goToVideoDetail(ShortPlayResponseList? shortPlayResponseList,int index, HomeModuleContext homeModuleContext) {
    int? id = shortPlayResponseList?.id;
    if (id == null) return;
    int? episodeNum = shortPlayResponseList?.episodeNum ?? 1;
    AppNavigator.startDetailsPage(DetailsOptions(
        businessId: id,
        playerEpisodeIndex: episodeNum - 1,
        scene: TrackEvent.discover,
        moduleId: homeModuleContext.moduleId ?? "",
        moduleName: homeModuleContext.moduleName ?? "",
        moduleStyle: homeModuleContext.moduleStyle,
        positionId: index.toString(),
        from: EventValue.fromDiscover));
  }
}

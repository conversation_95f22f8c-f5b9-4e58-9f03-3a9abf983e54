import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/track_event.dart';

class TwoRowsThreeColumnsView extends StatefulWidget {
  final HomeModuleContext homeModuleContext;
  final ShortPlayResponseList shortPlayResponseList;
  final double width; // 宽度
  final int index; // 索引

  const TwoRowsThreeColumnsView(
      {super.key,
      required this.shortPlayResponseList,
      required this.width,
      required this.homeModuleContext,
      required this.index});

  @override
  State<TwoRowsThreeColumnsView> createState() =>
      _TwoRowsThreeColumnsViewState();
}

class _TwoRowsThreeColumnsViewState extends State<TwoRowsThreeColumnsView> {
  @override
  Widget build(BuildContext context) {
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: widget.shortPlayResponseList.id?.toString() ?? "",
      TrackEvent.scene: TrackEvent.discover,
      TrackEvent.module_name: widget.homeModuleContext.moduleName ?? "",
      TrackEvent.module_id: widget.homeModuleContext.moduleId ?? "",
    });

    return GestureDetector(
      onTap: () {
        goToVideoDetail(widget.shortPlayResponseList);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(4.r)),
              child: CoverWidget(
                width: widget.width,
                height: widget.width * 152.sp / 114.sp,
                fit: BoxFit.cover,
                imageUrl: "${widget.shortPlayResponseList.picUrl}",
                compress: false,
              )),
          SizedBox(height: 8.sp),
          SizedBox(
            width: widget.width,
            height: 38.sp,
            child: Text(
              "${widget.shortPlayResponseList.shortPlayName}",
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 13.sp,
                height: 1.35,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void goToVideoDetail(ShortPlayResponseList? shortPlayResponseList) {
    int? id = shortPlayResponseList?.id;
    if (id == null) return;
    int? episodeNum = shortPlayResponseList?.episodeNum ?? 1;
    AppNavigator.startDetailsPage(DetailsOptions(
        businessId: id,
        playerEpisodeIndex: episodeNum - 1,
        scene: TrackEvent.discover,
        moduleId: widget.homeModuleContext.moduleId ?? "",
        moduleName: widget.homeModuleContext.moduleName ?? "",
        moduleStyle: widget.homeModuleContext.moduleStyle,
        positionId: widget.index.toString(),
        from: EventValue.fromDiscover));
  }
}

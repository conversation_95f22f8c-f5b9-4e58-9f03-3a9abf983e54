import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_main_module_protocol.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/two_rows_three_columns/two_rows_three_columns_controller.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/two_rows_three_columns/two_rows_three_columns_view.dart';
import 'package:get/get.dart';
import 'package:playlet/routers/app_navigator.dart';

class TwoRowsThreeColumnsImpl with HomeMainModuleDefault {
  @override
  void clickMoreEvent() {
    final data = moduleData;
    if (data == null) {
      return;
    }
    AppNavigator.startMorePage(data);
  }

  @override
  int getColumsNumber() => 1;

  @override
  int getRowsNumber() => 1;

  @override
  Widget getWidget(int row, int colum) {
    var movies = moduleData?.shortPlayResponseList ?? List.empty();
    if (movies.isEmpty) {
      return Container();
    }

    final controller = TwoRowsThreeColumnsController();
    controller.shortPlayResponseList = moduleData?.shortPlayResponseList;
    return GetBuilder<TwoRowsThreeColumnsController>(
        init: controller,
        builder: (_){
          final horizontalPadding = 16.sp;
          final itemSpacing = 10.sp;
          var itemWidth = (Get.width - 2 * horizontalPadding - 2 * itemSpacing) / 3;
          if (movies.length <= 3) {
            return Column(
              children: [
                _buildRows(movies, itemWidth),
              ],
            );
          } else {
            var maxSize = movies.length > 6 ? 6 : movies.length;
            return Column(
              children: [
                _buildRows(movies.sublist(0, 3), itemWidth),
                SizedBox(height: 12.sp),
                _buildRows(movies.sublist(3, maxSize), itemWidth),
              ],
            );
          }
        });
  }

  Widget _buildRows(List<ShortPlayResponseList> data, itemWidth) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildRowItem(0, itemWidth, data),
        _buildRowItem(1, itemWidth, data),
        _buildRowItem(2, itemWidth, data),
      ],
    );
  }

  Widget _buildRowItem(index, itemWidth, List<ShortPlayResponseList> data) {
    return index < data.length
        ? TwoRowsThreeColumnsView(
            shortPlayResponseList: data[index],
            width: itemWidth,
            homeModuleContext: HomeModuleContext(
              moduleId: moduleId,
              moduleName: title,
              moduleStyle: moduleData?.style,
            ),
            index: index)
        : Container(
            width: itemWidth,
          );
  }
}

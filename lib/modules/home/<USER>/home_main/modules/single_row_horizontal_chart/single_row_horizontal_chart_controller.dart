import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/utils/events.dart';

class SingleRowHorizontalChartController extends GetxController {
  late List<ShortPlayResponseList>? shortPlayResponseList;
  StreamSubscription? shortWatchHistoryEventShortSubscription;

  @override
  void onInit() {
    super.onInit();
    shortWatchHistoryEventShortSubscription = eventBus.on<ShortWatchHistoryEvent>().listen((event) {
      var item = shortPlayResponseList?.firstWhereOrNull((item)=>item.id == event.shortPlayId);
      item?.episodeNum = event.episodeNum;
    });
  }

  @override
  void onClose() {
    shortWatchHistoryEventShortSubscription?.cancel();
    super.onClose();
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/track_event.dart';

/// 单排横图子视图
class SingleRowHorizontalChartView extends StatefulWidget {
  final ShortPlayResponseList shortPlayResponseList;
  final HomeModuleContext homeModuleContext;
  final int positionId;

  const SingleRowHorizontalChartView({super.key, required this.shortPlayResponseList, required this.homeModuleContext, required this.positionId});

  @override
  State<SingleRowHorizontalChartView> createState() => _SingleRowHorizontalChartViewState();
}

class _SingleRowHorizontalChartViewState extends State<SingleRowHorizontalChartView> {
  @override
  Widget build(BuildContext context) {
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: widget.shortPlayResponseList.id?.toString() ?? "",
      TrackEvent.scene: TrackEvent.discover,
      TrackEvent.module_name: widget.homeModuleContext.moduleName ?? "",
      TrackEvent.module_id: widget.homeModuleContext.moduleId ?? "",
    });
    return GestureDetector(
      onTap: () {
        goToVideoDetail(widget.shortPlayResponseList);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(4.r)),
              child: CoverWidget(
                width: 112.sp,
                height: 149.sp,
                fit: BoxFit.cover,
                imageUrl: "${widget.shortPlayResponseList.picUrl}",
                compress: false,
              )),
          SizedBox(height: 8.sp),
          SizedBox(
            width: 112.sp,
            height: 38.sp,
            child: Text(
              "${widget.shortPlayResponseList.shortPlayName}",
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                height: 1.35,
                fontSize: 13.sp,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void goToVideoDetail(ShortPlayResponseList? shortPlayResponseList) {
    int? id = shortPlayResponseList?.id;
    if (id == null) return;
    int? episodeNum = shortPlayResponseList?.episodeNum ?? 1;

    AppNavigator.startDetailsPage(DetailsOptions(
      businessId: id,
      playerEpisodeIndex: episodeNum - 1,
      scene: TrackEvent.discover,
      from: EventValue.fromDiscover,
      moduleId: widget.homeModuleContext.moduleId ?? "",
      moduleName: widget.homeModuleContext.moduleName ?? "",
      moduleStyle: widget.homeModuleContext.moduleStyle,
      positionId: widget.positionId.toString(),
    ));
  }
}

import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_main_module_protocol.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/single_row_horizontal_chart/single_row_horizontal_chart_controller.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/single_row_horizontal_chart/single_row_horizontal_chart_view.dart';
import 'package:playlet/routers/app_navigator.dart';

class SingleRowHorizontalChartImpl with HomeMainModuleDefault {
  @override
  void clickMoreEvent() {
    final data = moduleData;
    if (data == null) {
      return;
    }
    AppNavigator.startMorePage(data);
  }

  @override
  int getColumsNumber() => 1;

  @override
  int getRowsNumber() => 1;

  @override
  Widget getWidget(int row, int colum) {
    var movies = moduleData?.shortPlayResponseList ?? List.empty();
    if (movies.isEmpty) {
      return Container();
    }
    final controller = SingleRowHorizontalChartController();
    controller.shortPlayResponseList = movies;
    return GetBuilder(
        init: controller,
        builder: (_) {
          return SizedBox(
            height: 198.sp,
            child: ListView.builder(
              scrollDirection: Axis.horizontal, // 水平滚动
              shrinkWrap: true,
              itemCount: movies.length,
              itemBuilder: (context, index) => Padding(
                  padding: EdgeInsets.only(right: 10.sp),
                  child: SingleRowHorizontalChartView(
                      shortPlayResponseList: movies[index],
                      homeModuleContext: HomeModuleContext(
                        moduleId: moduleId,
                        moduleName: title,
                        moduleStyle: moduleData?.style,
                      ),
                      positionId: index)),
            ),
          );
        });
  }

  @override
  EdgeInsetsGeometry get padding => EdgeInsets.only(left: 16.sp, bottom: 30.sp);
}

import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/routers/app_navigator.dart';

import '../../home_main_module_protocol.dart';
import 'waterfall_flow_controller.dart';
import 'waterfall_flow_view.dart';

// 瀑布流
class WaterfallFlowModuleImpl with HomeMainModuleDefault {

  late final int index;

  @override
  double get horizontalSpacing => 14.sp;

  @override
  double get verticalSpacing => 24.sp;
  
  @override
  int getRowsNumber() {
    final list = moduleData?.shortPlayResponseList;
    if (list == null) {
      return 0;
    }
    return (list.length + 1) ~/ 2;
  }

  @override
  int getColumsNumber() {
    return 2;
  }

  @override
  Widget getWidget(int row, int colum) {
    final shortData = getShortData(row, colum);
    if (shortData == null) {
      return const SizedBox();
    }
    final id = shortData.id;
    if (id == null) {
      return const SizedBox();
    }

    final String tag = id.toString();

    final controller = Get.put(
      WaterfallFlowController(homeModuleContext: HomeModuleContext(
        moduleId: moduleId,
        moduleName: title,
        moduleStyle: moduleData?.style,
      ), index: row * 2 + colum),
      tag: tag,
    );
    controller.shortData = shortData;
    
    return GetBuilder<WaterfallFlowController>(
      tag: tag,
      builder: (_) => WaterfallFlowView(tag: tag,),
    );
  }

  @override
  void clickMoreEvent() {
    final data = moduleData;
    if (data == null) {
      return;
    }
   AppNavigator.startMorePage(data);
  }

  // 获取行列对应的数据
  ShortPlayResponseList? getShortData(int row, int colum) {
    final list = moduleData?.shortPlayResponseList;
    if (list == null) {
      return null;
    }
    final index = row * 2 + colum;
    if (index >= list.length) {
      return null;
    }
    return list[index];
  }
}
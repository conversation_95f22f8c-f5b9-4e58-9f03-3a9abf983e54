import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/gen/fonts.gen.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/waterfall_flow/waterfall_flow_controller.dart';
import 'package:playlet/utils/track_event.dart';

class WaterfallFlowView extends StatelessWidget {
  final String tag;
  
  const WaterfallFlowView({super.key, required this.tag});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WaterfallFlowController>(tag: tag);
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: controller.shortData.id?.toString() ?? "",
      TrackEvent.scene: TrackEvent.discover,
      TrackEvent.module_name: controller.homeModuleContext.moduleName ?? "",
      TrackEvent.module_id: controller.homeModuleContext.moduleId ?? "",
    });
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth.sp;
        final imageHeight = (232.sp / 174.sp) * width;

        final placeholderImage = Assets.home.homePlaceholder.image();
        return GestureDetector(
          onTap: () {
            controller.goToVideoDetail();
          },
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: CoverWidget(
                  imageUrl: controller.pictureUrl,
                  width: width,
                  height: imageHeight,
                  fit: BoxFit.cover,
                  compress: false,
                  errorWidget: (context, url, error) => placeholderImage,
                  placeholder: (context, url) => placeholderImage,
                ),
              ),
              SizedBox(height: 10.sp),
              Text(
                controller.shortPlayName,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                  fontFamily: FontFamily.poppins,
                  fontStyle: FontStyle.normal,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      },
    );
  }
}

import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_main_module_protocol.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_modules_context.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/events.dart';

class WaterfallFlowController extends GetxController {
  late ShortPlayResponseList shortData;

  final HomeModuleContext homeModuleContext;
  final int index;

  String get pictureUrl => shortData.picUrl ?? '';

  String get shortPlayName => shortData.shortPlayName ?? '';

  StreamSubscription? shortWatchHistoryEventShortSubscription;


  WaterfallFlowController({required this.homeModuleContext, required this.index});

  @override
  void onInit() {
    super.onInit();
    shortWatchHistoryEventShortSubscription = eventBus.on<ShortWatchHistoryEvent>().listen((event) {
      if(shortData.id == event.shortPlayId){
        shortData.episodeNum = event.episodeNum;
      }
    });
  }

  @override
  void onClose() {
    shortWatchHistoryEventShortSubscription?.cancel();
    super.onClose();
  }

  void goToVideoDetail() {
    int? id = shortData.id;
    if (id == null) return;
    int? episodeNum = shortData.episodeNum ?? 1;
    AppNavigator.startDetailsPage(DetailsOptions(
        businessId: id,
        playerEpisodeIndex: episodeNum - 1,
        scene: TrackEvent.discover,
        moduleId: homeModuleContext.moduleId ?? "",
        moduleName: homeModuleContext.moduleName ?? "",
        moduleStyle: homeModuleContext.moduleStyle,
        positionId: index.toString(),
        from: EventValue.fromDiscover
        ));
  }
}

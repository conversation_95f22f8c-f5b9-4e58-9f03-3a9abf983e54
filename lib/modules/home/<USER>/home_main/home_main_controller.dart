import 'dart:async';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/horizontal_banner/horizontal_banner_impl.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/multi_left_image_right_text/multi_left_image_right_text_impl.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/native_ad/home_native_ad_impl.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/single_row_horizontal_chart/single_row_horizontal_chart_impl.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/single_row_rank/single_row_rank_impl.dart';
import 'package:playlet/modules/home/<USER>/home_main/modules/two_rows_three_columns/two_rows_three_columns_impl.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/recently_watch_service.dart';
import 'home_main_module_protocol.dart';
import 'modules/single_left_image_right_text/single_left_image_right_text_impl.dart';
import 'modules/waterfall_flow/waterfall_flow_module_impl.dart';

class HomeMainController extends GetxController {
  late final Map<HomeModuleStyle, HomeMainModuleProtocol Function()> _allModulesInstances;
  final RxList<HomeMainModuleProtocol> _modulesInstances = <HomeMainModuleProtocol>[].obs;
  final RxBool showRecentlyView = false.obs;

  List<HomeMainModuleProtocol> get modules => _modulesInstances;
  final RxString appName = "".obs;

  RxDouble resourceBitBottomFloatOffset = 16.sp.obs;
  Timer? _resourceBitBottomFloatTimer;

  TabPageResponse? _pageModel;

  EasyRefreshController refreshController = EasyRefreshController(
    controlFinishRefresh: true,
  );

  StreamSubscription? _showOnHomeSubscription;

  void updateModel(TabPageResponse? model) {
    _pageModel = model;
    _parseModulesData();
  }

  @override
  void onInit() {
    _fetchAppName();
    _registerModules();
    _parseModulesData();
    _addRecentlyViewListener();
    super.onInit();
  }

  @override
  void onClose() {
    _showOnHomeSubscription?.cancel();
    _showOnHomeSubscription = null;
    _stopResourceBitBottomFloatTimer();
    refreshController.dispose();

    // 释放所有模块资源
    for (var module in _modulesInstances) {
      if (module is HomeNativeAdImpl) {
        (module as HomeNativeAdImpl).dispose();
      }
    }

    super.onClose();
  }

  void onRequestResult(bool success) {
    refreshController.finishRefresh(success ? IndicatorResult.success : IndicatorResult.fail);
  }

  void _fetchAppName() async{
    appName.value = await AppDeviceService().getAppName();
  }

  /// 更新滚动状态
  Future<void> updateScrollingState(bool scrolling) async {
    if (scrolling) {
      // 滑动时向右移动
      _stopResourceBitBottomFloatTimer();
      _updateResourceBitBottomFloatOffset(-46.sp);
    } else {
      // 停止滑动时停留3秒，再把底部悬浮恢复原位
      _startResourceBitBottomFloatTimer(() {
        _updateResourceBitBottomFloatOffset(16.sp);
      });
    }
  }

  // 注册模块实现类
  void _registerModules() {
    _allModulesInstances = {
      HomeModuleStyle.promotionBanner: () => HorizontalBannerImpl(),
      HomeModuleStyle.singleHorizontalImage: () => SingleRowHorizontalChartImpl(),
      HomeModuleStyle.leftImageRightText: () => SingleLeftImageRightTextImpl(),
      HomeModuleStyle.multiColumnLeftImageRightText: () => MultiLeftImageRightTextImpl(),
      HomeModuleStyle.doubleColumnWaterfall: () => WaterfallFlowModuleImpl(),
      HomeModuleStyle.rankingList: () => SingleRowRankImpl(),
      HomeModuleStyle.threeColumnGrid: () => TwoRowsThreeColumnsImpl(),
      HomeModuleStyle.nativeAd: () => HomeNativeAdImpl(),
    };
  }

  void _parseModulesData() {
    _modulesInstances.clear();

    final bannerResponseList = _pageModel?.bannerResponseList;
    if (bannerResponseList != null && bannerResponseList.isNotEmpty) {
      int moduleCount = 0;
      
      for (var moduleData in bannerResponseList) {
        final block = _allModulesInstances[moduleData.style];
        if (block == null) {
          continue;
        }
        final instance = block();
        instance.setModuleData(moduleData);
        if (!instance.isValid) {
          // 模块无效，不显示
          continue;
        }
        _modulesInstances.add(instance);
        
        moduleCount++;
        
        // 在第2个模块后及之后每隔3个模块插入广告
        if ((moduleCount == 2) || (moduleCount > 2 && (moduleCount - 2) % 3 == 0)) {
          final nativeAdInstance = _allModulesInstances[HomeModuleStyle.nativeAd]?.call();
          if (nativeAdInstance != null) {
            _modulesInstances.add(nativeAdInstance);
          }
        }
      }
    }
  }

  void _addRecentlyViewListener() {
    // 监听最近播放是否显示
    RecentlyWatchService recentlyWatchService = Get.find<RecentlyWatchService>();
    _showOnHomeSubscription?.cancel();
    _showOnHomeSubscription = recentlyWatchService.showOnHome.listen((value) {
      _updateShowRecentlyView(value);
    });
    _updateShowRecentlyView(recentlyWatchService.showOnHome.value && recentlyWatchService.recentlyWatchModel != null);
  }

  void _updateShowRecentlyView(bool show) {
    if (showRecentlyView.value == show) {
      return;
    }
    showRecentlyView.value = show;
  }
}

extension HomeMainControllerResourceBitExtend on HomeMainController {
  
  void _updateResourceBitBottomFloatOffset(double offset) {
    if (resourceBitBottomFloatOffset.value != offset) {
      resourceBitBottomFloatOffset.value = offset;
    }
  }

  /// 开启底部悬浮资源位定时器
  void _startResourceBitBottomFloatTimer(void Function() callback) {
    _stopResourceBitBottomFloatTimer();
    _resourceBitBottomFloatTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _stopResourceBitBottomFloatTimer();
      callback();
    });
  }

  /// 停止底部悬浮资源位定时器
  void _stopResourceBitBottomFloatTimer() {
    _resourceBitBottomFloatTimer?.cancel();
    _resourceBitBottomFloatTimer = null;
  }
}
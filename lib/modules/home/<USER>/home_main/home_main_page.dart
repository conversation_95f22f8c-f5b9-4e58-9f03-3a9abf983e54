import 'dart:async';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart'; // 需要添加这个依赖
import 'package:get/get.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/rewards_bind/rewards_animation_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/gen/fonts.gen.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_main_controller.dart';
import 'package:playlet/modules/home/<USER>/home_main/home_main_module_protocol.dart';
import 'package:playlet/modules/home/<USER>/recently_controller.dart';
import 'package:playlet/modules/home/<USER>/recently_view.dart';
import 'package:playlet/modules/resource_bit/bottom_float/bottom_float_view.dart';

class HomeMainPage extends GetView<HomeMainController> {
  final FutureOr Function()? onRefresh;

  const HomeMainPage({
    super.key,
    required this.onRefresh,
  });

  Widget _buildHeader(HomeMainModuleProtocol module) {
    final headerRightWidget = module.getHeaderRightWidget();
    if (module.title.isEmpty && !module.isShowMore && headerRightWidget == null) {
      return const SizedBox();
    }
    return SizedBox(
      height: 44.sp,
      child: Padding(
        padding: EdgeInsets.only(left: 16.sp),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              height: 30.sp,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      module.title,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        fontFamily: FontFamily.poppins,
                        fontStyle: FontStyle.normal,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  headerRightWidget ?? (module.isShowMore ? IconButton(
                    onPressed: module.clickMoreEvent,
                    icon: FittedBox(
                      fit: BoxFit.none,
                      child: Assets.home.homeMore.image(width: 24.sp, height: 24.sp),
                    ),
                  ) : const SizedBox()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(HomeMainModuleProtocol module) {
    final rowNumber = module.getRowsNumber();
    final columnNumber = module.getColumsNumber();
    final gridView = MasonryGridView.builder(
      gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columnNumber,
      ),
      mainAxisSpacing: module.verticalSpacing,
      crossAxisSpacing: module.horizontalSpacing,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final row = index ~/ columnNumber;
        final column = index % columnNumber;
        return module.getWidget(row, column);
      },
      itemCount: rowNumber * columnNumber,
    );

    return module.padding != EdgeInsets.zero
      ? Padding(
          padding: module.padding,
          child: gridView,
        )
      : gridView;
  }

  Widget _buildListView() {
    if (controller.modules.isEmpty) {
      return const EmptyWidget(pageFrom: EmptyPageFrom.discover,
            type: EmptyType.noContent);
    }
    return Column(
      children: [
        _buildTopLogo(),
        Expanded(
          child: EasyRefresh.builder(
                  refreshOnStart: false,
                  controller: controller.refreshController,
                  header: BuilderHeader(
                    builder: (context, state) {
                      if (state.mode == IndicatorMode.inactive ||
                          state.mode == IndicatorMode.done) {
                        return const SizedBox();
                      }
                      return Assets.loading.dotLoading.image(
                        width: 30.sp,
                        height: 30.sp,
                      );
                    },
                    triggerOffset: 50.sp,
                    clamping: false,
                    position: IndicatorPosition.above,
                  ),
                  onRefresh: onRefresh,
                  triggerAxis: Axis.vertical,
                  childBuilder: (context, physics) => 
                    NotificationListener<ScrollNotification>(
                      onNotification: (ScrollNotification notification) {
                        if (notification is ScrollStartNotification) {
                          // dragDetails 不为空表示是用户手动拖拽触发的滚动
                          if (notification.dragDetails != null) {
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              controller.updateScrollingState(true);
                            });

                            if (Get.isRegistered<HomeRecentlyController>()) {
                              final recentlyController = Get.find<HomeRecentlyController>();
                              recentlyController.startAnimation();
                            }
                          }
                        } else if (notification is ScrollEndNotification) {
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            controller.updateScrollingState(false);
                          });
                        }
                        // 返回false允许通知继续冒泡
                        return false;
                      },
                      child: Obx(() => ListView(
                        physics: physics,
                        children: controller.modules.expand((module) {
                          return [
                            _buildHeader(module),
                            _buildContent(module),
                          ];
                        }).toList(),
                      ),),
                    ),
            ),
        ),
      ],
    );
  }

  Widget _buildTopLogo(){
    return Column(children: [
      SizedBox(
        height: 12.sp,
      ),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 20.sp,
              ),
              Assets.home.homeTopLogo.image(
                width: 38.sp,
                height: 38.sp,
              ),
              SizedBox(
                width: 6.sp,
              ),
              Obx(() => Text(
                controller.appName.value,
                style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white),
              ))
            ],
          ),

          const RewardsAnimationWidget(),
        ],
      ),
      SizedBox(
        height: 10.sp,
      ),
    ]);
  }

  Widget _buildResourceBitBottomFloatWidget() {
    return Obx(() => AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      bottom: 62.sp,
      right: controller.resourceBitBottomFloatOffset.value,
      child: HomeResourceBitBottomFloatView(
        closeButtonRight: 16.sp - controller.resourceBitBottomFloatOffset.value,
      )
    ));
  }

  Widget _buildRecentlyView() {
    return const Positioned(
      left: 0,
      bottom: 0,
      child: HomeRecentlyView()
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      List<Widget> children = [
        _buildListView()
      ];
      if (controller.showRecentlyView.value) {
        children.add(_buildRecentlyView());
      }
      children.add(_buildResourceBitBottomFloatWidget());
      return Stack(
        children: children,
      );
    });
  }
}

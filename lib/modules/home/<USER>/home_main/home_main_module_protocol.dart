import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';

abstract class HomeMainModuleProtocol {
  // 必须实现的

  // 行数
  int getRowsNumber();

  // 列数
  int getColumsNumber();

  // 获取行列对应的视图
  Widget getWidget(int row, int colum);

  // 可选实现的

  // 模块是否有效，如果false则不显示该模块
  bool get isValid;

  // 标题
  String get title;

  // 模块id
  String get moduleId;

  // 水平间距
  double get horizontalSpacing;
  
  // 垂直间距
  double get verticalSpacing;

  // 内边距
  EdgeInsetsGeometry get padding;

  // 是否显示更多按钮
  bool get isShowMore;

  // 填充数据
  void setModuleData(BannerResponseList data);

  // 点击更多按钮事件
  void clickMoreEvent();

  // 获取标题右侧视图
  Widget? getHeaderRightWidget();
}

mixin HomeMainModuleDefault implements HomeMainModuleProtocol {
  
  BannerResponseList? get moduleData => _moduleData;
  BannerResponseList? _moduleData;

  @override
  bool get isValid {
    final shortPlayResponseList = moduleData?.shortPlayResponseList;
    if (shortPlayResponseList == null || shortPlayResponseList.isEmpty) {
      return false;
    }
    return true;
  }

  @override
  String get title => moduleData?.title ?? '';

  @override
  String get moduleId => moduleData?.bannerId?.toString() ?? "";

  @override
  double get horizontalSpacing => 0.sp;
  
  @override
  double get verticalSpacing => 0.sp;

  @override
  EdgeInsetsGeometry get padding => EdgeInsets.only(left: 16.sp, right: 16.sp, bottom: 30.sp);

  @override
  bool get isShowMore => moduleData?.isShowMore??false;

  @override
  void setModuleData(BannerResponseList data) {
    _moduleData = data;
  }

  @override
  void clickMoreEvent() {}

  @override
  Widget? getHeaderRightWidget() => null;
}
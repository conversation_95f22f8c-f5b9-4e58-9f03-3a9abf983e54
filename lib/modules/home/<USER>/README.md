# home页面弹框统一管理
DialogChainManager用于管理弹框按优先级展示，DialogChainManager中已经定义了各种弹框的优先级。
通过调用DialogChainManager的add方法，把要弹框的任务加入到链表。
实现了只在home页面可见的时候展示，跳转到其他页面再回来，会继续展示剩余的弹框。

添加dialog管理可以有两种方式
1、传入DialogChain对象，DialogChain需要传组件widget和优先级priority，内部会直接实现了弹框，可以参考upgrade中的实现
目前内部是使用Get.dialog弹框，后续会改为使用SmartDialog弹框
``` dart
DialogChainManager.instance.add(
DialogChain(
AppUpgradeDialogWidget(upgrade: result),
DialogChainManager.appUpdate,
))
```

2、传入Chain对象，我可以实现处理一些业务后再自己弹框,需要传入优先级priority和执行逻辑。
需要注意的是逻辑执行完后，需要调用chain.finish()结束，这样才会执行下一个弹框的逻辑。可以参考notification_service中的实现
``` dart
DialogChainManager.instance.add(Chain(priority: DialogChainManager.customNotification, (chain, data) async {
    // 处理自己的任务
    // 弹框
    // 结束任务
    chain.finish()
}));
```
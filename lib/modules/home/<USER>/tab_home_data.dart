import 'package:playlet/service/ad/admodel/admob/ad_am_native_model.dart';

class TabHomeData {
  int? autoPlay;
  int? dramaRepository;
  TabPageResponse? tabPageResponse;
  List<HomeTabModel>? tabResponseList;

  TabHomeData({
    this.autoPlay,
    this.dramaRepository,
    this.tabPageResponse,
    this.tabResponseList,
  });

  factory TabHomeData.fromJson(Map<String, dynamic> json) {
    return TabHomeData(
      autoPlay: json['autoPlay'],
      dramaRepository: json['dramaRepository'],
      tabPageResponse: TabPageResponse.fromJson(json['tabPageResponse'] ?? {}),
      tabResponseList: (json['tabResponseList'] as List<dynamic>?)
          ?.map((e) => HomeTabModel.fromJson(e))
          .toList(),
    );
  }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'autoPlay': autoPlay,
  //     'dramaRepository': dramaRepository,
  //     'tabPageResponse': tabPageResponse?.toJson(),
  //     'tabResponseList': tabResponseList?.map((e) => e.toJson()).toList(),
  //   };
  // }
}

class TabPageResponse {
  List<BannerResponseList>? bannerResponseList;

  int? realizeType;
  int? tabId;

  TabPageResponse({
    this.bannerResponseList,
    this.realizeType,
    this.tabId,
  });

  factory TabPageResponse.fromJson(Map<String, dynamic> json) {
    return TabPageResponse(
      bannerResponseList: (json['bannerResponseList'] as List<dynamic>?)
          ?.map((e) => BannerResponseList.fromJson(e))
          .toList(),
      realizeType: json['realizeType'],
      tabId: json['tabId'],
    );
  }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'bannerResponseList': bannerResponseList?.map((e) => e.toJson()).toList(),
  //     'realizeType': realizeType,
  //     'tabId': tabId,
  //   };
  // }
}

class BannerResponseList {
  int? bannerId;
  bool? isShowMore;
  int? ruleCode;
  List<ShortPlayResponseList>? shortPlayResponseList;
  List<ContentResponse>? contentResponseList;
  int? showNum;
  int? sort;
  HomeModuleStyle? style;
  String? title;
  bool? useMultiTypeContentList;

  BannerResponseList({
    this.bannerId,
    this.isShowMore,
    this.ruleCode,
    this.shortPlayResponseList,
    this.contentResponseList,
    this.showNum,
    this.sort,
    this.style,
    this.title,
    this.useMultiTypeContentList,
  });

  factory BannerResponseList.fromJson(Map<String, dynamic> json) {
    return BannerResponseList(
      bannerId: json['bannerId'],
      isShowMore: json['isShowMore'],
      ruleCode: json['ruleCode'],
      shortPlayResponseList: (json['shortPlayResponseList'] as List<dynamic>?)
          ?.map((e) => ShortPlayResponseList.fromJson(e))
          .toList(),
      contentResponseList: (json['contentResponseList'] as List<dynamic>?)
          ?.map((e) => ContentResponse.fromJson(e))
          .toList(),
      showNum: json['showNum'],
      sort: json['sort'],
      style: HomeModuleStyle.fromValue(json['style']),
      // 修改这一行
      title: json['title'],
      useMultiTypeContentList: json['useMultiTypeContentList'],
    );
  }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'bannerId': bannerId,
  //     'isShowMore': isShowMore,
  //     'ruleCode': ruleCode,
  //     'shortPlayResponseList':
  //         shortPlayResponseList?.map((e) => e.toJson()).toList(),
  //     'contentResponseList':
  //         contentResponseList?.map((e) => e.toJson()).toList(),
  //     'showNum': showNum,
  //     'sort': sort,
  //     'style': style?.value, // 修改这一行
  //     'title': title,
  //     'useMultiTypeContentList': useMultiTypeContentList,
  //   };
  // }
}

class ShortPlayResponseList {
  int? collectNum;
  String? horizontalCoverId;
  int? id;
  bool? needDecrypt;
  String? picUrl;
  String? coverId;
  String? recommendContent;
  int? reservationNum;
  int? shortPlayCode;
  String? shortPlayName;
  int? shortPlayType;
  int? sort;
  String? summary;
  int? totalEpisodes;
  List<ShortPlayLabel>? labelResponseList;
  List<ShortPlayClass>? classList;
  int? updateEpisode;
  String? level1CategoryName;
  int? episodeNum;  
  // 添加一个变量来存储广告模型
  AdAmNativeModel? adModel;

  ShortPlayResponseList({
    this.collectNum,
    this.horizontalCoverId,
    this.id,
    this.needDecrypt,
    this.picUrl,
    this.coverId,
    this.recommendContent,
    this.reservationNum,
    this.shortPlayCode,
    this.shortPlayName,
    this.shortPlayType,
    this.sort,
    this.summary,
    this.totalEpisodes,
    this.labelResponseList,
    this.classList,
    this.updateEpisode,
    this.level1CategoryName,
    this.episodeNum,
  });

  factory ShortPlayResponseList.fromJson(Map<String, dynamic> json) {
    return ShortPlayResponseList(
      collectNum: json['collectNum'],
      horizontalCoverId: json['horizontalCoverId'],
      id: json['id'],
      needDecrypt: json['needDecrypt'],
      picUrl: json['picUrl'],
      coverId: json['coverId'],
      recommendContent: json['recommendContent'],
      reservationNum: json['reservationNum'],
      shortPlayCode: json['shortPlayCode'],
      shortPlayName: json['shortPlayName'],
      shortPlayType: json['shortPlayType'],
      sort: json['sort'],
      summary: json['summary'],
      totalEpisodes: json['totalEpisodes'],
      labelResponseList: (json['labelResponseList'] as List<dynamic>?)
              ?.map((e) => ShortPlayLabel.fromJson(e))
              .toList(),
      classList: (json['classList'] as List<dynamic>?)
              ?.map((e) => ShortPlayClass.fromJson(e))
              .toList(),
      updateEpisode: json['updateEpisode'],
      level1CategoryName: json['level1CategoryName'],
      episodeNum: json['episodeNum'],
    );
  }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'collectNum': collectNum,
  //     'horizontalCoverId': horizontalCoverId,
  //     'id': id,
  //     'needDecrypt': needDecrypt,
  //     'picUrl': picUrl,
  //     'recommendContent': recommendContent,
  //     'reservationNum': reservationNum,
  //     'shortPlayCode': shortPlayCode,
  //     'shortPlayName': shortPlayName,
  //     'shortPlayType': shortPlayType,
  //     'sort': sort,
  //     'summary': summary,
  //     'totalEpisodes': totalEpisodes,
  //     'labelResponseList': labelResponseList?.map((e) => e.toJson()).toList(),
  //     'classList': classList?.map((e) => e.toJson()).toList(),
  //     'updateEpisode': updateEpisode,
  //     'level1CategoryName': level1CategoryName,
  //     'episodeNum': episodeNum,
  //   };
  // }
}

extension ShortPlayResponseListExtension on ShortPlayResponseList {
  // 获取标签
  List<String> get displayTags {
    List<String> tags = [];
    if (level1CategoryName != null) {
      tags.add(level1CategoryName!);
    }
    final classes = classList;
    if (classes != null && classes.isNotEmpty) {
      for (var item in classes) {
        final displayName = item.displayName;
        if (displayName == null) {
          continue;
        }
        tags.add(displayName);
      }
    }
    final labelList = labelResponseList;
    if (labelList != null && labelList.isNotEmpty) {
      for (var item in labelList) {
        final labelName = item.labelName;
        if (labelName == null) {
          continue;
        }
        tags.add(labelName);
      }
    }
    return tags;
  }
}

class ContentResponse {
  int? contentType;
  String? coverId;
  ShortPlayResponseList? shortPlayResponse;
  int? sort;

  ContentResponse(
      {this.contentType, this.coverId, this.shortPlayResponse, this.sort});

  ContentResponse.fromJson(Map<String, dynamic> json) {
    contentType = json['contentType'];
    coverId = json['coverId'];
    shortPlayResponse = json['shortPlayResponse'] != null
        ? new ShortPlayResponseList.fromJson(json['shortPlayResponse'])
        : null;
    sort = json['sort'];
  }

  // Map<String, dynamic> toJson() {
  //   final Map<String, dynamic> data = new Map<String, dynamic>();
  //   data['contentType'] = this.contentType;
  //   data['coverId'] = this.coverId;
  //   if (this.shortPlayResponse != null) {
  //     data['shortPlayResponse'] = this.shortPlayResponse!.toJson();
  //   }
  //   data['sort'] = this.sort;
  //   return data;
  // }
}

class HomeTabModel {
  int? realizeType;
  int? sortValue;
  String? tabDisplayName;
  HomeTabFlag? tabFlagName;
  int? tabId;

  HomeTabModel({
    this.realizeType,
    this.sortValue,
    this.tabDisplayName,
    this.tabFlagName,
    this.tabId,
  });

  factory HomeTabModel.fromJson(Map<String, dynamic> json) {
    return HomeTabModel(
      realizeType: json['realizeType'],
      sortValue: json['sortValue'],
      tabDisplayName: json['tabDisplayName'],
      tabFlagName: HomeTabFlag.fromValue(json['tabFlagName']),
      tabId: json['tabId'],
    );
  }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'realizeType': realizeType,
  //     'sortValue': sortValue,
  //     'tabDisplayName': tabDisplayName,
  //     'tabFlagName': tabFlagName?.value,
  //     'tabId': tabId,
  //   };
  // }
}

class RankingResponse {
  int? bannerId;
  bool? isShowMore;
  int? rankingId;
  String? rankingName;
  List<ShortPlayResponseList>? shortPlayResponseList;

  RankingResponse(
      {this.bannerId,
      this.isShowMore,
      this.rankingId,
      this.rankingName,
      this.shortPlayResponseList});

  RankingResponse.fromJson(Map<String, dynamic> json) {
    bannerId = json['bannerId'];
    isShowMore = json['isShowMore'];
    rankingId = json['rankingId'];
    rankingName = json['rankingName'];
    if (json['shortPlayResponseList'] != null) {
      shortPlayResponseList = <ShortPlayResponseList>[];
      json['shortPlayResponseList'].forEach((v) {
        shortPlayResponseList!.add(new ShortPlayResponseList.fromJson(v));
      });
    }
  }

  // Map<String, dynamic> toJson() {
  //   final Map<String, dynamic> data = new Map<String, dynamic>();
  //   data['bannerId'] = this.bannerId;
  //   data['isShowMore'] = this.isShowMore;
  //   data['rankingId'] = this.rankingId;
  //   data['rankingName'] = this.rankingName;
  //   if (this.shortPlayResponseList != null) {
  //     data['shortPlayResponseList'] =
  //         this.shortPlayResponseList!.map((v) => v.toJson()).toList();
  //   }
  //   return data;
  // }
}

enum HomeTabFlag {
  unknown,
  homePageRecommendation,
  newbieTab,
  premiumDramaTab,
  activityTab,
  memberAreaTab,
  tabOne,
  tabTwo,
  tabThree,
  tabFour,
  tabFive;

  String get value {
    switch (this) {
      case HomeTabFlag.unknown:
        return '';
      case HomeTabFlag.homePageRecommendation:
        return 'home_page_recommendation';
      case HomeTabFlag.newbieTab:
        return 'newbie_tab';
      case HomeTabFlag.premiumDramaTab:
        return 'premium_drama_tab';
      case HomeTabFlag.activityTab:
        return 'activity_tab';
      case HomeTabFlag.memberAreaTab:
        return 'member_area_tab';
      case HomeTabFlag.tabOne:
        return 'tab_one';
      case HomeTabFlag.tabTwo:
        return 'tab_two';
      case HomeTabFlag.tabThree:
        return 'tab_three';
      case HomeTabFlag.tabFour:
        return 'tab_four';
      case HomeTabFlag.tabFive:
        return 'tab_five';
    }
  }

  static HomeTabFlag fromValue(String? value) {
    final v = value;
    if (v == null) {
      return HomeTabFlag.unknown;
    }
    switch (v) {
      case 'home_page_recommendation':
        return HomeTabFlag.homePageRecommendation;
      case 'newbie_tab':
        return HomeTabFlag.newbieTab;
      case 'premium_drama_tab':
        return HomeTabFlag.premiumDramaTab;
      case 'activity_tab':
        return HomeTabFlag.activityTab;
      case 'member_area_tab':
        return HomeTabFlag.memberAreaTab;
      case 'tab_one':
        return HomeTabFlag.tabOne;
      case 'tab_two':
        return HomeTabFlag.tabTwo;
      case 'tab_three':
        return HomeTabFlag.tabThree;
      case 'tab_four':
        return HomeTabFlag.tabFour;
      case 'tab_five':
        return HomeTabFlag.tabFive;
      default:
        return HomeTabFlag.unknown;
    }
  }
}

enum HomeModuleStyle {
  unknown(-999), // 未知
  nativeAd(-2),       // 广告
  newPromotionBanner(-1), // 新版宣传图(顶部轮播)
  singleHorizontalImage(1), // 单排横图-无icon
  leftImageRightText(2), // 左图右文
  singleRoundImageWithPlay(3), // 单排圆图-有播放icon
  promotionBanner(4), // 宣传图(顶部轮播)
  singleHorizontalImageWithPlay(5), // 单排横图-有播放icon
  rankingList(6), // 单排横图-排行榜
  newDramaReservation(7), // 新剧预约
  doubleColumnWaterfall(8), // 双列瀑布流
  threeColumnGrid(9), // 双排三列-横图
  largeWithSmallImages(10), // 大图-双列小图
  categoryFilter(11), // 分类筛选
  limitedTimeDiscount(12), // 限时折扣
  singleColumnAd(13), // 广告位(单排单列)
  horizontalWithVideoPreview(14), // 横图带视频预览
  ranking(15), // 排行榜
  multiColumnLeftImageRightText(16);  // 多列左图右文

  final int value;

  const HomeModuleStyle(this.value);

  // 添加 fromValue 方法
  static HomeModuleStyle fromValue(int? value) {
    return HomeModuleStyle.values.firstWhere(
      (style) => style.value == value,
      orElse: () => HomeModuleStyle.unknown,
    );
  }
}

class ShortPlayLabel {
  int? id;
  String? labelName;

  ShortPlayLabel({
    this.id,
    this.labelName,
  });

  factory ShortPlayLabel.fromJson(Map<String, dynamic> json) {
    return ShortPlayLabel(
      id: json['id'],
      labelName: json['labelName'],
    );
  }
  
  // Map<String, dynamic> toJson() {
  //   return {
  //     'id': id,
  //     'labelName': labelName,
  //   };
  // }
}

class ShortPlayClass {
  int? classId;
  String? displayName;
  int? parentId;
  int? storey;

  ShortPlayClass({
    this.classId,
    this.displayName,
    this.parentId,
    this.storey,
  });

  factory ShortPlayClass.fromJson(Map<String, dynamic> json) {
    return ShortPlayClass(
      classId: json['classId'],
      displayName: json['displayName'],
      parentId: json['parentId'],
      storey: json['storey'],
    );
  }
  
  // Map<String, dynamic> toJson() {
  //   return {
  //     'classId': classId,
  //     'displayName': displayName,
  //     'parentId': parentId,
  //     'storey': storey,
  //   };
  // }
}
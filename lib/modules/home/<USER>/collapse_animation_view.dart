import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/modules/home/<USER>/collapse_animation_controller.dart';

class CollapseAnimationView extends StatefulWidget {
  Widget normalWidget;
  double normalWidgetWidth;
  Widget smallWidget;
  double smallWidgetWidth;
  final Function? onAnimationComplete;
  
  CollapseAnimationView({
    super.key, 
    required this.normalWidget, 
    required this.normalWidgetWidth,
    required this.smallWidget, 
    required this.smallWidgetWidth, 
    this.onAnimationComplete
  });

  @override
  State<CollapseAnimationView> createState() => _CollapseAnimationViewState();
}

class _CollapseAnimationViewState extends State<CollapseAnimationView> with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _buildView(CollapseAnimationController controller) {
    if (controller.style.value == CollapseAnimationStyle.normal) {
      return widget.normalWidget;
    } else {
      return widget.smallWidget;
    }
  }

  Widget _buildAnimatingView(CollapseAnimationController controller) {
    final animationController = controller.animationController;
    if (animationController == null) {
      return _buildView(controller);
    }
    return AnimatedBuilder(
          animation: animationController,
          builder: (context, child) {
            return controller.style.value == CollapseAnimationStyle.normal ?
            Transform.translate(
              offset: Offset(
                widget.normalWidgetWidth * (controller.normalPositionAnimation?.value ?? 1),
                0,
              ),
              child: Opacity(
                opacity: controller.opacityAnimation?.value ?? 1,
                child: _buildView(controller),
              ),
            ) :
            Transform.translate(
              offset: Offset(
                widget.smallWidgetWidth * (controller.smallPositionAnimation?.value ?? 1),
                0,
              ),
              child: _buildView(controller),
            );
          },
        );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CollapseAnimationController>(
      init: CollapseAnimationController(this, widget.onAnimationComplete),
      builder: (controller) {
        return Obx(() {
          return controller.isAnimating.value ? _buildAnimatingView(controller) : _buildView(controller);
        });
      }
    );
  }
}
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/model/details.dart';
import 'dart:async';
import 'package:playlet/modules/home/<USER>/collapse_animation_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/recently_watch_service.dart';
import 'package:playlet/utils/track_event.dart';

class HomeRecentlyController extends GetxController  {
  int shortPlayId = -1;
  RxString imageUrl = "".obs;
  RxString shortPlayName = "".obs;
  RxInt episodeNumber = 1.obs;
  RxInt allEpisodesNumber = 1.obs;
  Timer? _animationTimer;

  bool isShowNormal = true;

  @override
  void onInit() {
    super.onInit();
    _loadData();
    _startAnimationTimer();
  }

  @override
  void onClose() {
    _stopAnimationTimer();
    super.onClose();
  }

  void trackReelShow() {
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: shortPlayId.toString(),
      TrackEvent.scene: isShowNormal ? EventValue.recentlyWatchedLong : EventValue.recentlyWatchedSquare,
    });
  }

  void _loadData() {
    RecentlyWatchService recentlyWatchService = Get.find<RecentlyWatchService>();
    shortPlayId = recentlyWatchService.recentlyWatchModel?.shortPlayId ?? -1;
    imageUrl.value = recentlyWatchService.recentlyWatchModel?.imageUrl ?? "";
    shortPlayName.value = recentlyWatchService.recentlyWatchModel?.shortPlayName ?? "";
    episodeNumber.value = recentlyWatchService.recentlyWatchModel?.episodeNum ?? 1;
    allEpisodesNumber.value = recentlyWatchService.recentlyWatchModel?.totalEpisodes ?? 1;
  }

  void _startAnimationTimer() {
    if (!isShowNormal) {
      return;
    }
    _stopAnimationTimer();
    _animationTimer = Timer.periodic(const Duration(seconds: 8), (timer) {
      _stopAnimationTimer();
      startAnimation();
    });
  }

  void _stopAnimationTimer() {
    _animationTimer?.cancel();
    _animationTimer = null;
  }

  void startAnimation() {
    if (!isShowNormal) {
      return;
    }
    CollapseAnimationController animationController = Get.find<CollapseAnimationController>();
    animationController.startAnimation();
  }

  void closeRecentlyView() {
    RecentlyWatchService recentlyWatchService = Get.find<RecentlyWatchService>();
    recentlyWatchService.showOnHome.value = false;
  }

  void tpDetailsPage() {
    final options = DetailsOptions(
      businessId: shortPlayId,
      playerEpisodeIndex: episodeNumber.value - 1,
      scene: isShowNormal ? EventValue.recentlyWatchedLong : EventValue.recentlyWatchedSquare,
    );
    AppNavigator.startDetailsPage(options);
  }
}
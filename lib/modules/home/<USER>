import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/home/<USER>';
import 'package:playlet/components/shimmer/home_shimmer.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'home_controller.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  Widget _buildWidget(HomeController controller) {
    return VisibilityDetector(
      key: const Key("home"),
      onVisibilityChanged: (VisibilityInfo info) {
        FFLog.info("home:${info.visibleFraction}%", tag: "VisibilityDetector");
        controller.handleDiloagChainShow(info.visibleFraction>0);
      },
      child: Obx(() {
        if (controller.loadingStatus.value == HomeTabLoadingStatus.idle) {
          // 空闲
          return const SizedBox();
        }
        if (controller.loadingStatus.value == HomeTabLoadingStatus.loading || controller.tabController == null) {
          // 加载中
          return const HomeShimmer();
        }
        if (controller.loadingStatus.value == HomeTabLoadingStatus.failed) {
          // 加载失败
          return EmptyWidget(
            pageFrom: EmptyPageFrom.discover,
            type: EmptyType.noNetwork,
            onRefresh: () {
              controller.initHomeData();
            },
          );
        }
        if (controller.tabTitles.isEmpty) {
          // 空数据
          return const EmptyWidget(pageFrom: EmptyPageFrom.discover, type: EmptyType.noContent);
        }
        // 有数据
        return Listener(
          onPointerDown:controller.recommendService.onPointerDown,
          onPointerMove:controller.recommendService.onPointerMove,
          onPointerUp:controller.recommendService.onPointerUp,
          child: Column(
            children: [
              Expanded(
                child: TabBarView(
                  controller: controller.tabController,
                  children: controller.tabPages,
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          // 背景图
          Assets.home.homeMaskGroup
              .image(width: double.infinity, height: 348.sp, fit: BoxFit.cover),
          // 原有内容
          SafeArea(
            child: _buildWidget(controller),
          ),
          const HomeRecommend(),
        ],
      ),
    );
  }
}

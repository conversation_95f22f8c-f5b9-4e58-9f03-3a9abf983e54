import 'dart:async';

import 'package:deeplink_dev/attribution_sdk_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/api/home_api.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/modules/home/<USER>/home_event.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain_manager.dart';
import 'package:playlet/modules/main/main_controller.dart';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/routers/route_observer_custom.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/ad/ad_track_event.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/app_service.dart';
import 'package:playlet/service/recommend_service.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/utils/track_event.dart';

import 'tab/home_tab_main.dart';
import 'tab/home_tab_protocol.dart';

enum HomeTabLoadingStatus {
  idle,
  loading,
  success,
  failed,
}

class HomeController extends GetxController with GetTickerProviderStateMixin {
  TabController? tabController;
  
  final RecommendService recommendService = Get.find<RecommendService>();
  // 加载状态
  late Rx<HomeTabLoadingStatus> loadingStatus = HomeTabLoadingStatus.idle.obs;

  late final Map<HomeTabFlag, HomeTabProtocol Function()> _allTabInstances = {
    // 在此处注册
    HomeTabFlag.homePageRecommendation: () => HomeTabMain(onRefresh: () async {
      await initHomeData();
    }),
  };

  final List<HomeTabProtocol> _visibleTabInstances = [];
  final Map<HomeTabFlag, HomeTabProtocol> _tabInstancesMap = {};

  // tab个数
  int get tabCount => _visibleTabInstances.length;

  // tab标题
  List<String> get tabTitles => _visibleTabInstances.map((e) => e.tabTitle).toList();

  // tab对应的页面
  List<Widget> get tabPages => List.generate(_visibleTabInstances.length, (index) {
        return Obx(() => _visibleTabInstances[index].isInitialized.value == true ? _visibleTabInstances[index].tabPage : const SizedBox()); // 未初始化时显示空容器
      });

  StreamSubscription? initLoginSubscription;
  StreamSubscription? refreshHomeSubscription;
  StreamSubscription? _showAdSubscription;
  StreamSubscription? routeSubscription;

  void Function()? _tabListener;

  /// 提前准备的首页数据
  static TabHomeData? _prepareData;

  /// 提前准备首页数据
  static Future<void> prepareHomeTabData() async {
    final result = await HomeApi.getHomeTabData();
    if (result.isSuccess == false) {
      return;
    }
    if (Get.isRegistered<HomeController>()) {
      // 如果已经进入首页了，就不需要提前准备数据
      return;
    }
    _prepareData = result.data as TabHomeData?;
  }

  @override
  void onInit() {
    initLoginSubscription?.cancel();
    initLoginSubscription = eventBus.on<InitLoginEventData>().listen((event) {
      initHomeData();
    });

    refreshHomeSubscription?.cancel();
    refreshHomeSubscription = eventBus.on<RefreshHomeEvent>().listen((event) {
      initHomeData();
    });

    routeSubscription?.cancel();
    routeSubscription = eventBus.on<RouteObserverCustomEvent>().listen((event) {
      if (event.to != '' &&
          event.from == Routes.mainPage ) {
          FFLog.info("route from",tag: "HomeController");
          DialogChainManager.instance.pause();
      }
    });

    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();

    initHomeData();
    Get.find<MainController>().onHomePageLoaded();

    AdTrackEvent.adPlacementShow(AdType.native, AdScene.homeFeed);
    // 监听广告开关
    _showAdSubscription?.cancel();
    _showAdSubscription = AdManager().shouldShowAd.listen((value) {
      update();
    });
  }

  @override
  void onClose() {
    // 全局的链表任务也停止
    _showAdSubscription?.cancel();
    _showAdSubscription = null;
    DialogChainManager.instance.stop();
    _disposeTabController();
    initLoginSubscription?.cancel();
    refreshHomeSubscription?.cancel();
    routeSubscription?.cancel();
    super.onClose();
  }

  // 初始化TabController
  void _initTabController() {
    // 销毁旧的 controller
    _disposeTabController();

    tabController = TabController(
      length: _visibleTabInstances.length,
      vsync: this,
    );

    void tabListener() {
      final tabC = tabController;
      if (tabC != null && !tabC.indexIsChanging) {
        _visibleTabInstances[tabC.index].isInitialized.value = true;
      }
    }

    _tabListener = tabListener;
    tabController?.addListener(tabListener);
  }

  void _disposeTabController() {
    void Function()? tabListener = _tabListener;
    if (tabListener != null) {
      tabController?.removeListener(tabListener);
      _tabListener = null;
    }
    tabController?.dispose();
    tabController = null;
  }

  Future<void> initHomeData() async {
    if (loadingStatus.value == HomeTabLoadingStatus.loading) {
      return;
    }
    if (_visibleTabInstances.isEmpty) {
      loadingStatus.value = HomeTabLoadingStatus.loading;
    }
    
    TabHomeData? data = _prepareData;
    _prepareData = null; // 此处把预先准备的数据清空，后续都要重新请求
    if (data == null) {
      // 没有准备数据
      final result = await HomeApi.getHomeTabData();
      
      if (result.isSuccess == false) {
        // 失败
        for (var instance in _visibleTabInstances) {
          instance.onRequestResult(false);
        }
        loadingStatus.value = HomeTabLoadingStatus.failed;
        return;
      }
      data = result.data as TabHomeData?;
    }
    
    for (var instance in _visibleTabInstances) {
      instance.onRequestResult(true);
    }
    
    if (data == null) {
      // 空数据
      _visibleTabInstances.clear();
      _tabInstancesMap.clear();
      loadingStatus.value = HomeTabLoadingStatus.success;
      return;
    }

    final firstTabPageData = data.tabPageResponse;
    if (firstTabPageData == null) {
      // 空数据
      _visibleTabInstances.clear();
      _tabInstancesMap.clear();
      loadingStatus.value = HomeTabLoadingStatus.success;
      return;
    }

    // 创建一个新的列表来存储排序后的tabs
    final List<HomeTabProtocol> sortedTabs = [];
    final Map<HomeTabFlag, HomeTabProtocol> newTabInstancesMap = {};

    final tabList = data.tabResponseList;
    int reviewTabId = -999;
    if (tabList == null || tabList.isEmpty) {
      // 空数据
      HomeTabFlag tabFlagName = HomeTabFlag.homePageRecommendation;
      HomeTabProtocol? matchedTab = _tabInstancesMap[tabFlagName];
      if (matchedTab == null) {
        HomeTabProtocol Function()? func = _allTabInstances[tabFlagName];
        if (func != null) {
          matchedTab = func.call();
        }
      }
      if (matchedTab != null) {
        matchedTab.setTabModel(null, firstTabPageData);
        sortedTabs.add(matchedTab);
        newTabInstancesMap[tabFlagName] = matchedTab;
      }
    } else {
      // 按照tabList的顺序重新排列tabs
      for (var tabModel in tabList) {
        if (tabModel.tabId != firstTabPageData.tabId) {
          continue;
        }
        HomeTabFlag tabFlagName = tabModel.tabFlagName ?? HomeTabFlag.unknown;
        if (tabFlagName == HomeTabFlag.unknown && tabModel.tabId == reviewTabId) {
          // 审核tab
          tabFlagName = HomeTabFlag.homePageRecommendation;
        }
        HomeTabProtocol? matchedTab = _tabInstancesMap[tabFlagName];
        if (matchedTab == null) {
          HomeTabProtocol Function()? func = _allTabInstances[tabFlagName];
          if (func != null) {
            matchedTab = func.call();
          }
        }
        if (matchedTab != null) {
          matchedTab.setTabModel(tabModel, firstTabPageData);
          sortedTabs.add(matchedTab);
          newTabInstancesMap[tabFlagName] = matchedTab;
        }
      }
    }

    _visibleTabInstances.clear();
    _tabInstancesMap.clear();
    if (sortedTabs.isNotEmpty) {
      _visibleTabInstances.addAll(sortedTabs);
      _tabInstancesMap.addAll(newTabInstancesMap);

      // 初始化 TabController
      _initTabController();

      final tab = sortedTabs.first;
      tab.isInitialized.value = true;

      _trackHomeEvent(tab);
    }

    loadingStatus.value = HomeTabLoadingStatus.success;
  }

  void _trackHomeEvent(HomeTabProtocol tab) async {
    AppService appService = Get.find<AppService>();
    var from = appService.getAppLaunchFrom();
    final attributionInfo = await AttributionSdkCore.instance.getAttributionInfo();

    if (attributionInfo != null) {
      from = EventValue.deeplink;
    }
    Map<String, String> extra = {TrackEvent.from: from, TrackEvent.tab_id: tab.tabId.toString()};

    useTrackEvent(TrackEvent.discover_show, extra: extra);
    useTrackEvent(TrackEvent.discover_show_success, extra: extra);
  }

  /// 控制弹框的展示
  void handleDiloagChainShow(bool isShow) {
    if (!isShow) {
      DialogChainManager.instance.pause();
    } else {
      DialogChainManager.instance.resume();
    }
  }
}

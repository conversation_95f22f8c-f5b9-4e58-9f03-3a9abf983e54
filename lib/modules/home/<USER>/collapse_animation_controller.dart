import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum CollapseAnimationStyle {
  normal,
  small,
}

class CollapseAnimationController extends GetxController {
  final TickerProvider _vsync;
  final Function? onAnimationComplete;
  
  AnimationController? animationController;
  Animation<double>? normalPositionAnimation;
  Animation<double>? smallPositionAnimation;
  Animation<double>? opacityAnimation;
  Rx<CollapseAnimationStyle> style = CollapseAnimationStyle.normal.obs;
  RxBool isAnimating = true.obs;

  CollapseAnimationController(TickerProvider vsync, this.onAnimationComplete) : _vsync = vsync;

  @override
  void onInit() {
    super.onInit();

    AnimationController animationVC = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: _vsync,
    );
    animationController = animationVC;

    normalPositionAnimation = Tween<double>(
      begin: 0.0,
      end: -1.0, // 移动到屏幕左侧
    ).animate(CurvedAnimation(
      parent: animationVC,
      curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
    ));

    smallPositionAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0, // 移动到屏幕
    ).animate(CurvedAnimation(
      parent: animationVC,
      curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
    ));

    opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: animationVC,
      curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
    ));

    animationVC.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (style.value == CollapseAnimationStyle.small) {
          isAnimating.value = false;
          stopAnimation();
          return;
        }
        style.value = CollapseAnimationStyle.small;
        startAnimation();

        if (onAnimationComplete != null) {
          onAnimationComplete!();
        }
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    stopAnimation();
  }

  void startAnimation() {
    final animationVC = animationController;
    if (animationVC == null) {
      return;
    }
    if (animationVC.status.isAnimating) {
      return;
    }
    animationController?.forward();
  }

  void stopAnimation() {
    animationController?.stop();
    animationController?.dispose();
    animationController = null;
  }
}
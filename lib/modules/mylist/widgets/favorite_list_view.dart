import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/mylist/collection.dart';

class MyListTabViewFavorites extends StatelessWidget {
  const MyListTabViewFavorites({
    super.key,
    required this.listData,
    required this.isEditing,
    this.physics,
    this.selectShortsCodeSet = const {},
    required this.onItemTap,
    required this.banner,
  });
  final List<CollectionDataModel> listData;
  final bool isEditing;
  final ScrollPhysics? physics;
  final Set<int> selectShortsCodeSet;
  final void Function(int index) onItemTap;
  final Widget banner;

  @override
  Widget build(BuildContext context) {
    double childAspectRatio = 114 / 219;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.sp),
      child: CustomScrollView(
        physics: physics,
        slivers: [
          SliverToBoxAdapter(child: banner),
          SliverGrid.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              mainAxisSpacing: 16.sp,
              crossAxisSpacing: 10.sp,
              childAspectRatio: childAspectRatio,
            ),
            itemCount: listData.length,
            itemBuilder: (context, index) => MyListTabViewFavoritesItem(
              aspectRatio: childAspectRatio,
              data: listData[index],
              isEditing: isEditing,
              isSelected:
                  selectShortsCodeSet.contains(listData[index].shortPlayCode),
              onTap: () => onItemTap(index),
            ),
          ),
        ],
      ),
    );
  }
}

class MyListTabViewFavoritesItem extends StatelessWidget {
  const MyListTabViewFavoritesItem({
    super.key,
    required this.aspectRatio,
    required this.data,
    required this.isEditing,
    required this.onTap,
    required this.isSelected,
  });
  final double aspectRatio;
  final CollectionDataModel data;
  final bool isEditing;
  final VoidCallback onTap;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              _buildCover(),
              isEditing
                  ? Positioned(
                      top: 10.sp,
                      right: 10.sp,
                      child: _buildSelect(),
                    )
                  : const SizedBox.shrink(),
            ],
          ),
          SizedBox(height: 8.sp * aspectRatio),
          _buildTitle(),
          SizedBox(height: 2.sp * aspectRatio),
          _buildDetail(),
        ],
      ),
    );
  }

  Widget _buildCover() {
    return AspectRatio(
      aspectRatio: 114 / 152,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4.r),
        clipBehavior: Clip.antiAlias,
        child: CoverWidget(
          imageUrl: data.coverId ?? '',
          fit: BoxFit.cover,
          width: 114.sp,
          height: 152.sp,
          compress: false,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      data.shortPlayName ?? '',
      style: TextStyle(
        color: Colors.white,
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildDetail() {
    return Text(
      '${AppTrans.all()} ${data.totalEpisodes ?? 0} ${AppTrans.ep()}',
      style: TextStyle(
        color: const Color(0xFF666666),
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSelect() {
    return Image.asset(
      width: 20.sp,
      height: 20.sp,
      isSelected
          ? Assets.mylist.mylistAlreadySelect.path
          : Assets.mylist.mylistNotSelect.path,
    );
  }
}

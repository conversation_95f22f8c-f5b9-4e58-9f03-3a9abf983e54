import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/components/ui/short_tag_view.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/mylist/history.dart';

class MyListTabViewHistory extends StatelessWidget {
  const MyListTabViewHistory({
    super.key,
    this.physics,
    required this.listDatas,
    required this.isEditing,
    required this.selectShortsCodeSet,
    required this.onItemTap,
    required this.onItemCollectTap,
  });
  final ScrollPhysics? physics;
  final List<HistoryDataModel> listDatas;
  final bool isEditing;
  final Set<int> selectShortsCodeSet;
  final void Function(int index) onItemTap;
  final void Function(int index) onItemCollectTap;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: EdgeInsets.fromLTRB(16.sp, 0.0, 8.sp, 0.0),
      physics: physics,
      itemCount: listDatas.length,
      separatorBuilder: (context, index) => Divider(
        height: 16.sp,
        color: Colors.transparent,
      ),
      itemBuilder: (context, index) => MyListHistoryItem(
        data: listDatas[index],
        isEditing: isEditing,
        onTap: () => onItemTap(index),
        isEditingSelected:
            selectShortsCodeSet.contains(listDatas[index].shortPlayCode),
        onCollectTap: () => onItemCollectTap(index),
      ),
    );
  }
}

class MyListHistoryItem extends StatelessWidget {
  const MyListHistoryItem({
    super.key,
    required this.data,
    required this.isEditing,
    required this.onTap,
    required this.isEditingSelected,
    required this.onCollectTap,
  });
  final HistoryDataModel data;
  final bool isEditing;
  final bool isEditingSelected;
  final VoidCallback onTap;
  final VoidCallback onCollectTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildSelect(),
          _buildCover(),
          SizedBox(width: 10.sp),
          Expanded(
            child: SizedBox(
              height: 107.sp,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTitle(),
                  _buildLabel(),
                  _buildEP(),
                ],
              ),
            ),
          ),
          SizedBox(width: 10.sp),
          _buildCollect(),
        ],
      ),
    );
  }

  Widget _buildSelect() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child: isEditing == false
          ? const SizedBox.shrink()
          : Padding(
              padding: EdgeInsets.only(right: 10.sp),
              child: Image.asset(
                width: 20.sp,
                height: 20.sp,
                isEditingSelected == true
                    ? Assets.mylist.mylistAlreadySelect.path
                    : Assets.mylist.mylistNotSelect.path,
              ),
            ),
    );
  }

  Widget _buildCover() {
    double watchProgress = data.videoDuration == 0 || data.videoDuration! == 0
        ? 0.0
        : ((data.watchTime ?? 0) / data.videoDuration!).toDouble();
    return ClipRRect(
      borderRadius: BorderRadius.circular(4.r),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CoverWidget(
            imageUrl: data.coverId ?? '',
            fit: BoxFit.cover,
            width: 80.sp,
            height: 107.sp,
            compress: false,
          ),
          Positioned(
            left: 0.0,
            bottom: 0.0,
            right: 0.0,
            child: Container(
              height: 3.sp,
              width: 80.sp,
              color: const Color(0xFF262726),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: watchProgress,
                child: Container(
                  height: 3.sp,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFB400),
                    borderRadius: BorderRadius.circular(1.5.sp),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      data.shortPlayName ?? '',
      style: TextStyle(
        color: Colors.white,
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        height: 1.35,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildLabel() {
    List<String> labels = [];
    if (data.labelResponseList != null && data.labelResponseList!.isNotEmpty) {
      for (var element in data.labelResponseList!) {
        if (element.labelName != null && element.labelName!.isNotEmpty) {
          labels.add(element.labelName!);
        }
      }
    }

    return labels.isEmpty
        ? const SizedBox.shrink()
        : ShortTagView(tags: labels);
  }

  Widget _buildEP() {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
              text: '${AppTrans.ep()}.${data.episodeNum}',
              style: TextStyle(
                color: Colors.white,
                fontSize: 13.sp,
                fontWeight: FontWeight.w400,
              )),
          TextSpan(
            text: ' / ${AppTrans.all()} ${data.totalEpisodes} ${AppTrans.ep()}',
            style: TextStyle(
              color: const Color(0xFF666666),
              fontSize: 13.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCollect() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child: isEditing == true
          ? const SizedBox.shrink()
          : IconButton(
              onPressed: onCollectTap,
              style: const ButtonStyle(
                padding: WidgetStatePropertyAll(EdgeInsets.zero),
              ),
              icon: Image.asset(
                width: 26.sp,
                height: 26.sp,
                data.collectStatus == 1
                    ? Assets.mylist.mylistAlreadyCollected.path
                    : Assets.mylist.mylistNotCollected.path,
              ),
            ),
    );
  }
}

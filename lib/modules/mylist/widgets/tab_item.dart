import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';

class MyListTabItem extends StatelessWidget {
  const MyListTabItem({
    super.key,
    required this.icon,
    required this.text,
    required this.isSelected,
    required this.onTap,
  });
  final String icon;
  final String text;
  final bool isSelected;
  final VoidCallback onTap;

  factory MyListTabItem.favorites({
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return MyListTabItem(
      icon: Assets.mylist.favorites.path,
      text: AppTrans.favorites(),
      isSelected: isSelected,
      onTap: onTap,
    );
  }

  factory MyListTabItem.history({
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return MyListTabItem(
      icon: Assets.mylist.history.path,
      text: AppTrans.history(),
      isSelected: isSelected,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    return FilledButton(
      onPressed: onTap,
      style: ButtonStyle(
        backgroundColor: WidgetStatePropertyAll(
          isSelected ? Colors.transparent : const Color(0xFF1D1D1D),
        ),
        padding: WidgetStatePropertyAll(
          EdgeInsets.symmetric(horizontal: 22.w, vertical: 6.w),
        ),
        shape: const WidgetStatePropertyAll(StadiumBorder()),
        side: WidgetStatePropertyAll(isSelected
            ? BorderSide(color: const Color(0xFFFFCD00), width: 1.sp)
            : BorderSide.none),
      ),
      child: Row(
        children: [
          Image.asset(
            icon,
            width: 18.sp,
            height: 18.sp,
          ),
          SizedBox(width: 6.sp),
          Text(
            text,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}

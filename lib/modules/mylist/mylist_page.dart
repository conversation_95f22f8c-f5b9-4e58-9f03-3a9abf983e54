import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/shimmer/mylist_shimmer.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/mylist/mylist_controller.dart';
import 'package:playlet/modules/mylist/widgets/favorite_list_view.dart';
import 'package:playlet/modules/mylist/widgets/history_list_view.dart';
import 'package:playlet/modules/mylist/widgets/tab_item.dart';
import 'package:playlet/modules/resource_bit/banner/banner_view.dart';

class MylistPage extends GetView<MylistController> {
  const MylistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: controller.showFullScreenShimmer.value == true
            ? null
            : _buildAppBar(),
        body: controller.showFullScreenShimmer.value == true
            ? const MylistFullScreenShimmer()
            : _buildBody(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    ButtonStyle buttonStyle = ButtonStyle(
      padding: const WidgetStatePropertyAll(EdgeInsets.zero),
      fixedSize: WidgetStatePropertyAll(Size(30.sp, 30.sp)),
      minimumSize: WidgetStatePropertyAll(Size(30.sp, 30.sp)),
      maximumSize: WidgetStatePropertyAll(Size(30.sp, 30.sp)),
    );

    return PreferredSize(
      preferredSize: const Size(0, kToolbarHeight),
      child: Obx(
        () => AppBar(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          title: Text(
            AppTrans.myList(),
            style: TextStyle(
              color: Colors.white,
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          centerTitle: controller.isEditing.value,
          leading: controller.isEditing.value
              ? IconButton(
                  padding: EdgeInsets.zero,
                  style: buttonStyle,
                  onPressed: controller.onHeaderActionBackTap,
                  icon: Icon(Icons.arrow_back_ios_rounded, size: 20.sp),
                )
              : null,
          actions: controller.isEditing.value == false
              ? [
                  IconButton(
                    padding: EdgeInsets.zero,
                    style: buttonStyle,
                    onPressed: controller.onHeaderActionEditingTap,
                    icon: Assets.mylist.mylistEdit.image(
                      width: 22.sp,
                      height: 22.sp,
                    ),
                  ),
                ]
              : [
                  IconButton(
                    padding: EdgeInsets.zero,
                    style: buttonStyle,
                    onPressed: controller.onHeaderActionSelectedAllTap,
                    icon: Assets.mylist.mylistSelectAll.image(
                      width: 22.sp,
                      height: 22.sp,
                    ),
                  ),
                  Obx(
                    () => IconButton(
                      padding: EdgeInsets.zero,
                      style: buttonStyle,
                      onPressed: controller.tabIndex.value == 0
                          ? controller.selectFavoritiesShortCodeSet.isEmpty
                              ? null
                              : controller.onHeaderActionDeleteTap
                          : controller.selectHistoryShortCodeSet.isEmpty
                              ? null
                              : controller.onHeaderActionDeleteTap,
                      icon: Image.asset(
                        width: 22.sp,
                        height: 22.sp,
                        controller.tabIndex.value == 0
                            ? controller.selectFavoritiesShortCodeSet.isEmpty
                                ? Assets.mylist.mylistDeleteDisable.path
                                : Assets.mylist.mylistDelete.path
                            : controller.selectHistoryShortCodeSet.isEmpty
                                ? Assets.mylist.mylistDeleteDisable.path
                                : Assets.mylist.mylistDelete.path,
                      ),
                    ),
                  ),
                ],
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _buildTab(),
        SizedBox(height: 20.sp),
        Expanded(
          child: _buildContent(),
        ),
      ],
    );
  }

  Widget _buildTab() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.sp),
      child: Row(
        children: [
          Obx(
            () => MyListTabItem.favorites(
              isSelected: controller.tabIndex.value == 0,
              onTap: () => controller.onTabItemTap(0),
            ),
          ),
          SizedBox(width: 24.sp),
          Obx(
            () => MyListTabItem.history(
              isSelected: controller.tabIndex.value == 1,
              onTap: () => controller.onTabItemTap(1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Obx(
      () => controller.isEditing.value == true
          ? _buildContentBody()
          : EasyRefresh.builder(
              refreshOnStart: false,
              controller: controller.refreshController,
              header: BuilderHeader(
                builder: (context, state) {
                  if (state.mode == IndicatorMode.inactive ||
                      state.mode == IndicatorMode.done) {
                    return const SizedBox();
                  }
                  return Assets.loading.dotLoading.image(
                    width: 50.sp,
                    height: 50.sp,
                  );
                },
                triggerOffset: 50.sp,
                clamping: false,
                position: IndicatorPosition.above,
              ),
              footer: BuilderFooter(
                builder: (context, state) {
                  if (state.mode == IndicatorMode.inactive ||
                      state.mode == IndicatorMode.done) {
                    return const SizedBox();
                  }
                  return Assets.loading.dotLoading.image(
                    width: 50.sp,
                    height: 50.sp,
                  );
                },
                triggerOffset: 50.sp,
                clamping: false,
                position: IndicatorPosition.behind,
              ),
              onRefresh: () => controller.onRefresh(),
              onLoad: () => controller.onLoad(),
              triggerAxis: Axis.vertical,
              childBuilder: (context, physics) => _buildContentBody(
                physics: physics,
              ),
            ),
    );
  }

  Widget _buildContentBody({ScrollPhysics? physics}) {
    return Obx(
      () => controller.tabIndex.value == 0
          ? Obx(() => _buildFavoritiesView(physics: physics))
          : Obx(() => _buildHistoryView(physics: physics)),
    );
  }

  Widget _buildFavoritiesView({ScrollPhysics? physics}) {
    return _buildFavoritiesContentView(physics: physics);
  }

  Widget _buildBanner() {
    return Obx(() {
      if (controller.bannerController.bannerList.isNotEmpty) {
        return Padding(
          padding: EdgeInsets.only(bottom: 16.sp),
          child: ResourceBitBannerView(
            tag: controller.bannerControllerTag,
            height: 75.sp,
          ),
        );
      } else {
        return const SizedBox(
          width: 0,
          height: 0,
        );
      }
    });
  }

  Widget _buildFavoritiesContentView({ScrollPhysics? physics}) {
    if (controller.favoritiesPageState.value == PageState.loading) {
      return Column(
        children: [
          _buildBanner(),
          const Expanded(child: MylistCollectViewShimmer()),
        ],
      );
    }
    if (controller.favoritiesPageState.value == PageState.failed) {
      return Column(
        children: [
          _buildBanner(),
          Expanded(
            child: Container(
              color: const Color(0xFF000000),
              alignment: Alignment.center,
              child: EmptyWidget(
                pageFrom: EmptyPageFrom.collections,
                type: EmptyType.noNetwork,
                onRefresh: () {
                  controller.refreshData();
                },
              ),
            ),
          ),
        ],
      );
    }

    if (controller.collectionDatas.isEmpty) {
      return Column(
        children: [
          _buildBanner(),
          Expanded(
              child: Container(
            color: const Color(0xFF000000),
            alignment: Alignment.center,
            child: const EmptyWidget(
              pageFrom: EmptyPageFrom.collections,
              type: EmptyType.noContent,
            ),
          )),
        ],
      );
    } else {
      return Obx(
        () => MyListTabViewFavorites(
          physics: physics,
          // ignore: invalid_use_of_protected_member
          listData: controller.collectionDatas.value,
          isEditing: controller.isEditing.value,
          onItemTap: controller.onFavoritiesShortsItemTap,
          selectShortsCodeSet:
              // ignore: invalid_use_of_protected_member
              controller.selectFavoritiesShortCodeSet.value,
          banner: _buildBanner(),
        ),
      );
    }
  }

  Widget _buildHistoryView({ScrollPhysics? physics}) {
    if (controller.historyPageState.value == PageState.loading) {
      return const MylistHistoryViewShimmer();
    }

    if (controller.historyPageState.value == PageState.failed) {
      return Container(
        color: const Color(0xFF000000),
        alignment: Alignment.center,
        child: EmptyWidget(
          pageFrom: EmptyPageFrom.recently,
          type: EmptyType.noNetwork,
          onRefresh: () {
            controller.refreshData();
          },
        ),
      );
    }

    if (controller.historyDatas.isEmpty) {
      return Container(
        color: const Color(0xFF000000),
        alignment: Alignment.center,
        child: const EmptyWidget(
          pageFrom: EmptyPageFrom.recently,
          type: EmptyType.noContent,
        ),
      );
    } else {
      return Obx(
        () => MyListTabViewHistory(
          physics: physics,
          // ignore: invalid_use_of_protected_member
          listDatas: controller.historyDatas.value,
          isEditing: controller.isEditing.value,
          // ignore: invalid_use_of_protected_member
          selectShortsCodeSet: controller.selectHistoryShortCodeSet.value,
          onItemTap: (index) => controller.onHistoryShortsItemTap(index),
          onItemCollectTap: (index) =>
              controller.onHistoryShortsItemCollectTap(index),
        ),
      );
    }
  }
}

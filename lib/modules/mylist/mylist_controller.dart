import 'dart:async';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/api/mylist.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/alert/action.dart';
import 'package:playlet/components/alert/index.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/mylist/collection.dart';
import 'package:playlet/model/mylist/history.dart';
import 'package:playlet/modules/home/<USER>/home_event.dart';
import 'package:playlet/modules/resource_bit/banner/banner_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/notificaiton_dialog_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/resource_bit_manager.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';
import 'package:playlet/service/shorts_service.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/utils/get_extension.dart';

enum PageState {
  none,
  loading,
  success,
  failed,
}

class MylistController extends GetxController {
  static String get _tag => 'MylistController';
  String get bannerControllerTag => 'myList';

  final ShortsService shortsService = Get.find<ShortsService>();
  int pageSize = 30;
  late RxBool showFullScreenShimmer;

  ResourceBitBannerController get bannerController {
    if (!Get.isRegistered<ResourceBitBannerController>(
        tag: bannerControllerTag)) {
      return Get.put(ResourceBitBannerController(), tag: bannerControllerTag);
    }
    return Get.find<ResourceBitBannerController>(tag: bannerControllerTag);
  }

  StreamSubscription<List<ResourceBitModel>?>? _resourceBitStreamSubscription;

  late final EasyRefreshController refreshController;
  late RxInt tabIndex;
  late RxBool isEditing;
  late RxList<CollectionDataModel> collectionDatas;
  late RxList<HistoryDataModel> historyDatas;
  late RxSet<int> selectFavoritiesShortCodeSet;
  late RxSet<int> selectHistoryShortCodeSet;
  late Rx<PageState> favoritiesPageState;
  late Rx<PageState> historyPageState;

  late StreamSubscription refreshWatchHistoryListSubscription;

  @override
  void onInit() {
    showFullScreenShimmer = true.obs;
    _addResourceBitChangedListener();
    // 加载数据
    unawaited(_loadBannerData());

    isEditing = false.obs;
    tabIndex = 0.obs;
    refreshController = EasyRefreshController(
      controlFinishLoad: true,
      controlFinishRefresh: true,
    );
    collectionDatas = <CollectionDataModel>[].obs;
    selectFavoritiesShortCodeSet = <int>{}.obs;
    historyDatas = <HistoryDataModel>[].obs;
    selectHistoryShortCodeSet = <int>{}.obs;
    favoritiesPageState = PageState.none.obs;
    historyPageState = PageState.none.obs;
    _initSubscription();
    super.onInit();
  }

  @override
  void onReady() {
    onRefresh();
    super.onReady();
  }

  @override
  void onClose() {
    _removeResourceBitChangedListener();
    refreshController.dispose();
    refreshWatchHistoryListSubscription.cancel();
    super.onClose();
  }

  void _initSubscription() {
    refreshWatchHistoryListSubscription =
        eventBus.on<RefreshWatchHistoryListEvent>().listen(
      (event) {
        /// 先延时1秒钟，避免正在更新数据时拿不到数据
        Future.delayed(const Duration(seconds: 1), () {
          refreshData();
        });
      },
    );
  }

  //////////////////////////////////////////////////////////////
  /// public
  //////////////////////////////////////////////////////////////
  void refreshCollectionData() {
    tabIndex.value = 0;
    refreshData();
  }

  //////////////////////////////////////////////////////////////
  /// refresh
  //////////////////////////////////////////////////////////////
  void refreshData() {
    if (tabIndex.value == 0) {
      favoritiesPageState.value = PageState.none;
      onRefresh();
    } else {
      historyPageState.value = PageState.none;
      onRefresh();
    }
  }

  void onRefresh() {
    if (tabIndex.value == 0) {
      _requrstCollectionDatas(true);
    } else {
      _requestHistoryDatas(true);
    }
  }

  void onLoad() {
    if (tabIndex.value == 0) {
      _requrstCollectionDatas(false);
    } else {
      _requestHistoryDatas(false);
    }
  }

  //////////////////////////////////////////////////////////////
  /// request
  //////////////////////////////////////////////////////////////

  Future<void> _loadBannerData() async {
    try {
      bannerController.bannerList.value = [];
      bannerController.currentIndex.value = 0;

      final service = _getResourceBitService();
      if (service == null) {
        return;
      }
      final models = service.resourceBitModels;
      if (models == null || models.isEmpty) {
        return;
      }
      FFLog.debug('开始获取追剧横幅资源位数据', tag: _tag);
      final initializedModel = await service.getResourceModel();
      final placeholderImage = Assets.resourcebit.bannerPlaceholder.image();

      List<ResourceBitBannerItem> bannerItems = [];
      int initializedIndex = -1;
      for (var model in models) {
        String? imageUrl = service.getResourceImageUrl(model);
        if (imageUrl == null || imageUrl.isEmpty) {
          continue;
        }
        if (model == initializedModel) {
          initializedIndex = bannerItems.length;
        }

        final item = ResourceBitBannerItem(
          model: model,
          widget: CoverWidget(
            imageUrl: imageUrl,
            width: 361.sp,
            height: 75.sp,
            fit: BoxFit.cover,
            errorWidget: (context, url, error) => placeholderImage,
          ),
          onTap: () {
            _onMyListResourceBitBannerClick(model);
          },
        );
        bannerItems.add(item);
      }
      bannerController.bannerList.value = bannerItems;
      if (initializedIndex >= 0) {
        bannerController.currentIndex.value = initializedIndex;

        if (initializedModel != null) {
          await service.onShown(initializedModel);
        }
      }
    } catch (e) {
      FFLog.debug('获取追剧横幅资源位数据源失败: $e', tag: _tag);
    }
  }

  ResourceBitBaseService? _getResourceBitService() {
    final service = ResourceBitManager.getInstance()
        .getService(ResourceBitScene.myListBanner);
    return service;
  }

  void _addResourceBitChangedListener() {
    final service = _getResourceBitService();
    if (service == null) {
      return;
    }
    _removeResourceBitChangedListener();
    _resourceBitStreamSubscription = service.modelsStream.listen((models) {
      unawaited(_loadBannerData());
    });
  }

  void _removeResourceBitChangedListener() {
    _resourceBitStreamSubscription?.cancel();
    _resourceBitStreamSubscription = null;
  }

  void _onMyListResourceBitBannerClick(ResourceBitModel model) {
    model.onResourceBitClick(ResourceBitScene.myListBanner);
  }

  Future<void> _requrstCollectionDatas(bool isRefresh) async {
    if (collectionDatas.isEmpty) {
      favoritiesPageState.value = PageState.loading;
    } else {
      favoritiesPageState.value = PageState.none;
    }
    int? lastTime;
    if (isRefresh) {
      lastTime = DateTime.now().millisecondsSinceEpoch;
    } else {
      if (collectionDatas.isNotEmpty == true) {
        lastTime = collectionDatas.last.collectTime ??
            DateTime.now().millisecondsSinceEpoch;
      } else {
        lastTime = DateTime.now().millisecondsSinceEpoch;
      }
    }
    List<CollectionDataModel>? datas = await ApiMyList.getCollectList(
      CollectionRequestDataModel(
        pageSize: pageSize,
        lastTime: lastTime,
        colletType: 1,
        collectSource: [1],
      ),
    );
    if (showFullScreenShimmer.value == true) {
      showFullScreenShimmer.value = false;
    }
    if (datas != null) {
      if (isRefresh) {
        refreshController.finishRefresh(IndicatorResult.success);
        refreshController.resetFooter();
      } else {
        refreshController.finishLoad(IndicatorResult.noMore);
      }
      if (isRefresh) collectionDatas.clear();
      for (var element in datas) {
        shortsService.confirmCollection(element.shortPlayCode);
      }
      collectionDatas.addAll(datas);
      favoritiesPageState.value = PageState.success;
    } else {
      if (isRefresh) {
        refreshController.finishRefresh(IndicatorResult.success);
      } else {
        refreshController.finishLoad(IndicatorResult.noMore);
      }
      favoritiesPageState.value = PageState.failed;
      collectionDatas.clear();
    }
  }

  Future<void> _requestHistoryDatas(bool isRefresh) async {
    if (historyDatas.isEmpty) {
      historyPageState.value = PageState.loading;
    } else {
      favoritiesPageState.value = PageState.none;
    }
    int? lastTime;
    if (isRefresh) {
      lastTime = -1;
    } else {
      if (historyDatas.isNotEmpty == true) {
        lastTime = historyDatas.last.lastWatchTime ??
            DateTime.now().millisecondsSinceEpoch;
      } else {
        lastTime = DateTime.now().millisecondsSinceEpoch;
      }
    }
    List<HistoryDataModel>? datas = await ApiMyList.getHistoryList(
      HistoryRequestDataModel(
        pageSize: pageSize,
        lastTime: lastTime,
      ),
    );

    if (isRefresh) historyDatas.clear();

    if (datas != null) {
      if (isRefresh) {
        refreshController.finishRefresh(IndicatorResult.success);
        refreshController.resetFooter();
      } else {
        refreshController.finishLoad(IndicatorResult.noMore);
      }
      for (var element in datas) {
        if (element.collectStatus == 1) {
          shortsService.confirmCollection(element.shortPlayCode);
        }
      }
      historyDatas.addAll(datas);
      historyPageState.value = PageState.success;
    } else {
      if (isRefresh) {
        refreshController.finishRefresh(IndicatorResult.success);
      } else {
        refreshController.finishLoad(IndicatorResult.noMore);
      }
      historyPageState.value = PageState.failed;
      historyDatas.clear();
    }
  }

  Future<void> _requestCollectShort(int index) async {
    HistoryDataModel data = historyDatas[index];
    if (data.shortPlayId == null ||
        data.dramaId == null ||
        data.watchTime == null) {
      return;
    }
    bool result = await ApiMyList.collectShort(
      businessId: data.shortPlayId!,
      scene: 'recently',
      dramaId: data.dramaId!,
      colletType: 1,
      collectSource: 1,
      watchTime: data.watchTime!,
    );
    if (result == true) {
      shortsService.confirmCollection(data.shortPlayCode);
      shortsService.increaseShortsCollectionNumber(data.shortPlayCode);

      /// 更新这条数据
      historyDatas[index] = data.copyWith(collectStatus: 1);
    }
  }

  Future<void> _requestCancelCollectShort(int index) async {
    HistoryDataModel data = historyDatas[index];
    if (data.shortPlayId == null) return;
    bool result = await ApiMyList.cancelCollectShort(
      businessId: data.shortPlayId!,
      scene: 'recently',
      colletType: 1,
      collectSource: 1,
    );
    if (result == true) {
      shortsService.cancelCollection(data.shortPlayCode);
      shortsService.decreaseShortsCollectionNumber(data.shortPlayCode);

      /// 更新这条数据
      historyDatas[index] = data.copyWith(collectStatus: 0);
    }
  }

  Future<void> _requestBatchCancelCollectShorts() async {
    if (selectFavoritiesShortCodeSet.isEmpty) return;
    List<int> businessIdList = [];
    for (var element in collectionDatas) {
      if (selectFavoritiesShortCodeSet.contains(element.shortPlayCode)) {
        if (element.id != null) {
          businessIdList.add(element.id!);
        }
      }
    }
    if (businessIdList.isEmpty) return;
    Get.loading();
    bool result = await ApiMyList.batchCancelCollectShort(
      businessIdList: businessIdList,
    );
    Get.dismiss();
    if (result == true) {
      for (var element in selectFavoritiesShortCodeSet) {
        shortsService.cancelCollection(element);
      }
      _closeEditing();
      refreshData();
    }
  }

  Future<void> _requestDeleteHitoryShorts() async {
    if (selectHistoryShortCodeSet.isEmpty) {
      return;
    }

    List<int> businessIdList = [];
    for (var element in historyDatas) {
      if (selectHistoryShortCodeSet.contains(element.shortPlayCode)) {
        if (element.id != null) {
          businessIdList.add(element.id!);
        }
      }
    }
    if (businessIdList.isEmpty) return;
    Get.loading();
    bool result = await ApiMyList.deleteWatchHistoryShorts(
      businessIdList: businessIdList,
    );
    Get.dismiss();
    if (result == true) {
      _closeEditing();
      refreshData();
      eventBus.fire(RefreshHomeEvent());
    }
  }

//////////////////////////////////////////////////////////////
  /// actions
//////////////////////////////////////////////////////////////

  /// 点击编辑按钮
  void onHeaderActionEditingTap() {
    isEditing.value = true;
  }

  /// 点击全部选择按钮
  void onHeaderActionSelectedAllTap() {
    if (tabIndex.value == 0) {
      if (selectFavoritiesShortCodeSet.length != collectionDatas.length) {
        for (var element in collectionDatas) {
          if (element.shortPlayCode != null) {
            selectFavoritiesShortCodeSet.add(element.shortPlayCode!);
          }
        }
      } else {
        selectFavoritiesShortCodeSet.clear();
      }
    } else {
      if (selectHistoryShortCodeSet.length != historyDatas.length) {
        for (var element in historyDatas) {
          if (element.shortPlayCode != null) {
            selectHistoryShortCodeSet.add(element.shortPlayCode!);
          }
        }
      } else {
        selectHistoryShortCodeSet.clear();
      }
    }
  }

  /// 点击删除按钮
  void onHeaderActionDeleteTap() {
    String alertTitle =
        '${AppTrans.clearAll()} ${tabIndex.value == 0 ? AppTrans.favorites() : AppTrans.history()}';
    String alertDetail =
        '${AppTrans.all()} ${tabIndex.value == 0 ? AppTrans.favorites() : AppTrans.history()} ${AppTrans.clearedRestored()}';
    FFAlert.action(
      container: FFAlertActionContainer(
        title: alertTitle,
        detail: alertDetail,
        actions: [
          FFAlertActionButton.cancel(text: AppTrans.cancel()),
          FFAlertActionButton.confirm(
            text: AppTrans.confirm(),
            onPressed: () async {
              if (tabIndex.value == 0) {
                await _requestBatchCancelCollectShorts();
              } else {
                await _requestDeleteHitoryShorts();
              }
            },
          ),
        ],
      ),
    );
  }

  /// 点击返回按钮
  void onHeaderActionBackTap() {
    _closeEditing();
  }

  /// 点击tab项
  void onTabItemTap(int index) {
    _closeEditing();
    if (refreshController.headerState != null &&
        (refreshController.headerState?.mode != IndicatorMode.inactive &&
            refreshController.headerState?.mode != IndicatorMode.done)) {
      return;
    }
    if (isEditing.value == false) {
      tabIndex.value = index;
      refreshData();
    }
  }

  /// 点击收藏短剧
  Future<void> onFavoritiesShortsItemTap(int index) async {
    CollectionDataModel data = collectionDatas[index];
    if (isEditing.value == true) {
      /// 编辑状态
      if (data.shortPlayCode != null) {
        if (selectFavoritiesShortCodeSet.contains(data.shortPlayCode)) {
          selectFavoritiesShortCodeSet.remove(data.shortPlayCode);
        } else {
          selectFavoritiesShortCodeSet.add(data.shortPlayCode!);
        }
      }
    } else {
      /// 跳转到沉侵页
      await AppNavigator.startDetailsPage(DetailsOptions(
        businessId: data.shortPlayId!,
        playerEpisodeIndex: data.episodeNum != null ? data.episodeNum! - 1 : 0,
        playerEpisodePosition: Duration.zero,
        scene: TrackEvent.collections,
        from: EventValue.fromCollections,
      ));
      refreshData();
    }
  }

  /// 点击历史短剧
  Future<void> onHistoryShortsItemTap(int index) async {
    HistoryDataModel data = historyDatas[index];
    if (isEditing.value == true) {
      /// 编辑状态
      if (data.shortPlayCode != null) {
        if (selectHistoryShortCodeSet.contains(data.shortPlayCode)) {
          selectHistoryShortCodeSet.remove(data.shortPlayCode);
        } else {
          selectHistoryShortCodeSet.add(data.shortPlayCode!);
        }
      }
    } else {
      /// 跳转到沉侵页
      AppNavigator.startDetailsPage(DetailsOptions(
        businessId: data.shortPlayId!,
        playerEpisodeIndex: data.episodeNum != null ? data.episodeNum! - 1 : 0,
        playerEpisodePosition: data.watchTime == null
            ? Duration.zero
            : Duration(seconds: data.watchTime!),
        videoDuration: data.videoDuration,
        scene: TrackEvent.collections,
        from: EventValue.fromCollections,
      ));
    }
  }

  /// 点击历史短剧收藏按钮
  void onHistoryShortsItemCollectTap(int index) {
    HistoryDataModel data = historyDatas[index];
    if (data.collectStatus != null && data.collectStatus == 1) {
      /// 取消收藏
      _requestCancelCollectShort(index);
    } else {
      /// 确认收藏
      _requestCollectShort(index);
    }

    /// 检查并弹出通知弹窗
    NotificationLoginService notificationLoginService =
        Get.find<NotificationLoginService>();
    notificationLoginService.checkAndShowNotificationAlert(2);
  }

  //////////////////////////////////////////////////////////////
  /// private
  //////////////////////////////////////////////////////////////
  void _closeEditing() {
    isEditing.value = false;
    selectFavoritiesShortCodeSet.clear();
    selectHistoryShortCodeSet.clear();
  }
}

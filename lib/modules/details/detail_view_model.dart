import 'dart:math';

import 'package:get/get.dart';
import 'package:playlet/api/details.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/details.dart';
import 'package:toastification/toastification.dart';

class DramaWithEpisodesModel {
  ShortPlayDetail shortPlayDetail =ShortPlayDetail();

  // 该模型对应的第一集的索引 
  // 比如第一部剧长度有3集,第一部的这个值为0, 
  // 第二部有4集,第二部的这个值为3,
  // 第三部有x集,第三部的这个值为7
  int startDataIndex = 0;

  // 更新到最新的剧集的索引长度
  int get lastDataIndex {
      int index = episodeTabs.lastIndexWhere((element) {
        return element.isDisable == false;
      });
      if (index != -1) {
        return startDataIndex + index;
      }
      return startDataIndex;
  }

  // 是否当前剧已经更新的最后一集
  bool get isLastEpisode {
     int index = episodeTabs.lastIndexWhere((element) {
        return element.isDisable == false;
      });
      if (index != -1) {
        return index == currentIndex.value;
      }
    return false;
  }

  // 当前剧更新的最后一集
  EpisodeData? get currentUpdatedDramaLastEpisode {
    final item = episodeTabs.lastWhere((element) {
      return element.isDisable == false;
   });
    return item.info;
  }

  /// 当前剧可以播放剧集列表
  List<Episode> episodeList;

  ///当前剧集tabs 分组
  List<EpisodeTabInfo> episodeTabList;
  ///当前剧集列表信息,包含未更新的剧集
  List<EpisodeTab> episodeTabs;
  // 当前剧集索引
  RxInt currentIndex = 0.obs;

  // 当前的剧集信息
  Rxn<EpisodeVideoData> currentEpisode = Rxn<EpisodeVideoData>();

  late int businessId;

  // 添加分辨率相关的变量
  RxString currentResolution = '720p'.obs;

  // 视频解锁那集展示解锁按钮视图
  RxBool isLastOneView = false.obs;
  // 是否是全部解锁
  RxBool allLock = false.obs;
  // 是否是需要解锁的那一集
  RxBool isUnlockEpisodId = false.obs;


  DramaWithEpisodesModel() :
    episodeList = <Episode>[],
    episodeTabList = <EpisodeTabInfo>[].obs,
    episodeTabs = <EpisodeTab>[].obs;

  // 更新剧的剧集
  void updateEpisodes(List<Episode> episodes) {
    List<Episode> tempList = List.from(episodeList);
    tempList.addAll(episodes);
    episodeList = tempList;
  }


}

class DetailViewModel extends GetxController {

  // 存储多部剧的列表
  final dramaDataList = <DramaWithEpisodesModel>[];
  // 当前正在观看的剧的索引
  final currentDramaIndex = 0.obs;
  // 上次滑动的索引,用于判断是否是上滑还是下滑和当前滚动回调的index相比
  int previousIndex = 0;

  // 是否正在加载下一部剧
  final isLoadingNextDrama = false.obs;
    // 提示下一集的播放toast
  bool isShowingNextPlayToast = false;
  ToastificationItem? nextDrameToast;
  // 是否正在展示连续看广告,这个字段在观看完广告,会触发生命周期回调,所以需要这个字段做判断
  bool isShowingContinueAdPopWidget = false;

  // 到当前剧集的的已经更新的总长度,用于判断上划判断是不是下一部剧了
  int get totalToCurrentPageNumLength {
    var totalNum = 0;
    for (int i = 0; i < currentDramaIndex.value + 1; i++) {
      int index = dramaDataList[i].episodeTabs.lastIndexWhere((element) {
        return element.isDisable == false;
      }); 
      if (index != -1) {
          totalNum += index + 1;
      }
    }
    FFLog.debug("剧集更新的总长度 $totalNum");
    return totalNum;
   }

  int totalEpisodesNumLength = 0;

  DetailViewModel() {
    // 初始化数据池
    dramaDataList.add(DramaWithEpisodesModel());
  }

  // 是否有下一部剧存在
  bool get hasNextDrama {
    return currentDramaIndex.value < dramaDataList.length - 1;
  }

  DramaWithEpisodesModel? get nextDramaModel {
    if (currentDramaIndex.value + 1 < dramaDataList.length) {
      return dramaDataList[currentDramaIndex.value + 1];
    }
    return null;
  }


  DramaWithEpisodesModel get currentDramaModel {
    return dramaDataList[currentDramaIndex.value];
  }

  //根据传入的index获取剧的模型和剧集的索引
  (int, DramaWithEpisodesModel)? getCurrentDramamModelAndIndexWithIndex(int index) {
    int accumulatedLength = 0;
    for (int i = 0; i < dramaDataList.length; i++) {
      final drama = dramaDataList[i];
      final dramaLength = drama.episodeList.length;
      // 检查全局索引是否在当前剧集范围内
      if (index < accumulatedLength + dramaLength) {
        // 计算在当前剧集内的索引
        final episodeIndex = index - accumulatedLength;
        return (episodeIndex, drama);
      }
      // 累加当前剧集的长度
      accumulatedLength += dramaLength;
    }
  }

  // 预加载下一部剧（在当前剧播放到最后一集时调用）
  Future<ShortPlayDetail?> preloadNextDrama(int currentBusinessId) async {
    if (isLoadingNextDrama.value == true){
      return null;
    }
    isLoadingNextDrama.value = true;
    final result = await _loadNextDrama(currentBusinessId);
    isLoadingNextDrama.value = false;
    return result;
  }

  // 加载下一部剧
  Future<ShortPlayDetail?> _loadNextDrama(int currentBusinessId, {String scene = ""}) async {
    try {
      final nextDramaId = await ApiDetails.getNextShortPlay(currentBusinessId);
      if (nextDramaId != null) {
        final playDetail = await ApiDetails.getDetail(nextDramaId, scene);
        return playDetail;
      }
      return null;
    } catch (e) {
      return null;
    } finally {
    }
  }
  
  // 切换到下一部剧索引
  bool switchToNextDrama() {
    FFLog.debug("准备切换剧 ${currentDramaIndex.value}");
    if (currentDramaIndex.value < dramaDataList.length - 1) {
      currentDramaIndex.value++;
      FFLog.debug("切换剧 ${currentDramaIndex.value}");
      return true;
    }
    return false;
  }
  
  // 切换到上一部剧索引
  bool switchToPreviousDrama() {
    if (currentDramaIndex.value > 0) {
      currentDramaIndex.value--;
      return true;
    }
    return false;
  }
  
  //增加模型
  void addDramaModel(DramaWithEpisodesModel model) {
    dramaDataList.add(model);
    int _startIndex = 0;
    // 获取当前剧之前的更新的长度
    for (int i = 0; i < dramaDataList.length; i++) {
      if (i <= currentDramaIndex.value) {
        var exitIndex = dramaDataList[i].episodeTabs.lastIndexWhere((element) {
          return element.isDisable == false;
        });
        if (exitIndex!= -1) {
          _startIndex += exitIndex + 1;
        }
      }
    }
    model.startDataIndex = _startIndex;
  }

  // 更新当前剧的可以观看的剧集列表信息
  void updateCurrentunlockedDramaEpisodes(List<Episode> episodes, {DramaWithEpisodesModel? model}) {
    if (model != null) {
      dramaDataList.firstWhere((element) => element.businessId == model.businessId).updateEpisodes(episodes);
    } else {
      currentDramaModel.updateEpisodes(episodes);
    }
    updateTotalEpisodesNumPage();
  }

  // 这边去计算可以滑动的总长度
  void updateTotalEpisodesNumPage() {
      var totalNum = 0;
      dramaDataList.forEach((element) {
        totalNum += element.episodeList.length;
      });
      FFLog.debug("可以播放的剧集总长度 $totalNum");
      totalEpisodesNumLength = totalNum;
  }
  

  void clearData() {
    dramaDataList.clear();
    previousIndex = 0;
    totalEpisodesNumLength = 0;
  }

}
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/utils/track_event.dart';

class DetailTrackEvent {
  DetailTrackEvent._();

  ///请求剧埋点
  static reelRequest({
    required final String status,
    required final int? shortPlayCode,
    required final int? episode,
    required final String scene,
    String? resourceBitId,
  }) {
    useTrackEvent(TrackEvent.reel_request, extra: {
      TrackEvent.status: status,
      TrackEvent.reel_id: shortPlayCode == null ? '' : shortPlayCode.toString(),
      TrackEvent.episode: episode == null ? '' : episode.toString(),
      TrackEvent.scene: scene,
      EventKey.resourceBitId: resourceBitId ?? '',
    });
  }

  static void videoSpeedAdjust({
    required final int? shortPlayCode,
    required final bool isLandscapeScreen,
  }) {
    useTrackEvent(TrackEvent.video_speed_adjust, extra: {
      TrackEvent.reel_id: shortPlayCode == null ? '' : shortPlayCode.toString(),
      EventKey.scene:
          isLandscapeScreen ? EventValue.horizontal : EventValue.vertical,
    });
  }
}

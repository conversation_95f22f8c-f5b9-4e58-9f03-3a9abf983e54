import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/log/file/ff_log_file_isolate.dart';
import 'package:playlet/components/details/details_header.dart';
import 'package:playlet/components/details/episode_item_widget.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/loading/ffloading.dart';
import 'package:playlet/components/shimmer/detail_shimmer.dart';

import 'details_controller.dart';

class DetailsPage extends StatefulWidget {
  const DetailsPage({super.key});

  @override
  State<DetailsPage> createState() => _DetailsPageState();
}

class _DetailsPageState extends State<DetailsPage> {
  late DetailsController controller;

  @override
  void initState() {
    super.initState();
    // 获取控制器实例
    controller = Get.find<DetailsController>(tag: 'details_controller');
  }

  @override
  void dispose() {
    // 确保在页面销毁时清理资源
    try {
      // 暂停当前视频
      controller.onCurrentVideoPause();

      // 清空预加载列表
      controller.playerService.clearPreloadList();

      // 确保从内存中彻底删除控制器
      Future.delayed(const Duration(milliseconds: 100), () {
        if (Get.isRegistered<DetailsController>(tag: 'details_controller')) {
          Get.delete<DetailsController>(tag: 'details_controller', force: true);
        }
      });
    } catch (e) {
      FFLog.error('DetailsPage dispose error: $e', tag: 'DetailsPage');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (controller.detailsOptions.isSupportLandscapeMode) {
      if (controller.isLandscapeScreen.value) {
        ScreenUtil.init(
          context,
          designSize: const Size(852, 393),
        );
      } else {
        ScreenUtil.init(
          context,
          designSize: const Size(393, 852),
        );
      }
    }
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: controller.onPopInvokedWithResult,
      child: Scaffold(
        body: Stack(
          alignment: Alignment.center,
          children: [
            Obx(() {
              if (controller.loadingStatus.value == LoadingStatus.loading) {
                // 加载中
                return const Stack(
                  children: [
                    DetailShimmer(),
                    Center(child: FFLoadingWidget()),
                  ],
                );
              }
              if (controller.loadingStatus.value == LoadingStatus.failed) {
                // 加载失败
                return EmptyWidget(
                  pageFrom: EmptyPageFrom.shorts,
                  type: EmptyType.noNetwork,
                  onRefresh: () {
                    controller.initData();
                  },
                );
              }
              return GetBuilder<DetailsController>(
                id: controller.episodeListId,
                init: controller,
                tag: "details_controller",
                builder: (_) {
                  return PageView.builder(
                    scrollDirection: Axis.vertical,
                    controller: controller.pageViewController,
                    itemCount: controller.viewModel.totalEpisodesNumLength,
                    onPageChanged: (index) {
                      FFLog.info("现在触发的内容index是多少 $index");
                      // 判断滑动方向
                      final isScrollingDown = index > controller.viewModel.previousIndex;
                      controller.viewModel.previousIndex = index;
                      // 调用控制器方法处理滑动
                      controller.onPageViewChanged(index, isScrollingDown: isScrollingDown);
                    },
                    physics: const ClampingScrollPhysics(),
                    // 以下三个属性暂时先注释,使用过程中,发现在播第一部剧的时候,下一部剧开始播放了,注释掉,好像就没有问题了
                    // padEnds: true,
                    // pageSnapping: true,
                    // allowImplicitScrolling: true, // 允许隐式滚动，预渲染前后页面
                    key: const PageStorageKey('details_page_view'),
                    itemBuilder: (context, index) {
                      FFLog.info('layout布局内容 index: $index', tag: 'DetailsPage');
                      // 检查是否需要加载下一部剧   
                      final result = controller.viewModel.getCurrentDramamModelAndIndexWithIndex(index);
                      if (result != null) {
                        final (episodeIndex, dramaModel) = result;
                        FFLog.info('layout布局内容 转换后 index: $episodeIndex, dramaModel = $dramaModel');
                        FFLog.info('layout布局内容 转换后 shortPlayDetail= ${dramaModel.shortPlayDetail}, shortPlayCode: ${dramaModel.shortPlayDetail.shortPlayCode}, episodeList = ${dramaModel.episodeList}');

                        return EpisodeItemWidget(
                          key: ValueKey("$index + ${dramaModel.shortPlayDetail.shortPlayName}"),
                          index: episodeIndex,
                          shortPlayCode:
                              dramaModel.shortPlayDetail.shortPlayCode,
                          shortPlayType: dramaModel.shortPlayDetail.shortPlayType,
                          episode: dramaModel.episodeList[episodeIndex],
                          onPlayStatusChanged: (isPlaying) async {
                            await controller.onPlayStatusChanged(isPlaying);
                          },
                          onEnded: controller.onEnded,
                          onTimeChange: (tiem) =>
                              controller.onVideoPlayTimeChange(episodeIndex, tiem),
                          detailsController: controller,
                        );
                      } else {
                        // 找不到剧就用空视图
                        return Container();
                      }
                    },
                  );
                },
              );
            }),
           
             Positioned(
              bottom: 0,
              left: 0,
              right: 0,
               height: Platform.isIOS 
                  ? (controller.isLandscapeScreen.value ? 10.sp : 25.sp) 
                  : (controller.isLandscapeScreen.value ? 10.sp : 20.sp),
              child: AbsorbPointer( // 阻止该区域的事件传递到下方
                child: Container(color: Colors.transparent),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
               height: Platform.isIOS 
                  ? (controller.isLandscapeScreen.value ? 10.sp : 25.sp) 
                  : (controller.isLandscapeScreen.value ? 10.sp : 20.sp),
              child: AbsorbPointer( // 阻止该区域的事件传递到下方
                child: Container(color: Colors.transparent),
              ),
            ),
             DetailsHeader(
              onBack: controller.onBack,
              detailsController: controller,
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:dlink_analytics/event.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/common/color/color_hex.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/service/ad/ad_track_event.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/utils/track_event.dart';


class AdvanceWatchAdPopWidget extends StatelessWidget {
  final EpisodeVideoData? episodeNumber;
  final VoidCallback? onWatchAdPressed;
  final VoidCallback? onCancelPressed;
  static int watchAdFailedCount = 0; //观看的失败次数
  final isLandscape = MediaQuery.of(Get.context!).orientation == Orientation.landscape;
  AdvanceWatchAdPopWidget({
    Key? key,
    this.episodeNumber,
    this.onWatchAdPressed,
    this.onCancelPressed,
  }) : super(key: key);

  static void show(EpisodeVideoData? episodetail, {
    VoidCallback? onWatchAdPressed,
    VoidCallback? onCancelPressed,
  }) {

    // 增加连续看广告的弹窗展示
    useTrackEvent(TrackEvent.continuousAPop);
    final detaiController = Get.find<DetailsController>(tag: "details_controller");
    // adplacementshow
    AdTrackEvent.adPlacementShow(AdType.reward, AdScene.continuousAdPop, param: {
      TrackEvent.reel_id: detaiController.viewModel.currentDramaModel.shortPlayDetail.shortPlayCode.toString(),
      TrackEvent.episode: episodetail?.episodeNum.toString() ?? "0",
    });

    SmartDialog.show(
      backType: SmartBackType.block,
      clickMaskDismiss: false,
      builder: (context) => AdvanceWatchAdPopWidget(
        episodeNumber: episodetail,
        onWatchAdPressed: onWatchAdPressed,
        onCancelPressed: (){
          SmartDialog.dismiss();
          onCancelPressed?.call();
        }
      ),
    );
  }

  static void hide() {
    SmartDialog.dismiss();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(18.sp),
      ),
      child: 
          // centerView现在包含所有其他控件
          Container(
            width: isLandscape ? 260.sp: 300.sp,
            height: isLandscape ? 236.sp: 284.sp,
            decoration: BoxDecoration(
              color: ColorHex.fromHex("#25252E"),
              borderRadius: BorderRadius.circular(18.sp),
            ),
            child: Stack(
              clipBehavior: Clip.none, // 允许子控件超出边界
              children: [
                // 背景图片
                SizedBox.expand(
                  child: Image.asset(
                    "assets/details/advance_watch_ad/mask_bg_pop.png",
                    fit: BoxFit.fill, // 或者使用BoxFit.fill
                  ),
                ),
                
                // 顶部图标 - 相对于Stack定位
                Positioned(
                  top: isLandscape ? -35.sp : -58.sp, // 让图标顶部超出centerView
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Image.asset(
                      "assets/details/advance_watch_ad/advance_top_icon.png",
                      width: isLandscape ? 100.sp : 124.sp,
                      height: isLandscape ? 100.sp : 124.sp,
                    ),
                  ),
                ),
                
                // 关闭按钮
                Positioned(
                  top: 18.sp,
                  right: 18.sp,
                  child: InkWell(
                    onTap: () {
                      onCancelPressed?.call();
                    },
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    child: Image.asset(
                      "assets/details/advance_watch_ad/advance_watch_close.png",
                      width: isLandscape ? 20.sp : 24.sp,
                      height: isLandscape ? 20.sp: 24.sp,
                    ),
                  ),
                ),
                
                // 内容区域
                Padding(
                  padding: EdgeInsets.only(top: isLandscape ? 70.sp : 84.sp), // 124.sp - 40.sp (图标高度的一半)
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 标题文本
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: isLandscape ? 35.sp: 37.sp),
                        child: Text(
                          AppTrans.advanceWatchUnlockCurrent(episodeNumber?.episodeNum),
                          textAlign: TextAlign.center,
                          softWrap: true,
                          overflow: TextOverflow.visible,
                          style: TextStyle(
                            fontSize: isLandscape ? 14.sp : 16.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      
                      SizedBox(height: isLandscape ? 20.sp : 30.sp),
                      
                      // Watch Ad 按钮
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: isLandscape ? 35.sp :44.sp),
                        child: TextButton(
                          onPressed: onWatchAdPressed,
                          child: Container(
                            height: isLandscape ? 32.sp : 40.sp,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.sp),
                              image: DecorationImage(
                                image: AssetImage("assets/details/advance_watch_ad/advance_watch_button_bg.png"),
                                fit: BoxFit.fill,
                              ),                            ),
                            child: Text(
                              AppTrans.watchAds(),
                              style: TextStyle(
                                fontSize: isLandscape ? 14.sp : 16.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      
                      // Cancel 按钮
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: isLandscape ? 35.sp :44.sp), // 调整顶部间距
                        child: TextButton(
                          onPressed: () {
                            onCancelPressed?.call();
                          },
                          child: Container(
                            height: isLandscape ? 32.sp :40.sp,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(isLandscape ? 16.sp :20.sp),
                              // 这里可以添加你想要的背景样式
                            ),
                            child: Text(
                              AppTrans.advanceWatchCancel(),
                              style: TextStyle(
                                fontSize: isLandscape ? 14.sp : 16.sp,
                                fontWeight: FontWeight.w500,
                                color: ColorHex.fromHex("#828282"),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/common/color/color_hex.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/store/unlock_store_product_result.dart';
import 'package:playlet/modules/details/advance_watch_controller.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/service/ad/ad_track_event.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/admodel/am_admob_model.dart';
import 'package:playlet/utils/track_event.dart';

//看广告挽留弹窗
typedef WatchAdCallback = void Function(EpisodeVideoData willUnlockDrama, WatchAdResultResponse);

class AdvanceContinueWatchAdWidget extends StatefulWidget  {

  static int watchAdFailedCount = 0; //观看的失败次数


  final isLandscape = MediaQuery.of(Get.context!).orientation == Orientation.landscape;

  final WatchAdCallback? watchAdClickAction;
  final VoidCallback? closeAction;

  AdvanceContinueWatchAdWidget({
    Key? key,
    this.watchAdClickAction,
    this.closeAction,
  }) : super(key: key);
  
  // 添加静态方法获取控制器
  static AdvanceWatchAdController getController() {
    return Get.put(AdvanceWatchAdController());
  }

  static void show({
    WatchAdCallback? onWatchAdPressed,
    VoidCallback? onCancelPressed,
  }) {

    // 增加连续看广告挽留的弹窗展示
    useTrackEvent(TrackEvent.continuousAdRetentioPop);

     // 使用SmartDialog显示弹窗
    SmartDialog.show(
      backType: SmartBackType.block,
      clickMaskDismiss: false,
      useAnimation: false,
      builder: (context) => AdvanceContinueWatchAdWidget(
        watchAdClickAction:onWatchAdPressed,
        closeAction: onCancelPressed,
      ),
    );
  }

static void hide() {
    SmartDialog.dismiss();
  }

  @override
  _AdvanceContinueWatchAdWidgetState createState() => _AdvanceContinueWatchAdWidgetState();
}
class _AdvanceContinueWatchAdWidgetState extends State<AdvanceContinueWatchAdWidget> {
  late AdvanceWatchAdController controller;
  
  @override
  void initState() {
    super.initState();
    controller = Get.find<AdvanceWatchAdController>();

    final detaiController = Get.find<DetailsController>(tag: "details_controller");
    // adplacementshow
    AdTrackEvent.adPlacementShow(AdType.reward, AdScene.continuousAdRetentionPop, param: {
      TrackEvent.reel_id: detaiController.viewModel.currentDramaModel.shortPlayDetail.shortPlayCode.toString(),
      TrackEvent.episode: controller.nextEpisodeVideoData.value?.episodeNum.toString() ?? "0",
    });

  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black.withOpacity(0.5),
      child: Center(
        child: Container(
          width: widget.isLandscape ? 280.sp: 300.sp,
          height: widget.isLandscape ? 272.sp :344.sp,
          decoration: BoxDecoration(
            color: ColorHex.fromHex("#25252E"),
            borderRadius: BorderRadius.circular(10.sp),
          ),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
               // 背景图片
                SizedBox.expand(
                  child: Image.asset(
                    "assets/details/advance_watch_ad/mask_bg_continue_top.png",
                    fit: BoxFit.fill, // 或者使用BoxFit.fill
                  ),
                ),
                
                // 关闭按钮
                Positioned(
                  top: 18.sp,
                  right: 18.sp,
                  child: InkWell(
                    onTap: () {
                      AdvanceContinueWatchAdWidget.hide();
                      widget.closeAction?.call();
                    },
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    child: Image.asset(
                      "assets/details/advance_watch_ad/advance_watch_close.png",
                      width: widget.isLandscape ?20.sp :24.sp,
                      height: widget.isLandscape ?20.sp :24.sp,
                    ),
                  ),
                ),
              
              // 内容区域
              Padding(
                padding: EdgeInsets.only(top: widget.isLandscape ? 12.sp: 38.sp),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal:widget.isLandscape ?12.sp: 37.sp),
                    child: 
                    Text(
                      AppTrans.advanceContinueCongratulations(),
                      style: TextStyle(
                        fontSize: widget.isLandscape ?16.sp: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ), 
                  ),
                    
                   
                    SizedBox(height: widget.isLandscape ? 4.sp :6.sp),
                    // 副标题
                    Padding(padding: 
                    EdgeInsets.symmetric(horizontal: widget.isLandscape ?37.sp:43.sp),
                    child: 
                    Text(
                      AppTrans.advanceContinueTip(),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: widget.isLandscape ? 13.sp:14.sp,
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                      ),
                      ),
                    ),
                   
                    SizedBox(height: widget.isLandscape ? 12.sp :18.sp),

                    Container(
                      padding: EdgeInsets.symmetric(horizontal: widget.isLandscape ? 30.sp: 32.sp),
                      child: 
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 左侧剧集信息列
                          Container(
                            width: widget.isLandscape ? 205.sp : 220.sp, // 设置固定宽度
                            child: Column(
                              children: [
                                // 已解锁的剧集
                                Container(
                                  height: widget.isLandscape ? 37.5.sp :40.sp,
                                  child: Stack(
                                    fit: StackFit.expand, // 确保Stack填充整个Container
                                    children: [
                                      // 背景图片
                                      Positioned.fill(
                                        child: Image.asset(
                                          "assets/details/advance_watch_ad/continue_watch_current.png",
                                          fit: BoxFit.fill,
                                        ),
                                      ),
                                      // 内容
                                      Padding(
                                        padding: EdgeInsets.symmetric(horizontal: widget.isLandscape ?17.sp: 19.sp),
                                        child: Row(
                                          children: [
                                            Image.asset(
                                              width: widget.isLandscape ? 18.5.sp : 20.sp,
                                              height: widget.isLandscape ? 18.5.sp : 20.sp,
                                             "assets/details/advance_watch_ad/advance_unlocked_icon.png",
                                            ),
                                            SizedBox(width: widget.isLandscape ? 11.sp: 12.sp),
                                            Obx(() {
                                              final current = controller.hasUnlockedVideoData.value;
                                              return Text(
                                                AppTrans.advanceContinueUnlocked(current?.episodeNum ?? ""),
                                                style: TextStyle(
                                                fontSize: widget.isLandscape ? 11.sp :12.sp,
                                                fontWeight: FontWeight.w400,
                                                color: ColorHex.fromHex("#999999"),
                                              ),
                                                // 样式保持不变
                                              );
                                            }),
                                          ],
                                        ),
                                      ),
                                    ]
                                  ),
                                ),
                                
                                SizedBox(height: 10.sp),
                                
                                // 待解锁的剧集
                                Container(
                                  height:widget.isLandscape ? 54.sp: 58.sp,
                                  child: Stack(
                                    fit: StackFit.expand, // 确保Stack填充整个Container
                                    children: [
                                      // 背景图片
                                      Positioned.fill(
                                        child: Image.asset(
                                          "assets/details/advance_watch_ad/continue_watch_next.png",
                                          fit: BoxFit.fill,
                                        ),
                                      ),
                                      // 内容
                                      Padding(
                                        padding: EdgeInsets.symmetric(horizontal: widget.isLandscape ?17.sp: 19.sp),
                                        child: Row(
                                          children: [
                                            Image.asset(
                                              width: widget.isLandscape ? 18.5.sp : 20.sp,
                                              height: widget.isLandscape ? 18.5.sp : 20.sp,
                                              "assets/details/advance_watch_ad/advance_unlock_icon.png",
                                            ),
                                            SizedBox(width: widget.isLandscape ? 11.sp: 12.sp),
                                            Column(
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                ShaderMask(
                                                  shaderCallback: (Rect bounds) {
                                                    return LinearGradient(
                                                      colors: [ColorHex.fromHex("#FF9937"), ColorHex.fromHex("#FBE840")],
                                                      begin: Alignment.centerLeft,
                                                      end: Alignment.centerRight,
                                                    ).createShader(bounds);
                                                  },
                                                  child: 
                                                  Obx(() {
                                                      final nextDrama = controller.nextEpisodeVideoData.value;
                                                      return Text(
                                                        AppTrans.advanceContinueNextUnlock(nextDrama?.episodeNum ?? ""),
                                                          style: TextStyle(
                                                            fontSize: widget.isLandscape? 13.sp:14.sp,
                                                            fontWeight: FontWeight.w600,
                                                            color: Colors.white, // 必须是白色，这样渐变才能正确显示
                                                          ),
                                                        );
                                                  }),
                                                ),
                                                Text(
                                                  AppTrans.advanceContinueOnemore(),
                                                  style: TextStyle(
                                                    fontSize: widget.isLandscape? 11.sp :12.sp,
                                                    fontWeight: FontWeight.w400,
                                                    color: ColorHex.fromHex("#999999", opacity: 0.6),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ]
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // 右侧分隔线图片
                          SizedBox(width: widget.isLandscape? 12.sp : 13.sp),

                          Image.asset(
                            "assets/details/advance_watch_ad/continue_right.png",
                            width: widget.isLandscape? 2.8.sp : 3.sp,
                            height: widget.isLandscape? 100.sp :108.sp,
                          ),
                        ],
                      ),
                    ),
                    
                    SizedBox(height: widget.isLandscape? 18.5.sp :25.sp),

                    // Watch Ad 按钮
                      // Watch Ad 按钮
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 44.sp),
                        child: TextButton(
                          onPressed: () {
 final action = widget.watchAdClickAction;
  action?.call(controller.nextEpisodeVideoData.value!, controller.watchAdInfo.value!);                            
                          },
                          child: Container(
                            height: widget.isLandscape? 32.sp: 40.sp,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(widget.isLandscape? 16.sp :20.sp),
                               image: DecorationImage(
                                image: AssetImage("assets/details/advance_watch_ad/advance_watch_button_bg.png"),
                                fit: BoxFit.fill,
                              ),
                            ),
                            child: Text(
                              AppTrans.watchAds(),
                              style: TextStyle(
                                fontSize: widget.isLandscape? 14.sp :16.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
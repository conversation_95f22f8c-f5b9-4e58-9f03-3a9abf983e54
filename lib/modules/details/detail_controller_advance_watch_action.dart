import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/api/details.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/alert/remind.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/store/unlock_store_product_result.dart';
import 'package:playlet/modules/details/advance_watch_ad/ui/advance_continue_watch_ad.dart';
import 'package:playlet/modules/details/advance_watch_ad/ui/advance_watch_ad_pop.dart';
import 'package:playlet/modules/details/advance_watch_controller.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/track_event.dart';

extension DetailControllerAdvanceWatchAction on DetailsController {

  Future<bool> advanceWatch(EpisodeVideoData nextDrama, bool hasWatchAd, UnlockAdCountData? unlockAdCount) {
    AdvanceWatchAdPopWidget.watchAdFailedCount = 0;
    // 是否正在展示广告弹窗
    viewModel.isShowingContinueAdPopWidget = true;
    Completer<bool> completer = Completer<bool>();
    AdvanceWatchAdPopWidget.show(nextDrama, onWatchAdPressed: () {
      unlockNextEpisodeWithAd(nextDrama);
      completer.complete(true);
    }, onCancelPressed: () {
      // 这边要直接弹出剧集解锁的内容
      // onStorePanelShow(viewModel.currentDramaModel.currentIndex.value, unlockAdCount: unlockAdCount);
      // 这边需求调整,关闭就什么都不要做
      onLastOneShow();
      AdvanceWatchAdPopWidget.hide();
      completer.complete(false);
    });
    return completer.future;
  }

  void unlockNextEpisodeWithAd(EpisodeVideoData nextDrama) async {
    if (AdvanceWatchAdPopWidget.watchAdFailedCount >= 3) {
      viewModel.isShowingContinueAdPopWidget = false;
      //解锁失败了三次那么就要不弹了
      onLastOneShow();
      AdvanceWatchAdPopWidget.hide();
      return;
    }
    UnlockWatchAdInfoResponse? result = await _watchAd(nextDrama);
    if (result != null) {
      AdvanceWatchAdPopWidget.watchAdFailedCount = 0;
      AdvanceWatchAdPopWidget.hide();
      bool canshowContinue = false;
      AdvanceContinueWatchAdWidget.watchAdFailedCount = 0;
      canshowContinue = ((result.canWatchAdNum ?? 0) > 0) && result.nextDrama != null && result.nextDrama?.unlockType != 1;
      if (canshowContinue){
        // 这边去展示连续弹窗
        AdvanceContinueWatchAdWidget.getController().updateData(
          unlockedData: nextDrama,
          nextEpisodeData: result.nextDrama,
          adInfo: WatchAdResultResponse(canWatchAdNum: result.canWatchAdNum, totalWatchAdNum: result.totalWatchAdNum),
        );

        AdvanceContinueWatchAdWidget.show( 
          onWatchAdPressed: (willUnlockDrama, adInfo) {
            continueWatchAd(willUnlockDrama, adInfo);
          },
          onCancelPressed: (){
            viewModel.isShowingContinueAdPopWidget = false;
            //  onTo(viewModel.currentDramaModel.currentIndex.value + viewModel.currentDramaModel.startDataIndex);
            onCurrentVideoPause();
            onCurrentVideoPlay();
          });
      } else {
        viewModel.isShowingContinueAdPopWidget = false;
        //这边不能连续弹窗了
        // 继续播放当前的内容
        // await onTo(viewModel.currentDramaModel.currentIndex.value + viewModel.currentDramaModel.startDataIndex);
        onCurrentVideoPause();
        onCurrentVideoPlay();
      }
    } else{
      AdvanceWatchAdPopWidget.watchAdFailedCount += 1;
    }
 }

  Future<UnlockWatchAdInfoResponse?> _watchAd(EpisodeVideoData nextDrama, {bool needShowToastWhenUnlockSuccess = true}) async{
    AdContext context = AdContext();
    context.eventParam = {
      TrackEvent.reel_id: viewModel.currentDramaModel.shortPlayDetail.shortPlayCode == null
          ? ''
          : viewModel.currentDramaModel.shortPlayDetail.shortPlayCode.toString(), 
      TrackEvent.episode: (nextDrama.episodeNum).toString(),
      TrackEvent.lock_begin: viewModel.currentDramaModel.shortPlayDetail.lockBegin.toString(),
      EventKey.ad_placement: (needShowToastWhenUnlockSuccess == true)? EventValue.continuousAdPopFrom : EventValue.continuousAdRetentionPopFrom
    };
    final scene = (needShowToastWhenUnlockSuccess == true)? AdScene.continuousAdPop : AdScene.continuousAdRetentionPop;
    bool isSuccess = await AdManager()
        .showFullScreenAd(scene, context: context);
    if (isSuccess) {

    // 增加观看广告成功打点
    useTrackEvent(TrackEvent.episodeUnlockAdSuccess, extra: {
      EventKey.reelId: viewModel.currentDramaModel.shortPlayDetail.shortPlayCode.toString(),
      EventKey.episode: nextDrama.episodeNum.toString(),
      EventKey.isEpEnd: (nextDrama.episodeNum == viewModel.currentDramaModel.shortPlayDetail.totalEpisodes) ? "1" : "0",
      EventKey.from: (needShowToastWhenUnlockSuccess == true)? EventValue.continuousAdPopFrom : EventValue.continuousAdRetentionPopFrom,
      EventKey.lockBegin: viewModel.currentDramaModel.shortPlayDetail.lockBegin.toString()
    });


      Get.loading();
      FFLog.debug("观看广告解锁当前的集数 ${nextDrama.episodeNum}");
      var index = 0;
      viewModel.currentDramaModel.episodeTabs.forEach((element) {
        if (element.info.episodeNum == nextDrama.episodeNum && element.info.id == nextDrama.id) {
            index = element.index;
        }
      });
      final episode = viewModel.currentDramaModel.episodeTabs[index].info;
      FFLog.debug("解锁的集数id ${episode.id},集数 ${episode.episodeNum}");
      FFLog.debug("nextdrana解锁的集数id ${nextDrama.id},集数 ${nextDrama.episodeNum}");
      final result = await ApiDetails.unlockByWatchAd(nextDrama.id!);
      if (result != null ) {
        lockNum = lockNum + 1;
        if (viewModel.currentDramaModel.episodeList.length <= (viewModel.currentDramaModel.shortPlayDetail.updateEpisode ?? 0)) {
            // 修改已经解锁的剧集的属性
            viewModel.currentDramaModel.episodeList.forEach((element) {
              element.info.alreadyLock = 1;
            });
            if (result.nextDrama != null) {
              onAddEpisode([result.nextDrama!]);
            }
         }
         
        // 设置判断是否已经全部解锁完毕
        setAllUnlockValue(index);

        updateEpioode(index);
        Get.dismiss();
        if (needShowToastWhenUnlockSuccess){
          Get.toast(AppTrans.unLockVideoTip(episode.episodeNum));
        }
        return result;
      }
      else {
        Get.dismiss();
        return null;
      } 
   }
  }


  void continueWatchAd(EpisodeVideoData willUnlockDrama, WatchAdResultResponse adInfo) async{

    if (AdvanceContinueWatchAdWidget.watchAdFailedCount >= 3) {
      viewModel.isShowingContinueAdPopWidget = false;
      //解锁失败了三次那么就要不弹了
      AdvanceContinueWatchAdWidget.hide();
      return;
    }

    UnlockWatchAdInfoResponse? result = await _watchAd(willUnlockDrama, needShowToastWhenUnlockSuccess: false);
    if (result == null){
      AdvanceContinueWatchAdWidget.watchAdFailedCount += 1;
    } else {
      AdvanceContinueWatchAdWidget.watchAdFailedCount = 0;
    }

    bool canshowContinue = false;

    canshowContinue = ((result?.canWatchAdNum ?? 0) > 0) && result?.nextDrama != null && result?.nextDrama?.unlockType != 1;

    if (canshowContinue){
          Get.find<AdvanceWatchAdController>().updateData(
          unlockedData: willUnlockDrama,
          nextEpisodeData: result?.nextDrama,
          adInfo: WatchAdResultResponse(canWatchAdNum: result?.canWatchAdNum, totalWatchAdNum: result?.totalWatchAdNum),
          );
    } else {
      viewModel.isShowingContinueAdPopWidget = false;
      AdvanceContinueWatchAdWidget.hide();
      onCurrentVideoPlay();
    }

  }

}


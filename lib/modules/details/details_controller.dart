import 'dart:async';
import 'dart:ffi';
import 'dart:io';

import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:device_orientation/device_orientation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:playlet/api/details.dart';
import 'package:playlet/common/color/color_hex.dart';
import 'package:playlet/common/controller/bottom_nav_controller.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/landscape/screen_orientation.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/components/alert/remind.dart';
import 'package:playlet/components/details/drama_retention/drama_retention_controller.dart';
import 'package:playlet/components/details/drama_retention/drama_retention_popup.dart';
import 'package:playlet/components/details/episode_panel_widget.dart';
import 'package:playlet/components/details/limited_time_widget.dart';
import 'package:playlet/components/store/store_pop_widget.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/model/store/store_product_result.dart';
import 'package:playlet/model/store/unlock_store_product_result.dart';
import 'package:playlet/modules/details/detail_controller_advance_watch_action.dart';
import 'package:playlet/modules/details/detail_track_event.dart';
import 'package:playlet/modules/details/detail_view_model.dart';
import 'package:playlet/modules/home/<USER>/home_event.dart';
import 'package:playlet/modules/notification/notification_select_view.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/ad/ad_track_event.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/adtrigger/ad_trigger_eventcollector.dart';
import 'package:playlet/service/good_review_service.dart';
import 'package:playlet/service/mmkv_service.dart';
import 'package:playlet/service/payment/payment_events.dart';
import 'package:playlet/service/payment/payment_service.dart';
import 'package:playlet/service/player_service.dart';
import 'package:playlet/service/recently_watch_service.dart';
import 'package:playlet/service/recommend_service.dart';
import 'package:playlet/service/shorts_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/limited_time.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/time_interval_checker.dart';
import 'package:playlet/utils/track_event.dart';
import 'package:screen_capture_restrictions/screen_capture_restrictions.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../service/notificaiton_dialog_service.dart';
import '../../service/payment/payment_model.dart';

enum LoadingStatus {
  loading,
  success,
  failed,
}

class StartPayModel {
  SkuInfoResponses skuInfo;
  int index;
  String strSource;
  StartPayModel(this.skuInfo, this.index, this.strSource);
}

class DetailsController extends GetxController with WidgetsBindingObserver {
  String episodePanelId = "episode_panel_id";
  String episodePanelTag = "episode_panel_tag";
  String storePanelTag = "store_panel_tag";
  String limitedTimePanelTag = "limited_time_panel_tag";
  String episodeListId = "episode_list_id";

  // 是否已经退出了页面
  bool hasExitController = false;

  final BottomNavController bottomNavController =
      Get.find<BottomNavController>();

  final PaymentService paymentService = Get.find<PaymentService>();
  final UserService userService = Get.find<UserService>();
  final ShortsService shortsService = Get.find<ShortsService>();
  final PlayerService playerService = Get.find<PlayerService>();
  final RecommendService recommendService = Get.find<RecommendService>();
  final GoodReviewService goodReviewService = Get.find<GoodReviewService>();
  final NotificationLoginService notificationLoginService =
      Get.find<NotificationLoginService>();

  //剧集数据管理
  DetailViewModel viewModel = DetailViewModel();
  Rxn<ShortPlayDetail> shortPlayDetail = Rxn<ShortPlayDetail>();
  bool isForeground = true;

  bool needShowAdWhenPop = false;

  // RxBool isCollect = false.obs;

  int lockNum = 0;

  RxInt collectNum = 0.obs;

  late DetailsOptions detailsOptions;

  // 当前是否已经展示过广告挽留弹窗
  bool hasShowAdContinuePopWidget = false;

  final PageController pageViewController = PageController();

  // 加载状态
  late Rx<LoadingStatus> loadingStatus = LoadingStatus.loading.obs;

  late Worker routerEver;
  late Worker userEver;

  bool isSubscription = false;

  RxDouble playbackSpeed = 1.0.obs;

  bool isAppInactive = true;

  Throttle<StartPayModel?>? storeDebouncer;

  // 标记是否可切换横竖屏
  bool canToggleDirection = false;
  // 标记是否为横屏状态
  RxBool isLandscapeScreen = false.obs;
  bool _isChangingOrientation = false;
  bool _isFirstOrientationEvent = true;

  /// 用户手动暂停播放
  bool isUserPaused = false;

  // 标记是否为手动点击选集解锁
  bool isClickItemUnlock = false;
  Rx<Color> playerBottomWidgetBackgroundColor = Colors.transparent.obs;
  StreamSubscription<DeviceOrientation>? _orientationSubscriptionForAndroid;
  // 系统旋转开关状态
  final RxBool _systemRotationEnabledForAndroid = true.obs;
  bool _isInCriticalTimeRange = false;

  // 切换横竖屏模式
  Future<void> toggleLandscapeScreen() async {
    final playerKey = viewModel.currentDramaModel
        .episodeList[viewModel.currentDramaModel.currentIndex.value].playerKey;
    final playerWidgetState = playerKey.currentState;
    if (playerWidgetState != null) {
      _isInCriticalTimeRange = playerWidgetState.isInCriticalTimeRange;
    }

    if (_isChangingOrientation || _isInCriticalTimeRange) return;
    _isChangingOrientation = true;
    final isExistStorePanel = SmartDialog.checkExist(tag: storePanelTag);
    if (isExistStorePanel) {
      Get.dismiss();
      await SmartDialog.dismiss(tag: storePanelTag);
    }
    await SmartDialog.dismiss();
    if (isLandscapeScreen.value) {
      // 退出横屏
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      if (Platform.isIOS) {
        // 切换横屏模式后，需要重新设置屏幕方向
        await ScreenOrientation.setPortrait();
      }
      isLandscapeScreen.value = false;
    } else {
      // 进入横屏
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      if (Platform.isIOS) {
        // 切换横屏模式后，需要重新设置屏幕方向
        await ScreenOrientation.setLandscape();
      }
      isLandscapeScreen.value = true;
    }

    _isChangingOrientation = false;
  }

  void _initDeviceOrientationListenerForIOS() {
    if (!Platform.isIOS) {
      return;
    }
    const EventChannel("com.flareflow.ios/device_orientation_stream")
        .receiveBroadcastStream()
        .listen((orientation) {
      _onDeviceOrientationDidChanged(orientation == 3 || orientation == 4);
    }, onError: (error) {
      FFLog.error("iOS设备方向监听错误: $error");
    });
  }

  void _initRotationListenerForAndroid() async {
    if (!Platform.isAndroid) {
      return;
    }
    // 获取初始状态
    _systemRotationEnabledForAndroid.value =
        await _getSystemRotationStatusForAndroid();

    // 监听系统设置变化
    const EventChannel('system_rotation_channel')
        .receiveBroadcastStream()
        .listen((event) {
      _systemRotationEnabledForAndroid.value = event as bool;
    }, onError: (error) {
      FFLog.error("Rotation监听错误: $error");
    });
  }

  // 平台方法获取旋转状态
  Future<bool> _getSystemRotationStatusForAndroid() async {
    if (!Platform.isAndroid) {
      return false;
    }
    try {
      return await const MethodChannel('system_settings')
          .invokeMethod('getRotationStatus');
    } on PlatformException catch (e) {
      FFLog.error("获取旋转状态失败: ${e.message}");
      return true; // 默认开启
    }
  }

  // 重力感应监听
  void _initOrientationListenerForAndroid() {
    if (!Platform.isAndroid) {
      return;
    }
    _orientationSubscriptionForAndroid =
        deviceOrientation$.listen((orientation) async {
      // 过滤第一次竖屏的监听
      if (_isFirstOrientationEvent &&
              orientation == DeviceOrientation.portraitUp ||
          orientation == DeviceOrientation.portraitDown) {
        _isFirstOrientationEvent = false;
        return;
      }
      FFLog.info("系统旋转开关状态：${_systemRotationEnabledForAndroid.value}",
          tag: "OrientationTest");
      if (!_systemRotationEnabledForAndroid.value) return; // 系统旋转关闭时不处理
      final isLandscape = orientation == DeviceOrientation.landscapeLeft ||
          orientation == DeviceOrientation.landscapeRight;
      _onDeviceOrientationDidChanged(isLandscape);
    });
  }

  void _onDeviceOrientationDidChanged(bool isLandscape) {
    canToggleDirection =
        (viewModel.currentDramaModel.currentEpisode.value?.aspectRatio ?? 0) >=
            1;
    if (isLandscapeScreen.value) {
      canToggleDirection = true;
    }
    // 过滤初始状态不一致的情况
    if (canToggleDirection &&
        isLandscape != isLandscapeScreen.value &&
        !_isChangingOrientation &&
        Get.currentRoute == Routes.detailsPage) {
      toggleLandscapeScreen();
    }
  }

  @override
  void onInit() {
    FFLog.info("DetailsController -> onInit", tag: "DetailsController");
    Get.put(DramaRetentionController(), tag: DramaRetentionController.tag);
    isSubscription = userService.userInfo.value?.isSubscription ?? false;
    userEver = ever(userService.userInfo, userInfoCallback);
    WidgetsBinding.instance.addObserver(this);
    detailsOptions = Get.arguments;
    Get.log("DetailsOptions ${detailsOptions.toString()}");
    routerEver = ever(bottomNavController.currentRoute, routerCallback);
    viewModel.currentDramaModel.businessId = detailsOptions.businessId;

    /// 存储触发了归因剧或兜底剧进入
    if (isFromAttributionOrFallback()) {
      recommendService.onSaveDeepleepPlaylet();
    }

    if (detailsOptions.isSupportLandscapeMode) {
      isLandscapeScreen.value = true;
      // 先设置横屏方向
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      if (Platform.isIOS) {
        // 切换横屏模式后，需要重新设置屏幕方向
        ScreenOrientation.setLandscape();
      }
    }
    if (Platform.isAndroid) {
      _initRotationListenerForAndroid();
      // 设置支持横竖屏和重力感应
      _initOrientationListenerForAndroid();
    } else {
      _initDeviceOrientationListenerForIOS();
    }

    /// 添加防抖处理，避免频繁点击限时弹窗中的购买按钮
    /// 1800毫秒内只执行一次回调
    storeDebouncer = Throttle<StartPayModel?>(
      const Duration(milliseconds: 1800),
      initialValue: null,
      onChanged: (value) {
        if (value != null) {
          onStartPay(value);
        }
      },
    );
    // 设置iOS设备即使在静音模式下也能播放声音
    playerService.configureAudioSessionForSilentMode();

    initData();
    super.onInit();
    ScreenCaptureRestrictions.enableSecure();
    WakelockPlus.enable();
  }

  @override
  void onReady() {
    super.onReady();
    // 进入沉浸页就关闭首页最近观看
    _dismissHomeRecentlyWatchView();
    eventBus.fire(DetailsPageOpenedEvent(detailsController: this));
  }

  void userInfoCallback(UserResponse? value) async {
    if (value?.isSubscription != null &&
        value?.isSubscription != isSubscription) {
      isSubscription = value?.isSubscription ?? false;
      onRefresh();
    }
  }

  void onRefresh() {
    initData();
  }

  void onToInfo(DetailsOptions options) async {
    onAddWatchHistory(viewModel.currentDramaModel.currentIndex.value);
    detailsOptions = options;
    loadingStatus.value = LoadingStatus.loading;
    collectNum.value = 0;
    detailsOptions.playerEpisodeIndex = options.playerEpisodeIndex;
    detailsOptions.playerEpisodePosition = options.playerEpisodePosition;
    detailsOptions.videoDuration = options.videoDuration;
    viewModel.clearData();
    // playerService.clearPreloadList();
    viewModel.dramaDataList.add(DramaWithEpisodesModel());
    viewModel.currentDramaModel.businessId = detailsOptions.businessId;
    viewModel.currentDramaIndex.value = 0;
    initData();
  }

  void routerCallback(String value) {
    if (value == Routes.detailsPage) {
      onCurrentVideoPlay();
    } else {
      onCurrentVideoPause();
    }
  }

  bool getNoDialog() {
    return !SmartDialog.checkExist(tag: storePanelTag) &&
        !SmartDialog.checkExist(tag: limitedTimePanelTag) &&
        !SmartDialog.checkExist(tag: DramaRetentionPopup.tag);
  }

  @override
  void onClose() async {
    FFLog.info("DetailsController -> onClose", tag: "DetailsController");
    // 取消方向监听
    _orientationSubscriptionForAndroid?.cancel();

    // 离开该页面时，将屏幕方向恢复为竖屏
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    if (Platform.isIOS) {
      // 切换横屏模式后，需要重新设置屏幕方向
      ScreenOrientation.setPortrait();
    }
    userEver.dispose();
    routerEver.dispose();
    paymentService.refreshUserBalance();

    WidgetsBinding.instance.removeObserver(this);
    onCurrentVideoPause();
    ScreenCaptureRestrictions.disableSecure();
    WakelockPlus.disable();

    Get.delete(tag: DramaRetentionController.tag);

    // 退出沉浸页统计次数
    AdTriggerEventCollector.shared.collect(Event.closeEpisodeDetail);

    hasExitController = true; // 标记已经退出了页面

    eventBus.fire(DetailsPageClosedEvent(detailsController: this));

    // 退出沉浸页立即上报
    flushDataOnPageChange();

    super.onClose();
  }

  Future<void> initData() async {
    loadingStatus.value = LoadingStatus.loading;
    final result = await ApiDetails.getDetail(
      viewModel.currentDramaModel.businessId,
      null,
      onError: (result) {
        /// status code = 20012 剧集已下架
        /// status code = 20013 短剧已下架
        /// status code = 20014 短剧已下架
        if (result.status == 20012 ||
            result.status == 20013 ||
            result.status == 20014) {
          if (result.message != null || result.message!.isNotEmpty) {
            Get.toast(result.message!);
            Future.delayed(const Duration(seconds: 2), () {
              Get.back();
            });
          } else {
            Get.back();
          }
        }
      },
    );
    if (result != null) {
      final dramaRetentionController =
          Get.find<DramaRetentionController>(tag: DramaRetentionController.tag);
      dramaRetentionController.init(result.id!);
      viewModel.currentDramaModel.shortPlayDetail = result;
      shortPlayDetail.value = result;
      viewModel.currentDramaModel.businessId = result.id!;
      collectNum.value = result.collectNum ?? 0;
      trackEnterReelPlay();
      // isCollect.value = result.isCollect == 1 ? true : false;
      if (result.isCollect == 1) {
        shortsService.confirmCollection(result.shortPlayCode);
      }
      shortsService.setShortsCollectionNumberBy(
        result.shortPlayCode,
        result.collectNum,
      );

      final pageSize =
          viewModel.currentDramaModel.shortPlayDetail.updateEpisode ?? 0;
      final totalEpisodes =
          viewModel.currentDramaModel.shortPlayDetail.totalEpisodes ?? 0;
      final dramaList = await getDramaList(
        viewModel.currentDramaModel.businessId,
        pageSize,
      );

      if (dramaList?.isEmpty == false) {
        // 进行剧集分
        groupedEpisodePanelTabList(dramaList!, pageSize, totalEpisodes);

        // 初始值
        viewModel.currentDramaModel.currentIndex.value =
            detailsOptions.playerEpisodeIndex;
        // 获取已解锁剧集详情
        List<EpisodeVideoData>? list =
            await getHasUnlockedEpisodesDetails(dramaList);
        if (list != null) {
          onSetEpisode(list);
          final currentEpisode = _getEpisodeDataSafely(
              viewModel.currentDramaModel.currentIndex.value);
          if (currentEpisode != null) {
            viewModel.currentDramaModel.currentEpisode.value = currentEpisode;
          }
        }
        onStartOpenEpisodePanel();
      }

      // 更新剧集面板
      update([episodePanelId]);
      loadingStatus.value = LoadingStatus.success;
      _handleTrackReelRequestClick(result, 'success');
    } else {
      loadingStatus.value = LoadingStatus.failed;
      _handleTrackReelRequestClick(null, 'fail');
      // 如果是归因剧，打获取失败埋点
      if (detailsOptions.from == EventValue.campaign) {
        useTrackEvent(EventName.drama_fetch_detail_failed, extra: {
          EventKey.errorCode: "1",
          EventKey.errorMessage: "获取剧集详情失败",
          EventKey.reelId: viewModel
                  .currentDramaModel.shortPlayDetail.shortPlayCode
                  .toString() ??
              "-1",
          EventKey.scene: EventValue.immersion
        });
      }
    }
  }

  void trackEnterReelPlay() {
    bool firstDaram = SafeStorage().read<bool>('firstDaram') ?? true;
    Map<String, String> extra = {
      TrackEvent.from: detailsOptions.from,
      TrackEvent.reel_id:
          viewModel.currentDramaModel.shortPlayDetail.shortPlayCode == null
              ? ''
              : viewModel.currentDramaModel.shortPlayDetail.shortPlayCode
                  .toString(),
      TrackEvent.module_name: detailsOptions.moduleName,
      TrackEvent.module_id: detailsOptions.moduleId,
    };
    final moduleStyle = detailsOptions.moduleStyle?.value;
    if (moduleStyle != null) {
      extra[TrackEvent.module_type] = moduleStyle.toString();
    }
    extra[EventKey.playDirection] = ScreenUtils.isLandscape(Get.context!)
        ? EventValue.horizontal
        : EventValue.vertical;
    extra[EventKey.firstDrama] = firstDaram == true ? 'yes' : 'no';
    if (detailsOptions.resourceBitId != null) {
      extra[EventKey.resourceBitId] = detailsOptions.resourceBitId!;
    }
    useTrackEvent(TrackEvent.enter_reel_play, extra: extra);
    SafeStorage().write('firstDaram', false);
  }

  void onConfirmCollect() {
    shortsService.confirmCollection(
        viewModel.currentDramaModel.shortPlayDetail.shortPlayCode);
    shortsService.increaseShortsCollectionNumber(
        viewModel.currentDramaModel.shortPlayDetail.shortPlayCode);
  }

  void onCancelCollect() {
    shortsService.cancelCollection(
        viewModel.currentDramaModel.shortPlayDetail.shortPlayCode);
    shortsService.decreaseShortsCollectionNumber(
        viewModel.currentDramaModel.shortPlayDetail.shortPlayCode);
  }

  // 安全获取剧集信息
  EpisodeVideoData? _getEpisodeDataSafely(int index) {
    if (index < 0 || index >= viewModel.currentDramaModel.episodeList.length) {
      FFLog.info(
          'DetailsController._getEpisodeDataSafely -> Index out of bounds, index=$index, listLength=${viewModel.currentDramaModel.episodeList.length}');
      return null;
    }
    return viewModel.currentDramaModel.episodeList[index].info;
  }

  // 请求剧埋点
  void _handleTrackReelRequestClick(ShortPlayDetail? result, String status) {
    final currentEpisode =
        _getEpisodeDataSafely(viewModel.currentDramaModel.currentIndex.value);

    DetailTrackEvent.reelRequest(
      status: status,
      shortPlayCode: result?.shortPlayCode,
      episode: currentEpisode?.episodeNum,
      scene: detailsOptions.scene,
      resourceBitId: detailsOptions.resourceBitId,
    );
  }

  Future<void> onPlayStatusChanged(bool isPlaying) async {
    // 通过EventBus通知外部播放器状态变化
    eventBus.fire(PlayerStatusChangedEvent(
        detailsController: this, isPlaying: isPlaying));

    if (isPlaying) {
      /// 保存最近观看
      await _saveRecentlyWatchData();
    }
  }

  /// 播放结束
  void onEnded() {
    if (viewModel.currentDramaModel.currentIndex.value <
        viewModel.currentDramaModel.episodeList.length) {
      final episode = viewModel.currentDramaModel
          .episodeList[viewModel.currentDramaModel.currentIndex.value];
      _resetPlayDurationToLocal(episode);
    }
    onNext();
  }

  /// 下一集
  void onNext() {
    try {
      pageViewController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } catch (e) {
      FFLog.error(
          "nextPage error episodeList.length ${viewModel.currentDramaModel.episodeList.length} currentIndex ${viewModel.currentDramaModel.currentIndex.value} $e");
    }
  }

  void onLastOneShow() {
    if (getNoDialog()) {
      viewModel.currentDramaModel.isLastOneView.value = true;
    }
  }

  void jumpToPage(int index) {
    try {
      pageViewController.jumpToPage(index);
    } catch (e) {
      FFLog.error(
          "jumpToPage error episodeList.length ${viewModel.currentDramaModel.episodeList.length} toIndex $index $e");
    }
  }

  bool isToErrToast = false;

  /// 判断是否来自归因剧或兜底剧
  /// 输入是 detailsOptions
  /// 返回 true 表示来自归因剧或兜底剧，false 表示来自其他来源
  bool isFromAttributionOrFallback() {
    // 检查 from 参数是否为归因剧或兜底剧
    return detailsOptions.from == EventValue.campaign ||
        detailsOptions.from == EventValue.defaultValue;
  }

  Future<void> onTo(int index) async {
    //可以直接播放的就播放
    if (index - viewModel.currentDramaModel.startDataIndex <
        viewModel.currentDramaModel.episodeList.length - 1) {
      onCurrentVideoPause();
      jumpToPage(index);
      onCurrentVideoPlay();
      await onEpisodePanelClose();
      return;
    }
    if (index - viewModel.currentDramaModel.startDataIndex ==
        viewModel.currentDramaModel.episodeList.length - 1) {
      jumpToPage(index);
      await onEpisodePanelClose();
      onCurrentVideoPlay();
      return;
    }
    if (index - viewModel.currentDramaModel.startDataIndex >=
        (viewModel.currentDramaModel.shortPlayDetail.updateEpisode ?? 0)) {
      if (isToErrToast) return;
      isToErrToast = true;
      Future.delayed(const Duration(milliseconds: 1800), () {
        isToErrToast = false;
      });
      FFLog.debug("不能解锁未发布的");
      Get.toast(AppTrans.unableUnlockPublished());
      return;
    }
    // 判断是否跨剧集解锁
    if (index - viewModel.currentDramaModel.startDataIndex >
        viewModel.currentDramaModel.episodeList.length - 1) {
      if (isToErrToast) return;
      isToErrToast = true;
      Future.delayed(const Duration(milliseconds: 1800), () {
        isToErrToast = false;
      });
      Get.toast(AppTrans.unableUnlockEpisodes());
      FFLog.debug("不能跨剧集解锁");
      return;
    }
  }

  bool isUnlock = false;

  ///
  /// 这边处理提前看广告的内容
  /// 条件1 广告用户
  /// 条件2 没有展示过提前看广告的弹窗
  /// 条件3 可以观看广告次数大于0
  /// 条件4 是否还在沉浸页
  /// 条件5 该集不是金币解锁
  ///
  bool canShowAdvanceWatchAdPopWidget(
      EpisodeVideoData? episode, UnlockAdCountData? unlockAdCountData) {
    EpisodeVideoData? nextDrame = episode;
    bool canAdvanceWatchAd = (unlockAdCountData?.canWatchAdNum ?? 0) > 0 &&
        hasShowAdContinuePopWidget == false &&
        (nextDrame != null && nextDrame.unlockType != 1) &&
        AdTriggerEventCollector.shared.canTrigger;
    if (canAdvanceWatchAd && (hasExitController == false)) {
      if (SmartDialog.checkExist(tag: DramaRetentionPopup.tag)) {
        return false;
      }
      return true;
    }
    return false;
  }

  // 显示提前看广告的弹窗
  void showAdvanceWatchAdPopWidget(
      SkuInfoResponses? skuInfo,
      int index,
      bool isPaySuccess,
      bool isWatchedAd,
      EpisodeVideoData episode,
      UnlockAdCountData? unlockAdCountData) async {
    bool canAdvanceWatchAd =
        canShowAdvanceWatchAdPopWidget(episode, unlockAdCountData);
    if (canAdvanceWatchAd) {
      FFLog.debug("提前看广告");
      EpisodeVideoData nextDrame = episode;
      bool action =
          await advanceWatch(nextDrame, isWatchedAd, unlockAdCountData);
      hasShowAdContinuePopWidget = true;
      if (action == false) {
        // 这边要去弹那些弹窗了膨胀挽留/资产提醒
        if (isPaySuccess == true || isWatchedAd == true) {
          await onTo(index + viewModel.currentDramaModel.startDataIndex);
          //资产保护提醒弹窗
          _handleProtectProperityRemindAlert();
        } else {
          if (skuInfo != null &&
              (isPaySuccess == false && isWatchedAd == false)) {
            onLimitedTimePanelShow(skuInfo, index);
          } else {
            onCurrentVideoPlay();
          }
        }
      }
    }
  }

  /// 金币解锁
  Future<void> onUnLock(int index, {bool isClickButton = false}) async {
    // 不符合解锁逻辑
    bool isLock = getNotAllow(index);
    if (isLock == false) {
      return;
    }
    if (isUnlock) {
      return;
    }
    isUnlock = true;
    Get.loading();
    final toIndex = index;
    final episode = viewModel.currentDramaModel.episodeTabs[toIndex].info;
    final result = await ApiDetails.unlockByCoin(episode.id!);
    if (result != null) {
      useTrackEvent(TrackEvent.episodeUnlockSuccess, extra: {
        EventKey.reelId: viewModel
            .currentDramaModel.shortPlayDetail.shortPlayCode
            .toString(),
        EventKey.episode: episode.episodeNum.toString(),
        EventKey.isEpEnd: (episode.episodeNum ==
                viewModel.currentDramaModel.shortPlayDetail.totalEpisodes)
            ? "1"
            : "0",
        "coins": (result.useCoins ?? 0).toString(),
        "bonus": (result.useBonus ?? 0).toString(),
        "price": (episode.price ?? 0).toString(),
        "is_auto": isClickButton ? "1" : "0",
        "account_coins": (result.coins ?? 0).toString(),
        "account_tickets": (result.bonus ?? 0).toString(),
        TrackEvent.lock_begin:
            viewModel.currentDramaModel.shortPlayDetail.lockBegin.toString(),
      });
      if (toIndex >=
          (viewModel.currentDramaModel.shortPlayDetail.updateEpisode ?? 0) -
              1) {
        viewModel.currentDramaModel.isUnlockEpisodId.value = false;
      }
      lockNum = lockNum + 1;
      paymentService.refreshUserBalance();
      if (viewModel.currentDramaModel.episodeList.length <=
          (viewModel.currentDramaModel.shortPlayDetail.updateEpisode ?? 0)) {
        // 修改已经解锁的剧集的属性
        for (var element in viewModel.currentDramaModel.episodeList) {
          element.info.alreadyLock = 1;
        }
        if (result.unlockDramas != null) {
          onAddEpisode(result.unlockDramas!);
        }
      }

      //判断是否已经到了最后一集,标志全部解锁
      setAllUnlockValue(toIndex);

      updateEpioode(toIndex);
      // 跳转下一集
      await onEpisodePanelClose();
      Get.log('解锁逻辑 余额解锁 onUnLock $toIndex');
      await onTo(toIndex + viewModel.currentDramaModel.startDataIndex);
      Get.dismiss();
      isUnlock = false;
      // 解锁成功重置点击解锁标志位
      isClickItemUnlock = false;
      // 添加解锁提示
      Get.toast(AppTrans.unLockVideoTip(episode.episodeNum));
    } else if (result != null && result.unlockDramas != null) {
      isUnlock = false;
      Get.log("提交限流");
      Get.dismiss();
    } else {
      isUnlock = false;

      UnlockAdCountData? unlockAdCountData = await ApiDetails.unlockAdCountData(
        viewModel.currentDramaModel.businessId,
        scene: 'collections',
      );

      String action = isClickButton
          ? EventValue.pageUnlocked
          : isClickItemUnlock
              ? EventValue.shortMMenu
              : EventValue.auto;
      await onStorePanelShow(index,
          unlockAdCount: unlockAdCountData, action: action);
    }
  }

  /// 观看广告解锁
  Future<UnlockWatchAdInfoResponse?> onUnLockByWatchAd() async {
    Get.loading();
    final toIndex = viewModel.currentDramaModel.currentIndex.value;
    final episode = viewModel.currentDramaModel.episodeTabs[toIndex].info;
    final result = await ApiDetails.unlockByWatchAd(episode.id!);
    if (result != null) {
      useTrackEvent(TrackEvent.episodeUnlockAdSuccess, extra: {
        EventKey.reelId: episode.shortPlayCode.toString(),
        EventKey.episode: episode.episodeNum.toString(),
        EventKey.isEpEnd: (episode.episodeNum ==
                viewModel.currentDramaModel.shortPlayDetail.totalEpisodes)
            ? "1"
            : "0",
        EventKey.from: EventValue.adUnlockFrom,
        EventKey.lockBegin:
            viewModel.currentDramaModel.shortPlayDetail.lockBegin.toString()
      });

      lockNum = lockNum + 1;
      paymentService.refreshUserBalance();
      if (viewModel.currentDramaModel.episodeList.length <=
          (viewModel.currentDramaModel.shortPlayDetail.updateEpisode ?? 0)) {
        // 修改已经解锁的剧集的属性
        for (var element in viewModel.currentDramaModel.episodeList) {
          element.info.alreadyLock = 1;
        }
        if (result.unlockDramas != null) {
          onAddEpisode(result.unlockDramas!);
        }
      }
      // 判断设置是全部解锁
      setAllUnlockValue(toIndex);

      updateEpioode(toIndex);
      Get.dismiss();
      // 解锁成功重置点击解锁标志位
      isClickItemUnlock = false;

      return result;
    } else {
      Get.dismiss();
      FFLog.debug('广告解锁失败');
      return null;
    }
  }

  // 设置全部解锁标志
  void setAllUnlockValue(int toIndex) {
    // 判断是否已经全部解锁
    final index =
        viewModel.currentDramaModel.episodeTabs.lastIndexWhere((element) {
      return element.isDisable == false;
    });
    if (index != -1) {
      // 判断是否是最后一集
      if (toIndex == index &&
          (viewModel.currentDramaModel.episodeList.last.info.alreadyLock == 1 ||
              viewModel.currentDramaModel.episodeList.last.info.lock == 2)) {
        viewModel.currentDramaModel.allLock.value = true;
      }
    }
  }

  ///刚进入时是否需要打开剧集弹窗
  ///需要在详情页接口初始化完成调用
  void onStartOpenEpisodePanel() {
    if (Get.currentRoute == Routes.detailsPage) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (SmartDialog.checkExist(tag: DramaRetentionPopup.tag)) return;
        if (detailsOptions.openEpisodePanel == true) {
          onEpisodePanelShow();
          detailsOptions.openEpisodePanel = false;
        }
      });
    }
  }

  void updateEpioode(int index) {
    for (int i = 0; i < viewModel.currentDramaModel.episodeTabs.length; i++) {
      if (i <= index) {
        viewModel.currentDramaModel.episodeTabs[i].info.alreadyLock = 1;
      }
    }
    Get.log(
        'updateEpioode $index ${viewModel.currentDramaModel.episodeTabs[index].info.alreadyLock}');
    viewModel.currentDramaModel.episodeTabList =
        transformArrayToStructuredFormat(
            viewModel.currentDramaModel.episodeTabs, 30);
    update([episodePanelId]);
  }

  Future<void> onStorePanelShow(int index,
      {UnlockAdCountData? unlockAdCount,
      String action = EventValue.auto}) async {
    if (SmartDialog.checkExist(tag: storePanelTag)) return;
    final toIndex = index;
    viewModel.currentDramaModel.isLastOneView.value = false;
    //这边增加一个打点adplacementshow
    final episodeData = _getEpisodeDataSafely(toIndex);
    final episodeNum = episodeData?.episodeNum;
    AdTrackEvent.adPlacementShow(AdType.reward, AdScene.adsUnlock, param: {
      TrackEvent.reel_id:
          viewModel.currentDramaModel.shortPlayDetail.shortPlayCode == null
              ? ''
              : viewModel.currentDramaModel.shortPlayDetail.shortPlayCode
                  .toString(),
      TrackEvent.episode: episodeNum.toString(),
    });
    SkuInfoResponses? skuInfo;
    bool isPaySuccess = false;
    bool isWatchedAdSuccess = false;
    UnlockWatchAdInfoResponse? unlockWatchAdInfoResponse;
    onCurrentVideoPause();
    var unlockAdCountData = unlockAdCount;
    unlockAdCountData ??= await ApiDetails.unlockAdCountData(
      viewModel.currentDramaModel.businessId,
      scene: 'collections',
    );
    bool showAd =
        viewModel.currentDramaModel.currentEpisode.value?.unlockType == 2 ||
            viewModel.currentDramaModel.currentEpisode.value?.unlockType == 3;
    if (showAd == true) {
      showAd = unlockAdCountData?.canWatchAdNum != null &&
          unlockAdCountData!.canWatchAdNum! > 0;
    }
    final episode = episodeData; // 复用之前获取的 episodeData
    final isLandscape = ScreenUtils.isLandscape(Get.context!);
    await SmartDialog.show(
      alignment: isLandscape ? Alignment.bottomRight : Alignment.bottomCenter,
      tag: storePanelTag,
      keepSingle: true,
      builder: (context) {
        // 在builder中获取方向并调整位置
        return Align(
          alignment:
              isLandscape ? Alignment.bottomRight : Alignment.bottomCenter,
          child: StorePopWidget(
            detailsController: this,
            businessId: viewModel.currentDramaModel.businessId,
            episode:
                viewModel.currentDramaModel.currentEpisode.value?.episodeNum ??
                    0,
            dramaId:
                viewModel.currentDramaModel.episodeTabs[toIndex].info.id ?? -1,
            showAdItem: showAd,
            episodeCoins: episode?.price?.toString() ?? "0",
            lockBegin: viewModel.currentDramaModel.shortPlayDetail.lockBegin
                .toString(),
            shortPlayId: episode?.shortPlayId ?? -1,
            shortPlayCode:
                viewModel.currentDramaModel.shortPlayDetail.shortPlayCode ?? -1,
            action: action,
            onClose: () {
              onStorePanelClose(
                  isPaySuccess: isPaySuccess,
                  isWatchedAdSuccess: isWatchedAdSuccess,
                  unlockAdCount: unlockAdCountData,
                  index: index,
                  skuInfo: skuInfo);
            },
            onPlaySuccess: () async {
              ///  支付成功 不显示挽留弹框
              isPaySuccess = true;

              /// 存储触发了归因剧或兜底剧支付
              if (isFromAttributionOrFallback()) {
                recommendService.onSavePaySuccess();
              }
              await onStorePanelClose(
                  isPaySuccess: isPaySuccess,
                  isWatchedAdSuccess: isWatchedAdSuccess,
                  unlockAdCount: unlockAdCountData,
                  index: index,
                  skuInfo: skuInfo);
              await onUnLock(index);
            },
            onSubscriptionSuccess: () async {
              ///  订阅成功 不显示挽留弹框
              isPaySuccess = true;

              /// 存储触发了归因剧或兜底剧支付
              if (isFromAttributionOrFallback()) {
                recommendService.onSavePaySuccess();
              }
              // 订阅成功，关闭弹窗，剧集全免由用户数据变更来刷新
              onStorePanelClose(
                  isPaySuccess: isPaySuccess,
                  isWatchedAdSuccess: isWatchedAdSuccess,
                  unlockAdCount: unlockAdCountData,
                  index: index,
                  skuInfo: skuInfo);
            },
            onRetainSkuInfoResponses: (value) {
              skuInfo = value;
            },
            onAdItemTap: () async {
              // 这里请求广告
              // success,代表看完了广告,得到了奖励,允许去调用观看完接口,然后去解锁
              AdContext context = AdContext();
              context.eventParam = {
                TrackEvent.reel_id: viewModel
                            .currentDramaModel.shortPlayDetail.shortPlayCode ==
                        null
                    ? ''
                    : viewModel.currentDramaModel.shortPlayDetail.shortPlayCode
                            .toString() ??
                        "0",
                TrackEvent.episode: viewModel
                        .currentDramaModel.currentEpisode.value?.episodeNum
                        .toString() ??
                    "0",
                TrackEvent.lock_begin: viewModel
                        .currentDramaModel.shortPlayDetail.lockBegin
                        .toString() ??
                    "0",
              };
              bool isSuccess = await AdManager()
                  .showFullScreenAd(AdScene.adsUnlock, context: context);
              if (isSuccess) {
                // 去调用观看完视频的接口,然后刷新当前剧集,并提示解锁当前剧集
                Get.log('解锁逻辑 onUnLockByWatchAd $toIndex');
                final watchAdResult = await onUnLockByWatchAd();
                if (watchAdResult != null) {
                  unlockWatchAdInfoResponse = watchAdResult;
                  // 解锁成功才能关闭付费卡点弹窗
                  isWatchedAdSuccess = true;
                  await onStorePanelClose(
                      isPaySuccess: isPaySuccess,
                      isWatchedAdSuccess: isWatchedAdSuccess,
                      unlockAdCount: unlockAdCountData,
                      index: index,
                      unlockWatchAdInfoResponse: unlockWatchAdInfoResponse,
                      skuInfo: skuInfo);
                  Get.toast(AppTrans.unLockVideoTip(episode?.episodeNum ?? 0));
                }
              }
            },
          ),
        );
      },
    );
  }

  Future<void> onStartPay(StartPayModel options) async {
    Get.loading();
    final skuId =
        Platform.isAndroid ? options.skuInfo.gpSkuId : options.skuInfo.iosSkuId;
    final productId = options.skuInfo.skuProductId;
    final skuModelConfigId = options.skuInfo.skuModelConfigId;
    final skuType = options.skuInfo.skuType;

    String bonus = '';
    if (options.skuInfo.skuType == 7) {
      bonus = options.skuInfo.keepGiveCoins == null
          ? ''
          : options.skuInfo.keepGiveCoins.toString();
    } else {
      bonus = options.skuInfo.productGiveCoins.toString();
    }
    final result = await paymentService.startPurchase(
      skuId: skuId,
      productId: productId,
      // source: detailsOptions.scene,
      source: SourceType.unlockDialogPage,
      shortPlayId: viewModel.currentDramaModel.shortPlayDetail.shortPlayId ??
          viewModel.currentDramaModel.shortPlayDetail.id ??
          -1,
      episode: viewModel.currentDramaModel.currentEpisode.value?.episodeNum,
      shortPlayCode:
          viewModel.currentDramaModel.shortPlayDetail.shortPlayCode ?? -1,
      skuModelConfigId: skuModelConfigId,
      skuType: skuType,
      isRetain: options.skuInfo.skuType == 7,
      strSource: options.strSource,
      amount: options.skuInfo.recharge,
      coins: options.skuInfo.coins.toString(),
      bonus: bonus,
      lockBegin:
          viewModel.currentDramaModel.shortPlayDetail.lockBegin.toString(),
      resourceBitId: detailsOptions.resourceBitId,
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );
    Get.dismiss();
    if (result) {
      LimitedTime.closeThatDayShow();
      await onUnLock(options.index);
      onLimitedTimePanelClose();
    }
  }

  Future<void> onStorePanelClose(
      {required bool isPaySuccess,
      required bool isWatchedAdSuccess,
      required UnlockAdCountData? unlockAdCount,
      required int index,
      UnlockWatchAdInfoResponse? unlockWatchAdInfoResponse,
      SkuInfoResponses? skuInfo}) async {
    await SmartDialog.dismiss(tag: storePanelTag);

    //warning: 这边理论上不应该出现这个方法处理,关闭就只做关闭的内容,后面再优化
    _dealDismissAction(isPaySuccess, isWatchedAdSuccess, index,
        unlockWatchAdInfoResponse, unlockAdCount, skuInfo);
  }

  void _dealDismissAction(
      bool isPaySuccess,
      bool isWatchedAdSuccess,
      int index,
      UnlockWatchAdInfoResponse? unlockWatchAdInfoResponse,
      UnlockAdCountData? unlockAdCount,
      SkuInfoResponses? skuInfo) async {
    isClickItemUnlock = false;
    onLastOneShow();
    if (isPaySuccess == false) {
      // 如果没有付钱关闭的,那么就是付费弹窗关闭的条件要加上
      AdTriggerEventCollector.shared.collect(Event.closePaySceneWithoutPay);
    }
    EpisodeVideoData? currentEpisode = _getEpisodeDataSafely(index);
    var unlockAdCountData = unlockAdCount;
    // 广告解锁成功，判断是否可以显示广告解锁弹窗
    bool canAdvanceWatchAd = false;
    if (isWatchedAdSuccess) {
      final unlockWatchAdInfoResponse0 = unlockWatchAdInfoResponse;
      if (unlockWatchAdInfoResponse0 != null &&
          unlockWatchAdInfoResponse0.nextDrama != null) {
        currentEpisode = unlockWatchAdInfoResponse0.nextDrama;
        unlockAdCountData = UnlockAdCountData(
            canWatchAdNum: unlockWatchAdInfoResponse0.canWatchAdNum,
            totalWatchAdNum: unlockWatchAdInfoResponse0.totalWatchAdNum);
        canAdvanceWatchAd =
            canShowAdvanceWatchAdPopWidget(currentEpisode, unlockAdCountData);
      }
    } else {
      canAdvanceWatchAd =
          canShowAdvanceWatchAdPopWidget(currentEpisode, unlockAdCountData);
    }
    if (canAdvanceWatchAd && isPaySuccess == false) {
      // 这边要判断是否出现连续看广告
      if (currentEpisode != null) {
        showAdvanceWatchAdPopWidget(skuInfo, index, isPaySuccess,
            isWatchedAdSuccess, currentEpisode, unlockAdCountData);
      }
    } else {
      if (isPaySuccess == true || isWatchedAdSuccess == true) {
        await onTo(index + viewModel.currentDramaModel.startDataIndex);
        //资产保护提醒弹窗处理
        _handleProtectProperityRemindAlert();
      } else {
        if (skuInfo != null &&
            (isPaySuccess == false && isWatchedAdSuccess == false)) {
          onLimitedTimePanelShow(skuInfo, index);
        } else {
          onCurrentVideoPlay();
        }
      }
    }
  }

  Future<void> _handleProtectProperityRemindAlert() async {
    UserService userService = Get.find<UserService>();
    String? oldUserId = userService.userInfo.value?.userId;
    //资产保护提醒弹窗
    if (checkShouldShowProtectProperityRemindAlert() == true) {
      onCurrentVideoPause();
      bool? result = await showProtectProperityRemindAlert(inDetails: true);
      /// 如果返回了true，即为点击了登录按钮
      if (result != null && result == true) {
        DetailsController detailsController =
            Get.find<DetailsController>(tag: "details_controller");
        if (ScreenUtils.isLandscape(Get.context!)) {
          await detailsController.toggleLandscapeScreen();
        }
        /// 弹出底部登录弹窗
        await Get.loginDialog(
          from: EventValue.assetProtection,
          fbRewardsTrackFrom: EventValue.assetProtection,
        );

        /// 根据与产品沟通，当前登录账户id与之前的游客id不一致时，即视为登录的账户之前绑定了游客id，这时需要退出沉浸页
        String? newUserId = userService.userInfo.value?.userId;
        if (oldUserId != null && newUserId != null && oldUserId == newUserId) {
          Get.back();
        } else {
          onCurrentVideoPlay();
        }
      } else {
        onCurrentVideoPlay();
      }
    }
  }

  // 限时商品
  Future<void> onLimitedTimePanelShow(
      SkuInfoResponses skuInfo, int index) async {
    final isShow = LimitedTime.getLimitedTimePopupShow();
    if (!isShow) return;
    bool isPay = false;
    String skuId = Platform.isAndroid ? skuInfo.gpSkuId : skuInfo.iosSkuId;
    bool canShow = LimitedTime.setFirstPopup(
        skuInfo.retentionSeconds, skuId, skuInfo.skuProductId);
    if (canShow == false) {
      if (isPay == false) {
        onCurrentVideoPlay();
        AdTriggerEventCollector.shared.collect(Event.closePaySceneWithoutPay);
      }
      return;
    }

    // 添加recharge show 埋点
    PaymentEvent.submitRechargeShow(
      reelId: viewModel.currentDramaModel.shortPlayDetail.shortPlayCode == null
          ? ''
          : viewModel.currentDramaModel.shortPlayDetail.shortPlayCode
              .toString(),
      episode: viewModel.currentDramaModel.currentEpisode.value?.episodeNum
              .toString() ??
          "0",
      action: "auto",
      lockBegin:
          viewModel.currentDramaModel.shortPlayDetail.lockBegin.toString(),
      strScene: 'pay_retain',
      resourceBitId: detailsOptions.resourceBitId,
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );
    await SmartDialog.show(
      alignment: Alignment.center,
      tag: limitedTimePanelTag,
      keepSingle: true,
      builder: (context) {
        // 获取当前屏幕方向
        final isLandscape = ScreenUtils.isLandscape(context);
        return Align(
          // 竖屏居中，横屏顶部水平居中
          alignment: isLandscape ? Alignment.topCenter : Alignment.center,
          child: LimitedTimeWidget(
            skuInfo: skuInfo,
            detailsController: this,
            onStartPay: () {
              isPay = true;
              storeDebouncer
                  ?.setValue(StartPayModel(skuInfo, index, "pay_retain"));
            },
          ),
        );
      },
      onDismiss: () {
        onLastOneShow();
        PaymentEvent.submitRechargeShowEnd(
          reelId:
              viewModel.currentDramaModel.shortPlayDetail.shortPlayCode == null
                  ? ''
                  : viewModel.currentDramaModel.shortPlayDetail.shortPlayCode
                      .toString(),
          episode: viewModel.currentDramaModel.currentEpisode.value?.episodeNum
                  .toString() ??
              "0",
          action: "auto",
          lockBegin:
              viewModel.currentDramaModel.shortPlayDetail.lockBegin.toString(),
          strScene: 'pay_retain',
          resourceBitId: detailsOptions.resourceBitId,
          playDirection: ScreenUtils.isLandscape(Get.context!)
              ? EventValue.horizontal
              : EventValue.vertical,
        );
        if (isPay == false) {
          onCurrentVideoPlay();
          AdTriggerEventCollector.shared.collect(Event.closePaySceneWithoutPay);
        }
      },
    );
  }

  Future<void> onLimitedTimePanelShowClose() async {
    SmartDialog.dismiss(tag: limitedTimePanelTag);
  }

  Future<void> onLimitedTimePanelClose() async {
    await SmartDialog.dismiss(tag: limitedTimePanelTag);
  }

  Future<void> onCurrentVideoPlay() async {
    if (viewModel.currentDramaModel.episodeList.isEmpty) return;
    if (viewModel.currentDramaModel.currentIndex.value >=
        viewModel.currentDramaModel.episodeList.length) return;
    if (viewModel.currentDramaModel.currentEpisode.value?.alreadyLock == 1 ||
        viewModel.currentDramaModel.currentEpisode.value?.lock == 2) {
      final playerKey = viewModel
          .currentDramaModel
          .episodeList[viewModel.currentDramaModel.currentIndex.value]
          .playerKey;
      final playerWidgetState = playerKey.currentState;
      if (playerWidgetState != null) {
        await playerWidgetState.onPlay();
        await onPlayerEpisodePosition();
      }
    }
  }

  Future<void> onCurrentVideoSeek(Duration position) async {
    if (viewModel.currentDramaModel.episodeList.isEmpty) return;
    if (viewModel.currentDramaModel.currentIndex.value >=
        viewModel.currentDramaModel.episodeList.length) return;
    final playerKey = viewModel.currentDramaModel
        .episodeList[viewModel.currentDramaModel.currentIndex.value].playerKey;
    final playerWidgetState = playerKey.currentState;
    if (playerWidgetState != null) {
      await playerWidgetState.onSeekTo(position);
    }
  }

  Future<void> onCurrentVideoPause() async {
    if (viewModel.currentDramaModel.episodeList.isEmpty) return;
    if (viewModel.currentDramaModel.currentIndex.value >=
        viewModel.currentDramaModel.episodeList.length) return;
    final playerKey = viewModel.currentDramaModel
        .episodeList[viewModel.currentDramaModel.currentIndex.value].playerKey;
    final playerWidgetState = playerKey.currentState;
    if (playerWidgetState != null) {
      await playerWidgetState.onPause();
    }
  }

  void onPageViewChanged(int index, {bool isScrollingDown = true}) async {
    FFLog.info(
        'onPageViewChanged index: $index, isScrollingDown: $isScrollingDown');
    // 将isShowingNextPlayToast置为false
    viewModel.isShowingNextPlayToast = false;
    if (viewModel.nextDrameToast != null) {
      Get.toastDismiss(item: viewModel.nextDrameToast);
    }

    try {
      // 先暂停上一次的内容
      onCurrentVideoPause();
      FFLog.info(
          'onPageViewChanged 暂停上一次的内容 ${viewModel.currentDramaModel.currentIndex.value}, index: $index ');
      onAddWatchHistory(viewModel.currentDramaModel.currentIndex.value);
    } catch (e) {
      Get.log("DetailsControllerEvent onPageViewChanged $e");
    }

    final lastDataIndex = viewModel.currentDramaModel.lastDataIndex;
    FFLog.info('onPageViewChanged lastDataIndex $lastDataIndex');

    // 滑到下一集
    if (isScrollingDown) {
      FFLog.info("onPageViewChanged开始划到下一集");
      FFLog.info("onPageViewChanged看看是否是下一部剧的第一集");
      if (index - lastDataIndex == 1) {
        FFLog.info("onPageViewChanged是下一部剧的第一集,去更新面板,切换剧的index到下一部剧");
        // 下一部剧的一集，需要切换到下一部剧
        viewModel.switchToNextDrama();
        shortPlayDetail.value = viewModel.currentDramaModel.shortPlayDetail;

        // 请求退出挽留的剧
        _requestDramaRetainData(shortPlayDetail.value);

        viewModel.currentDramaModel.currentIndex.value = 0;
        viewModel.currentDramaModel.currentEpisode.value =
            viewModel.currentDramaModel.episodeList.first.info;
        FFLog.debug(
            'onPageViewChanged下一部剧的内容currentDramaindex = ${viewModel.currentDramaIndex}, shortPlayDetail = $shortPlayDetail, currentIndex = ${viewModel.currentDramaModel.currentIndex.value}, currentEpisode = ${viewModel.currentDramaModel.currentEpisode.value}');

        collectNum.value =
            viewModel.currentDramaModel.shortPlayDetail.collectNum ?? 0;
        trackEnterReelPlay();
        // isCollect.value = result.isCollect == 1 ? true : false;
        if (viewModel.currentDramaModel.shortPlayDetail.isCollect == 1) {
          shortsService.confirmCollection(
              viewModel.currentDramaModel.shortPlayDetail.shortPlayCode);
        }
        shortsService.setShortsCollectionNumberBy(
          viewModel.currentDramaModel.shortPlayDetail.shortPlayCode,
          viewModel.currentDramaModel.shortPlayDetail.collectNum,
        );

        // 更新剧集面板内容
        update([episodePanelId]);
      } else {
        viewModel.currentDramaModel.currentIndex.value =
            index - viewModel.currentDramaModel.startDataIndex;
        viewModel.currentDramaModel.currentEpisode.value = viewModel
            .currentDramaModel
            .episodeList[viewModel.currentDramaModel.currentIndex.value]
            .info;

        // 如果是当前剧的最后一集,需要去请求下一部剧的内容
        FFLog.info(
            'onPageViewChanged currentIndex = ${viewModel.currentDramaModel.currentIndex.value}, currentEpisode = ${viewModel.currentDramaModel.currentEpisode.value}');
        if (index - lastDataIndex == 0) {
          // 加载下一部剧
          FFLog.info("onPageViewChanged是当前剧的最后一集");
          // 这边要判断,是否已经存在有下一部剧的内容了,防止再次去请求多的数据
          if (viewModel.hasNextDrama == false) {
            FFLog.info("onPageViewChanged没有找到下部剧内容,请求下一部剧的信息");
            _requestNextDramaData();
          }
        }
        // 更新剧集面板内容
        update([episodePanelId]);
      }
    } else {
      // 滑到上一集, 需要判断是否是本剧的第一集,要滑动到上一部剧的最后一集
      FFLog.info("开始划到上一集 index $index");
      final currentDramaFirstEpisodeIndex =
          viewModel.currentDramaModel.startDataIndex;
      FFLog.info(
          "currentDramaFirstEpisodeIndex $currentDramaFirstEpisodeIndex");
      if (viewModel.currentDramaModel.startDataIndex - index == 1 &&
          viewModel.currentDramaIndex.value != 0) {
        viewModel.switchToPreviousDrama();
        FFLog.info(
            "onPageViewChanged切换到上一部剧 currentDramaIndex = ${viewModel.currentDramaIndex}");
        shortPlayDetail.value = viewModel.currentDramaModel.shortPlayDetail;

        // 请求退出挽留的剧
        _requestDramaRetainData(shortPlayDetail.value);

        collectNum.value =
            viewModel.currentDramaModel.shortPlayDetail.collectNum ?? 0;
        FFLog.info(
            'onPageViewChanged shortPlayDetail = ${viewModel.currentDramaModel.shortPlayDetail}');
        // 更新剧集面板内容
      } else {
        // 往上一集
        viewModel.currentDramaModel.currentIndex.value =
            index - viewModel.currentDramaModel.startDataIndex;
        viewModel.currentDramaModel.currentEpisode.value = viewModel
            .currentDramaModel
            .episodeList[viewModel.currentDramaModel.currentIndex.value]
            .info;
        FFLog.info(
            'onPageViewChanged 往上一集 currentIndex = ${viewModel.currentDramaModel.currentIndex.value}, currentEpisode = ${viewModel.currentDramaModel.currentEpisode.value}');
      }
    }

    viewModel.currentDramaModel.isLastOneView.value = false;
    onCurrentVideoPlay();

    FFLog.info(
        'onPageViewChanged currentIndex = ${viewModel.currentDramaModel.currentIndex.value}, length = ${viewModel.currentDramaModel.episodeList.length}, alllock = ${viewModel.currentDramaModel.allLock.value}');
    if (viewModel.currentDramaModel.currentIndex.value ==
            viewModel.currentDramaModel.episodeList.length - 1 &&
        !viewModel.currentDramaModel.allLock.value) {
      FFLog.info("滑动去调用解锁当前剧的可解锁的那一集,并且不是全部已经解锁");
      onUnLock(viewModel.currentDramaModel.currentIndex.value);
    }
    update([episodePanelId]);
  }

  // 请求退出挽留的剧
  void _requestDramaRetainData(ShortPlayDetail? shortPlayDetail) {
    final dramaRetentionController =
        Get.find<DramaRetentionController>(tag: DramaRetentionController.tag);
    dramaRetentionController
        .init(shortPlayDetail?.shortPlayId ?? shortPlayDetail?.id ?? 0);
  }

  // 请求下一部剧的内容
  Future<bool> _requestNextDramaData() async {
    ShortPlayDetail? newDrama = await viewModel
        .preloadNextDrama(viewModel.currentDramaModel.businessId);
    // 这边去请求剧集信息啊
    if (newDrama == null) {
      _handleTrackReelRequestClick(null, 'fail');
      return false;
    }
    _handleTrackReelRequestClick(newDrama, 'success');
    DramaWithEpisodesModel model = DramaWithEpisodesModel();
    var newbusinessId = newDrama.shortPlayId ?? newDrama.id ?? 0;
    var updateEpisode = newDrama.updateEpisode ?? 0;
    var totalEpisodes = newDrama.totalEpisodes ?? 0;

    model.shortPlayDetail = newDrama;
    model.shortPlayDetail.shortPlayId = newbusinessId;
    model.businessId = newbusinessId;

    final dramaList = await getDramaList(
      newbusinessId,
      totalEpisodes,
    );

    if (dramaList?.isEmpty == false) {
      // 进行剧集分
      List<EpisodeTab> episodes = [];
      var lastEpisodeNum = 0;
      // 已经更新的剧集信息
      for (int i = 0; i < dramaList!.length; i++) {
        final info = dramaList[i];
        episodes.add(EpisodeTab(index: i, info: info));
        lastEpisodeNum = info.episodeNum ?? 0;
      }
      model.episodeTabs = episodes;
      // 没有更新的剧集信息
      for (int i = 0; i < totalEpisodes - updateEpisode; i++) {
        episodes.add(
          EpisodeTab(
              index: updateEpisode + i,
              info: EpisodeData(episodeNum: lastEpisodeNum + i + 1),
              isDisable: true),
        );
      }
      model.episodeTabList = transformArrayToStructuredFormat(episodes, 30);

      // 获取已解锁剧集详情
      List<EpisodeVideoData>? list =
          await getHasUnlockedEpisodesDetails(dramaList);
      if (list != null) {
        List<Episode> episodes = [];
        List<String> urls = [];
        for (int i = 0; i < list.length; i++) {
          final item = list[i];
          item.alreadyLock = dramaList[i].alreadyLock;
          item.lock = dramaList[i].lock;
          item.unlockType = dramaList[i].unlockType;
          String videoUrl = getVideoUrlByResolution(
              item.videoUrl!, model.currentResolution.value);
          episodes.add(Episode(
            info: item,
            videoUrl: videoUrl,
          ));
          urls.add(videoUrl);
        }
        playerService.addPreloadList(urls);
        model.updateEpisodes(episodes);
        viewModel.addDramaModel(model);
        viewModel.updateTotalEpisodesNumPage();
        FFLog.info(
            "查看请求下来的剧集是否全部解锁了 可以播放的最后一集 ${model.episodeList.last.info.episodeNum}, 剧集列表的最后一集 ${model.currentUpdatedDramaLastEpisode?.episodeNum} 是否已经解锁 ${model.episodeList.last.info.alreadyLock == 1}  或者是否需要解锁 =${model.episodeList.last.info.lock == 2} ");
        // 这边要判断是否已经解锁了全部的集数
        if (model.episodeList.last.info.episodeNum ==
                model.currentUpdatedDramaLastEpisode?.episodeNum &&
            (model.episodeList.last.info.alreadyLock == 1 ||
                model.episodeList.last.info.lock == 2)) {
          model.allLock.value = true;
        }

        // 判断是否最后一集解锁了
        FFLog.info(
            "判断当前剧集最后一集解锁了吗 ${viewModel.currentDramaModel.currentUpdatedDramaLastEpisode?.alreadyLock}");
        if (viewModel.currentDramaModel.currentUpdatedDramaLastEpisode
                    ?.alreadyLock ==
                1 ||
            viewModel.currentDramaModel.currentUpdatedDramaLastEpisode?.lock ==
                2) {
          FFLog.info("最后一集解锁了,更新剧集长度");
          // 解锁了再去更新剧集长度
          update([episodeListId]);
        }
        return true;
      }
    }
    return false;
  }

  /// 显示剧集列表面板
  void onEpisodePanelShow() async {
    // 预加载广告
    AdManager().prefetchAds();
    final isLandscape = ScreenUtils.isLandscape(Get.context!);
    await SmartDialog.show(
      tag: episodePanelTag,
      alignment: isLandscape ? Alignment.bottomRight : Alignment.bottomCenter,
      keepSingle: true,
      builder: (context) {
        // 在builder中获取方向并调整位置
        return Align(
          alignment:
              isLandscape ? Alignment.bottomRight : Alignment.bottomCenter,
          child: EpisodePanelWidget(
            onItem: (episodeVideoData) {
              onTo(episodeVideoData.index +
                  viewModel.currentDramaModel.startDataIndex);
            },
            detailsController: this,
          ),
        );
      },
    );
  }

  /// 关闭剧集列表面板
  Future<void> onEpisodePanelClose() async {
    await SmartDialog.dismiss(tag: episodePanelTag);
  }

  /// 解锁逻辑
  /// 1. 只能解锁下一集
  /// 2. 不能跨剧集解锁
  /// 3. 不能解锁大于更新的剧集
  bool getNotAllow(int index) {
    Get.log('当前集数：$index');
    if (viewModel.currentDramaModel.allLock.value) {
      Get.log("全部解锁了");
      return false;
    }
    //  判断是否大于更新的剧集
    if (index >
        (viewModel.currentDramaModel.shortPlayDetail.updateEpisode ?? 0)) {
      FFLog.debug("不能解锁未发布的");
      Get.toast(AppTrans.unableUnlockPublished());
      return false;
    }
    // 判断是否跨剧集解锁
    if (index > viewModel.currentDramaModel.episodeList.length - 1) {
      Get.toast(AppTrans.unableUnlockEpisodes());
      FFLog.debug("不能跨剧集解锁");
      return false;
    }
    return true;
  }

  // 获取剧集信息列表
  Future<List<EpisodeData>?> getDramaList(int businessId, int pageSize) async {
    FFLog.debug("请求剧集列表的信息:$businessId, $pageSize");
    final result = await ApiDetails.getDramaList(businessId, 1, pageSize);
    return result;
  }

  //更新分组
  void groupedEpisodePanelTabList(
      List<EpisodeData> list, int pageSize, int totalEpisodes) {
    List<EpisodeTab> episodes = [];
    var lastEpisodeNum = 0;
    // 已经更新的剧集信息
    for (int i = 0; i < list.length; i++) {
      final info = list[i];
      episodes.add(EpisodeTab(index: i, info: info));
      lastEpisodeNum = info.episodeNum ?? 0;
    }
    viewModel.currentDramaModel.episodeTabs = episodes;
    // 没有更新的剧集信息
    for (int i = 0; i < totalEpisodes - pageSize; i++) {
      episodes.add(
        EpisodeTab(
            index: pageSize + i,
            info: EpisodeData(episodeNum: lastEpisodeNum + i + 1),
            isDisable: true),
      );
    }
    viewModel.currentDramaModel.episodeTabList =
        transformArrayToStructuredFormat(episodes, 30);
    update([episodePanelId]);
  }

  // 获取已经解锁的剧集信息
  Future<List<EpisodeVideoData>?> getHasUnlockedEpisodesDetails(
      List<EpisodeData> list,
      {DramaWithEpisodesModel? model}) async {
    List<int> alreadyLockEpisodIds = getAlreadyLockEpisod(list);
    Get.log(
        "DetailsControllerEvent alreadyLockEpisodIds ${alreadyLockEpisodIds.length}");
    // 获取需要解锁的剧集的第一个id
    final unlockEpisodId = getUnlockEpisodId(list, alreadyLockEpisodIds.length);
    List<int> episodIds = [...alreadyLockEpisodIds];
    if (unlockEpisodId != null) {
      episodIds.add(unlockEpisodId);
      if (model != null) {
        model.isUnlockEpisodId.value = true;
      } else {
        viewModel.currentDramaModel.isUnlockEpisodId.value = true;
      }
    }
    Get.log("DetailsControllerEvent episodIds ${episodIds.length}");
    final result = await getEpisodeDetails(episodIds);
    return result;
  }

  /// 获取需要解锁的剧集id
  /// 1. 如果已经解锁的剧集数量为0，则返回第一个剧集id
  /// 2. 如果已经解锁的剧集数量等于已经发布剧集长度则返回null
  /// 3. 如果
  int? getUnlockEpisodId(List<EpisodeData> list, int alreadyLockEpisodIdsLen) {
    if (alreadyLockEpisodIdsLen == 0) {
      return list.first.id;
    } else if (alreadyLockEpisodIdsLen == list.length) {
      return null;
    } else if (alreadyLockEpisodIdsLen < list.length) {
      return list[alreadyLockEpisodIdsLen].id;
    }
    return null;
  }

  /// 这个进入后只需要处理一次
  Future<void> onPlayerEpisodePosition() async {
    if (detailsOptions.playerEpisodePosition != Duration.zero) {
      if (!_isPlayDurationShouldReset(detailsOptions.videoDuration,
          detailsOptions.playerEpisodePosition.inSeconds)) {
        await onCurrentVideoSeek(detailsOptions.playerEpisodePosition);
      }
      detailsOptions.playerEpisodePosition = Duration.zero;
    } else if (viewModel.currentDramaModel.currentIndex.value <
        viewModel.currentDramaModel.episodeList.length) {
      final episode = viewModel.currentDramaModel
          .episodeList[viewModel.currentDramaModel.currentIndex.value];
      final duration = _readPlayDurationFromLocal(episode);
      if (duration != Duration.zero) {
        await onCurrentVideoSeek(duration);
      }
    }
  }

  Future<List<EpisodeVideoData>?> getEpisodeDetails(
      List<int> episodeIds) async {
    final result = await ApiDetails.getEpisodeDetails(episodeIds, "recently");
    return result;
  }

  void setPlaybackSpeed(double speed) {
    playbackSpeed.value = speed;

    DetailTrackEvent.videoSpeedAdjust(
      shortPlayCode: viewModel.currentDramaModel.shortPlayDetail.shortPlayCode,
      isLandscapeScreen: isLandscapeScreen.value,
    );
  }

  void onSetEpisode(List<EpisodeVideoData> result) {
    List<Episode> episodes = [];
    List<String> urls = [];
    for (int i = 0; i < result.length; i++) {
      final item = result[i];
      item.alreadyLock =
          viewModel.currentDramaModel.episodeTabs[i].info.alreadyLock;
      item.lock = viewModel.currentDramaModel.episodeTabs[i].info.lock;
      item.unlockType =
          viewModel.currentDramaModel.episodeTabs[i].info.unlockType;
      String videoUrl = getVideoUrlByResolution(
          item.videoUrl!, viewModel.currentDramaModel.currentResolution.value);
      episodes.add(Episode(
        info: item,
        videoUrl: videoUrl,
      ));
      urls.add(videoUrl);
    }
    viewModel.updateCurrentunlockedDramaEpisodes(episodes);
    update([episodeListId]);

    if (viewModel.currentDramaModel.episodeList.last.info.episodeNum ==
            viewModel
                .currentDramaModel.currentUpdatedDramaLastEpisode?.episodeNum &&
        (viewModel.currentDramaModel.episodeList.last.info.alreadyLock == 1 ||
            viewModel.currentDramaModel.episodeList.last.info.lock == 2)) {
      viewModel.currentDramaModel.allLock.value = true;
    }
    Get.log("isUnlockEpisodId allLock $viewModel.currentDramaModel.allLock");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (detailsOptions.playerEpisodeIndex != 0 &&
          detailsOptions.playerEpisodeIndex <
              viewModel.currentDramaModel.episodeList.length) {
        jumpToPage(detailsOptions.playerEpisodeIndex);
        detailsOptions.playerEpisodeIndex = 0;
      }
    });
    // 第一次获取视频列表时，使用setPreloadList
    if (viewModel.currentDramaModel.episodeList.length == result.length) {
      playerService.setPreloadList(urls);
    } else {
      playerService.addPreloadList(urls);
    }
  }

  void onAddEpisode(List<EpisodeVideoData> result) {
    List<Episode> episodes = [];
    List<String> urls = [];
    for (EpisodeVideoData? item in result) {
      if (item == null) {
        continue;
      }
      // 查找对应的解锁信息,由于服务端没有返回多余的数据,后续需要服务端优化,多返回数据,现在由客户端处理
      final exitItem = viewModel.currentDramaModel.episodeTabs.firstWhere(
          (element) =>
              element.info.id == item.id &&
              element.info.shortPlayId == item.shortPlayId);
      item.alreadyLock = exitItem.info.alreadyLock;
      item.lock = exitItem.info.lock;
      item.unlockType = exitItem.info.unlockType;
      episodes.add(Episode(
        info: item,
        videoUrl: getVideoUrlByResolution(item.videoUrl!,
            viewModel.currentDramaModel.currentResolution.value),
      ));
      urls.add(getVideoUrlByResolution(
          item.videoUrl!, viewModel.currentDramaModel.currentResolution.value));
    }
    // 加入到预加载列表
    playerService.addPreloadList(urls);
    viewModel.updateCurrentunlockedDramaEpisodes(episodes);
    update([episodeListId]);
  }

  List<EpisodeTabInfo> transformArrayToStructuredFormat(
      List<EpisodeTab> array, int chunkSize) {
    List<EpisodeTabInfo> result = [];
    for (int i = 0; i < array.length; i += chunkSize) {
      int end = (i + chunkSize) < array.length ? i + chunkSize : array.length;
      List<EpisodeTab> chunk = array.sublist(i, end);
      String label = '${i + 1}-$end';
      result.add(EpisodeTabInfo(label: label, list: chunk));
    }
    return result;
  }

  String getVideoUrlByResolution(VideoInfoMap videoInfoMap, String resolution) {
    switch (resolution) {
      case '1080p':
        return videoInfoMap.video1080 ??
            videoInfoMap.video720 ??
            videoInfoMap.video480!;
      case '720p':
        return videoInfoMap.video720 ??
            videoInfoMap.video480 ??
            videoInfoMap.video1080!;
      case '480p':
        return videoInfoMap.video480 ??
            videoInfoMap.video720 ??
            videoInfoMap.video1080!;
      default:
        return videoInfoMap.video720 ??
            videoInfoMap.video480 ??
            videoInfoMap.video1080!;
    }
  }

  List<int> getAlreadyLockEpisod(List<EpisodeData> episodes) {
    List<int> alreadyLockEpisod = [];
    for (EpisodeData item in episodes) {
      if ((item.alreadyLock == 1 || item.lock == 2) && item.id != null) {
        alreadyLockEpisod.add(item.id!);
      }
    }
    return alreadyLockEpisod;
  }

  void onVideoPlayTimeChange(int index, Duration time) {
    final episode = viewModel.currentDramaModel.episodeList[index];
    episode.setPlayPosition(time);
    _writePlayDurationToLocal(episode, time);
    // 判断是否即将播放下一剧,10秒内 有下一部剧
    final leftTime = (episode.info.videoDuration ?? 0) - time.inSeconds;

    if (leftTime <= 10 &&
        leftTime > 0 &&
        (viewModel.currentDramaModel.isLastEpisode) &&
        viewModel.hasNextDrama &&
        !viewModel.isShowingNextPlayToast) {
      // 显示即将播放下一剧
      viewModel.isShowingNextPlayToast = true;
      viewModel.nextDrameToast = Get.toast(
          AppTrans.comingsoonNextDrama(
              '${viewModel.nextDramaModel?.shortPlayDetail.shortPlayName}'),
          alignment: Alignment.topCenter,
          duration: Duration(seconds: leftTime),
          margin: const EdgeInsets.only(top: 64, left: 42, right: 42),
          padding: EdgeInsets.only(
              top: 4.sp, bottom: 4.sp, left: 16.sp, right: 16.sp),
          background: ColorHex.fromHex("#000000", opacity: 0.7));
    } else {
      if (leftTime > 10) {
        viewModel.isShowingNextPlayToast = false;
        if (viewModel.nextDrameToast != null) {
          Get.toastDismiss(item: viewModel.nextDrameToast);
        }
      }
    }
  }

  Future<void> onAddWatchHistory(int index) async {
    if (index >= viewModel.currentDramaModel.episodeList.length) {
      return;
    }
    final dramaId = viewModel.currentDramaModel.episodeList[index].info.id;
    if (dramaId == null) {
      return;
    }
    int duration =
        viewModel.currentDramaModel.episodeList[index].playPosition.inSeconds;
    // 如果不是来自归因剧或兜底剧，且播放时长小于5秒，则不记录观看历史
    if (!isFromAttributionOrFallback() && duration < 5) {
      return;
    }

    ApiDetails.saveWatchHistory(dramaId, duration).then((success) {
      if (success) {
        eventBus.fire(RefreshHomeEvent());
        eventBus.fire(RefreshWatchHistoryListEvent());
      }
    });
  }

  void onBack() {
    _backHandle();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.inactive ||
        state == AppLifecycleState.paused) {
      isForeground = false;
      onCurrentVideoPause();
    } else if (state == AppLifecycleState.resumed) {
      isForeground = true;
      // 是当前路由并且不是手动暂停
      if (Get.currentRoute == Routes.detailsPage &&
          !isUserPaused &&
          !viewModel.isShowingContinueAdPopWidget) {
        onCurrentVideoPlay();
      }
      if (needShowAdWhenPop == true) {
        await _showAdWhenPop();
        Get.back();
      }
    }
  }

  bool isBackground = false;
  // 新增解锁一集时弹出通知弹窗
  void onPopInvokedWithResult(didPop, _) async {
    if (didPop == true) {
      return;
    } else {
      await _backHandle();
    }
  }

  Future<void> _backHandle() async {
    //在这判断如果是横屏，就切换为竖屏，否则才执行下面的退出逻辑
    if (isLandscapeScreen.value) {
      await toggleLandscapeScreen();
      return;
    }
    if (isBackground == true) {
      return;
    }
    isBackground = true;
    onAddWatchHistory(viewModel.currentDramaModel.currentIndex.value);
    await onCurrentVideoPause();
    bool isShowNotDialog = false;
    if (lockNum >= 1) {
      /// 检查并弹出通知弹窗
      PermissionStatus status = await Permission.notification.status;
      final isShowNotificationDialog =
          notificationLoginService.isShowNotificationDialogByReels(3) == true;
      if (!status.isGranted && isShowNotificationDialog) {
        isShowNotDialog = true;
        bool? isOpenAppSetting = await SmartDialog.show(
          tag: 'first_dialog_last_shown_3',
          clickMaskDismiss: false,
          builder: (context) => const NotificationSelectView(type: 3),
        );
        if (isOpenAppSetting == true) {
          needShowAdWhenPop = true;
          Future.delayed(const Duration(milliseconds: 500), () {
            isBackground = false;
          });
          return;
        }
      }
    }
    if (isFromAttributionOrFallback() &&
        recommendService.getIsRecommendPage()) {
      await AppNavigator.startRecommendPage(from: "immersion_back", off: true);
      Future.delayed(const Duration(milliseconds: 500), () {
        isBackground = false;
      });
      return;
    } else {
      if (isShowNotDialog == false) {
        final val = await onDramaRetentionBackPressed();
        if (val == true) {
          Future.delayed(const Duration(milliseconds: 500), () {
            isBackground = false;
          });
          return;
        }
      }
    }
    await _showAdWhenPop();
    Get.back();
    Future.delayed(const Duration(milliseconds: 500), () {
      isBackground = false;
    });
  }

  // 更新预加载列表为
  void updatePreloadList() {
    List<String> urls = [];
    for (var episode in viewModel.currentDramaModel.episodeList) {
      String videoUrl = getVideoUrlByResolution(episode.info.videoUrl!,
          viewModel.currentDramaModel.currentResolution.value);
      urls.add(videoUrl);
    }
    playerService.setPreloadList(urls);
  }

  // 修改分辨率切换方法
  void changeResolution(String resolution) {
    if (viewModel.currentDramaModel.currentResolution.value != resolution) {
      viewModel.currentDramaModel.currentResolution.value = resolution;
      // 后续切换分辨率需求, 切换后需更新预加载列表
      updatePreloadList();
    }
  }

  void _writePlayDurationToLocal(Episode episode, Duration duration) {
    final episodeId = episode.info.id;
    if (episodeId == null) {
      return;
    }
    final mmkvService = Get.find<MMKVService>();
    mmkvService.setPlayDuration(episodeId, duration);
  }

  Duration _readPlayDurationFromLocal(Episode episode) {
    final episodeId = episode.info.id;
    if (episodeId == null) {
      return Duration.zero;
    }
    final videoDuration = episode.info.videoDuration;
    final mmkvService = Get.find<MMKVService>();
    final duration = mmkvService.getPlayDuration(episodeId);
    if (_isPlayDurationShouldReset(videoDuration, duration.inSeconds)) {
      _resetPlayDurationToLocal(episode);
      return Duration.zero;
    }
    return duration;
  }

  void _resetPlayDurationToLocal(Episode episode) {
    final episodeId = episode.info.id;
    if (episodeId == null) {
      return;
    }
    final mmkvService = Get.find<MMKVService>();
    mmkvService.removePlayDuration(episodeId);
  }

  /// 是否需要重置播放时间
  bool _isPlayDurationShouldReset(int? videoDuration, int? currentDuration) {
    if (videoDuration == null) {
      return false;
    }
    if (currentDuration == null) {
      return false;
    }
    if ((videoDuration <= 2) || currentDuration >= (videoDuration - 2)) {
      // 此处是临时特殊处理，因为规范的解决方法需要在播放器播放结束时不再回调onTimeChanged，影响范围太大。
      // 当剩余时间小于2秒时直接从头开始播放，避免剩余时间太短导致自动往下滑的问题。
      return true;
    }
    return false;
  }

  Future<void> _showAdWhenPop() async {
// 这边增加一个打点adplacementshow
    AdTrackEvent.adPlacementShow(AdType.reward, AdScene.exitImmersionPage,
        param: {
          TrackEvent.reel_id:
              viewModel.currentDramaModel.shortPlayDetail.shortPlayCode == null
                  ? ''
                  : viewModel.currentDramaModel.shortPlayDetail.shortPlayCode
                      .toString(),
          TrackEvent.episode: getCurrentEpisodeNumberString(),
        });

    // 这边去展示广告退出沉浸页广告
    AdContext context = AdContext();
    // 这里需要传入当前剧集的信息
    context.eventParam = {
      TrackEvent.reel_id:
          viewModel.currentDramaModel.shortPlayDetail.shortPlayCode == null
              ? ''
              : viewModel.currentDramaModel.shortPlayDetail.shortPlayCode
                  .toString(),
      TrackEvent.episode: getCurrentEpisodeNumberString(),
      TrackEvent.lock_begin:
          viewModel.currentDramaModel.shortPlayDetail.lockBegin == null
              ? ''
              : viewModel.currentDramaModel.shortPlayDetail.lockBegin
                  .toString(),
    };

    if ((AdTriggerEventCollector.shared.checkExitDetailPageGapFilled() ==
            true) &&
        AdManager().getAdModelFromCache(AdScene.exitImmersionPage) != null) {
      FFLog.info("展示广告退出沉浸页广告是否横屏 ${isLandscapeScreen.value}");
      await AdManager()
          .showFullScreenAd(AdScene.exitImmersionPage, context: context);
    }
  }

  Future<bool> onDramaRetentionBackPressed() async {
    final TimeIntervalChecker timeIntervalChecker = TimeIntervalChecker();
    const key = "drama_retention";
    final isShow = await timeIntervalChecker.isIntervalExceeded(key, 5 * 60);
    if (isShow && !isLandscapeScreen.value) {
      final res = await SmartDialog.show(
        tag: DramaRetentionPopup.tag,
        keepSingle: true,
        clickMaskDismiss: false,
        builder: (context) => const DramaRetentionPopup(),
        alignment: Alignment.center,
        maskColor: Colors.black.withValues(alpha: 0.8),
        onDismiss: () {
          timeIntervalChecker.saveCurrentTime(key);
        },
      );
      if (res == true) {
        return true;
      }
    }
    return false;
  }

  int? getCurrentEpisodeNumberInt() {
    return viewModel.currentDramaModel.episodeTabs.isEmpty
        ? null
        : viewModel
            .currentDramaModel
            .episodeTabs[viewModel.currentDramaModel.currentIndex.value]
            .info
            .episodeNum;
  }

  String getCurrentEpisodeNumberString() {
    int? number = getCurrentEpisodeNumberInt();
    return number == null ? '' : number.toString();
  }
}

extension DetailsControllerRecentlyWatchExtension on DetailsController {
  void _dismissHomeRecentlyWatchView() {
    RecentlyWatchService recentlyWatchService =
        Get.find<RecentlyWatchService>();
    if (recentlyWatchService.showOnHome.value == true) {
      recentlyWatchService.showOnHome.value = false;
    }
  }

  Future<void> _saveRecentlyWatchData() async {
    EpisodeVideoData? data;
    if (viewModel.currentDramaModel.currentIndex.value <
        viewModel.currentDramaModel.episodeList.length) {
      final episode = viewModel.currentDramaModel
          .episodeList[viewModel.currentDramaModel.currentIndex.value];
      data = episode.info;
    }
    if (data == null) {
      return;
    }

    final shortPlayId = data.shortPlayId;
    final shortPlayName =
        viewModel.currentDramaModel.shortPlayDetail.shortPlayName ?? '';
    final imageUrl = data.coverId;
    final totalEpisodes = data.totalEpisodes;
    final episodeNum = data.episodeNum;
    if (shortPlayId == null ||
        imageUrl == null ||
        totalEpisodes == null ||
        episodeNum == null) {
      return;
    }

    final model = RecentlyWatchModel(
        shortPlayId: shortPlayId,
        shortPlayName: shortPlayName,
        imageUrl: imageUrl,
        totalEpisodes: totalEpisodes,
        episodeNum: episodeNum);

    RecentlyWatchService recentlyWatchService =
        Get.find<RecentlyWatchService>();
    await recentlyWatchService.setRecentlyWatchModel(model);
  }
}

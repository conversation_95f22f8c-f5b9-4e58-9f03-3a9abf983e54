import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';

import 'details_controller.dart';

class DetailsBinding implements Bindings {
  @override
  void dependencies() {
    // 使用tag注册控制器，与details_page.dart中的Get.find<DetailsController>(tag: 'details_controller')对应
    try {
      Get.put<DetailsController>(DetailsController(), tag: 'details_controller');
      FFLog.info('DetailsController registered with tag: details_controller', tag: 'DetailsBinding');
    } catch (e) {
      FFLog.error('Failed to register DetailsController: $e', tag: 'DetailsBinding');
    }
  }
}

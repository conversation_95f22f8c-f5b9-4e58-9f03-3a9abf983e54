import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/store/unlock_store_product_result.dart';

class AdvanceWatchAdController extends GetxController {
  Rxn<EpisodeVideoData> hasUnlockedVideoData = Rxn<EpisodeVideoData>();
  Rxn<EpisodeVideoData> nextEpisodeVideoData = Rxn<EpisodeVideoData>();
  Rxn<WatchAdResultResponse> watchAdInfo = Rxn<WatchAdResultResponse>();
  
  void updateData({
    EpisodeVideoData? unlockedData,
    EpisodeVideoData? nextEpisodeData,
    WatchAdResultResponse? adInfo,
  }) {
    if (unlockedData != null) hasUnlockedVideoData.value = unlockedData;
    if (nextEpisodeData != null) nextEpisodeVideoData.value = nextEpisodeData;
    if (adInfo != null) watchAdInfo.value = adInfo;
  }
}
# 接口实现

所有接口类均已 Api 开头，例如：

```dart
class ApiUser
```

## 示例

```dart
// lib/api/home_api.dart
class ApiHome {
  static Future<HomeModel?> list() async {
    final response = await HttpService().get('/film/v2/channelIndexPage');
    if (response.data != null) {
      return HomeModel.fromJson(response.data as Map<String, dynamic>);
    }
    return null;
  }
}
```

```dart
// lib/model/home_model.dart
class HomeModel {
  List<SectionModel>? records;
  String? backgroundColor;

  HomeModel({this.records, this.backgroundColor});

  HomeModel.fromJson(Map<String, dynamic> json) {
    if (json['records'] != null) {
      records = <SectionModel>[];
      json['records'].forEach((v) {
        records!.add(SectionModel.fromJson(v));
      });
    }
    backgroundColor = json['backgroundColor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (records != null) {
      data['records'] = records!.map((v) => v.toJson()).toList();
    }
    data['backgroundColor'] = backgroundColor;
    return data;
  }
}
```

import 'package:playlet/common/http/http.dart';
import 'package:playlet/model/notification/push_check_info.dart';
import 'package:playlet/model/rewards/task_receive_result.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/service/app_device_service.dart';

class ApiNotification {
  /// 上报token
  static Future<void> uploadToken(int type, String token, int iosPushTest, String apnsToken) async {
    await HttpService().post("/userRegistrationToken/report", data: {
      "type": type,
      "token": token,
      "model": await AppDeviceService.instance.getDeviceModel(),
      "iosPushTest": iosPushTest,
      "apnsToken": apnsToken,
    });
  }

  /// 领取任务奖励
  static Future<TaskReceiveResult?> receiveRewards(int taskId) async {
    final response = await HttpService().post("/appTask/receiveRewards", data: {"taskId": taskId});
    if (response.isSuccess) {
      return TaskReceiveResult.fromJson(response.data);
    } else {
      return null;
    }
  }

  /// 推送池随机获取一条数据 -加密接口
  static Future<ShortPlayResponseList?> getForYouListOnlyOne() async {
    final response = await HttpService().post("/forYou/encrypt/getForYouListOnlyOne");
    if (response.isSuccess && response.data != null) {
      return ShortPlayResponseList.fromJson(response.data);
    } else {
      return null;
    }
  }

  /// 签到提醒
  static Future<PushCheckInfo?> signReminder() async {
    final response = await HttpService().post("/push/sign/signReminder");
    if (response.isSuccess && response.data != null) {
      return PushCheckInfo.fromJson(response.data);
    } else {
      return null;
    }
  }

  /// 错过签到提醒
  static Future<PushCheckInfo?> missSignReminder() async {
    final response = await HttpService().post("/push/sign/missSignReminder");
    if (response.isSuccess && response.data != null) {
      return PushCheckInfo.fromJson(response.data);
    } else {
      return null;
    }
  }

  /// 奖励即将过期
  static Future<PushCheckInfo?> bonusExpiring() async {
    final response = await HttpService().post("/push/bonusExpiring/getPushInfo");
    if (response.isSuccess && response.data != null) {
      return PushCheckInfo.fromJson(response.data);
    } else {
      return null;
    }
  }

  // 获取全屏通知配置
  static Future<int?> getFullScreenConfig() async {
    String key = "android_fsi_push_num";
    final response = await HttpService().post("/system/getConfigByKey", data: {'key': key});
    if (response.isSuccess) {
      final configValue = response.data?.toString(); 
      return configValue != null ? int.tryParse(configValue) ?? 0 : 0;
    } else {
      return null;
    }
  }
}

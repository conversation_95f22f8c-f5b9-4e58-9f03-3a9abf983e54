import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/http/result.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';

class HomeApi {
  // 获取首页Tab数据
  static Future<Result> getHomeTabData() async {
    final result =
        await HttpService().post("/homeData/encrypt/getTabHomeData", data: {
      "newbieShowType": 1,
    });
    if (result.isSuccess) {
      try {
        result.data = TabHomeData.fromJson(result.data);
      } catch (e) {
        result.data = TabHomeData.fromJson({});
      }
    }
    return result;
  }
}

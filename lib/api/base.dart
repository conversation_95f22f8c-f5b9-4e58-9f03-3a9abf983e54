import 'dart:io';

import 'package:get/get.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/http/encryption_utils.dart';
import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/http/result.dart';
import 'package:playlet/common/http/secret_repo.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/app_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/token.dart';
import 'package:playlet/utils/track_event.dart';

class ApiBase {
  /// 标记是否有登录初始化正在进行
  static bool _isInitLoginRunning = false;

  /// 从本地存储读取用户信息
  static Future<UserResponse?> readUserResponse() async {
    try {
      final storage = SafeStorage();
      final userData =
          storage.read<Map<String, dynamic>>(UserService.userInfoKey);
      if (userData != null) {
        return UserResponse.fromJson(userData);
      }
      return null;
    } catch (e) {
      FFLog.error("读取用户信息失败: $e");
      return null;
    }
  }

  /// todo!! 初始化登录, 登录过期需要处理
  static Future<InitLoginResult?> initLogin() async {
    // 检查是否已经有token，表示已经登录成功
    final existingToken = Token.getToken();
    if (existingToken.isNotEmpty) {
      FFLog.info("已经登录成功，无需重复登录");
      // 可以在这里添加逻辑来确认token有效性，例如调用用户信息接口
      // 如果已经有token，尝试获取当前用户信息
      final UserService userService = Get.find<UserService>();
      final userResponse = await readUserResponse();
      if (userResponse != null) {
        // 有token且已有用户信息，直接返回成功
        userService.setUserInfo(userResponse);
        final userId = userResponse.userId;
        if (userId != null) {
          await Get.find<AppService>().reportPendingLaunches(userId);
        }
        return InitLoginResult(
          token: existingToken,
          userResponse: userResponse,
        );
      }
    }

    // 如果已经有一个initLogin正在运行，直接返回并打印警告
    if (_isInitLoginRunning) {
      FFLog.warning("initLogin已经在运行中，避免重复调用");
      return null;
    }

    // 设置标记，表示initLogin正在运行
    _isInitLoginRunning = true;

    try {
      final UserService userService = Get.find<UserService>();
      final String publicKey = SecretRepo.getPublicKey();
      final String apiRc4Key = SecretRepo.generateRC4Key();
      final deviceId = await AppDeviceService.instance.getFingerprint();
      final String secretKey = EncryptionUtils.encryptWithRSA(
        apiRc4Key,
        publicKey,
      );
      final result = await HttpService().post("/login/initLogin", data: {
        'deviceId': deviceId,
        'secretKey': secretKey,
      });
      if (result.isSuccess) {
        final data = InitLoginResult.fromJson(result.data);
        if (data.userResponse != null) {
          userService.setUserInfo(data.userResponse!);
          final userId = data.userResponse!.userId;
          if (userId != null) {
            await Get.find<AppService>().reportPendingLaunches(userId);
            useTrackEvent(EventName.register, priority: TrackEventPriority.high);
          }
        }
        if (data.token != null) {
          await Token.setToken(data.token!);
        }

        return data;
      }
      return null;
    } catch (e) {
      FFLog.error("initLogin执行异常: $e");
      return null;
    } finally {
      // 无论成功或失败，都需要重置标记
      _isInitLoginRunning = false;
    }
  }

  static Future<Result> getConfig({
    String? defaultKey,
    String? iosKey,
    String? androidKey,
  }) async {
    String key;
    if (Platform.isIOS && iosKey != null) {
      key = iosKey;
    } else if (Platform.isAndroid && androidKey != null) {
      key = androidKey;
    } else {
      key = defaultKey ?? '';
    }
    try {
      final response = await HttpService()
          .post("/system/getConfigByKey", data: {'key': key});
      return response;
    } catch (e) {
      FFLog.debug('获取 $key 配置失败: $e');
      return Result(status: 0, message: e.toString());
    }
  }
}

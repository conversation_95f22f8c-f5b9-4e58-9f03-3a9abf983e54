import 'package:playlet/common/log/ff_log.dart';

import '../common/http/http.dart';
import '../model/rewards/ad_task_receive_result.dart';
import '../model/rewards/check_in_list_result.dart';
import '../model/rewards/check_in_result.dart';
import '../model/rewards/task_ad_list_result.dart';
import '../model/rewards/task_list_result.dart';
import '../model/rewards/user_task_receive_result.dart';

class ApiRewards {
  // 签到接口
  static Future<CheckInResult?> checkIn() async {
    final response = await HttpService().post("/sig/sign");
    if (response.isSuccess) {
      var result = CheckInResult.fromJson(response.data);
      return result;
    } else {
      return null;
    }
  }

  // 获取签到列表。/app/sig/signRecord
  static Future<CheckInListResult?> getSignRecord() async {
    final response = await HttpService().post("/sig/signRecord");
    FFLog.debug("签到列表  $response");
    if (response.isSuccess) {
      var result = CheckInListResult.fromJson(response.data);
      return result;
    } else {
      return null;
    }
  }

  // 获取任务列表
  static Future<TaskListResult?> getAppTaskList() async {
    final response = await HttpService().post("/appTask/getAppTaskList");
    if (response.isSuccess) {
      return TaskListResult.fromJson(response.data);
    } else {
      return null;
    }
  }

  // 获取广告任务列表
  static Future<TaskAdListResult?> getADTaskList() async {
    final response = await HttpService().post("/ad/getAdBonus");
    if (response.isSuccess) {
      return TaskAdListResult.fromJson(response.data);
    } else {
      return null;
    }
  }

  static Future<UserTaskReceiveResult?> receiveRewards(int taskId) async {
    final response = await HttpService()
        .post("/appTask/receiveRewards", data: {"taskId": taskId});
    if (response.isSuccess) {
      var result = UserTaskReceiveResult.fromJson(response.data);
      return result;
    } else {
      return null;
    }
  }

  // 领取签到双倍奖励
  static Future<SignWatchADResultModel?> receiveDoubleCheckInRewards() async {
    final response = await HttpService().post("/ad/signWatchAd");
    if (response.isSuccess) {
      var result = SignWatchADResultModel.fromJson(response.data);
      return result;
    } else {
      return null;
    }
  }

  // 领取广告任务奖励
  static Future<AdTaskReceiveResult?> receiveADCompleteRewards(int id) async {
    final response =
        await HttpService().post("/ad/watchAdUnLockComplete", data: {"id": id});
    if (response.isSuccess) {
      var result = AdTaskReceiveResult.fromJson(response.data);
      return result;
    } else {
      return null;
    }
  }

//   获取今日可得到的bonus总数

  static Future<int?> getBonusTotal() async {
    final response = await HttpService().post("/bonusRecord/getBonusTotal");
    if (response.isSuccess && response.data != null && response.data is Map) {
      var dataMap = response.data as Map;
      if (dataMap.containsKey("userTodayBonusTotal")) {
        return response.data["userTodayBonusTotal"];
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}

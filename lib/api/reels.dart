import 'package:playlet/common/http/http.dart';
import 'package:playlet/model/reels.dart';

class ApiReels {
  /// 获取推荐列表
  static Future<ShortPlayResult?> getForYouListPage(
    int pageNum,
    int pageSize, {
    int? isColdBoot,
    int? lastShortPlayId,
    int? consecutiveTimes,
    String? realLanguageCode,
  }) async {
    final result = await HttpService().post(
      "/forYou/encrypt/getForYouListPageNewV2",
      data: {
        "pageNum": pageNum,
        "pageSize": pageSize,
        "isColdBoot": isColdBoot,
        "lastShortPlayId": lastShortPlayId,
        "consecutiveTimes": consecutiveTimes,
        "realLanguageCode": realLanguageCode,
      },
    );
    if (result.isSuccess) {
      return ShortPlayResult.fromJson(result.data);
    }
    return null;
  }

  static Future<ForYouShortInfo?> getShortInfoById(int dramaId) async {
    final result = await HttpService().post(
      "/dramaInfo/encrypt/dramaDetail",
      data: {
        "businessId": dramaId,
        "scene": 'recently',
      },
    );
    if (result.isSuccess && result.data != null) {
      return ForYouShortInfo.fromJson(result.data);
    }
    return null;
  }

  static Future<int?> getDramaIdByShortPlayId(int shortPlayId) async {
    final result = await HttpService().post(
      "/hiDrama/getDramaIdByShortPlayId",
      data: {
        "businessId": shortPlayId,
        "scene": 'recently',
      },
    );
    if (!result.isSuccess) {
      return null;
    }
    final data = result.data;
    if (data == null) {
      return null;
    }
    int? dramaId = data["dramaId"];
    return dramaId;    
  }
}

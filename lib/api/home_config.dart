import 'package:get/get.dart';
import 'package:playlet/model/config/home_config.dart';
import 'package:playlet/service/user_service.dart';
import '../common/http/http.dart';

class ApiHomeConfig {

  // 首页相关配置接口
  static Future<HomeConfigResult?> getHomeConfig() async {
    final response = await HttpService().post("/homeData/getHomeConfig");
     if (response.data != null && response.isSuccess) {
      final result = HomeConfigResult.fromJson(response.data);
      String? metaLoginBonus= result.metaLoginBonus;
      if (metaLoginBonus != null) {
        Get.find<UserService>().setMetaLoginBonus(metaLoginBonus);
      }
      }
    return null;
  }

}

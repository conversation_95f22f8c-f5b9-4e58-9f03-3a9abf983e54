import 'dart:io';

import 'package:playlet/api/base.dart';
import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/http/result.dart';
import 'package:playlet/common/log/ff_log.dart';

class AdApi {
  // 获取广告配置
  static Future<Result> getConfig() async {
    String iosKey = 'sys.ios.advertising.caching.strategy';
    String androidKey = 'sys.android.advertising.caching.strategy';
    
    try {

      final response = await ApiBase.getConfig(iosKey: iosKey, androidKey: androidKey);
      return response;
    } catch (e) {
      FFLog.debug('获取广告配置失败: $e');
      return Result(status: 0, message: e.toString());
    }
  }
}
import 'package:flutter/widgets.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/http/http.dart';
import 'package:playlet/components/widget/app_upgrade_dialog_widget.dart';
import 'package:playlet/model/upgrade/app_upgrade_result.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain_manager.dart';
import 'package:playlet/utils/app_analysis.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';

class ApiUpgrade {
  static final _storage = SafeStorage();
  static const String _lastPromptTimeKey = "lastUpgradePromptTime";

  static Future<AppUpgradeResult?> getUpgradeVersionManageInfo() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final lastPromptTime = _storage.read<int>(_lastPromptTimeKey) ?? 0;

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    AppAnalysisStore.saveLastAppVersion(packageInfo.version);
    final response = await HttpService().post("/system/getUpgradeVersionManageInfo", data: {'version': packageInfo.version});

    if (response.data != null && response.isSuccess) {
      final result = AppUpgradeResult.fromJson(response.data);

      // 强制更新立即显示
      if (result.update == AppUpgradeType.force) {
        _showUpgradeDialog(result);
        return result;
      }

      // 推荐更新检查提醒周期
      if (result.update == AppUpgradeType.suggest) {
        final shouldPrompt = _shouldPromptUpgrade(now: now, lastPromptTime: lastPromptTime, remindCycle: result.tipPeriod ?? 1 // 默认1天
            );

        if (shouldPrompt) {
          _showUpgradeDialog(result);
          await _storage.write(_lastPromptTimeKey, now);
        }
      }

      return result;
    }
    return null;
  }

  static bool _shouldPromptUpgrade({
    required int now,
    required int lastPromptTime,
    required int remindCycle,
  }) {
    // 首次提示或超过提醒周期
    return lastPromptTime == 0 || (now - lastPromptTime) >= remindCycle * 24 * 3600 * 1000;
  }

  static void _showUpgradeDialog(AppUpgradeResult result) {
    useTrackEvent(EventName.update_show, extra: {EventKey.type: result.update == AppUpgradeType.force ? EventValue.mandatory_updates : EventValue.recommended_updates});
    AppAnalysisStore.saveLastAppUpdateType(result.update == AppUpgradeType.force ? EventValue.mandatory_updates : EventValue.recommended_updates);
    DialogChainManager.instance.add(DialogChain(
      AppUpgradeDialogWidget(upgrade: result),
      DialogChainManager.appUpdate,
    ));
  }
}

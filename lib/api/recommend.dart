import 'package:playlet/common/http/http.dart';
import 'package:playlet/model/recommend.dart';

class ApiRecommend {
  static Future<RecommendData?> getData() async {
    final result =
        await HttpService().post("/recommend/getNewUserRecommendInfo");
    if (result.isSuccess) {
      return RecommendData.fromJson(result.data);
    }
    return null;
  }

  /// 检查是否是新用户 <br/>
  /// data 直接返回 true false,   true代表未触发，false代表已经触发
  static Future<bool> checkNewUserRecommend() async {
    final result = await HttpService().post("/recommend/checkNewUserRecommend");
    if (result.isSuccess) {
      return result.data;
    }
    return false;
  }

  static Future<RecommendTimeData?> getTimeData() async {
    final result = await HttpService().post("/recommend/getNewUserTimeInfo");
    if (result.isSuccess) {
      return RecommendTimeData.fromJson(result.data);
    }
    return null;
  }
}

import 'package:get/instance_manager.dart';
import 'package:playlet/common/http/enum.dart';
import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/http/result.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/drama_retention.dart';

class ApiDetails {
  /// 获取短剧详情
  static Future<ShortPlayDetail?> getDetail(
    int businessId,
    String? scene, {
    void Function(Result result)? onError,
  }) async {
    final result = await HttpService().post(
      "/shortPlay/shortPlayDetail",
      data: {"businessId": businessId, "scene": scene},
    );
    Get.log("getDetail: ${result.toJson()}");
    if (result.isSuccess) {
      return ShortPlayDetail.fromJson(result.data);
    } else {
      if (onError != null) {
        onError(result);
      }
    }
    return null;
  }

  /// 获取短剧剧集列表
  static Future<List<EpisodeData>> getDramaList(
      int businessId, int pageNum, int pageSize) async {
    final result = await HttpService().post(
      "/dramaInfo/dramaList",
      data: {
        "shortPlayId": businessId,
        "pageNum": pageNum,
        "pageSize": pageSize,
      },
    );
    if (result.isSuccess) {
      return (result.data as List).map((e) => EpisodeData.fromJson(e)).toList();
    }
    return [];
  }

  /// 保存观看记录
  static Future<bool> saveWatchHistory(int dramaId, int watchTime) async {
    Result result = await HttpService().post(
      "/watchHistory/saveWatchHistory",
      data: {"dramaId": dramaId, "watchTime": watchTime},
    );
    return result.isSuccess;
  }

  /// 批量获取剧集详情
  static Future<List<EpisodeVideoData>> getEpisodeDetails(
      List<int> dramaIds, String? scene) async {
    final result = await HttpService().post(
      "/dramaInfo/dramaDetailBatch",
      data: {"businessIdList": dramaIds, "scene": scene},
    );
    if (result.isSuccess) {
      return (result.data as List)
          .map((e) => EpisodeVideoData.fromJson(e))
          .toList();
    }
    return [];
  }

  /// 剧集解锁 -> 使用金币
  static Future<UnlockResult?> unlockByCoin(int businessId,
      {bool? autoUnlock = false}) async {
    final result = await HttpService().post("/shortPlay/unlockByCoin",
        data: {"businessId": businessId, "autoUnlock": autoUnlock});
    if (result.isSuccess) {
      return UnlockResult.fromJson(result.data);
    }
    if (result.status == HttpResultStatus.resubmit) {
      return UnlockResult(unlockDramas: []);
    }
    return null;
  }

  /// 剧集解锁 -> 观看广告
  static Future<UnlockWatchAdInfoResponse?> unlockByWatchAd(int businessId,
      {bool? autoUnlock}) async {
    final result = await HttpService().post("/shortPlay/unlockByWatchAd",
        data: {"businessId": businessId, "autoUnlock": autoUnlock});
    if (result.isSuccess) {
      return UnlockWatchAdInfoResponse.fromJson(result.data);
    }
    return null;
  }

  static Future<UnlockAdCountData?> unlockAdCountData(
    int businessId, {
    String? scene,
  }) async {
    final result = await HttpService().post("/shortPlay/watchAdUnlockInfo",
        data: {"businessId": businessId, "scene": scene});
    if (result.isSuccess) {
      return UnlockAdCountData.fromJson(result.data);
    }
    return null;
  }

  /// 收藏操作
  static Future<bool> collectOp({
    required int businessId,
    required String scene,
    required int dramaId,
    required int colletType,
    required int collectSource,
    required int watchTime,
  }) async {
    final result = await HttpService().post("/collect/collectOp", data: {
      "businessId": businessId,
      "scene": scene,
      "dramaId": dramaId,
      "colletType": colletType,
      "collectSource": collectSource,
      "watchTime": watchTime
    });
    if (result.isSuccess) {
      return true;
    }
    return false;
  }

  /// 取消收藏
  static Future<bool> cancelCollect({
    required int businessId,
    required String scene,
    required int colletType,
    required int collectSource,
  }) async {
    final result = await HttpService().post("/collect/cancelCollect", data: {
      "businessId": businessId,
      "scene": scene,
      "colletType": colletType,
      "collectSource": collectSource,
    });
    if (result.isSuccess) {
      return true;
    }
    return false;
  }

  /// 获取下一部剧id
  static Future<int?> getNextShortPlay(int businessId) async {
    final result = await HttpService().post(
      "/retain/getComingSoonShortPlays",
      data: {"shortPlayId": businessId},
    );
    if (result.isSuccess) {
      return result.data.first["shortPlayId"];
    }
    return null;
  }

  /// 获取剧集挽留列表
  static Future<List<DramaRetentionItem>> getRetainList(int shortPlayId) async {
    final result = await HttpService().post(
      "/retain/getExitRetainShortPlays",
      data: {
        "shortPlayId": shortPlayId,
      },
    );
    if (result.isSuccess) {
      return result.data
          .map<DramaRetentionItem>((e) => DramaRetentionItem.fromJson(e))
          .toList();
    }
    return [];
  }
}

// BEGIN: Generated code
class Details {
  Data? data;
  String? message;
  int? status;

  Details({
    this.data,
    this.message,
    this.status,
  });

  factory Details.fromJson(Map<String, dynamic> json) => Details(
        data: Data.fromJson(json['data']),
        message: json['message'],
        status: json['status'],
      );

  Map<String, dynamic> toJson() => {
        'data': data?.toJson(),
        'message': message,
        'status': status,
      };
}

class Data {
  bool? autoUnlock;
  bool? autoUnlockEpisode;
  int? canWatchAdNum;
  NextDrama? nextDrama;
  int? totalWatchAdNum;
  List<UnlockDramas>? unlockDramas;

  Data({
    this.autoUnlock,
    this.autoUnlockEpisode,
    this.canWatchAdNum,
    this.nextDrama,
    this.totalWatchAdNum,
    this.unlockDramas,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        autoUnlock: json['autoUnlock'],
        autoUnlockEpisode: json['autoUnlockEpisode'],
        canWatchAdNum: json['canWatchAdNum'],
        nextDrama: NextDrama.fromJson(json['nextDrama']),
        totalWatchAdNum: json['totalWatchAdNum'],
        unlockDramas: (json['unlockDramas'] as List)
            .map((e) => UnlockDramas.fromJson(e))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'autoUnlock': autoUnlock,
        'autoUnlockEpisode': autoUnlockEpisode,
        'canWatchAdNum': canWatchAdNum,
        'nextDrama': nextDrama?.toJson(),
        'totalWatchAdNum': totalWatchAdNum,
        'unlockDramas': unlockDramas?.map((e) => e.toJson()).toList(),
      };
}

class NextDrama {
  int? alreadyLock;
  int? episodeNum;
  int? id;
  int? lock;
  bool? needDecrypt;
  int? price;
  int? shortPlayId;
  int? unlockType;
  int? unlockTypeAb;
  String? videoUrl;

  NextDrama({
    this.alreadyLock,
    this.episodeNum,
    this.id,
    this.lock,
    this.needDecrypt,
    this.price,
    this.shortPlayId,
    this.unlockType,
    this.unlockTypeAb,
    this.videoUrl,
  });

  factory NextDrama.fromJson(Map<String, dynamic> json) => NextDrama(
        alreadyLock: json['alreadyLock'],
        episodeNum: json['episodeNum'],
        id: json['id'],
        lock: json['lock'],
        needDecrypt: json['needDecrypt'],
        price: json['price'],
        shortPlayId: json['shortPlayId'],
        unlockType: json['unlockType'],
        unlockTypeAb: json['unlockTypeAb'],
        videoUrl: json['videoUrl'],
      );

  Map<String, dynamic> toJson() => {
        'alreadyLock': alreadyLock,
        'episodeNum': episodeNum,
        'id': id,
        'lock': lock,
        'needDecrypt': needDecrypt,
        'price': price,
        'shortPlayId': shortPlayId,
        'unlockType': unlockType,
        'unlockTypeAb': unlockTypeAb,
        'videoUrl': videoUrl,
      };
}

class UnlockDramas {
  int? episodeNum;
  int? id;
  bool? needDecrypt;
  int? price;
  int? shortPlayId;
  String? videoUrl;

  UnlockDramas({
    this.episodeNum,
    this.id,
    this.needDecrypt,
    this.price,
    this.shortPlayId,
    this.videoUrl,
  });

  factory UnlockDramas.fromJson(Map<String, dynamic> json) => UnlockDramas(
        episodeNum: json['episodeNum'],
        id: json['id'],
        needDecrypt: json['needDecrypt'],
        price: json['price'],
        shortPlayId: json['shortPlayId'],
        videoUrl: json['videoUrl'],
      );

  Map<String, dynamic> toJson() => {
        'episodeNum': episodeNum,
        'id': id,
        'needDecrypt': needDecrypt,
        'price': price,
        'shortPlayId': shortPlayId,
        'videoUrl': videoUrl,
      };
}

// END: Generated code

import 'package:playlet/model/more/banner_more_model.dart';

import '../common/http/http.dart';

class ApiMore {
  /// more接口
  static Future<BannerMoreModel?> getBannerMore(int bannerId) async {
    final response = await HttpService().post("/homeData/encrypt/getBannerMore",
        data: {"businessId": bannerId});
    if (response.isSuccess) {
      return BannerMoreModel.fromJson(response.data);
    } else {
      return null;
    }
  }
}

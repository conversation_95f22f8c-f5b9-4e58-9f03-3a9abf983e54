import 'package:get/get.dart';
import 'package:playlet/common/http/encryption_utils.dart';
import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/http/secret_repo.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/model/user/user_auto_unlock_result.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/app_service.dart';
import 'package:playlet/service/recommend_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/token.dart';

class ApiUser {
  ApiUser._();

  /// 修改用户自动解锁配置
  /// param : value 自动解锁是否开启
  static Future<bool> updateUserAutoUnlockResult({required bool value}) async {
    final response = await HttpService().post(
      "/user_config",
      data: {'autoUnlock': value},
    );
    return response.data ?? false;
  }

  /// 获取用户自动解锁配置
  static Future<UserAutoUnlockResult?> getUserAutoUnlockResult() async {
    final response = await HttpService().get(
      "/user_config",
    );
    return response.data;
  }

  /// 退出登录
  static Future<bool> loginOut() async {
    final response = await HttpService().post(
      "/login/loginOut",
    );
    return response.data ?? false;
  }

  /// 删除账户
  static Future<bool> deleteAccount() async {
    final response = await HttpService().post(
      "/login/deleteAccount",
    );
    return response.data ?? false;
  }

  /// 用户信息查询
  static Future<UserResponse?> fetchUserInfo() async {
    final response = await HttpService().post('/login/getUserInfo');
    if (response.data != null) return UserResponse.fromJson(response.data);
    return null;
  }

  /// 第三方登录
  static Future<InitLoginResult?> tripartiteLogin(int firebaseSource,
      String? authToken, String? idToken, String? userCode) async {
    final UserService userService = Get.find<UserService>();
    final String publicKey = SecretRepo.getPublicKey();
    final String apiRc4Key = SecretRepo.generateRC4Key();
    final deviceId = await AppDeviceService.instance.getFingerprint();
    final String secretKey = EncryptionUtils.encryptWithRSA(
      apiRc4Key,
      publicKey,
    );
    final result = await HttpService().post("/login/tripartiteLogin", data: {
      'authType': 1000,
      'firebaseSource': firebaseSource,
      'authToken': authToken,
      'idToken': idToken,
      'userCode': userCode,
      'registerCode': deviceId,
      'secretKey': secretKey,
    });
    if (result.isSuccess) {
      final data = InitLoginResult.fromJson(result.data);
      if (data.userResponse != null) {
        userService.setUserInfo(data.userResponse!);
        final userId = data.userResponse!.userId;
        if (userId != null) {
          await Get.find<AppService>().reportPendingLaunches(userId);
        }
      }
      if (data.token != null) {
        await Token.setToken(data.token!);
      }
      await Get.find<RecommendService>().onSwitchUser();
      return data;
    }
    return null;
  }

  /// 账号迁移
  /// userId为1.0版本的用户id
  static Future<bool> migrateAccount(int userId) async {
    final result = await HttpService()
        .post("/user/migrateAccount", data: {'userId': userId});
    return result.isSuccess;
  }

  /// 用户上报活跃时间
  static Future<bool> reportActiveTime() async {
    final result = await HttpService().post("/user/reportActiveTime");
    return result.isSuccess;
  }
}

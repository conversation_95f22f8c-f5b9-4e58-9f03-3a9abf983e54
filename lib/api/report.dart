import 'dart:io';

import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/app_device_service.dart';

class ReportApi {
  static bool _isGaidReported = false;

  /// 上报用户广告ID信息
  static Future<bool> reportGaid() async {
    if (_isGaidReported) {
      return true;
    }

    _isGaidReported = true;

    try {
      // 根据平台获取不同的广告ID
      Map<String, dynamic> params = {};
      
      if (Platform.isAndroid) {
        final gaid = await AppDeviceService.instance.getAdvertisingId();
        if (gaid != null && gaid.isNotEmpty) {
          params['gaid'] = gaid;
        }
      } else if (Platform.isIOS) {
        final idfa = await AppDeviceService.instance.getAdvertisingId();
        if (idfa != null && idfa.isNotEmpty) {
          params['idfa'] = idfa;
        }
      }
      
      // 如果没有获取到广告ID，则不上报
      if (params.isEmpty) {
        FFLog.warning("没有获取到广告ID，跳过上报");
        _isGaidReported = false;
        return false;
      }

      final result = await HttpService().post(
        "/user/reportUserAdInfo",
        data: params,
      );

      if (result.isSuccess) {
        FFLog.info("广告ID上报成功 $params");
        return true;
      } else {
        FFLog.error("广告ID上报失败: $params ${result.message} ");
        _isGaidReported = false;
        return false;
      }
    } catch (e) {
      FFLog.error("广告ID上报异常: $e");
      _isGaidReported = false;
      return false;
    }
  }
}

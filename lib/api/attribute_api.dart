import '../common/http/http.dart';
import '../service/app_device_service.dart';

class ApiAttribute {
  /// 上报归因信息
  static Future<bool> reportAttribution(
      Map<String, dynamic>? attribution) async {
    final result = await HttpService().post(
      "/adMatch/deepLinkReport",
      data: attribution,
    );

    return result.isSuccess;
  }

  /// 上报LP信息
  static Future<bool> reportLp({String? source}) async {
    // 先等待获取userAgent字符串
    final userAgent = await AppDeviceService.instance.getSystemUserAgent();

    // 然后再使用获取到的实际字符串进行请求
    final result = await HttpService().post(
      "/appReport/lpReport",
      data: {"userAgent": userAgent},
    );

    return result.isSuccess;
  }
  
  /// 获取Facebook用户信息
  static Future<Map<String, dynamic>?> getFBUserInfo() async {
    final result = await HttpService().post("/user/getFBUserInfo");
    
    if (result.isSuccess && result.data != null) {
      return result.data;
    }
    
    return null;
  }
}

import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/store/android_recover_result.dart';
import 'package:playlet/model/store/unlock_store_product_result.dart';

import '../common/http/http.dart';
import '../model/rewards/user_balance_result.dart';
import '../model/store/ios_recover_result.dart';
import '../model/store/store_product_result.dart';
import '../model/store/store_subscription_product_result.dart';
import '../utils/product_storage.dart';

class ApiStore {
  //   查询用户余额接口

// /app/user/getUserBalance
  static Future<UserBalanceResult?> getUserBalance() async {
    final response = await HttpService().post("/user/getUserBalance");
    if (response.isSuccess) {
      var result = UserBalanceResult.fromJson(response.data);
      return result;
    }
    return null;
  }

  // 获取商品列表
  static Future<StoreProductResult?> getProductList() async {
    final response =
        await HttpService().post("/sku/getCoinsStoreListBySkuModel");
    if (response.isSuccess) {
      var result = StoreProductResult.fromJson(response.data);
      return result;
    }
    return null;
  }

  // 获取订阅商品列表
  static Future<StoreSubscriptionProductResult?>
      getSubscriptionProductList() async {
    final response = await HttpService().post("/subscription/getProductListV3");
    if (response.isSuccess) {
      var result = StoreSubscriptionProductResult.fromJson(response.data);
      return result;
    }
    return null;
  }

  // 获取用户订阅信息
  static Future<String?> getSubscriptionState() async {
    final response = await HttpService().post("/subscription");
    if (response.isSuccess) {
      return response.data;
    }
    return null;
  }

  /// 获取解锁页商品列表
  static Future<UnLockStoreProductResult?> getUnlockProductList({
    required int businessId, // 必须 - 短剧id, 在banner为banner主键id
    String? scene, // 非必须 - 查询的场景：埋点对应的场景，例如collections，recently，deeplink
    int? dramaId, // 非必须 - 剧集id
    int? blockSubscription, // 非必须 - 实验值 1：实验a 2:实验b 3: 实验c 0或其它 对照组
    String? reelPlaySource, // 非必须 - 播放页面来源
  }) async {
    final data = {
      'businessId': businessId,
      if (scene != null) 'scene': scene,
      if (dramaId != null) 'dramaId': dramaId,
      if (blockSubscription != null) 'blockSubscription': blockSubscription,
      if (reelPlaySource != null) 'reelPlaySource': reelPlaySource,
    };

    final response = await HttpService()
        .post("/sku/getCoinsStoreListAndAdInfoBySkuModel", data: data);
    if (response.isSuccess) {
      var result = UnLockStoreProductResult.fromJson(response.data);

      return result;
    }
    return null;
  }

  /// 苹果订阅入账
  static Future<bool?> appleSubscribePosting({
    required String receiptData, // 必须 - 支付回执
    required String skuId, // 必须 - skuId
    required String payAmount, // 必须 - 支付金额
    required String currency, // 必须 - 货币代码
    int? skuType, // 非必须 -
    String? extra, // 非必须 -
    int? source, // 非必须 - 订阅场景
    int? receiveType, // 非必须 - level=0是对照组，只能领取当天；level=1是实验组，可以领取之前所有未领取的天数的
    String? quantity, // 非必须 - 数量
    String? introPayAmount, // 非必须 - 首充等intro活动价格
    String? introCurrency, // 非必须 - 首充等intro活动价格单位
    int? shortPlayId, // 非必须 - 短剧id
    int? episode, // 非必须 - 集数
    String? skuProductId, // 非必须 -
    String? activityId, // 非必须 - 活动ID
    String? orderNo, // 非必须 -
    String? isRestore, // 非必须 - 是否重新入账
  }) async {
    final data = {
      'receiptData': receiptData,
      'skuId': skuId,
      'payAmount': payAmount,
      'currency': currency,
      if (skuType != null) 'skuType': skuType,
      if (extra != null) 'extra': extra,
      if (source != null) 'source': source,
      if (receiveType != null) 'receiveType': receiveType,
      if (quantity != null) 'quantity': quantity,
      if (introPayAmount != null) 'introPayAmount': introPayAmount,
      if (introCurrency != null) 'introCurrency': introCurrency,
      if (shortPlayId != null) 'shortPlayId': shortPlayId,
      if (episode != null) 'episode': episode,
      if (skuProductId != null) 'skuProductId': skuProductId,
      if (activityId != null) 'activityId': activityId,
      if (orderNo != null) 'orderNo': orderNo,
      if (isRestore != null) 'isRestore': isRestore,
    };

    FFLog.info("苹果订阅入账  $data");
    final result = await postSubscriptionApple(data);
    return result;
  }

  //   为了方便补单 提取到外部
  static Future<bool> postSubscriptionApple(Map<String, dynamic> data) async {
    final response =
        await HttpService().post("/subscription/iosV2", data: data);
    if (response.isSuccess) {
      return true;
    }

    return response.isSuccess;
  }

  /// 谷歌订阅入账
  static Future<bool?> googleSubscribePosting({
    required String token, // 必须 - token
    required String skuId, // 必须 - skuId google 商店ID
    required String productId, // 必须 - productId。服务器下发数据ID
    int? source, // 非必须 - 订阅场景
    int? receiveType, // 非必须 - 奖level=0是对照组，只能领取当天；level=1是实验组，可以领取之前所有未领取的天数的
    bool? recover, // 非必须 - 是否补单
    int? shortPlayId, // 非必须 - 短剧id
    int? episode, // 非必须 - 集数

    String? oldToken, // 非必须 - 老订阅购买token, 升降级时需要传不需要
    int? changeMode, // 非必须 - 升降级模式, 升降级时需要传；0-降级，1-升级,2-普通购买不需要
    int? newProductId, // 非必须 - 降级时需要传，传新productId不需要
    int? marketingCampaignId, // 非必须 - 常规营销活动  不需要
    int? skuType,
    String? activityId, // 非必须 - 活动id (包含常规营销活动id)不需要
  }) async {
    final data = {
      'token': token,
      'skuId': skuId,
      'productId': productId,
      if (source != null) 'source': source,
      if (receiveType != null) 'receiveType': receiveType,
      if (recover != null) 'recover': recover,
      if (shortPlayId != null) 'shortPlayId': shortPlayId,
      if (episode != null) 'episode': episode,
      if (oldToken != null) 'oldToken': oldToken,
      if (changeMode != null) 'changeMode': changeMode,
      if (skuType != null) 'skuType': skuType,
      if (newProductId != null) 'newProductId': newProductId,
      if (marketingCampaignId != null)
        'marketingCampaignId': marketingCampaignId,
      if (activityId != null) 'activityId': activityId,
    };

    FFLog.info("谷歌订阅入账  $data");

    final result = await postSubscriptionAndroid(data);
    return result;
  }

//   为了方便补单 提取到外部
  static Future<bool> postSubscriptionAndroid(Map<String, dynamic> data) async {
    final response =
        await HttpService().post("/subscription/android", data: data);
    if (response.isSuccess) {
      return true;
    }
    return response.isSuccess;
  }

  ///谷歌内购支付入账
  static Future<bool?> googlePosting({
    required String purchaseData, // 必须 - 购买数据
    required String signature, // 必须 - 签名
    String? receiptData, // 非必须 - 支付回执
    String? skuId, // 非必须 - skuId
    String? price, // 非必须 - 支付金额
    String? currency, // 非必须 - 货币代码
    String? skuProductId, // 非必须 - sku产品ID
    String? skuModelConfigId, // 非必须 - sku模型配置ID
    bool? isRetain, // 非必须 - 是否膨胀商品，true：是；false：否
    String? activitySkuConfigId, // 非必须 - 活动ID
    int? skuType, // 非必须 - sku类型
    int? prizeId, // 非必须 - 奖品ID
    String? extra, // 非必须 - 额外信息
    int? shortPlayId, // 非必须 - 短剧ID
    int? episode, // 非必须 - 集数
  }) async {
    final data = {
      'purchaseData': purchaseData,
      'signature': signature,
      if (receiptData != null) 'receiptData': receiptData,
      if (currency != null) 'currency': currency,
      if (price != null) 'price': price,
      if (skuId != null) 'skuId': skuId,
      if (skuProductId != null) 'skuProductId': skuProductId,
      if (skuModelConfigId != null) 'skuModelConfigId': skuModelConfigId,
      if (isRetain != null) 'isRetain': isRetain,
      if (activitySkuConfigId != null)
        'activitySkuConfigId': activitySkuConfigId,
      if (skuType != null) 'skuType': skuType,
      if (prizeId != null) 'prizeId': prizeId,
      if (extra != null) 'extra': extra,
      if (shortPlayId != null) 'shortPlayId': shortPlayId,
      if (episode != null) 'episode': episode,
    };

    FFLog.info("谷歌内购支付入账  $data");
    // 预先保存 支付成功移除
    // if (skuId != null) {
    //   FFLog.info("提前保存支付数据  $skuId");
    //   ProductStorage.savePayment(skuId, data);
    // }

    final response =
        await HttpService().post("/pay/android/coinSkuBuy", data: data);
    if (response.isSuccess) {
      if (skuId != null) {
        FFLog.info("支付成功移除数据  $skuId");
        ProductStorage.removePayment(skuId);
      }
      return true;
    }
    return null;
  }

  /// 苹果内购支付入账
  static Future<bool?> applePosting({
    required String receiptData, // 必须 - 支付回执
    required String skuId, // 必须 - skuId
    required String payAmount, // 必须 - 支付金额
    required String currency, // 必须 - 货币代码
    String? quantity, // 非必须 - 数量
    String? skuProductId, // 非必须 - sku产品ID
    String? skuModelConfigId, // 非必须 - sku模型配置ID
    bool? isRetain, // 非必须 - 是否膨胀商品，true：是；false：否
    String? activitySkuConfigId, // 非必须 - 活动ID
    int? skuType, // 非必须 - sku类型
    int? prizeId, // 非必须 - 奖品ID
    String? extra, // 非必须 - 额外信息
    int? shortPlayId, // 非必须 - 短剧ID
    int? episode, // 非必须 - 集数
  }) async {
    final data = {
      'receiptData': receiptData,
      'skuId': skuId,
      'payAmount': payAmount,
      'currency': currency,
      if (quantity != null) 'quantity': quantity,
      if (skuProductId != null) 'skuProductId': skuProductId,
      if (skuModelConfigId != null) 'skuModelConfigId': skuModelConfigId,
      if (isRetain != null) 'isRetain': isRetain,
      if (activitySkuConfigId != null)
        'activitySkuConfigId': activitySkuConfigId,
      if (skuType != null) 'skuType': skuType,
      if (prizeId != null) 'prizeId': prizeId,
      if (extra != null) 'extra': extra,
      if (shortPlayId != null) 'shortPlayId': shortPlayId,
      if (episode != null) 'episode': episode,
    };

    FFLog.info("苹果内购支付入账  $data");
    // 预先保存 支付成功移除
    // FFLog.info("提前保存支付数据  $skuId");
    // ProductStorage.savePayment(skuId, data);

    final response =
        await HttpService().post("/pay/iOS/coinSkuBuy", data: data);
    if (response.isSuccess) {
      FFLog.info("支付成功移除数据  $skuId");
      ProductStorage.removePayment(skuId);
      return true;
    }
    return null;
  }

  /// 苹果内购恢复
  static Future<IosRecoverResult?> appleRecover({
    // required Map<String, dynamic> data
    required String receiptData, // 必须 - 支付回执
    required String skuId, // 必须 - skuId
  }) async {
    final data = {
      'receiptData': receiptData,
      'skuId': skuId,
    };

    final response = await HttpService().post("/pay/iOS/recover", data: data);
    if (response.isSuccess) {
      return IosRecoverResult.fromJson(response.data);
    }
    return null;
  }

  /// 安卓内购恢复
  static Future<AndroidRecoverResult?> googleRecover({
    // required Map<String, dynamic> data
    String? skuId, // 非必须 - 谷歌、苹果skuId
    required String purchaseData, // 必须 - 支付回执
    required String signature, // 必须 - 签名
    int? skuType, // 非必须 - 商品类别
    int? shortPlayId, // 非必须 - 短剧id
    String? skuProductId, // 非必须 - 商品主键id
  }) async {
    final payRecoverAndroidInfoRequests = {
      "payRecoverAndroidInfoRequests": [
        {
          if (skuId != null) 'skuId': skuId,
          'purchaseData': purchaseData,
          'signature': signature,
          if (skuType != null) 'skuType': skuType,
          if (shortPlayId != null) 'shortPlayId': shortPlayId,
          if (skuProductId != null) 'skuProductId': skuProductId,
        }
      ]
    };

    FFLog.info("安卓内购恢复参数  $payRecoverAndroidInfoRequests ");

    final response = await HttpService()
        .post("/pay/android/recover", data: payRecoverAndroidInfoRequests);
    if (response.isSuccess) {
      return AndroidRecoverResult.fromJson(response.data);
    }
    return null;
  }
}

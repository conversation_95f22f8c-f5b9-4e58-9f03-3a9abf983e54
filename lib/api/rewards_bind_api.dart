import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/utils/get_extension.dart';

import '../common/http/http.dart';
import '../model/rewards_bind/bind_list_result.dart';
import '../model/rewards_bind/country_count_resultdart.dart';

class ApiRewardsBind {
  ApiRewardsBind._();
  static int _phoneSendTimes = 0;
  static void increasePhoneSendTimes() {
    _phoneSendTimes += 1;
    FFLog.debug('_phoneSendTimes : $_phoneSendTimes');
  }

  static int getPhoneSendTimes() {
    FFLog.debug('_phoneSendTimes : $_phoneSendTimes');
    return _phoneSendTimes;
  }

  static void clearPhoneSendTimes() {
    _phoneSendTimes = 0;
    FFLog.debug('_phoneSendTimes : $_phoneSendTimes');
  }

  // 获取国家区号信息列表
  static Future<List<CountryCodeInfo>?> getCountryAreaCodeInfoList() async {
    final response =
        await HttpService().get("/message/getAllCountryAreaCodeInfoList");
    if (response.isSuccess) {
      var result = parseCountryCodeList(response.data);
      FFLog.info("国家区号返回。${result.toString()}");
      return result;
    } else {
      return null;
    }
  }

  // 获取绑定状态列表
  static Future<BindListResult?> getBindList() async {
    final response = await HttpService().post("/user/bindList");
    if (response.isSuccess) {
      var result = BindListResult.fromJson(response.data);
      return result;
    } else {
      return null;
    }
  }

  // 发送邮箱验证码
  static Future<bool> sendEmail(String email) async {
    final response =
        await HttpService().post("/user/sendEmail", data: {"email": email});

    if (response.isSuccess) {
      return response.isSuccess;
    } else {
      if (response.message != null) {
        Get.toast(response.message!);
      }
    }

    return false;
  }

  // 绑定用户邮箱验证码
  static Future<int?> bindByEmail(String email, String otp) async {
    final response = await HttpService()
        .post("/user/bindByEmail", data: {"email": email, "otp": otp});

    if (response.isSuccess) {
      if (response.data is Map) {
        if ((response.data as Map).containsKey('bonus')) {
          return response.data["bonus"];
        }
      }
      return null;
    } else {
      if (response.message != null) {
        Get.toast(response.message!);
      }
    }
    return null;
  }

  // 发送手机验证码
  static Future<bool> sendPhoneOtp({
    required String phone,
    required String areaCode,
    required int sendTimes,
  }) async {
    final response = await HttpService().post(
      "/message/sendMessageVerificationCode",
      data: {
        "phone": phone,
        'areaCode': areaCode,
        'sendTimes': sendTimes,
      },
    );
    if (response.isSuccess != true) {
      if (response.message != null) {
        Get.toast(response.message!);
      }
    }
    return response.isSuccess;
  }

  // 绑定用户手机号
  static Future<int?> bindByPhone({
    required String verificationCode,
    required String phone,
    required String areaCode,
  }) async {
    final response = await HttpService().post(
      "/user/bindByPhone",
      data: {
        "verificationCode": verificationCode,
        "phone": phone,
        'areaCode': areaCode
      },
    );

    if (response.isSuccess) {
      if (response.data is Map) {
        if ((response.data as Map).containsKey('bonus')) {
          return response.data["bonus"] as int;
        }
      }
      return null;
    } else {
      if (response.message != null) {
        Get.toast(response.message!);
      }
    }
    return null;
  }
}

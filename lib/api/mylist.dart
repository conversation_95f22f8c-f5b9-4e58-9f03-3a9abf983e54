import 'package:playlet/model/mylist/collection.dart';
import 'package:playlet/model/mylist/history.dart';

import '../common/http/http.dart';

class ApiMyList {
  // // 收藏列表
  // static Future<dynamic> collectList(data) async {
  //   final response =
  //       await HttpService().post("/collect/collectList", data: data);
  //   if (response.isSuccess) {
  //     return response.data;
  //   }
  //   return null;
  // }

  // // 收藏
  // static Future<dynamic> collectOp(data) async {
  //   final response = await HttpService().post("/collect/collectOp", data: data);
  //   return response.data;
  // }

  // // 取消收藏
  // static Future<dynamic> cancelCollect(data) async {
  //   final response =
  //       await HttpService().post("/collect/cancelCollect", data: data);
  //   return response.data;
  // }

  // // 批量取消收藏
  // static Future<dynamic> batchCancelCollect(data) async {
  //   final response =
  //       await HttpService().post("/collect/batchCancelCollect", data: data);
  //   return response.data;
  // }

  // // 历史列表
  // static Future<dynamic> getWatchHistoryList(data) async {
  //   final response = await HttpService()
  //       .post("/watchHistory/getWatchHistoryList", data: data);

  //   return response.data;
  // }

  // // 删除历史记录
  // static Future<dynamic> delWatchHistory(data) async {
  //   final response =
  //       await HttpService().post("/watchHistory/delWatchHistory", data: data);

  //   return response.data;
  // }

  // // 保存观看历史记录
  // static Future<dynamic> saveWatchHistory(data) async {
  //   final response =
  //       await HttpService().post("/watchHistory/saveWatchHistory", data: data);

  //   return response.data;
  // }

  ///////////////////////////////////////////////////////////////////////////

  // 收藏列表
  static Future<List<CollectionDataModel>?> getCollectList(
      CollectionRequestDataModel requestData) async {
    final response = await HttpService()
        .post("/collect/collectList", data: requestData.toJson());
    if (response.isSuccess) {
      return (response.data as List<dynamic>)
          .map((e) => CollectionDataModel.fromJson(e))
          .toList();
    }
    return null;
  }

  // 历史列表
  static Future<List<HistoryDataModel>?> getHistoryList(
      HistoryRequestDataModel requestData) async {
    final response = await HttpService()
        .post("/watchHistory/getWatchHistoryList", data: requestData.toJson());

    if (response.isSuccess) {
      return (response.data as List<dynamic>)
          .map((e) => HistoryDataModel.fromJson(e))
          .toList();
    }
    return null;
  }

  // 收藏
  static Future<bool> collectShort({
    required int businessId,
    required String scene,
    required int dramaId,
    required int colletType,
    required int collectSource,
    required int watchTime,
  }) async {
    final response = await HttpService().post("/collect/collectOp", data: {
      "businessId": businessId,
      "scene": scene,
      "dramaId": dramaId,
      "colletType": colletType,
      "collectSource": collectSource,
      "watchTime": watchTime,
    });
    if (response.isSuccess) {
      if (response.data is bool) {
        return response.data as bool;
      }
    }
    return false;
  }

  /// 取消收藏
  static Future<bool> cancelCollectShort({
    required int businessId,
    required String scene,
    required int colletType,
    required int collectSource,
  }) async {
    final response = await HttpService().post("/collect/cancelCollect", data: {
      "businessId": businessId,
      "scene": scene,
      "colletType": colletType,
      "collectSource": collectSource,
    });
    if (response.isSuccess) {
      if (response.data is bool) {
        return response.data as bool;
      }
    }
    return false;
  }

  /// 批量取消收藏
  static Future<bool> batchCancelCollectShort({
    required List<int> businessIdList,
  }) async {
    final response =
        await HttpService().post("/collect/batchCancelCollect", data: {
      "businessIdList": businessIdList,
    });
    if (response.isSuccess) {
      if (response.data is bool) {
        return response.data as bool;
      }
    }
    return false;
  }

  /// 删除历史记录
  static Future<bool> deleteWatchHistoryShorts({
    required List<int> businessIdList,
  }) async {
    final response =
        await HttpService().post("/watchHistory/delWatchHistory", data: {
      "businessIdList": businessIdList,
    });
    if (response.isSuccess) {
      if (response.data is bool) {
        return response.data as bool;
      }
    }
    return false;
  }
}

import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/http/result.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';

class ResourceBitApi {
  // 获取资源位配置
  static Future<Result> getResourceList() async {
    List<ResourceBitExtraType> extraTypes = [
      ResourceBitExtraType.bottomTab,
      ResourceBitExtraType.myProfileBanner,
      ResourceBitExtraType.myListBanner,
      ResourceBitExtraType.feed,
    ];

    final data = {
      "extraTypes": extraTypes.map((e) => e.toInt()).toList().join(","),
      "supportSkipTypes": "0,1,2,3,4",
    };
    final result = await HttpService().post("/activityResource/resourceListByTypes", data: data);
    return result;
  }
}
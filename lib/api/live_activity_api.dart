import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/http/result.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/live_activity/model/live_activity_config.dart';
import 'package:playlet/service/live_activity/model/live_activity_top_shorts.dart';

class LiveActivityApi {
  // 获取实时活动配置
  static Future<List<LiveActivityConfig>?> getLiveActivityConfig() async {
    final result = await HttpService().get("/app/realtimeActivity/config");
    if (!result.isSuccess) {
      return null;
    }
    final dataList = result.data as List?;
    if (dataList == null) {
      return null;
    }
    try {
      List<LiveActivityConfig> configList = [];
      for (var data in dataList) {
        LiveActivityConfig config = LiveActivityConfig.fromJson(data);
        configList.add(config);
      }
      return configList;
    } catch (e) {
      FFLog.error('获取实时活动配置失败: $e');
      return null;
    }
  }

  // 获取排名前30的剧
  static Future<List<LiveActivityTopShorts>?> getTopRechargeShortPlays() async {
    final result = await HttpService().post("/shortPlay/getTopRechargeShortPlays");
    if (!result.isSuccess) {
      return null;
    }
    try {
      final dataList = result.data?["shortPlayRealActiveRecommendResponses"] as List?;
      if (dataList == null || dataList.isEmpty) {
        return [];
      }
      List<LiveActivityTopShorts> topShortsList = [];
      for (var item in dataList) {
        final topShorts = LiveActivityTopShorts.fromJson(item);
        topShortsList.add(topShorts);
      }
      return topShortsList;
    } catch (e) {
      FFLog.error('获取排名前30的剧失败: $e');
      return [];
    }
  }
}
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/utils/safe_storage.dart';

class RecentlyWatchModel {
  int shortPlayId;
  String shortPlayName;
  String imageUrl;
  int totalEpisodes;
  int episodeNum;

  RecentlyWatchModel({
    required this.shortPlayId,
    required this.shortPlayName,
    required this.imageUrl,
    required this.totalEpisodes,
    required this.episodeNum,
  });

  factory RecentlyWatchModel.fromJson(Map<String, dynamic> json) {
    return RecentlyWatchModel(
      shortPlayId: json['shortPlayId'],
      shortPlayName: json['shortPlayName'],
      imageUrl: json['imageUrl'],
      totalEpisodes: json['totalEpisodes'],
      episodeNum: json['episodeNum'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'shortPlayId': shortPlayId,
      'shortPlayName': shortPlayName,
      'imageUrl': imageUrl,
      'totalEpisodes': totalEpisodes,
      'episodeNum': episodeNum,
    };
  }
}

class RecentlyWatchService extends GetxService {
  /// 是否显示在首页
  RxBool showOnHome = true.obs;
  RecentlyWatchModel? recentlyWatchModel;

  SafeStorage get _storage => SafeStorage();
  String get _key => 'recentlyWatch';

  /// 获取最近观看记录
  RecentlyWatchModel? getRecentlyWatchModel() {
    try {
      Map<String, dynamic>? json = _storage.read(_key);
      if (json == null || json.isEmpty) {
        return null;
      }
      return RecentlyWatchModel.fromJson(json);
    } catch (e) {
      FFLog.error('获取最近观看记录失败 $e');
      return null;
    }
  }

  /// 保存最近观看记录
  Future<void> setRecentlyWatchModel(RecentlyWatchModel model) async {
    try {
      recentlyWatchModel = model;
      await _storage.write(_key, model.toJson());
    } catch (e) {
      FFLog.error('保存最近观看记录失败 $e');
    }
  }

  @override
  void onInit() {
    super.onInit();

    recentlyWatchModel = getRecentlyWatchModel();
  }
}
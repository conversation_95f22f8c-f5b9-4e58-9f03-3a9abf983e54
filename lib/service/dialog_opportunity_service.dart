import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/safe_storage.dart';

class DialogOpportunityService extends GetxService {
  final storage = SafeStorage();
  DateFormat format = DateFormat('yyyy-dd-MM');

  initProfileLoginDialog() {
  UserService userService = Get.find<UserService>();

  /// 判断：已登录不展示
  if (userService.isLogin == true) {
    return false;
  }
    String? date = storage.read('profile_login_dialog');
    if (date != null) {
      if (date == format.format(DateTime.now())) {
        return false;
      }
    }
    storage.write('profile_login_dialog', format.format(DateTime.now()));
    return true;
  }
}

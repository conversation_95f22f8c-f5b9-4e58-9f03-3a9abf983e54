import 'package:playlet/api/rewards_api.dart';
import 'package:playlet/utils/ff_userdefault_util.dart';

class GetWatchAdTaskService {
  static final GetWatchAdTaskService _instance = GetWatchAdTaskService._internal();
  factory GetWatchAdTaskService() {
    return _instance;
  }
  GetWatchAdTaskService._internal();

  void getRewardAdTask() async{
    var result = await ApiRewards.getADTaskList();
    var adBonusResponses = result?.adBonusResponses;
    var completeCount = 0;
    if (result != null && (adBonusResponses?.length ?? 0) > 0){
      adBonusResponses?.forEach((element) {
        if (element.watched == true){
          completeCount += 1;
        }
      });
      FFUserDefault.watchAdRewardCount.value = completeCount;
    }
  }
}
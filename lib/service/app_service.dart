import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/notification/android_notification_manager.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/app_analysis.dart';
import 'package:playlet/utils/app_usage_statistics.dart';
import 'package:playlet/utils/notification_util.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';

class AppService extends GetxService {
  final SafeStorage _storage = SafeStorage();

  static const String _lastLaunchTimeKey = "lastLaunchTime";
  static const String _pendingLaunchReportKey = "pendingLaunchReports";
  static const String _firstOpen = "firstOpen";
  static int _lastReportTime = 0;
  _LifecycleEventHandler? _lifecycleHandler;
  bool _isInitialized = false;

  /// 是否是冷启动
  bool isColdOpen = true;

  // 获取启动app来源
  String getAppLaunchFrom() {
    return AppAnalysisStore.getAppLaunchFrom();
  }

  bool isFirstOpen() {
    return _storage.read<bool>(_firstOpen) ?? true;
  }

  void savaFirstOpen(bool firstOpen) async {
    await _storage.write(_firstOpen, firstOpen);
  }

  void trackInstallEvent() {
    if (isFirstOpen()) {
      useTrackEvent(EventName.app_install, priority: TrackEventPriority.high);
      savaFirstOpen(false);
    }
  }

  // 初始化生命周期监听
  void initLifecycleObserver() {
    if (_isInitialized) return;
    _isInitialized = true;
    _lifecycleHandler = _LifecycleEventHandler(
      resumeCallBack: () => {
        recordAppLaunch(),
        AppUsageStatistics.onForeground(),
        NotificationUtil.onForeground(),
        AndroidNotificationManager.instance.stopAlert(),
      },
      pausedCallBack: () => {
        reportAppBackground(),
        AppUsageStatistics.onBackground(),
        NotificationUtil.onBackground(),
        AndroidNotificationManager.instance.startAlert(),
      },
      detachedCallBack: () => reportAppDetached(),
    );
    WidgetsBinding.instance.addObserver(_lifecycleHandler!);
  }

  /// 移除应用生命周期观察者
  void _removeLifecycleObserver() {
    if (_lifecycleHandler != null) {
      WidgetsBinding.instance.removeObserver(_lifecycleHandler!);
      _lifecycleHandler = null;
    }
  }

  @override
  void onClose() {
    // 确保在服务销毁时移除监听
    _removeLifecycleObserver();
    super.onClose();
  }

  //记录启动事件
  Future<void> recordAppLaunch() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    await _storage.write(_lastLaunchTimeKey, now);

    // 获取当前uid
    final uid = Get.find<UserService>().userInfo.value?.userId;

    if (uid != null) {
      // 有uid立即上报
      _reportLaunchEvent(uid, getAppLaunchFrom());
    } else {
      // 无uid先存储待上报记录
     await _storage.write(_pendingLaunchReportKey, {
        'timestamp': now,
        'source': getAppLaunchFrom(),
      });
    }
  }

  // 当获取到uid时调用此方法补报
  Future<void> reportPendingLaunches(String uid) async {
   final pendingReport = _storage.read<Map<String, dynamic>>(_pendingLaunchReportKey);
    if (pendingReport != null) {
      _reportLaunchEvent(uid, pendingReport['source'] as String);
      await _storage.remove(_pendingLaunchReportKey);
    }
  }

  void reportAppBackground() {
    if (Get.find<UserService>().userInfo.value?.userId == null) {
      return;
    }
    useTrackEvent(EventName.app_active_background);
  }

  void reportAppDetached() {
    if (Get.find<UserService>().userInfo.value?.userId == null) {
      return;
    }
    useTrackEvent(EventName.app_close);
  }

  // 上报逻辑
  void _reportLaunchEvent(String uid, String source) {
    final now = DateTime.now().millisecondsSinceEpoch;
    if (now - _lastReportTime < 1500) {
      return;
    }
     _lastReportTime = now;
    useTrackEvent(EventName.app_start, extra: {EventKey.from: source}, priority: TrackEventPriority.high);
    FFLog.info("上报启动事件，uid: $uid, source: $source");
  }
}

class _LifecycleEventHandler extends WidgetsBindingObserver {
  final VoidCallback? resumeCallBack;
  final VoidCallback? pausedCallBack;
  final VoidCallback? detachedCallBack;

  _LifecycleEventHandler({
    this.resumeCallBack,
    this.pausedCallBack,
    this.detachedCallBack,
  });

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      resumeCallBack?.call();
    } else if (state == AppLifecycleState.paused) {
      pausedCallBack?.call();
    } else if (state == AppLifecycleState.detached) {
      detachedCallBack?.call();
    }
  }
}

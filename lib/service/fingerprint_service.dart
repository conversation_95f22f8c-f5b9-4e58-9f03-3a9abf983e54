import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:dlink_fingerprint/fingerprint_core.dart';

class FingerprintService extends GetxService {
  bool isFingerprint = false;
  String? fingerprint;
  String? error;

  Future<void> init() async {
    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    getFingerprint();
  }

  Future<String?> getFingerprint() async {
    try {
      fingerprint = await FingerprintCore.instance.generate();
      if (fingerprint != null) {
        error = null;
        isFingerprint = true;
        Get.log("Fingerprint $fingerprint");
        return fingerprint;
      } else {
        error = 'Failed to get fingerprint.';
        return null;
      }
    } on PlatformException {
      isFingerprint = false;
      error = 'Failed to get fingerprint.';
      return null;
    }
  }
}

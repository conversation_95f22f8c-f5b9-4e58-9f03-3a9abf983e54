import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/api/base.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/widget/good_review_popup.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain_manager.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/safe_storage.dart';

class GoodReviewService extends GetxService {
  static const storageTimesKey = "good_review_service";
  static const storageShowKey = "good_review_service";

  final SafeStorage safeStorage = SafeStorage();

  bool isConfigShow = false;

  DateTime? feedStartTime;
  bool feedTimeShow = false;
  DateTime? detailTimes;
  bool detailTimeShow = false;

  final UserService userService = Get.find<UserService>();

  ///时长，单位毫秒
  int times = 0;

  String configVal = "0";

  @override
  void onInit() {
    super.onInit();
    if (getShow()) return;
    appLanch();
    ApiBase.getConfig(defaultKey: "positive_review_switch").then((value) {
      if (value.data != null) {
        configVal = value.data;
      } else {
        FFLog.error("getConfigByKey  positive_review_switch 为空");
      }

      onUpdateShow();
      userService.userInfo.listen((val) {
        onUpdateShow();
      });
    });
  }

  void onUpdateShow() async {
    final user = await userService.getUserInfo();
    if (Platform.isIOS) {
      if (user.isUserSpecial == false) {
        if (configVal == "1") {
          isConfigShow = true;
        } else {
          isConfigShow = false;
        }
      }
    } else {
      if (configVal == "1") {
        isConfigShow = true;
      } else {
        isConfigShow = false;
      }
    }
    FFLog.info(
        "userService.userInfo: ${user.isUserSpecial} isConfigShow: $isConfigShow",
        tag: "GoodReviewService");
  }

  Future appLanch() async {
    final isToShow = getShow();
    if (isToShow) {
      FFLog.info("弹出过了不再记录", tag: "GoodReviewService");
      return;
    }
    // 获取之前保存的时间戳列表
    List<int> timestamps = loadTimestamps();
    DateTime now = DateTime.now();

    // 过滤出最近7天内的记录
    List<int> recentTimestamps = timestamps.where((ts) {
      DateTime date = DateTime.fromMillisecondsSinceEpoch(ts);
      return date.isAfter(now.subtract(const Duration(days: 7)));
    }).toList()
      ..add(now.millisecondsSinceEpoch); // 添加当前时间戳
    // 保存更新后的时间戳数组
    await saveTimestamps(recentTimestamps);
  }

  List<String> parseDatesFromStorage(String? datesRaw) {
    if (datesRaw == null || datesRaw.trim().isEmpty) return [];
    try {
      final decoded = jsonDecode(datesRaw) as List;
      return decoded.map((e) => e.toString()).toList();
    } catch (e) {
      return []; // 解析失败返回空列表
    }
  }

  Future<bool> isCheckAndTrigger() async {
    // 获取之前保存的时间戳列表
    List<int> timestamps = loadTimestamps();
    DateTime now = DateTime.now();
    // 过滤出最近7天内的记录
    List<int> recentTimestamps = timestamps.where((ts) {
      DateTime date = DateTime.fromMillisecondsSinceEpoch(ts);
      return date.isAfter(now.subtract(const Duration(days: 7)));
    }).toList();
    Get.log("GoodReviewService recentTimestamps: ${recentTimestamps.length}");
    // 判断是否累计3次及以上
    return recentTimestamps.length >= 3;
  }

  List<int> loadTimestamps() {
    String? encoded = safeStorage.read<String>(storageTimesKey);
    if (encoded == null) return [];
    try {
      final List<dynamic> decoded = jsonDecode(encoded);
      return decoded.map((e) => e as int).toList();
    } catch (e) {
      return [];
    }
  }

  Future<void> saveTimestamps(List<int> timestamps) async {
    String encoded = jsonEncode(timestamps); // 将 List<int> 转为 JSON 字符串
    await safeStorage.write(storageTimesKey, encoded);
  }

  /// 是否已经弹出过了
  bool getShow() {
    bool? val = safeStorage.read(storageShowKey);
    Get.log("GoodReviewService getShow: $val ${val == true}");
    if (val == null) return false;
    return val == true ? true : false;
  }

  /// 页面返回时调用该方法触发好评弹窗
  Future<void> onShow() async {
    if (isConfigShow == false) return;
    final isCheck = await isCheckAndTrigger();
    final isToShow = getShow();
    FFLog.info(
        "onHomeShow isConfigShow $isConfigShow isCheckAndTrigger $isCheck getShow $isToShow detailTimeShow: $detailTimeShow feedTimeShow: $feedTimeShow",
        tag: "GoodReviewService");
    if (isToShow) return;
    if (isCheck) {
      await SmartDialog.show(
        tag: GoodReviewPopup.tag,
        keepSingle: true,
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.6),
        builder: (context) => const GoodReviewPopup(),
        onDismiss: () async {
          await safeStorage.write(storageShowKey, true);
        },
      );
    }
  }

  /// 返回首页后调用该方法触发好评弹窗
  Future<void> onHomeShow() async {
    final isCheck = await isCheckAndTrigger();
    final isToShow = getShow();
    FFLog.info(
        "onHomeShow isConfigShow $isConfigShow isCheckAndTrigger $isCheck getShow $isToShow detailTimeShow: $detailTimeShow feedTimeShow: $feedTimeShow",
        tag: "GoodReviewService");
    if (isConfigShow == false) return;
    if (isToShow) return;
    if (!isCheck) return;
    if (detailTimeShow == false && feedTimeShow == false) return;
    DialogChainManager.instance.addOpportunityChain(
      DialogChain(
        const GoodReviewPopup(),
        markTag: 'GoodReviewService',
        DialogChainManager.goodReviewGuide,
        dialogOptions: DialogOptions(
          tag: GoodReviewPopup.tag,
          keepSingle: true,
          alignment: Alignment.bottomCenter,
          maskColor: Colors.black.withValues(alpha: 0.6),
          onDismiss: () async {
            await safeStorage.write(storageShowKey, true);
            detailTimeShow = false;
            feedTimeShow = false;
          },
        ),
      ),
    );
  }

  void onStartDetail() {
    detailTimes = DateTime.now();
  }

  void onEndDetail() {
    if (detailTimes == null) return;
    times = times + DateTime.now().difference(detailTimes!).inMilliseconds;
    Get.log("GoodReviewService onEndDetail times: $times");
    detailTimes = null;
    if (times > 20 * 60 * 1000) {
      detailTimeShow = true;
    }
  }

  void onStartFeed() {
    feedStartTime = DateTime.now();
  }

  void onEndFeed() {
    if (feedStartTime == null) return;
    times = times + DateTime.now().difference(feedStartTime!).inMilliseconds;
    feedStartTime = null;
    Get.log("GoodReviewService onEndFeed times: $times");
    if (times > 20 * 60 * 1000) {
      feedTimeShow = true;
    }
  }
}

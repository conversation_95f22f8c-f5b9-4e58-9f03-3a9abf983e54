import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dlink_analytics/analytics.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:dlink_analytics/analytics_config.dart';
import 'package:dlink_analytics/analytics_config_builder.dart';
import 'package:dlink_analytics/analytics_core.dart';
import 'package:dlink_analytics/data_package.dart';
import 'package:dlink_analytics/data_sender.dart';
import 'package:dlink_analytics/default_data_service.dart';
import 'package:playlet/api/base.dart';
import 'package:playlet/common/http/result.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/attribute_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/app_language.dart';
import 'package:playlet/utils/map_extension.dart';
import 'package:playlet/utils/track_event.dart';

class AnalyticsService extends GetxService {
  /// Firebase 埋点上报开关
  bool? firebaseEventSwitch;
  StreamSubscription? _userInfoSubscription;

  @override
  void onInit() {
    init();
    super.onInit();
  }

  @override
  void onClose() {
    _userInfoSubscription?.cancel();
    _userInfoSubscription = null;
    super.onClose();
  }

  void init() {
    AnalyticsConfig config = AnalyticsConfigBuilder()
        // TODO: 埋点上报参数配置
        //[require] Developers need to pass in the account ID and dev token.
        .setAccountId(Config.analyticsAccountId)
        .setDevToken(Config.analyticsDevToken)
        //[require] Developers need to pass in the key for event data.
        //It is strongly recommended that you set different keys for different users.
        .setCryptKey(Config.analyticsCryptKey)
        //[optional] The minimum interval for reporting event data, in seconds.Default 10 seconds.
        .setMinReportInterval(10)
        //[optional] The maximum number of event data reported each time.Default 50.
        .setMaxReportNumEachTime(50)
        .build();
    //[require] Call this method to initialize the SDK
    AnalyticsCore.instance.setup(config);

    AnalyticsCore.instance
        .register("default", DefaultDataService(RequestDataSender()));
  }

  // 立即上报点位
  void immediatelyReport() {
      AnalyticsCore.instance.flush();
  }

  void listenUserInfo() {
    final userService = Get.find<UserService>();

    _userInfoSubscription?.cancel();
    _userInfoSubscription = userService.userInfo.listen((userInfo) {
      setUidToAnalytics();
    });
  }

  Future<void> setSystemInfoToAnalytics() async {
    final deviceInfo = AppDeviceService.instance;

    // 获取设备型号
    String? deviceModel = await deviceInfo.getDeviceModel();

    // 获取系统语言
    String language = deviceInfo.getSystemLanguage();

    // 获取 App 语言
    String appLanguage = AppLanguage.getCurrentLanguage();

    // 获取系统名称
    String? systemName;
    if (Platform.isAndroid) {
      systemName = 'Android';
    } else if (Platform.isIOS) {
      systemName = 'iOS';
    }

    // 获取设备唯一标识
    String? deviceId = await AppDeviceService.instance.getFingerprint();

    // 获取 IP 地址
    String? ipAddress;
    try {
      AttributeService attributeService = Get.find<AttributeService>();
      ipAddress = attributeService.clientIpForApi;
    } catch (e) {
      FFLog.debug('get ip failed: $e');
    }

    AnalyticsCore.instance.addCustomParams({
      'model': deviceModel ?? '',
      'language': language,
      'app_language': appLanguage,
      'system_name': systemName ?? '',
      'device_id': deviceId ?? '',
      'ip_address': ipAddress ?? '',
    });
  }

  void setUidToAnalytics() {
    final userService = Get.find<UserService>();
    final uid = userService.userInfo.value?.userId;
    if (uid == null) {
      return;
    }
    AnalyticsCore.instance.addCustomParams({'uid': uid});
  }
}

//Send the DataPackage to your own server
class RequestDataSender extends DataSender {
  @override
  void send(DataPackage dataPackage, OnFinished finished) async {
    // 向自己的服务器发送数据
    final status = await sendRequest(dataPackage);

    // 向 Firebase 发送数据
    await _sendToFirebase(dataPackage);
    
    finished.call(status);
  }

  Future<void> _sendToFirebase(DataPackage data) async {
    try {
      bool enable = await _getFirebaseEventSwitch();
      if (enable == false) {
        // 开关关闭状态，不上报
        return;
      }

      final analytics = FirebaseAnalytics.instance;
      // 先打一个点：dink_event_callback
      await analytics.logEvent(
        name: _normalizeEventName("dink_event_callback")
      );

      // 再解析上报dlink返回的事件列表
      final eventsJson = data.eventsJson;
      if (eventsJson == null || eventsJson.isEmpty) {
        return;
      }

      List<dynamic>? jsonList = jsonDecode(eventsJson);
      if (jsonList == null || jsonList.isEmpty) {
        return;
      }

      // 遍历所有事件并上报到 Firebase
      for (var event in jsonList) {
        if (event is! Map<String, dynamic>) {
          continue;
        }
        String? eventName = event.safeGet<String>('eventName');
        if (eventName == null || eventName.isEmpty) {
          continue;
        }
        TrackEventPriority? priority = TrackEventPriority.fromInt(event.safeGet<int>('priority'));
        if (priority == null || priority != TrackEventPriority.high) {
          // 非高优先级的不上报到firebase
          continue;
        }
        
        // 过滤和转换参数，Firebase 只接受特定类型的参数值
        final Map<String, Object> firebaseParams = {};
        event.forEach((key, value) {
          if (key == 'eventName') {
            return;
          }
          if (key == 'customParams') {
            // customParams内容太多了，firebase的参数值最多只能100字符，所以这个字段过滤掉
            return;
          }
          if (value is String || value is num || value is bool) {
            firebaseParams[key] = value;
          } else if (value != null) {
            firebaseParams[key] = value.toString();
          }
        });
        
        await analytics.logEvent(
          name: _normalizeEventName(eventName),
          parameters: firebaseParams,
        );
      }
      FFLog.info('Firebase event logged successful', tag: "AnalyticsService");
    } catch (e) {
      FFLog.error('Firebase analytics error: $e', tag: "AnalyticsService");
    }
  }
  
  // Firebase 事件名称有格式要求：只能包含字母数字和下划线，长度不超过 40 个字符
  String _normalizeEventName(String eventName) {
    // 移除非字母数字和下划线字符
    final normalized = eventName.replaceAll(RegExp(r'[^\w]'), '_');
    // 确保长度不超过 40 个字符
    return normalized.length > 40 ? normalized.substring(0, 40) : normalized;
  }

  Future<bool> sendRequest(DataPackage data) async {
    final dio = Dio();
    final version = await AppDeviceService.instance.getVersion();
    Map<String, dynamic> map = {};

    _addToMap(map, 'eventList', data.eventsJson);
    _addToMap(map, 'signVersion', data.signVersion);
    _addToMap(map, 'eventsSign', data.eventsSign);
    _addToMap(map, 'signTime', data.signTime);
    _addToMap(map, 'localTimeLong', DateTime.now().millisecondsSinceEpoch);
    _addToMap(map, 'appVersion', version);
    try {
      final result = await dio.post(Config.analyticsServerUrl, data: map);
      FFLog.info(result, tag: "AnalyticsService");
      if (result.data != null) {
        final data = Result.fromJson(result.data);
        if (data.isSuccess) {
          return true;
        }
        return false;
      }
    } on DioException catch (e) {
      FFLog.error(e, tag: "AnalyticsService");
      return false;
    }
    return false;
  }

  void _addToMap(Map<String, dynamic> map, String key, dynamic value) {
    if (value == null) {
      return;
    }
    map[key] = value;
  }
  
  /// 获取 Firebase埋点上报 开关状态
  Future<bool> _getFirebaseEventSwitch() async {
    AnalyticsService analyticsService = Get.find<AnalyticsService>();
    bool? enable = analyticsService.firebaseEventSwitch;
    if (enable != null) {
      return enable;
    }
    Result result = await ApiBase.getConfig(iosKey: "ios.start.config", androidKey: "android.start.config");
    if (!result.isSuccess) {
      analyticsService.firebaseEventSwitch = false;
      return false;
    }
    final data = result.data;
    if (data == null) {
      analyticsService.firebaseEventSwitch = false;
      return false;
    }

    try {
      final json = jsonDecode(data);
      if (json == null || json is! Map<String, dynamic>) {
        analyticsService.firebaseEventSwitch = false;
        return false;
      }
      enable = json.safeGet<bool>("event.report.firebase.switch") ?? false;
      analyticsService.firebaseEventSwitch = enable;
      return enable;
    } catch (e) {
      FFLog.error('get firebase event switch failed: $e');
      analyticsService.firebaseEventSwitch = false;
      return false;
    }
  }
}

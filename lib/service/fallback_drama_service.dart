import 'package:get/get.dart';
import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/drama_fallback_item.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/attribute_service.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'dart:async';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain_manager.dart';

/// 兜底剧服务
/// 用于获取和播放兜底剧
class FallbackDramaService extends GetxService {
  static const String tag = "FallbackDramaService";

  // 缓存的兜底剧信息
  DramaFallbackItem? _cachedFallbackDramas;

  // 是否正在获取兜底剧
  bool _isFetchingDramas = false;

  // MMKV存储键
  static const String _fallbackDramaStatusKey = "fallback_drama_status";
  static const String _firstLaunchFallbackShownKey =
      "first_launch_fallback_shown";

  // 兜底剧状态
  static const String statusEmpty = ""; // 未播放过
  static const String statusPlayed = "played"; // 已播放
  static const String statusFailed = "failed"; // 播放失败

  // 服务实例
  final AttributeService attributeService = Get.find<AttributeService>();

  // 对话框管理器实例
  final DialogChainManager _dialogChainManager = DialogChainManager.instance;

  // 首次启动弹窗关闭后的定时器
  Timer? _firstLaunchTimer;

  // 是否已经注册了对话框关闭监听
  bool _hasRegisteredDialogListener = false;

  // 首次启动检查次数计数
  int _checkCount = 0;

  // 最大检查次数 (3秒 / 500ms = 6次)
  static const int _maxCheckCount = 6;

  /// 获取兜底剧状态
  /// 返回兜底剧状态：空字符串(未播放)、"played"(已播放)、"failed"(播放失败)
  String getFallbackDramaStatus() {
    final storage = SafeStorage();
    return storage.read<String>(_fallbackDramaStatusKey) ?? statusEmpty;
  }

  /// 设置兜底剧状态
  /// 将兜底剧状态记录到MMKV中
  Future<void> setFallbackDramaStatus(String status) async {
    final storage = SafeStorage();
    await storage.write(_fallbackDramaStatusKey, status);
    FFLog.info("已记录兜底剧状态: $status", tag: tag);
  }

  /// 获取兜底剧信息
  /// 返回兜底剧信息，如果获取失败则返回null
  /// 只有在兜底剧状态为空时才会调用此方法
  Future<DramaFallbackItem?> getFallbackDramas() async {
    // 检查状态，只有在状态为空时才继续
    String fallbackDramaStatus = getFallbackDramaStatus();
    if (fallbackDramaStatus != statusEmpty) {
      FFLog.info("兜底剧状态=$fallbackDramaStatus，不获取兜底剧信息", tag: tag);
      return null;
    }

    _initDialogCloseListener();
    
    // // 检查是否正在获取中
    if (_isFetchingDramas) {
      FFLog.info("正在获取兜底剧信息，不重复获取", tag: tag);
      return null;
    }

    // // 检查缓存是否存在
    if (_cachedFallbackDramas != null) {
      FFLog.info("使用缓存的兜底剧信息", tag: tag);
      return _cachedFallbackDramas;
    }

    try {
      _isFetchingDramas = true;
      FFLog.info("开始获取兜底剧信息", tag: tag);
      final result = await HttpService().post(
        "/shortPlay/getPopularShortPlay",
      );
      FFLog.info("获取兜底剧信息结果: $result", tag: tag);
      if (result.isSuccess) {
        final item = DramaFallbackItem.fromJson(result.data);

        // 检查是否有 shortPlayCode
        if (item.shortPlayCode != null) {
          FFLog.info(
              "成功解析兜底剧信息: shortPlayCode=${item.shortPlayCode}, shortPlayName=${item.shortPlayName}",
              tag: tag);
        } else {
          FFLog.error("解析后的兜底剧信息缺少 shortPlayCode", tag: tag);
        }

        // 更新缓存
        _cachedFallbackDramas = item;

        FFLog.info("成功获取兜底剧信息", tag: tag);
        return item;
      } else {
        FFLog.error("获取兜底剧信息失败: ${result.message}", tag: tag);
        return null;
      }
    } catch (e) {
      FFLog.error("获取兜底剧信息异常: $e", tag: tag);
      return null;
    } finally {
      _isFetchingDramas = false;
    }
  }

  /// 启动兜底剧检查器
  /// 直接播放兜底剧
  Future<bool> startFallbackDramaChecker() async {
    try {
      // 检查兜底剧状态，只有在状态为空时才继续
      final status = getFallbackDramaStatus();
      if (status != statusEmpty) {
        FFLog.info("兜底剧状态非空，不播放兜底剧: $status", tag: tag);
        return false;
      }

      // 检查当前页面是否是详情页
      if (Get.currentRoute == Routes.detailsPage) {
        FFLog.info("当前在详情页，标记为播放失败", tag: tag);
        await setFallbackDramaStatus(statusFailed);
        return false;
      }

      // 获取兜底剧信息
      final fallbackDrama = await getFallbackDramas();

      // 获取兜底剧ID（如果有）
      if (fallbackDrama == null || fallbackDrama.shortPlayCode == null) {
        FFLog.info("没有可用的兜底剧ID，标记为播放失败", tag: tag);
        await setFallbackDramaStatus(statusFailed);
        return false;
      }

      // 确保 shortPlayCode 非空
      final int shortPlayCode = fallbackDrama.shortPlayCode!;
      FFLog.info("获取到兜底剧ID: $shortPlayCode", tag: tag);

      // 满足所有条件，可以播放兜底剧
      FFLog.info("开始播放兜底剧，ID: $shortPlayCode", tag: tag);
      AppNavigator.startDetailsPage(DetailsOptions(
        businessId: shortPlayCode, // 前面已经检查过非空
        playerEpisodeIndex: 0, // 默认从第一集开始播放
        scene: EventValue.defaultValue, // 标记来源
        from: EventValue.defaultValue, // 兜底剧来源
      ));

      // 记录兜底剧已播放
      await setFallbackDramaStatus(statusPlayed);
      return true;
    } catch (e) {
      FFLog.error("播放兜底剧异常: $e", tag: tag);
      await setFallbackDramaStatus(statusFailed);
      return false;
    }
  }

  /// 检查是否已经显示过首次启动兜底剧
  bool isFirstLaunchFallbackShown() {
    final storage = SafeStorage();
    return storage.read<bool>(_firstLaunchFallbackShownKey) ?? false;
  }

  /// 标记首次启动兜底剧已显示
  Future<void> markFirstLaunchFallbackAsShown() async {
    final storage = SafeStorage();
    await storage.write(_firstLaunchFallbackShownKey, true);
    FFLog.info("已标记首次启动兜底剧为已显示", tag: tag);
  }

  /// 处理首个弹窗关闭事件
  /// 在首个弹窗关闭后，启动定时检查，最多等待3秒
  Future<void> onFirstPopupClosed() async {
    // 如果不是首次启动或已经显示过，则不执行
    if (isFirstLaunchFallbackShown()) {
      FFLog.info("非首次启动或已显示过，不执行首次启动兜底剧逻辑", tag: tag);
      return;
    }

    FFLog.info("首个弹窗关闭，启动定时检查归因状态", tag: tag);

    // 取消已有定时器
    _cancelFirstLaunchTimer();

    // 标记为已显示，避免重复触发
    await markFirstLaunchFallbackAsShown();

    // 重置检查计数
    _checkCount = 0;

    // 立即检查一次
    bool shouldContinue = await _checkAttributionAndPlayFallback();

    // 如果需要继续等待，则启动定时检查
    if (shouldContinue) {
      _checkCount = 1; // 已经检查了一次

      // 启动定时检查，每500ms检查一次，最多检查6次（总共3秒）
      _firstLaunchTimer =
          Timer.periodic(const Duration(milliseconds: 500), (timer) async {
        _checkCount++;

        // 如果达到最大检查次数，则超时处理
        if (_checkCount >= _maxCheckCount) {
          FFLog.info("归因检查超时，直接播放兜底剧", tag: tag);
          _cancelFirstLaunchTimer();
          // 超时后直接播放兜底剧
          await startFallbackDramaChecker();
          return;
        }

        // 继续检查
        FFLog.info("继续检查归因状态 ($_checkCount/$_maxCheckCount)", tag: tag);
        bool shouldContinueWaiting = await _checkAttributionAndPlayFallback();

        // 如果不需要继续等待，取消定时器
        if (!shouldContinueWaiting) {
          _cancelFirstLaunchTimer();
        }
      });
    }
  }

  /// 检查归因状态并播放兜底剧
  /// 返回是否需要继续等待
  Future<bool> _checkAttributionAndPlayFallback() async {
    try {
      FFLog.info("检查归因状态", tag: tag);

      // 检查归因是否已返回且是否为自然用户
      final isAttributionReturned = attributeService.isAttributionReturned();
      final isNatural = await attributeService.isNaturalUser();

      FFLog.info("归因状态: 是否返回=$isAttributionReturned, 是否自然用户=$isNatural",
          tag: tag);

      // 如果归因已返回
      if (isAttributionReturned) {
        // 如果是非自然用户，不播放兜底剧
        if (!isNatural) {
          FFLog.info("归因已返回且非自然用户，不播放兜底剧", tag: tag);
          return false; // 不需要继续等待
        } else {
          // 如果是自然用户，播放兜底剧
          FFLog.info("归因已返回且是自然用户，播放兜底剧", tag: tag);
          await startFallbackDramaChecker();
          return false; // 不需要继续等待
        }
      }

      // 归因未返回，需要继续等待
      FFLog.info("归因未返回，继续等待", tag: tag);
      return true; // 需要继续等待
    } catch (e) {
      FFLog.error("检查归因状态并播放兜底剧异常: $e", tag: tag);
      return false; // 出错不继续等待
    }
  }

  /// 取消首次启动定时器
  void _cancelFirstLaunchTimer() {
    if (_firstLaunchTimer != null && _firstLaunchTimer!.isActive) {
      _firstLaunchTimer!.cancel();
      _firstLaunchTimer = null;
      FFLog.info("已取消首次启动定时器", tag: tag);
    }
  }

  /// 初始化对话框关闭监听
  void _initDialogCloseListener() {
    if (_hasRegisteredDialogListener) {
      return;
    }

    // 注册对话框关闭监听
    _dialogChainManager.addDialogCloseListener(_onDialogClosed);
    _hasRegisteredDialogListener = true;
    FFLog.info("已注册对话框关闭监听", tag: tag);
  }

  /// 对话框关闭回调
  void _onDialogClosed() {
    FFLog.info("检测到对话框关闭", tag: tag);

    // 立即移除监听，避免重复触发
    if (_hasRegisteredDialogListener) {
      _dialogChainManager.removeDialogCloseListener(_onDialogClosed);
      _hasRegisteredDialogListener = false;
      FFLog.info("已移除对话框关闭监听", tag: tag);
    }

    // 处理首个弹窗关闭事件
    onFirstPopupClosed();
  }

  @override
  void onClose() {
    // 确保移除对话框关闭监听（以防万一还没被移除）
    if (_hasRegisteredDialogListener) {
      _dialogChainManager.removeDialogCloseListener(_onDialogClosed);
      _hasRegisteredDialogListener = false;
      FFLog.info("在onClose中移除对话框关闭监听", tag: tag);
    }

    _cancelFirstLaunchTimer();
    super.onClose();
  }
}

import 'package:get/get.dart';

class ShortsService extends GetxService {
  void clear() {
    _collectionShortsIds.clear();
    _shortsCollectionNumberMap.clear();
  }

  /// 用户收藏的剧id集合
  final RxSet<int> _collectionShortsIds = <int>{}.obs;
  RxSet<int> get collectionShortsIds => _collectionShortsIds;

  void confirmCollection(int? shortPlayCode) {
    if (shortPlayCode != null) {
      _collectionShortsIds.add(shortPlayCode);
    }
  }

  void cancelCollection(int? shortPlayCode) {
    if (shortPlayCode != null) {
      _collectionShortsIds.remove(shortPlayCode);
    }
  }

  /// 剧点赞数量
  final RxMap<int, int> _shortsCollectionNumberMap = <int, int>{}.obs;
  RxMap<int, int> get shortsCollectionNumberMap => _shortsCollectionNumberMap;

  void setShortsCollectionNumberBy(int? shortPlayCode, int? number) {
    if (shortPlayCode != null) {
      shortsCollectionNumberMap[shortPlayCode] = (number ?? 0);
    }
  }

  int? getShortsCollectionNumberBy(int? shortPlayCode) {
    if (shortPlayCode == null) return null;
    return shortsCollectionNumberMap[shortPlayCode];
  }

  void increaseShortsCollectionNumber(int? shortPlayCode) {
    if (shortPlayCode != null &&
        shortsCollectionNumberMap.keys.contains(shortPlayCode) == true) {
      int oldNumber = shortsCollectionNumberMap[shortPlayCode]!;
      shortsCollectionNumberMap[shortPlayCode] = (oldNumber + 1);
    }
  }

  void decreaseShortsCollectionNumber(int? shortPlayCode) {
    if (shortPlayCode != null &&
        shortsCollectionNumberMap.keys.contains(shortPlayCode) == true) {
      int oldNumber = shortsCollectionNumberMap[shortPlayCode]!;
      if (oldNumber > 0) {
        shortsCollectionNumberMap[shortPlayCode] = (oldNumber - 1);
      }
    }
  }
}

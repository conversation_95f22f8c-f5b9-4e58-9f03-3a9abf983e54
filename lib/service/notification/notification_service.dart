import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart' hide Trans;
import 'package:permission_handler/permission_handler.dart';
import 'package:playlet/api/notification_api.dart';
import 'package:playlet/api/rewards_api.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/alert/action.dart';
import 'package:playlet/components/alert/index.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/main.dart';
import 'package:playlet/model/notification/push_info.dart';
import 'package:playlet/model/rewards/task_list_result.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain_manager.dart';
import 'package:playlet/modules/notification/notification_reward_view.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/notification/task/FixedPushTask.dart';
import 'package:playlet/service/notification/task/PermanentTask.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/service/notification/firebase_message_manager.dart';
import 'package:playlet/utils/notification_util.dart';
import 'package:playlet/utils/notification_store.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';
import '../../common/event/track_event.dart';
import 'android_notification_manager.dart';

class NotificationService extends GetxService {
  AppTaskReponseList? notificationRewardTask;
  StreamSubscription? backgroundNotificationSubscription;
  StreamSubscription? loginEventSubscription;
  StreamSubscription? _routeSubscription;
  static const String _storageKeyPushInfo = "push_info";
  static const String _storageKeySystemNotificationShow = "system_notification_show";

  var isCheckGoToDetailed = false;

  Future<void> init() async {
    // 监听事件
    _registerSubscription();
    await NotificationUtil.instance.init();
    await FirebaseMessageManager.instance.init();
  }

  // 监听事件
  _registerSubscription() {
    loginEventSubscription = eventBus.on<InitLoginEventData>().listen((event) {
      // 重新登入后，初始化数据
      _fetchAppConfigList();
      FirebaseMessageManager.instance.tokenUploadConfigInit();
    });
    backgroundNotificationSubscription = eventBus.on<BackgroundNotificationClickEvent>().listen((event) {
      if (!isCheckGoToDetailed) {
        // 首页还没有初始化，先保存记录
        setPushInfo(event.data);
      } else {
        _handleNotificationClick(event.data);
      }
    });
  }

  // 通知栏点击
  void _handleNotificationClick(Map<dynamic, dynamic> data) {
// 首页已经初始化，直接跳转到详情页
    _handlePushTrackEvent(data);
    var pushId = data["data"]?["pushId"] ?? "";
    var shortPlayCode = data["data"]?["shortPlayCode"] ?? "";
    // push推送或者固定推送active_push
    if (shortPlayCode != null && (shortPlayCode.isNotEmpty)) {
      if (pushId == "active_push") {
        var dramaId = data["data"]?["dramaId"] ?? "";
        checkPushToReelsShort(shortPlayId: shortPlayCode, dramaId: dramaId);
      } else {
        var from = EventValue.fromPush;
        if (Platform.isAndroid) {
          var notificationType = data["data"]?["notificationType"] ?? "";
          if (notificationType == AndroidNotificationManager.notificationPermanent) {
            from = EventValue.permanent;
          } else if (notificationType == AndroidNotificationManager.notificationFsi) {
            from = EventValue.fsi;
          }
        }
        AppNavigator.startDetailsPage(DetailsOptions(
          businessId: int.parse(shortPlayCode),
          scene: EventValue.fromPush,
          from: from,
        ));
      }
    } else if (pushId == "check_in_alert") {
      AppNavigator.startRewardsPage(from: TrackEvent.push);
    } else if (pushId == "miss_check_in") {
      AppNavigator.startRewardsPage(from: TrackEvent.push);
    } else if (pushId == "expiring_soon") {
      // 跳转到我的页面
      AppNavigator.startMainPage(options: MainOptions(selectIndex: 3));
    }
  }

  // push相关埋点
  void _handlePushTrackEvent(Map<dynamic, dynamic> data) {
    var pushInfo = PushInfo.fromJson(data["data"] ?? {});
    useTrackEvent(TrackEvent.push_open, extra: {
      TrackEvent.push_id: pushInfo.pushId ?? "",
      TrackEvent.title: data["title"] ?? "",
      TrackEvent.content: data["body"] ?? "",
      TrackEvent.reel_id: pushInfo.shortPlayCode ?? "",
    });
    // 后台通知栏，需要多上报一个曝光埋点
    var isBackground = data["isBackground"];
    if (isBackground == "true" && pushInfo.shortPlayCode != null) {
      var scene = EventValue.fromPush;
      if (Platform.isAndroid) {
        var notificationType = data["data"]?["notificationType"] ?? "";
        if (notificationType == AndroidNotificationManager.notificationPermanent) {
          scene = EventValue.permanent;
        } else if (notificationType == AndroidNotificationManager.notificationFsi) {
          scene = EventValue.fsi;
        }
      }
      useTrackEvent(TrackEvent.reel_show, extra: {
        TrackEvent.reel_id: pushInfo.shortPlayCode ?? "",
        TrackEvent.scene: scene,
      });
    }
  }

  // 首页初始化后，检查是否需要跳转到详情页
  void checkGoToDetail() {
    isCheckGoToDetailed = true;
    var pushInfoMap = getPushInfo();
    if (pushInfoMap != null) {
      _handleNotificationClick(pushInfoMap);
      _handlePushTrackEvent(pushInfoMap);
      removePushInfo();
    }
  }

// 获取奖励通知任务
  _fetchAppConfigList() async {
    var result = await ApiRewards.getAppTaskList();
    if (result != null) {
      result.taskModuleResponseList?.forEach((item) {
        // "新手任务"
        if (item.moduleTaskType == 1) {
          item.appTaskReponseList?.forEach((appTask) {
            // "获取奖励通知"
            if (appTask.taskType == 4) {
              notificationRewardTask = appTask;
            }
          });
        }
      });
    }
  }

// 上报领取
  receiveReceiveRewardsRewards(int? taskId) async {
    if (taskId != null) {
      await ApiNotification.receiveRewards(taskId);
    }
  }

// 请求通知权限
  requestNotificationPermission() {
    DialogChainManager.instance.add(Chain(priority: DialogChainManager.customNotification, (chain, data) async {
      try {
        // 先加载任务
        if (notificationRewardTask == null) {
          await _fetchAppConfigList();
        }
        if (Platform.isAndroid || Platform.isIOS) {
          final status = await Permission.notification.status;
          FFLog.info('当前通知权限状态: $status', tag: "NotificationService");
          if (!getSystemNotificationShow() && status.isDenied) {
            useTrackEvent(TrackEvent.system_not_permission_show, extra: {TrackEvent.scene: TrackEvent.discover});
            saveSystemNotificationShow();
            final result = await Permission.notification.request();
            FFLog.info('请求通知权限结果: $result', tag: "NotificationService");
            // 拒绝通知权限并且奖励通知任务存在，显示通知自定义弹窗
            // 拒绝通知权限
            if (result.isPermanentlyDenied || result.isDenied) {
              await _showRewardNotificationDialog();
            } else if (result.isGranted) {
              // 启动常驻通知栏任务
              PermanentTask.instance.init();
              // 通知权限通过后，上报firebase token
              FirebaseMessageManager.instance.tokenUploadConfigInit();
              // 授权成功
              useTrackEvent(TrackEvent.not_permission_success, extra: {
                TrackEvent.scene: TrackEvent.discover,
                TrackEvent.from: TrackEvent.system,
              });
              showUseFullScreenDialog();
            }
          } else if (status.isPermanentlyDenied || status.isDenied) {
            // 尝试展示通知自定义弹框
            await _showRewardNotificationDialog();
          }
        }
      } catch (e) {
        FFLog.error('请求通知权限失败: $e', tag: "NotificationService");
      } finally {
        chain.finish();
      }
    }));
  }

  // 弹出自定义通知弹框
  _showRewardNotificationDialog() async {
    var receiveRewardsNum = notificationRewardTask?.receiveRewardsNum ?? 0;
    var showCustomNotificationCount = NotificationStore.getShowCustomNotificationCount() ?? 0;
    var isShowedCustomNotificationToday = NotificationStore.isShowedCustomNotificationToday();
    FFLog.info(
        '准备弹出自定义通知弹框,notificationRewardTask:$notificationRewardTask,receiveRewardsNum:$receiveRewardsNum,isShowedCustomNotificationToday:$isShowedCustomNotificationToday,showCustomNotificationCount:$showCustomNotificationCount',
        tag: "NotificationService");
    // 拒绝通知权限并且奖励通知任务存在，显示通知自定义弹窗
    if (notificationRewardTask != null && // 奖励通知任务存
            receiveRewardsNum == 0 // 奖励通知任务未完成
            &&
            showCustomNotificationCount < 3 // 奖励通知任务累计拒绝次数小于3
            &&
            !isShowedCustomNotificationToday // 奖励通知任务当天未显示
        ) {
      NotificationStore.setShowCustomNotificationCount(showCustomNotificationCount + 1);
      NotificationStore.saveCurrentShowCustomNotificationDate();
      await SmartDialog.show(
        builder: (context) => NotificationRewardView(appTask: notificationRewardTask!),
        clickMaskDismiss: false,
      );
    }
  }

  // 弹出全屏通知栏弹框
  showUseFullScreenDialog() async {
    if (!Platform.isAndroid) {
      return;
    }
    var canUse = await FixedPushTask.instance.canUseFullScreen();
    if (!canUse) {
      // 弹框
      DialogChainManager.instance.add(Chain(priority: DialogChainManager.fsiPermission, (chain, data) async {
        await FFAlert.action(
          container: FFAlertActionContainer(
            close: true,
            title: "",
            detail: AppTrans.fsiAlertContent(),
            detailStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFFFFFFFF),
              height: 1.5,
            ),
            actions: [
              FFAlertActionButton.cancel(
                text: AppTrans.cancel(),
              ),
              FFAlertActionButton.confirm(
                text: AppTrans.fsiAlertAllow(),
                onPressed: () {
                  NotificationUtil.instance.checkFullScreenIntentPermission();
                },
              ),
            ],
          ),
        );
        chain.finish();
      }));
    }
  }

  static void saveSystemNotificationShow() {
    SafeStorage().write(_storageKeySystemNotificationShow, true);
  }

  static bool getSystemNotificationShow() {
    return SafeStorage().read<bool>(_storageKeySystemNotificationShow) ?? false;
  }

  static void setPushInfo(Map<dynamic, dynamic> data) {
    SafeStorage().write(_storageKeyPushInfo, jsonEncode(data));
  }

  static void removePushInfo() {
    SafeStorage().remove(_storageKeyPushInfo);
  }

  static Map<dynamic, dynamic>? getPushInfo() {
    var json = SafeStorage().read<String>(_storageKeyPushInfo);
    return json != null ? jsonDecode(json) : null;
  }

  @override
  void onClose() {
    loginEventSubscription?.cancel();
    _routeSubscription?.cancel();
    backgroundNotificationSubscription?.cancel();
    super.onClose();
  }

  /// 点击固定推送到feed流页面播放该剧
  void checkPushToReelsShort({
    required String shortPlayId,
    required String dramaId,
  }) {
    eventBus.fire(PushNotificationToReelEventData(
      shortPlayId: shortPlayId,
      dramaId: dramaId,
    ));
  }
}

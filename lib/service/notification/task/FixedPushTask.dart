import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/api/notification_api.dart';
import 'package:playlet/common/image/ImageUtil.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/service/notification/android_notification_manager.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/index.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/time_util.dart';
import 'package:get/get.dart';

class FixedPushTask {
  static final FixedPushTask instance = FixedPushTask._internal();

  factory FixedPushTask() => instance;

  FixedPushTask._internal();

  static const String tag = "AndroidNotificationManager";
  static const String storeKey = "FixedPushTask";
  static const String activePush = "active_push";
  static const String fsiPushNumKey = "fsiPushNum";
  static const List<String> emotes = ["💞", "😍", "🥳", "✨", "🥰", "🎄", "🎅", "📺", "🧨", "‌🔥"];

  Timer? fixedPushTaskTimer;

  // 固定推送 轮询时间 秒
  int fixedPushPeriodicTime = 1 * 60 * 60;

  // fsi通知每天显示次数
  int fsiPushNum = 0;

  void init() {
    initFixedPushTask();
  }

  /// 初始化固定推送任务
  initFixedPushTask() async {
    var result = await ApiNotification.getFullScreenConfig();
    if (result != null) {
      fsiPushNum = result;
    }
    handleFetchFixedPush();
  }

  void handleFetchFixedPush() {
    fixedPushTaskTimer?.cancel();
    var lastTime = SafeStorage().read(storeKey);
    int offsetTime = 0;
    if (TimeUtil.isOffsetTimeBefore(lastTime, fixedPushPeriodicTime)) {
      //上一个请求超过一小时，则每小时整点请求
      var currentDate =  DateTime.now();
      var targetHour = currentDate.hour + 1;
      var targetMinute = 0;
      if(fixedPushPeriodicTime< 1 * 60 * 60){
        targetHour = currentDate.hour;
        targetMinute = currentDate.minute + (fixedPushPeriodicTime/60).toInt();
      }
      offsetTime = TimeUtil.getSecondsUntilForTime(days: 0, targetHour: targetHour, targetMinute: targetMinute, targetSecond: 0);
    } else {
      // 没有超过1小时，则按1小时后请求
      lastTime ??= DateTime.now().millisecondsSinceEpoch;
      var targetDate = DateTime.fromMillisecondsSinceEpoch(lastTime + fixedPushPeriodicTime * 1000);
      offsetTime = targetDate.difference(DateTime.now()).inSeconds;
    }
    FFLog.printToAndroidLogcat("PushManager", "handleFetchFixedPush offsetTime:$offsetTime", "debug");
    fixedPushTaskTimer = Timer(Duration(seconds: offsetTime), () async {
      await _fetchFixedPush();
      // 执行下一个任务
      handleFetchFixedPush();
    });
  }

  /// 获取固定推送,并发送通知
  Future<void> _fetchFixedPush() async {
    try {
      await SafeStorage().write(storeKey, DateTime.now().millisecondsSinceEpoch);

      var hasDoTask = await _hasTodyDoTask(activePush);
      if (TimeUtil.isBetween22PmTo6Am() || (_isSubscription() && hasDoTask)) {
        //(22-6AM)不推送, 订阅用户一天只触发一次
        FFLog.printToAndroidLogcat("PushManager", "(22-6AM)不推送, 订阅用户一天只触发一次", "debug");
        return;
      }

      ShortPlayResponseList? short = await ApiNotification.getForYouListOnlyOne();
      if (short == null) {
        return;
      }
      // 下图剧集图片
      final imageUrl = Utils.getImageRatioScale(short.coverId ?? "", width: 91.sp, height: 120.sp);
      String? imagePath = await ImageUtil.fetchImageForPath(imageUrl);
      FFLog.info("_fetchFixedPush:$imagePath", tag: tag);
      var title = "${_randomEmote()}<${short.shortPlayName ?? ""}>${AppTrans.pushBeingBroadcasted()}";
      var content = _fetchShortContent(short);
      var canSendFsi = await _checkCanSendFsi();
      var type = canSendFsi ? AndroidNotificationManager.notificationFsi : AndroidNotificationManager.notificationDefault;
      FFLog.printToAndroidLogcat("PushManager", "canSendFsi:$canSendFsi", "debug");
      AndroidNotificationManager.instance.sendNotification(
        type,
        title,
        content,
        {
          "title": title,
          "body": content,
          "data": {
            "pushParam": "",
            "pushId": activePush,
            "shortPlayId": short.shortPlayCode?.toString() ?? "",
            "shortPlayCode": short.shortPlayCode?.toString() ?? "",
            "dramaId": short.id?.toString() ?? "",
            "pushName": "1", //目前只有1.固定推送传的值是1:foryou,2:推送短句，3：h5_push_topic，4：爆款剧
            "notificationType": type,
          },
        },
        imagePath: imagePath ?? "",
      );
      await SafeStorage().write(activePush, DateTime.now().millisecondsSinceEpoch);
      if (canSendFsi) {
        // 进入今天fsi通知展示的次数
        _saveFsiPushNum();
      }
    } catch (e) {
      FFLog.error("_fetchFixedPush:$e", tag: tag);
    }
  }

  Future<bool> _checkCanSendFsi() async {
    var canUse = await canUseFullScreen();
    FFLog.printToAndroidLogcat("PushManager", "canUseFullScreen:$canUseFullScreen", "debug");
    if (!canUse) {
      // 没有权限也不展示
      return canUse;
    }
    var fsiPushNumSended = _getFisPushNum();
    FFLog.printToAndroidLogcat("PushManager", "fsiPushNumSended:$fsiPushNumSended,fsiPushNum:$fsiPushNum", "debug");
    return fsiPushNumSended < fsiPushNum;
  }

  int _getFisPushNum() {
    var result = SafeStorage().read(fsiPushNumKey);
    if (result != null) {
      var json = jsonDecode(result);
      var time = json["time"] ?? 0;
      var num = json["num"] ?? 0;
      if (!TimeUtil.isSameDay(time)) {
        num = 0;
      }
      return num;
    } else {
      return 0;
    }
  }

  void _saveFsiPushNum() async {
    var oldNum = 0;
    var result = SafeStorage().read(fsiPushNumKey);
    if (result != null) {
      var json = jsonDecode(result);
      var time = json["time"] ?? 0;
      var num = json["num"] ?? 0;
      if (!TimeUtil.isSameDay(time)) {
        num = 0;
      }
      oldNum = num;
    }
    await SafeStorage().write(fsiPushNumKey, jsonEncode({"time": DateTime.now().millisecondsSinceEpoch, "num": oldNum + 1}));
  }

  /// 支持默认读取该剧的推荐语，当推荐语为空，则读取短剧简介——(取短剧的推荐语/简介，非剧集)
  String _fetchShortContent(ShortPlayResponseList? short) {
    if (short == null) {
      return "";
    }
    var content = short.recommendContent ?? short.summary;
    return content ?? "";
  }

  /// 随机获取表情
  String _randomEmote() {
    final random = Random();
    final index = random.nextInt(emotes.length);
    return emotes[index];
  }

  /// 是否已经执行了任务
  Future<bool> _hasTodyDoTask(String taskKey) async {
    var time = SafeStorage().read(taskKey);
    if (time == null) {
      return false;
    }
    return TimeUtil.isSameDay(time);
  }

  /// 是否是订阅用户
  bool _isSubscription() {
    return Get.find<UserService>().userInfo.value?.isSubscription ?? false;
  }

  /// 是否有全屏通知权限
  Future<bool> canUseFullScreen() async {
    try {
      const platform = MethodChannel('com.flareflow.android/notification');
      var result = await platform.invokeMethod('canUseFullScreen');
      return result;
    } catch (e) {
      FFLog.error('sendNotification error: $e', tag: tag);
      return false;
    }
  }

  /// 是否锁屏
  Future<bool> fetchIsKeyguardLocked() async {
    try {
      const platform = MethodChannel('com.flareflow.android/notification');
      var result = await platform.invokeMethod('isKeyguardLocked');
      return result;
    } catch (e) {
      FFLog.error('sendNotification error: $e', tag: tag);
      return false;
    }
  }

  /// 获取push
  void tryFetchPush() async {
    FFLog.printToAndroidLogcat("PushManager", "FixedPushTask", "debug");
    var time = SafeStorage().read(storeKey);
    if (TimeUtil.isOffsetTimeBefore(time, fixedPushPeriodicTime)) {
      // 超过一小时,则去请求
      await _fetchFixedPush();
      handleFetchFixedPush();
    }
  }
}

import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/api/notification_api.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/notification/push_check_info.dart';
import 'package:playlet/service/notification/android_notification_manager.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/time_util.dart';

/// 签到相关任务
class CheckTask {
  static final CheckTask instance = CheckTask._internal();

  factory CheckTask() => instance;

  CheckTask._internal();

  static const String tag = "AndroidNotificationManager";
  static const String checkInAlert = "check_in_alert";
  static const String missCheckIn = "miss_check_in";
  static const String expiringSoon = "expiring_soon";

  List<Task> tasks = [];
  Timer? checkTimer;

  // 下一次执行的时间,秒
  int nextTime = 0;

  void init() async {
    _initTask();
    // 签到相关任务
    initCheckTask();
  }

  _initTask() {
    tasks.clear();
    tasks.add(Task(checkInAlert, hour: 6, minute: 0, second: 0, endHour: 11, endMinute: 59, endSecond: 59));
    tasks.add(Task(missCheckIn, hour: 12, minute: 0, second: 0, endHour: 13, endMinute: 59, endSecond: 59));
    tasks.add(Task(expiringSoon, hour: 14, minute: 0, second: 0, endHour: 21, endMinute: 59, endSecond: 59));
  }

  initCheckTask() async {
    checkTimer?.cancel();
    int days = 0;
    // 查找今天的任务
    Task? task = await findNextTask(0);
    FFLog.info("_initCheckTask: findCurrentTask ${task?.key}", tag: tag);
    // 今天的没有，则查找第二天的任务
    if (task == null) {
      task = await findNextTask(1);
      days = 1;
      FFLog.info("_initCheckTask: findNextTask ${task?.key}", tag: tag);
    }
    if (task == null) {
      FFLog.info("_initCheckTask:has not NextTask", tag: tag);
      return;
    }

    int offsetTime = TimeUtil.getSecondsUntilForTime(days: days, targetHour: task.hour, targetMinute: task.minute, targetSecond: task.second);
    if (offsetTime < 0) {
      FFLog.info("_initCheckTask:offsetTime已经超过时间了", tag: tag);
      return;
    }
    FFLog.info("_initCheckTask:offsetTime $offsetTime", tag: tag);
    checkTimer = Timer(Duration(seconds: offsetTime), () async {
      await handleCheckTask(task);
      // 执行下一个任务
      initCheckTask();
    });
  }

  Future<void> handleCheckTask(Task? task) async {
    FFLog.info("_initCheckTask handleCheckTask", tag: tag);
    if (task == null || _isSubscription()) {
      // 订阅用户不触发签到相关push
      return;
    }
    var pushCheckInfo = await fetchPushCheckInfo(task.key);
    await SafeStorage().write(task.key, DateTime.now().millisecondsSinceEpoch);
    if (pushCheckInfo != null) {
      var title = pushCheckInfo.title ?? "";
      var content = pushCheckInfo.content ?? "";
      AndroidNotificationManager.instance.sendNotification(
          AndroidNotificationManager.notificationDefault,
          title,
          content,
          {
            "title": title,
            "body": content,
            "data": {
              "pushId": task.key,
              "pushParam": "",
              "shortPlayId": "",
              "shortPlayCode": "",
              "dramaId": "",
              "notificationType": AndroidNotificationManager.notificationDefault,
            },
          },
          imageRes: "ic_coin",
          canFlod: "false");
    }
  }

  Future<PushCheckInfo?> fetchPushCheckInfo(String key) async {
    PushCheckInfo? pushCheckInfo;
    if (key == checkInAlert) {
      pushCheckInfo = await ApiNotification.signReminder();
    } else if (key == missCheckIn) {
      pushCheckInfo = await ApiNotification.missSignReminder();
    } else if (key == expiringSoon) {
      pushCheckInfo = await ApiNotification.bonusExpiring();
    }
    return pushCheckInfo;
  }

  /// 查找下一个要执行的任务
  /// [days] 默认为0，表示当前，1表示明天
  Future<Task?> findNextTask(int days) async {
    Task? task = tasks.firstWhereOrNull((item) {
      // 查找今天的任务，要判断是否已经触发过
      if (days == 0) {
        var hasCheckInTask = _hasTodyDoTask(item.key);
        if (hasCheckInTask) {
          // 今天已经触发过，不再触发
          return false;
        }
        if (item.key == expiringSoon && _hasTodyDoTask(missCheckIn)) {
          // 如果推送了错过签到任务，今天则不再推，计算明天的时差
          return false;
        }
      }
      // 查找今天还没有执行的任务
      int offsetTime = TimeUtil.getSecondsUntilForTime(days: days, targetHour: item.hour, targetMinute: item.minute, targetSecond: item.second);
      return offsetTime > 0;
    });
    return task;
  }

  /// 是否已经执行了任务
  bool _hasTodyDoTask(String taskKey) {
    var time = SafeStorage().read(taskKey);
    if (time == null) {
      return false;
    }
    return TimeUtil.isSameDay(time);
  }

  /// 是否是订阅用户
  bool _isSubscription() {
    return Get.find<UserService>().userInfo.value?.isSubscription ?? false;
  }

  /// 获取push
  void tryFetchPush() async {
    // 查找可以获取的任务
    if (TimeUtil.isBetween22PmTo6Am()) {
      return;
    }
    // 查找可以执行的任务
    Task? task = tasks.firstWhereOrNull((item) {
      var hasCheckInTask = _hasTodyDoTask(item.key);
      if (hasCheckInTask) {
        // 今天已经触发过，不再触发
        return false;
      }
      if (item.key == expiringSoon && _hasTodyDoTask(missCheckIn)) {
        // 如果推送了错过签到任务，今天则不再推，计算明天的时差
        return false;
      }
      if (_isBetweenTaskTime(item)) {
        return true;
      }
      return false;
    });
    FFLog.info("_initCheckTask tryFetchPush task：${task?.key}", tag: tag);
    handleCheckTask(task);
  }

  /// 判断是否在任务的时间段
  bool _isBetweenTaskTime(Task task) {
    final now = DateTime.now();
    final startTarget = DateTime(now.year, now.month, now.day, task.hour, task.minute, task.second);
    final endTarget = DateTime(now.year, now.month, now.day, task.endHour, task.endMinute, task.endSecond);
    return now.isAfter(startTarget) && now.isBefore(endTarget);
  }

  initTestTask() {
    tasks.clear();
    final now = DateTime.now();
    tasks.add(Task(checkInAlert, hour: now.hour, minute: now.minute + 1, second: 0, endHour: now.hour, endMinute: now.minute + 1, endSecond: 59));
    tasks.add(Task(missCheckIn, hour: now.hour, minute: now.minute + 2, second: 0, endHour: now.hour, endMinute: now.minute + 2, endSecond: 59));
    tasks.add(Task(expiringSoon, hour: now.hour, minute: now.minute + 3, second: 0, endHour: now.hour, endMinute: now.minute + 3, endSecond: 59));
  }

  initBonusExpiringTask() {
    tasks.clear();
    final now = DateTime.now();
    tasks.add(Task(expiringSoon, minute: now.minute + 1, second: 0));
  }
}

class Task {
  final String key;
  int hour = -1;
  int minute = -1;
  int second = -1;

  // 结束时间
  int endHour = -1;
  int endMinute = -1;
  int endSecond = -1;

  Task(this.key, {this.hour = -1, this.minute = -1, this.second = -1, this.endHour = -1, this.endMinute = -1, this.endSecond = -1});
}

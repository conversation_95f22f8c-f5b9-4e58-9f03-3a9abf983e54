import 'dart:async';
import 'dart:io';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/api/notification_api.dart';
import 'package:playlet/common/image/ImageUtil.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/i18n/translation_service.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/service/notification/android_notification_manager.dart';
import 'package:playlet/utils/index.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/time_util.dart';

class PermanentTask {
  static final PermanentTask instance = PermanentTask._internal();

  factory PermanentTask() => instance;

  PermanentTask._internal();

  static const String tag = "AndroidNotificationManager";
  static const String storeKey = "PermanentTask";

  Timer? permanentTaskTimer;

  // 常驻通知栏 轮询时间 秒
  int permanentNotificationPeriodicTime = 1 * 60 * 60;

  // 测试环境兜底剧
  ShortPlayResponseList devShort = ShortPlayResponseList(
      shortPlayCode: 7386368,
      shortPlayName: "Boss's Part-Time Roommate",
      summary: "Amelia, the breadwinner of her family, gets employed as the secretary to the CEO of the Salazar Group while working part-time. Everything is going well until her boss, Maximus Salazar, finds out about her side gigs. Instead of dismissal, he makes her an intriguing offer: to live with him secretly! As Amelia navigates this new dynamic, she finds it difficult to keep things \"business as usual\" due to her boss's irresistible charm.",
      coverId: "https://static.flareflow.tv/images/cover/2025/03/31/c8f44d51116a43f0848c63573747760f.jpg?auth_key=1747637937-0-0-c5a7a16c4694bef951edfc50df0ef245");

  Map<String, ShortPlayResponseList> proShortMap = {
    "en": ShortPlayResponseList(
        shortPlayCode: 100922,
        shortPlayName: AppTrans.pushLocalShortName(),
        summary: AppTrans.pushLocalShortSummary(),
        coverId: "https://static.flareflow.tv/images/cover/2025/04/29/9c2efe5bc8f647659bfe62e6d62c12e8.jpg?auth_key=1747054089-0-0-f3b1c060c6b13c8c38d9313d562edb8d"),
    "zh_hans": ShortPlayResponseList(
        shortPlayCode: 102653,
        shortPlayName: "江春不入旧年",
        summary: "一位女子与一位神秘人物缔结承诺，六年来悉心照料着一对疏远的父子，她忍受着背叛、羞辱和冷漠。契约到期，旧伤再次被撕开——她曾守护的人背叛了她，于是义无反顾地离开了。与救她之人重逢后，她拥抱了新的生活。与此同时，抛弃她的人终于意识到自己傲慢的代价，但为时已晚。在一个充满牺牲、背叛和救赎的故事中，爱与恨在此交织碰撞。",
        coverId: "https://static.flareflow.tv/images/cover/2025/05/12/6167f928b7ea42f2878e012a3ee1d1af.jpg?auth_key=1747126571-0-0-97e6387eb6045395c3fd01f5b2222cd7"),
    "ja": ShortPlayResponseList(
        shortPlayCode: 701645,
        shortPlayName: "君を守り、君に捨てられ",
        summary:
            "天野夕月は、謎めいた男との契約により、父子・氷室 晴真と氷室 景翔を六年間、陰ながら支え続けることになります。裏切り、屈辱、冷遇──彼らからの理不尽な仕打ちを耐え忍びながらも、夕月はその約束を果たし続けました。すべてを捨てて立ち去った夕月は、振り返ることなく新たな道を歩き始めます。かつて命を救ってくれた男、橘 玄成との再会が、彼女に新たな人生をもたらします。これは、犠牲と裏切り、救いと成長を描いた物語です。",
        coverId: "https://static.flareflow.tv/images/cover/2025/04/30/560ebc7de2b14d5ea790494b280a68e4.jpg?auth_key=1747126459-0-0-3202a9010a0f5fd694ac8bd73e71cd19"),
    "ko": ShortPlayResponseList(
        shortPlayCode: 701911,
        shortPlayName: "다시 피는 봄",
        summary:
            "신비로운 사람과 계약을 체결한 그녀는 배신, 굴욕, 냉대를 참아내며 이 냉담한 부자를 6년 동안 묵묵히 지켜주었다. 그러나 계약이 끝나던 날, 예전의 상처가 다시 드러났다. 그녀는 단호하게 등을 돌리며 다시는 돌아보지도 않았다.그녀는 위기에서 자신을 구해준 남자와 재회하며 새로운 인생을 맞이한다,한편, 그녀를 버렸던 사람들은 그제야 깨닫고 비로소 후회하며 오만함이 치른 대가를 톡톡히 알게 된다.희생과 배신, 구원과 성장이 얽힌 이야기, 그 속에서 사랑과 증오가 격렬하게 부딪힌다.",
        coverId: "https://static.flareflow.tv/images/cover/2025/05/08/34e4fd356a1c4227bab0627d9c28ffa2.jpg?auth_key=1747126425-0-0-ac7f3b5391f225c27b7872aa8ba430d0"),
    "de": ShortPlayResponseList(
        shortPlayCode: 401089,
        shortPlayName: "Als erste erbin der welt geboren",
        summary:
            "Olivia ist die wahre thronfolgerin des starling clans, und alles, was sie hat ihre reportage, ihre niere und sogar ihr leben wurde Von einer gerissenen stiefschwester gestohlen, und ihre eigenen brüder haben sie verraten. Mit dieser wiedergeburt trennte sie sich Von allen bindungen und schlug voller herrlichkeit zurück. Als die wahrheit herauskam, kam ihr bedauern zu spät.",
        coverId: "https://static.flareflow.tv/images/cover/2025/04/28/e6350a19da7c4fcd850f16b2a3fa9819.jpg?auth_key=1747125471-0-0-cc225f190103636d4ec36c42ffff6760"),
    "es": ShortPlayResponseList(
        shortPlayCode: 401016,
        shortPlayName: "Renace para ser la heredera superior",
        summary:
            "Olivia, la verdadera herede de la familia Sterling, tenía todo arreglado — su tesis, su riñón, incluso su vida — por una hermanastra intrig, mientras que sus propios hermanos la traicion. Renace con venganza, ella corta todos los lazos y lucha con brill. Cuando la verdad explota, su arrepentimiento llega demasiado tarde.",
        coverId: "https://static.flareflow.tv/images/cover/2025/04/28/207d7e5b20284e6592668b84bd0a5f89.jpg?auth_key=1747126394-0-0-9257493dce00a6040a37ce35f848cf26"),
    "fr": ShortPlayResponseList(
        shortPlayCode: 401018,
        shortPlayName: "Renaître pour être la première héritière",
        summary:
            "Olivia, la véritable héritière de la famille Sterling, avait tout stol— sa thèse, son rein, même sa vie — par une demi-sœur complotante, tandis que ses propres frères la trahissaient. Renaître avec vengeance, elle coupe tous les liens et se bat avec éclat. Quand la vérité explose, leur regret arrive trop tard.",
        coverId: "https://static.flareflow.tv/images/cover/2025/04/28/bf16737cb63745388b9acbde3ec7c529.jpg?auth_key=1747126255-0-0-246a08b348c65d514d1b4378607dd34e"),
    "id": ShortPlayResponseList(
        shortPlayCode: 702524,
        shortPlayName: "Pernikahan seperti itu",
        summary: "Setelah melihat dia memeluk pria lain, dia menjadi cemburu dan ingin membalas dendam!",
        coverId: "https://static.flareflow.tv/images/cover/2025/05/12/72972914dcff40909e337259568150a1.jpg?auth_key=1747126516-0-0-3b51c2aa5e7c5d2b6a3ab11f3260b6d3"),
    "it": ShortPlayResponseList(
        shortPlayCode: 100922,
        shortPlayName: "Reborn to Be the Top Heiress",
        summary:
            "Olivia, the true heiress of the Sterling family, had everything stolen—her thesis, her kidney, even her life—by a scheming stepsister, while her own brothers betrayed her. Reborn with vengeance, she cuts all ties and fights back with brilliance. When the truth explodes, their regret comes too late.",
        coverId: "https://static.flareflow.tv/images/cover/2025/04/29/9c2efe5bc8f647659bfe62e6d62c12e8.jpg?auth_key=1747126602-0-0-f796754f6f9e2d1c3e23e068c619181a"),
    "pt": ShortPlayResponseList(
        shortPlayCode: 101725,
        shortPlayName: "Renascida para ser a principal herdeira",
        summary:
            "Olivia, a verdadeira herdeira da família Sterling, teve tudo roubado - sua tese, seu rim, até mesmo sua vida - por uma meia-irmã intrigante, enquanto seus próprios irmãos a traíram. Renascida com vingança, ela corta todos os laços e revida com brilhantismo. Quando a verdade explode, seu arrependimento chega tarde demais.",
        coverId: "https://static.flareflow.tv/images/cover/2025/05/07/3b9ffeb1ab3341c095048fbbfcc1de79.jpg?auth_key=1747053487-0-0-b50cff688f125e1bfe879fd934d67377"),
    "th": ShortPlayResponseList(
        shortPlayCode: 701624,
        shortPlayName: "สายไปแล้วที่จะมาเรียกชื่อฉัน",
        summary:
            "เนื่องจากเซ็นสัญญากับผู้ลึกลับ เธอปกป้องพ่อลูกที่เย็นชาเงียบ ๆ มานานถึงหกปี อดทนอดกลั้นต่อการทรยศ เหยียดหยามและละเลย ทันทีที่สัญญาสิ้นสุดลง ความเจ็บช้ำในอดีตถูกเปิดขึ้นมาอีกครั้ง...คนที่เคยปกป้องอย่างสุดความสามารถกลับเลือกทรยศ เธอตัดสินใจจากไปแบบไม่หันหลังกลับ หลังจากได้พบกับผู้ชายที่เธอเคยช่วยให้รอดพ้นจากวิกฤตอีกครั้ง เธอก็ได้ชีวิตใหม่ ขณะเดียวกัน กระทั่งทุกอย่างมันสายไปแล้ว คนที่เคยทอดทิ้งเธอจึงจะตาสว่าง เข้าใจผลจากความถือดี นี่คือเรื่องราวเกี่ยวกับการเสียสละและการหักหลัง การกอบกู้และการเติบโต โดยมีความรักความแค้นขัดแย้งกันอย่างดุเดือดอยู่ในนั้น",
        coverId: "https://static.flareflow.tv/images/cover/2025/04/30/da8d7f49b5834185a8d5ba0fe47d8187.jpg?auth_key=1747126492-0-0-d30bf05e6121a3a550aca5bcf3cf7c2b"),
  };

  void init() {
    if (Platform.isAndroid) {
      initPermanentNotification();
    }
  }

  /// 常驻通知栏
  initPermanentNotification() async {
    await _fetchPermanentNotification();
    handleFetchPermanentPush();
  }

  void handleFetchPermanentPush() {
    permanentTaskTimer?.cancel();
    var lastTime = SafeStorage().read(storeKey);
    // 上次请求的1小时后请求
    if(lastTime == null){
      lastTime == DateTime.now().millisecondsSinceEpoch;
    }
    var targetDate = DateTime.fromMillisecondsSinceEpoch(lastTime + permanentNotificationPeriodicTime * 1000);
    int offsetTime = targetDate.difference(DateTime.now()).inSeconds;
    permanentTaskTimer = Timer(Duration(seconds: offsetTime), () async {
      await  _fetchPermanentNotification();
      // 执行下一个任务
      handleFetchPermanentPush();
    });
  }

  /// 获取常驻推送,并发送通知
  Future<void> _fetchPermanentNotification() async {
    try {
      await SafeStorage().write(storeKey, DateTime.now().millisecondsSinceEpoch);

      ShortPlayResponseList? short = await ApiNotification.getForYouListOnlyOne();
      short ??= _getLocalShort();
      if (short == null) {
        return;
      }
      // 下图剧集图片
      final imageUrl = Utils.getImageRatioScale(
        short.coverId ?? "",
        width: 50.sp,
        height: 67.sp,
      );
      String? imagePath = await ImageUtil.fetchImageForPath(imageUrl);
      FFLog.info("initPermanentNotification:$imagePath", tag: tag);
      var title = short.shortPlayName ?? "";
      var content = short.summary ?? "";
      AndroidNotificationManager.instance.sendNotification(
          AndroidNotificationManager.notificationPermanent,
          title,
          content,
          {
            "title": title,
            "body": content,
            "data": {
              "pushParam": "",
              "shortPlayId": short.shortPlayCode?.toString() ?? "",
              "shortPlayCode": short.shortPlayCode?.toString() ?? "",
              "dramaId": short.id?.toString() ?? "",
              "notificationType": AndroidNotificationManager.notificationPermanent
            },
          },
          imagePath: imagePath ?? "");
    } catch (e) {
      FFLog.error("_fetchFixedPush:$e", tag: tag);
    }
  }

  // 获取本地兜底剧
  ShortPlayResponseList? _getLocalShort() {
    if (!Config.isProduction) {
      return devShort;
    } else {
      var currentLanguage = Get.find<TranslationService>().currentLanguage.value;
      return proShortMap[currentLanguage] ?? proShortMap["en"];
    }
  }

  /// 获取push
  void tryFetchPush() async {
    var time = SafeStorage().read(storeKey);
    if (TimeUtil.isOffsetTimeBefore(time, permanentNotificationPeriodicTime)) {
      // 超过一小时,则去请求
      await _fetchPermanentNotification();
      // 执行下一个任务
      handleFetchPermanentPush();
    }
  }
}

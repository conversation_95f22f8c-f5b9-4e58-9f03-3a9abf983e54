import 'dart:convert';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:playlet/api/notification_api.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/notification/push_info.dart';
import 'package:playlet/utils/app_analysis.dart';
import 'package:playlet/utils/app_usage_statistics.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/utils/notification_util.dart';
import 'package:playlet/utils/track_event.dart';

/// 后台消息 将处理器移到类外部作为顶级函数，仅限 Android，是一个完全隔离的环境，实现任何功能时都要重新初始化
// @pragma('vm:entry-point')
// Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   // 如果要使用Firebase 服务，要先初始化
//   // await Firebase.initializeApp();
//
//   FFLog.debug('Handling a background message: ${message.messageId}');
//   FFLog.debug('Background message data: ${message.data}');
//
//   // 带notification字段的，Firebase自动显示通知，不带notification字段的，需要自己处理
//   FFLog.debug('Background message notification: ${message.notification}');
// }

class FirebaseMessageManager {
  static final FirebaseMessageManager instance = FirebaseMessageManager._internal();

  factory FirebaseMessageManager() => instance;

  FirebaseMessageManager._internal();

  init() async {
    // firebase已经初始化后并且是ios,再初始化消息推送
    if (Firebase.apps.isNotEmpty && Platform.isIOS) {
      try {
        // 处理后台消息
        _backgroundConfigInit();

        // 处理前台消息，ios不支持前台消息
        // _foregroundConfigInit();
      } catch (e) {
        FFLog.error('initFirebaseMessage: $e', tag: "FirebaseMessageUtil");
      }
    } else {
      FFLog.info('Firebase has not init on FirebaseMessageUtil.init', tag: "FirebaseMessageUtil");
    }
  }

  _backgroundConfigInit() async {
    // FirebaseMessaging.onBackgroundMessage仅支持android
    // FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    // 监听应用通过通知栏点击打开（应用在后台时）
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      if (message.notification != null) {
        _handleNotificationClick(message);
      }
      FFLog.info('Message data onMessageOpenedApp : ${message.data}', tag: "FirebaseMessageUtil");
    });

    // 处理应用通过通知栏启动（应用完全终止时）
    RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null && initialMessage.notification != null) {
      _handleNotificationClick(initialMessage);
      FFLog.info('Message data getInitialMessage : ${initialMessage.data}', tag: "FirebaseMessageUtil");
    }
  }

  _handleNotificationClick(RemoteMessage message) {
    AppAnalysisStore.saveAppLaunchFrom(EventValue.push.toString());
    AppUsageStatistics.trackActive();
    AppUsageStatistics.trackActiveUser();
    var payload = {
      "title": message.notification?.title ?? "",
      "body": message.notification?.body ?? "",
      "data": message.data,
      "isBackground": "true",
    };
    // 通过发送事件 EventBus
    eventBus.fire(BackgroundNotificationClickEvent(data: payload));
  }

  _foregroundConfigInit() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      FFLog.info('Got a message whilst in the foreground!', tag: "FirebaseMessageUtil");
      FFLog.info('Message data foreground : ${message.data}', tag: "FirebaseMessageUtil");

      FFLog.info('Message also contained a notification: ${message.notification}');
      if (message.notification != null) {
        var payload = {};
        payload["title"] = message.notification?.title ?? "";
        payload["body"] = message.notification?.body ?? "";
        payload["data"] = message.data;
        // 发送本地通知
        NotificationUtil.instance.sendNotification(
          message.hashCode,
          message.notification?.title ?? "",
          message.notification?.body ?? "",
          jsonEncode(payload),
        );
        if (Platform.isAndroid) {
          // 根据需求，只有android需要在客户端埋点
          var pushInfo = PushInfo.fromJson(message.data);
          useTrackEvent(TrackEvent.push_send, extra: {
            TrackEvent.push_id: pushInfo.pushId ?? "",
            TrackEvent.title: message.notification?.title ?? "",
            TrackEvent.content: message.notification?.body ?? "",
            TrackEvent.reel_id: pushInfo.shortPlayCode ?? "",
          });

          useTrackEvent(TrackEvent.reel_show, extra: {
            TrackEvent.reel_id: pushInfo.shortPlayCode ?? "",
            TrackEvent.scene: EventValue.fromPush,
          });
        }
      }
    });
  }

  /// 上传firebase token，只有ios平台才使用firebase push
  tokenUploadConfigInit() async {
    try {
      if (Firebase.apps.isNotEmpty && Platform.isIOS) {
        // 请求通知栏权限，和另一个请求通知栏权限逻辑冲突了，先注释
        final token = await FirebaseMessaging.instance.getToken();
        final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
        FFLog.info('APNS Token: $apnsToken,token : $token', tag: "FirebaseMessageUtil");
        if (token != null && token.isNotEmpty) {
          ApiNotification.uploadToken(10, token, 0, apnsToken ?? "");
        }
        // 每次令牌更新时获得通知
        FirebaseMessaging.instance.onTokenRefresh.listen((fcmToken) async {
          FFLog.info('FCM Token refreshed: $fcmToken', tag: "FirebaseMessageUtil");
          // 上传新的 token
          final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
          FFLog.info('apnsToken refreshed: $fcmToken', tag: "FirebaseMessageUtil");
          if (fcmToken.isNotEmpty) {
            ApiNotification.uploadToken(10, fcmToken, 0, apnsToken ?? "");
          }
        }).onError((err) {
          FFLog.info('onTokenRefresh: $err', tag: "FirebaseMessageUtil");
        });
      } else {
        FFLog.info('tokenUploadConfigInit: Firebase is not init', tag: "FirebaseMessageUtil");
      }
    } catch (e) {
      FFLog.error('tokenUploadConfigInit: $e', tag: "FirebaseMessageUtil");
    }
  }
}

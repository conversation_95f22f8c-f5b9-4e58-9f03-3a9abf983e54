import 'dart:async';
import 'dart:convert';
import 'dart:core';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/service/notification/task/CheckTask.dart';
import 'package:playlet/service/notification/task/FixedPushTask.dart';
import 'package:playlet/service/notification/task/PermanentTask.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';

/// 处理android通知栏相关逻辑
class AndroidNotificationManager {
  static final AndroidNotificationManager instance = AndroidNotificationManager._internal();

  factory AndroidNotificationManager() => instance;

  AndroidNotificationManager._internal();

  static const String tag = "AndroidNotificationManager";
  static const String notificationDefault = "default";
  static const String notificationPermanent = "permanent";
  static const String notificationFsi = "fsi";

  // EventChannel 实例
  final EventChannel _channel = const EventChannel('com.flareflow.android/pushAlert');
  // 事件流控制器
  final StreamController<String> _eventController = StreamController<String>.broadcast();
  // 公开的事件流
  Stream<String> get eventStream => _eventController.stream;

  void init() async {
    if (Platform.isAndroid) {
      // 签到相关任务
      CheckTask.instance.init();
      // 加载固定推送相关信息
      FixedPushTask.instance.init();
      // 常驻通知栏
      PermanentTask.instance.init();
      // 监听原生的闹钟通知，触发加载push
      initAlertPushListen();
    }
  }

  /// 删除对应任务的存储记录
  removeTaskStorage(String taskKey) {
    SafeStorage().remove(taskKey);
  }

  /// 发送通知
  void sendNotification(
    String type,
    String title,
    String content,
    Map<dynamic, dynamic> payload, {
    String imagePath = "",
    String imageRes = "",
    String canFlod = "true",
  }) async {
    try {
      _handleTrackEvent(payload);
      const platform = MethodChannel('com.flareflow.android/notification');
      await platform.invokeMethod('notification', {
        'type': type,
        'title': title,
        'body': content,
        'imagePath': imagePath,
        'imageRes': imageRes,
        'canFlod': canFlod,
        'watchNowLabel': AppTrans.pushWatchNow(),
        'closeLabel': AppTrans.pushClose(),
        'payload': jsonEncode(payload),
      });
    } catch (e) {
      FFLog.error('sendNotification error: $e', tag: tag);
    }
  }

  void _handleTrackEvent(Map<dynamic, dynamic> payload) {
    var shortPlayCode = payload["data"]?["shortPlayCode"] ?? "";
    // 根据需求，只有android需要在客户端埋点
    useTrackEvent(TrackEvent.push_send, extra: {
      TrackEvent.push_id: payload["data"]?["pushId"] ?? "",
      TrackEvent.title: payload["title"] ?? "",
      TrackEvent.content: payload["body"] ?? "",
      TrackEvent.reel_id: shortPlayCode,
    });
    if (shortPlayCode != "") {
      var notificationType = payload["data"]?["notificationType"] ?? "";
      var scene = EventValue.fromPush;
      if (notificationType == AndroidNotificationManager.notificationPermanent) {
        scene = EventValue.permanent;
      } else if (notificationType == AndroidNotificationManager.notificationFsi) {
        scene = EventValue.fsi;
      }
      useTrackEvent(TrackEvent.reel_show, extra: {
        TrackEvent.reel_id: shortPlayCode,
        TrackEvent.scene: scene,
      });
    }
  }

  void startAlert() async {
    if (Platform.isAndroid) {
      try {
        const platform = MethodChannel('com.flareflow.android/notification');
        await platform.invokeMethod('startAlert');
      } catch (e) {
        FFLog.error('startAlert error: $e', tag: tag);
      }
    }
  }

  void stopAlert() async {
    if (Platform.isAndroid) {
      try {
        const platform = MethodChannel('com.flareflow.android/notification');
        await platform.invokeMethod('stopAlert');
      } catch (e) {
        FFLog.error('startAlert error: $e', tag: tag);
      }
    }
  }

  // 监听原生的闹钟通知，触发加载push
  void initAlertPushListen() {
    // 监听 EventChannel 事件
    _channel.receiveBroadcastStream().listen((dynamic event) {
      if (event is String) {
        _eventController.add(event);
      }
    }, onError: (e) {
      FFLog.error('receiveBroadcastStream error: $e', tag: tag);
    });

    eventStream.listen((String event) {
      FFLog.info('eventStream.listen: $event', tag: tag);
      if (event == "checkPush") {
        CheckTask.instance.tryFetchPush();
        FixedPushTask.instance.tryFetchPush();
        PermanentTask.instance.tryFetchPush();
      }
    });
  }
}

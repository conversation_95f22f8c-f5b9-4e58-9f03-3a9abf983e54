import 'dart:io';

import 'package:audio_session/audio_session.dart';
import 'package:get/get.dart';
import 'package:lexo_ttplayer_decryption/ttsdk_manager.dart';
import 'package:lexo_ttplayer_decryption/vod_player_flutter.dart';
import 'package:lexo_ttplayer_decryption/vod_player_log.dart';
import 'package:lexo_ttplayer_decryption/vod_player_mediaSource.dart';
import 'package:lexo_ttplayer_decryption/vod_player_strategy.dart';
import 'package:lexo_ttplayer_decryption/vod_player_typedef.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/player/video_source_repo.dart';
import 'package:playlet/config/index.dart';

class PlayerService extends GetxService {
  static const String tag = "PlayerService";

  // 初始化播放器插件
  Future<PlayerService> init() async {
    // await FlutterTTSDKManager.openAllLog();
    TTFLogger.onLog = (logLevel, msg) {
      FFLog.debug("$logLevel $msg", tag: tag);
    };
    String appID = Config.playerAppId;
    String licPath = Config.playerLicPath;
    // Android 平台请传入有效的渠道号，用于统计；iOS 选填，默认为 App Store
    String channel = Platform.isAndroid ? 'your channel' : 'App Store';
    // 初始化点播配置
    TTSDKVodConfiguration vodConfig = TTSDKVodConfiguration();
    // 设置最大缓存 Size，默认 100M，可根据自身业务场景调整，超过缓存大小按照 LRU 规则清理
    vodConfig.cacheMaxSize = 300 * 1024 * 1024;

    TTSDKConfiguration sdkConfig =
        TTSDKConfiguration.defaultConfigurationWithAppIDAndLicPath(
      appID: appID,
      licenseFilePath: licPath,
      channel: channel,
    );
    sdkConfig.vodConfiguration = vodConfig;
    await FlutterTTSDKManager.startWithConfiguration(sdkConfig);
    FFLog.info("播放器插件初始化完成", tag: tag);
    await initPreloadStrategy();
    return this;
  }

  /// 开启预加载策略
  Future<void> initPreloadStrategy() async {
    FFLog.info("开启预加载策略", tag: tag);
    try {
      await TTVideoEngineStrategy.enableEngineStrategy(
        strategyType: TTVideoEngineStrategyType.preload,
        scene: TTVideoEngineStrategyScene.smallVideo,
      );
      await TTVideoEngineStrategy.init();
      TTVideoEngineStrategy.onCreatePreRenderEngine = ((source) async {
        FFLog.debug("创建预渲染引擎: ${source.getUniqueId}", tag: tag);
        VodPlayerFlutter player = VodPlayerFlutter();
        player.setUrlSource(source as TTVideoEngineUrlSource);
        return player;
      });
      FFLog.info("预加载策略初始化完成", tag: tag);
    } catch (e) {
      FFLog.error("预加载策略初始化失败: $e", tag: tag);
    }
  }

  /// 关闭预加载策略
  Future<void> disablePreloadStrategy() async {
    FFLog.info("关闭预加载策略");
    await TTVideoEngineStrategy.clearAllEngineStrategy();
  }

  // 设置预加载列表
  Future<void> setPreloadList(List<String> urls) async {
    try {
      if (urls.isEmpty) return;
      FFLog.debug("设置预加载列表: ${urls.length}个视频", tag: tag);


      // 创建媒体源的同时保存cacheKey
      List<TTVideoEngineMediaSource> sources = [];
      for (String url in urls) {
        if (url.isNotEmpty) {
          TTVideoEngineUrlSource source = VideoSourceRepo.createVideoSource(url);
          sources.add(source);
        }
      }

      if (sources.isNotEmpty) {
        await TTVideoEngineStrategy.setStrategyVideoSources(
            videoSources: sources);
        FFLog.debug("成功设置预加载列表: ${sources.length}个源", tag: tag);
      }
    } catch (e) {
      FFLog.error("设置预加载列表失败: $e", tag: tag);
    }
  }

  // 更新预加载列表
  Future<void> addPreloadList(List<String> urls) async {
    try {
      if (urls.isEmpty) return;
      FFLog.debug("添加预加载列表: ${urls.length}个视频}", tag: tag);

      // 创建媒体源的同时保存cacheKey
      List<TTVideoEngineMediaSource> sources = [];
      for (String url in urls) {
        if (url.isNotEmpty) {
          TTVideoEngineUrlSource source = VideoSourceRepo.createVideoSource(url);
          sources.add(source);
        }
      }

      if (sources.isNotEmpty) {
        await TTVideoEngineStrategy.addStrategyVideoSources(
            videoSources: sources);
        FFLog.debug("成功添加预加载列表: ${sources.length}个源", tag: tag);
      }
    } catch (e) {
      FFLog.error("添加预加载列表失败: $e", tag: tag);
    }
  }

  // 配置iOS设备的音频会话，使其在静音模式下也能播放声音
  Future<void> configureAudioSessionForSilentMode() async {
    if (Platform.isIOS) {
      try {
        final session = await AudioSession.instance;
        await session.configure(const AudioSessionConfiguration(
          avAudioSessionCategory: AVAudioSessionCategory.playback,
          avAudioSessionMode: AVAudioSessionMode.defaultMode,
        ));
        await session.setActive(true);
      } catch (e) {
        FFLog.error("配置音频会话失败: $e", tag: tag);
      }
    }
  }

  /// 清空预加载列表
  /// 用于在页面销毁时释放资源，避免内存泄漏
  Future<void> clearPreloadList() async {
    try {
      FFLog.info("清空预加载列表", tag: tag);
      // 设置空列表来清空预加载
      await setPreloadList([]);
      FFLog.info("预加载列表已清空", tag: tag);
    } catch (e) {
      FFLog.error("清空预加载列表失败: $e", tag: tag);
    }
  }
}

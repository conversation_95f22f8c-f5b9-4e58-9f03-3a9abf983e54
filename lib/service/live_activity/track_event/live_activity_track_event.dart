
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/service/live_activity/model/live_activity_model.dart';
import 'package:playlet/utils/track_event.dart';

class LiveActivityTrackEvent {

  // 实时活动发送
  // 实时活动触发时上报，无论用户是否真的有看到，只要在通知栏或者锁屏上有显示就上报
  static void realTimeActivitySend(LiveActivityType type) {
    _addEvent(TrackEvent.realTimeActivitySend, [
      EventKey.from,
      EventKey.sort,
    ], type);
  }

  // 实时活动显示
  // 实时活动在用户看到时上报
  static void realTimeActivityShow(LiveActivityType type) {
    _addEvent(TrackEvent.realTimeActivityShow, [
      EventKey.from,
      EventKey.sort,
    ], type);
  }

  // 实时活动点击
  // 实时活动点击时上报
  static void realTimeActivityClick(LiveActivityType type) {
    _addEvent(TrackEvent.realTimeActivityClick, [
      EventKey.from,
      EventKey.sort,
    ], type);
  }

  /// 实时活动曝光
  static void reelShow(String shortPlayId) {
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: shortPlayId,
      TrackEvent.scene: EventValue.realTimeActivity,
    });
  }

  static void _addEvent(
    String eventName, 
    List<String> paramNames, 
    LiveActivityType type, 
    {LiveActivityFrom from = LiveActivityFrom.notificationBar}) {
    Map<String, String> extra = {};
    for (String paramName in paramNames) {
      String? paramValue = _getParamValueForName(type, from, paramName);
      if (paramValue != null) {
        extra[paramName] = paramValue;
      }
    }
    useTrackEvent(eventName, extra: extra);
  }

  static String? _getParamValueForName(LiveActivityType type, LiveActivityFrom from, String paramName) {
    // 来源
    if (paramName == EventKey.from) {
      return from.value; 
    }
    // 类型
    if (paramName == EventKey.sort) {
      return type.getTrackEventParamValue();
    }
    return null;
  }
}
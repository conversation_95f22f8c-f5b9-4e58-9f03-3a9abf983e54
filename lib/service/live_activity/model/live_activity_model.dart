import 'package:get/get.dart';
import 'package:live_activities/models/live_activity_file.dart';

enum LiveActivityType {
  signIn(1),
  defaultType(2),
  watch(3),
  recommend(4);

  final int value;
  const LiveActivityType(this.value);

  static LiveActivityType? fromInt(int? value) {
    if (value == null) {
      return null;
    }
    return LiveActivityType.values.firstWhereOrNull(
      (type) => type.value == value,
    );
  }

  /// 获取埋点参数值
  String getTrackEventParamValue() {
    switch (this) {
      case LiveActivityType.signIn:
        return "signin";
      case LiveActivityType.defaultType:
        return "default";
      case LiveActivityType.watch:
        return "watch";
      case LiveActivityType.recommend:
        return "recommend";
    }
  }
}

enum LiveActivityFrom {
  lockScreen("lock_screen"),            // 锁屏
  notificationBar("notification_bar");  // 通知栏

  final String value;
  const LiveActivityFrom(this.value);
}

class LiveActivityBaseModel {
  LiveActivityType get type => LiveActivityType.defaultType;
  String? title;
  LiveActivityFileFromAsset? logoImage;
  String? buttonTitle;
  LiveActivityFileFromAsset? shortsImagePlaceholder;
  String? schemeURL;

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {
        'type': type.value,
        'title': title ?? "",
        'buttonTitle': buttonTitle ?? "",
        'schemeURL': schemeURL ?? "",
    };
    if (logoImage != null) {
      map['logoImage'] = logoImage;
    }
    if (shortsImagePlaceholder != null) {
      map['shortsImagePlaceholder'] = shortsImagePlaceholder;
    }
    return map;
  }
}

class LiveActivityDefaultModel extends LiveActivityBaseModel {
  @override
  LiveActivityType get type => LiveActivityType.defaultType;
}

class LiveActivitySignInModel extends LiveActivityBaseModel {
  @override
  LiveActivityType get type => LiveActivityType.signIn;

  LiveActivityFileFromAsset? coinsImage;
  LiveActivityFileFromAsset? bonusImage;
  String? remainText;
  int? remainTime;

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = super.toJson();
    map.addAll({
      'remainText': remainText ?? "",
      'remainTime': remainTime ?? 0,
    });
    if (coinsImage != null) {
      map['coinsImage'] = coinsImage;
    }
    if (bonusImage != null) {
      map['bonusImage'] = bonusImage;
    }
    return map;
  }
}

class LiveActivityRecommendModel extends LiveActivityBaseModel {
  @override
  LiveActivityType get type => LiveActivityType.recommend;

  LiveActivityFileFromUrl? shortsImage;
  LiveActivityFileFromAsset? playImage;
  String? collectText;
  int? collectNumber;

  String? shortPlayId;

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = super.toJson();
    map.addAll({
      'collectText': collectText?? "",
      'collectNumber': collectNumber?? 0,
    });
    if (shortsImage != null) {
      map['shortsImage'] = shortsImage;
    }
    if (playImage != null) {
      map['playImage'] = playImage;
    }
    return map;
  }
}

class LiveActivityWatchModel extends LiveActivityBaseModel {
  @override
  LiveActivityType get type => LiveActivityType.watch;

  LiveActivityFileFromUrl? shortsImage;
  LiveActivityFileFromAsset? playImage;
  String? shortPlayName;
  String? startTimeText;
  int? startTime;

  int? shortPlayId;

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = super.toJson();
    map.addAll({
      'shortPlayName': shortPlayName?? "",
      'startTimeText': startTimeText?? "",
      'startTime': startTime?? 0,
    });
    if (shortsImage != null) {
      map['shortsImage'] = shortsImage;
    }
    if (playImage != null) {
      map['playImage'] = playImage;
    }
    return map;
  }
}

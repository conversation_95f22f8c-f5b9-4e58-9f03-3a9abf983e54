import 'package:playlet/service/live_activity/model/live_activity_model.dart';
import 'package:playlet/utils/map_extension.dart';

class LiveActivityConfig {
  LiveActivityType? displayStyle;
  int? priority;

  String? activityType;
  String? activityName;
  String? activityKey;
  int? jumpType;
  String? contentTemplate;
  String? buttonText;
  String? displayLogic;
  String? otherLogic;

  LiveActivityConfig({
    this.displayStyle,
    this.priority,
    this.activityType,
    this.activityName,
    this.activityKey,
    this.jumpType,
    this.contentTemplate,
    this.buttonText,
    this.displayLogic,
    this.otherLogic,
  });

  LiveActivityConfig.fromJson(Map<String, dynamic> json) {
    displayStyle = LiveActivityType.fromInt(json['displayStyle']);
    priority = json.safeGet<int>('priority');
    activityType = json.safeGet<String>('activityType');
    activityName = json.safeGet<String>('activityName');
    activityKey = json.safeGet<String>('activityKey');
    jumpType = json.safeGet<int>('jumpType');
    contentTemplate = json.safeGet<String>('contentTemplate');
    buttonText = json.safeGet<String>('buttonText');
    displayLogic = json.safeGet<String>('displayLogic');
    otherLogic = json.safeGet<String>('otherLogic');
  }
}
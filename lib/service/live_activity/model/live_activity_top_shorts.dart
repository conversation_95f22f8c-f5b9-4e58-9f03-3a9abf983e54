import 'package:playlet/utils/map_extension.dart';

class LiveActivityTopShorts {
  String? shortPlayId;
  int? dramaId;
  String? shortPlayName;
  String? coverUrl;
  int? collectNum;
  int? episodeNum;

  LiveActivityTopShorts({
    this.shortPlayId,
    this.dramaId,
    this.shortPlayName,
    this.coverUrl,
    this.collectNum,
    this.episodeNum
  });

  factory LiveActivityTopShorts.fromJson(Map<String, dynamic> json) => LiveActivityTopShorts(
    shortPlayId: json.safeGet<String>('shortPlayId'),
    dramaId: json.safeGet<int>('dramaId'),
    shortPlayName: json.safeGet<String>('shortPlayName'),
    coverUrl: json.safeGet<String>('coverUrl'),
    collectNum: json.safeGet<int>('collectNum'),
    episodeNum: json.safeGet<int>('episodeNum')
  );
}
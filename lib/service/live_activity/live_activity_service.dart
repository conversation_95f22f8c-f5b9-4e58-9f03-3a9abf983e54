import 'dart:async';
import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:live_activities/live_activities.dart';
import 'package:live_activities/models/url_scheme_data.dart';
import 'package:playlet/api/live_activity_api.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/i18n/translation_service.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain_manager.dart';
import 'package:playlet/service/ios_app_delegate_service.dart';
import 'package:playlet/service/live_activity/model/live_activity_config.dart';
import 'package:playlet/service/live_activity/model/live_activity_model.dart';
import 'package:playlet/service/live_activity/services/base_handler.dart';
import 'package:playlet/service/live_activity/services/default_handler.dart';
import 'package:playlet/service/live_activity/services/live_activity_handler_protocol.dart';
import 'package:playlet/service/live_activity/services/recommend_handler.dart';
import 'package:playlet/service/live_activity/services/sign_in_handler.dart';
import 'package:playlet/service/live_activity/services/watch_handler.dart';
import 'package:playlet/utils/app_analysis.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/utils/map_extension.dart';

class LiveActivityService extends GetxService with WidgetsBindingObserver implements IOSAppDelegateHandlerProtocol {
  /// 实时活动配置
  List<LiveActivityConfig>? _configs;
  /// 实时活动处理器
  final Map<LiveActivityType, LiveActivityHandlerPrivateProtocol> _handlers = {};
  /// 任务
  final Map<LiveActivityType, LiveActivityBaseModel> _tasks = {};

  /// 实时活动插件
  LiveActivities? _liveActivitiesPlugin;
  /// 当前正在展示的实时活动id
  String? _currentActivityId;
  /// 当前正在执行的任务
  Completer<void>? _currentTaskCompleter;

  /// 实时活动跳转监听
  StreamSubscription<UrlSchemeData>? _urlSchemeSubscription;
  /// 语言变化监听
  StreamSubscription<String>? _languageSubscription;
  /// 监听进入沉浸页
  StreamSubscription<DetailsPageOpenedEvent>? _detailsPageOpenedSubscription;
  /// 监听关闭沉浸页
  StreamSubscription<DetailsPageClosedEvent>? _detailsPageClosedSubscription;

  /// 初始化实时活动服务
  void init() {
    if (!Platform.isIOS) {
      // 非iOS不处理
      return;
    }
    // 因为实时活动创建的时候会去请求系统的通知权限，所以要先等通知权限服务请求权限后，才能初始化实时活动
    DialogChainManager.instance.add(Chain(priority: DialogChainManager.liveActivityNotification, (chain, data) async {
      // 初始化实时活动服务
      try {
        // 注册应用生命周期观察者
        WidgetsBinding.instance.addObserver(this);
        Get.find<IOSAppDelegateService>().registerLaunchHandler(this);
        _addListner();
        _getConfig().then((_) {
          _setup();
        });
      } catch (e) {
        FFLog.error('实时活动服务初始化失败: $e');
      } finally {
        chain.finish();
      }
    }));
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    Get.find<IOSAppDelegateService>().removeLaunchHandler(this);
    _cancelListner();
    _stopActivity();
    super.onClose();
  }

  /// 获取对应类型的实时活动处理器
  LiveActivityHandlerProtocol? getHandler(LiveActivityType type) {
    return _handlers[type];
  }

  @override
  Future<void> onAppLaunch(Map? launchOptions) async {
    if (launchOptions == null || launchOptions.isEmpty) {
      return;
    }
    final launchURL = launchOptions["UIApplicationLaunchOptionsURLKey"];
    if (launchURL == null || launchURL.isEmpty) {
      return;
    }
    final url = Uri.parse(launchURL);
    if (url.host != liveActivityHost) {
      return;
    }
    await AppAnalysisStore.saveAppLaunchFrom(EventValue.realTimeActivity.toString());
  }

  Future<void> _addListner() async {
    await _cancelListner();
    
    final translationService = Get.find<TranslationService>();
    _languageSubscription = translationService.currentLanguage.listen((language) async {
      await _tryToStartAllTasks();
    });

    _detailsPageOpenedSubscription = eventBus.on<DetailsPageOpenedEvent>().listen((event) {
      // 进入沉浸页设置实时活动状态
      _changeDetailsPageState(true, event.detailsController);
    });

    _detailsPageClosedSubscription = eventBus.on<DetailsPageClosedEvent>().listen((event) {
      // 停止实时活动
      _changeDetailsPageState(false, event.detailsController);
    });
  }

  Future<void> _cancelListner() async {
    await _languageSubscription?.cancel();
    _languageSubscription = null;

    await _detailsPageOpenedSubscription?.cancel();
    _detailsPageOpenedSubscription = null;

    await _detailsPageClosedSubscription?.cancel();
    _detailsPageClosedSubscription = null;
  }

  /// 切换沉浸页状态
  void _changeDetailsPageState(bool isInDetailsPage, DetailsController? detailsController) {
    _handlers.forEach((type, handler) {
      handler.changeDetailsPageState(isInDetailsPage, detailsController);
    });
  }

  /// 注册实时活动处理器
  void _registerHandlers() {
    _handlers.clear();
    final configList = _configs;
    if (configList == null) {
      return;
    }
    for (var config in configList) {
      final type = config.displayStyle;
      if (type == null) {
        continue;
      }
      LiveActivityHandlerPrivateProtocol handler = _createHandler(type);
      handler.priority = config.priority;
      _handlers[type] = handler;
    }
  }

  /// 创建实时活动处理器
  LiveActivityHandlerPrivateProtocol _createHandler(LiveActivityType type) {
    LiveActivityHandlerPrivateProtocol handler;
    switch (type) {
      case LiveActivityType.defaultType:
        handler = LiveActivityDefaultHandler();
      case LiveActivityType.signIn:
        handler = LiveActivitySignInHandler();
      case LiveActivityType.watch:
        handler = LiveActivityWatchHandler();
      case LiveActivityType.recommend:
        handler = LiveActivityRecommendHandler();
    }
    handler.onStartTask = _onStartTask;
    handler.onUpdateTask = _onUpdateTask;
    handler.onStopTask = _onStopTask;
    handler.onInit();
    return handler;
  }

  /// 初始化实时活动插件
  Future<void> _setup() async {
    LiveActivities liveActivitiesPlugin = LiveActivities();
    _liveActivitiesPlugin = liveActivitiesPlugin;
    await liveActivitiesPlugin.init(appGroupId: Config.appGroupId, urlScheme: "ff");

    await _cancelUrlSchemeSubscription();
    _urlSchemeSubscription = liveActivitiesPlugin.urlSchemeStream().listen(_onHandleClickEvent);
    liveActivitiesPlugin.activityUpdateStream.listen((event) {
      FFLog.debug('Activity update: $event');
    });
    
    await _tryToStartAllTasks();
  }

    @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      List<LiveActivityHandlerPrivateProtocol> handlers = _getSortedHandlers();
      for (var handler in handlers) {
        handler.onAppResume();
      }
    }
    super.didChangeAppLifecycleState(state);
  }

  Future<void> _tryToStartAllTasks() async {
    List<LiveActivityHandlerPrivateProtocol> handlers = _getSortedHandlers();
    for (var handler in handlers) {
      await handler.startTask();
    }
  }

  Future<void> _getConfig() async {
    List<LiveActivityConfig>? configList = await LiveActivityApi.getLiveActivityConfig();
    _configs = configList;
    if (configList == null || configList.isEmpty) {
      _handlers.clear();
      return;
    }
    _registerHandlers();
  }

  Future<void> _addDefaultTask() async {
    LiveActivityHandlerProtocol? handler = getHandler(LiveActivityType.defaultType);
    if (handler == null) {
      return;
    }
    await handler.startTask();
  }

  /// 处理开始任务
  Future<void> _onStartTask(LiveActivityBaseModel model) async {
    _tasks[model.type] = model;
    await _excuteTask(true);
  }

  /// 处理更新任务
  Future<void> _onUpdateTask(LiveActivityBaseModel model) async {
    _tasks[model.type] = model;
    await _excuteTask(false);
  }

  /// 处理停止任务
  Future<void> _onStopTask(LiveActivityType type, LiveActivityBaseModel? model) async {
    _tasks.remove(type);
    await _excuteTask(false);
  }

  /// 执行任务
  Future<void> _excuteTask(bool isCreate) async {
    final liveActivitiesPlugin = _liveActivitiesPlugin;
    if (liveActivitiesPlugin == null) {
      return;
    }
    LiveActivityBaseModel? model = await _popTask();
    if (model == null) {
      // 没有任务，添加默认任务
      await _addDefaultTask();
      return;
    }
    
    // 等待前一个任务完成
    final completer = _currentTaskCompleter;
    if (completer != null && !completer.isCompleted) {
      await completer.future;
    }
    
    // 设置当前任务的 Completer
    _currentTaskCompleter = Completer<void>();
    
    try {
      final data = model.toJson();
      final activityId = _currentActivityId;
      if (activityId == null) {
        await liveActivitiesPlugin.endAllActivities();
        _currentActivityId = await liveActivitiesPlugin.createActivity(data);
      } else {
        await liveActivitiesPlugin.updateActivity(activityId, data);
      }
      LiveActivityHandlerPrivateProtocol? handler = _handlers[model.type];
      if (handler != null) {
        handler.onSend(model, isCreate);
      }
      // 标记任务完成
      _currentTaskCompleter?.complete();
      _currentTaskCompleter = null;
    } catch (e) {
      FFLog.error('启动或更新实时活动失败 $e');
      _currentTaskCompleter?.complete();
      _currentTaskCompleter = null;
    }
  }

  /// 停止实时活动
  Future<void> _stopActivity() async {
    try {
      await _cancelUrlSchemeSubscription();
      _tasks.clear();

      // 等待前一个任务完成
      final completer = _currentTaskCompleter;
      if (completer != null && !completer.isCompleted) {
        await completer.future;
      }
      
      // 设置当前任务的 Completer
      _currentTaskCompleter = Completer<void>();
      await _liveActivitiesPlugin?.endAllActivities();
      _currentTaskCompleter?.complete();
    } catch (e) {
      FFLog.error('停止实时活动失败 $e');
    }
    _currentActivityId = null;
  }

  Future<void> _cancelUrlSchemeSubscription() async {
    if (_urlSchemeSubscription == null) {
      return;
    }
    await _urlSchemeSubscription?.cancel();
    _urlSchemeSubscription = null;
  }

  List<LiveActivityHandlerPrivateProtocol> _getSortedHandlers() {
    List<LiveActivityHandlerPrivateProtocol> handlers = _handlers.values.toList();
    handlers.sort((a, b) {
      final aPriority = a.priority;
      final bPriority = b.priority;
      if (aPriority == bPriority) {
        return 0;
      }
      if (aPriority == null) {
        return 1;
      }
      if (bPriority == null) {
        return -1;
      }
      return bPriority.compareTo(aPriority);
    });
    return handlers;
  }

  /// 获取对应类型的任务
  Future<LiveActivityBaseModel?> _popTask() async {
    List<LiveActivityHandlerPrivateProtocol> handlers = _getSortedHandlers();
    for (var handler in handlers) {
      LiveActivityType type = handler.type;
      LiveActivityBaseModel? model = _tasks[type];
      if (model == null) {
        continue;
      }
      bool valid = await handler.checkTaskValid(model);
      if (!valid) {
        // 任务无效
        continue;
      }
      return model;
    }
    return null;
  }

  Future<void> _onHandleClickEvent(UrlSchemeData schemeData) async {
    if (schemeData.host != liveActivityHost) {
      return;
    }
    await AppAnalysisStore.saveAppLaunchFrom(EventValue.realTimeActivity.toString());
    
    LiveActivityType? activityType;
    Map<String, String> queryParameters = {};
    for (var param in schemeData.queryParameters) {
      final name = param.safeGet<String>("name");
      if (name == null || name.isEmpty) {
        continue;
      }
      final value = param.safeGet<String>("value");
      if (value == null || value.isEmpty) {
        continue;
      }
      queryParameters[name] = value;
      if (name == liveActivityTypeKey) {
        try {
          activityType = LiveActivityType.fromInt(int.parse(value));
        } catch (e) {
          FFLog.error('解析实时活动类型失败 $e');
        }
      }
    }
    if (activityType == null) {
      return;
    }
    LiveActivityHandlerPrivateProtocol? handler = _handlers[activityType];
    if (handler == null) {
      return;
    }
    await handler.onClick(queryParameters);
  }
}
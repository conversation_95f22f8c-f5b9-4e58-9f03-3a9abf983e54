import 'package:flutter/foundation.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/service/live_activity/model/live_activity_model.dart';
import 'package:playlet/service/live_activity/services/live_activity_handler_protocol.dart';
import 'package:playlet/service/live_activity/track_event/live_activity_track_event.dart';

const String liveActivityScheme = "ff";
const String liveActivityHost = "live_activity";
const String liveActivityTypeKey = "type";

class LiveActivityBaseHandler implements LiveActivityHandlerPrivateProtocol {
  LiveActivityBaseModel? currentModel;

  /// 是否在沉浸页中
  bool isInDetailsPage = false;

  /// 沉浸页控制器，弱引用
  WeakReference<DetailsController>? detailsControllerRef;
  
  @override
  bool enable = true;

  @override
  LiveActivityType get type => throw UnimplementedError();

  @override
  int? priority;

  @override
  Function(LiveActivityBaseModel model)? onStartTask;

  @override
  Function(LiveActivityType type, LiveActivityBaseModel? model)? onStopTask;

  @override
  Function(LiveActivityBaseModel model)? onUpdateTask;

  @override
  void onInit() {
    // Do nothing
  }

  @mustCallSuper
  @override
  void changeDetailsPageState(bool isIn, DetailsController? detailsController) {
    isInDetailsPage = isIn;
    if (isIn == true && detailsController != null) {
      detailsControllerRef = WeakReference<DetailsController>(detailsController);
    } else {
      detailsControllerRef = null;
    }
  }

  @mustCallSuper
  @override
  Future<void> startTask() async {
    final model = currentModel;
    if (model == null) {
      await stopTask();
      return;
    }
    bool isValid = await checkTaskValid(model);
    if (!isValid) {
      await stopTask();
      return;
    }
    await onStartTask?.call(model);
  }

  @mustCallSuper
  @override
  Future<void> stopTask() async {
    final model = currentModel;
    if (model == null) {
      return;
    }
    await onStopTask?.call(model.type, model);
    currentModel = null;
  }

  @mustCallSuper
  @override
  Future<void> updateTask() async {
    final model = currentModel;
    if (model == null) {
      stopTask();
      return;
    }
    bool isValid = await checkTaskValid(model);
    if (!isValid) {
      stopTask();
      return;
    }
    await onUpdateTask?.call(model);
  }
  
  @mustCallSuper
  @override
  Future<bool> checkTaskValid(LiveActivityBaseModel? model) async {
    if (!enable) {
      return false;
    }
    return true;
  }
  
  @mustCallSuper
  @override
  Future<void> onClick(Map<String, String> queryParameters) async {
    LiveActivityTrackEvent.realTimeActivityClick(type);
  }

  @mustCallSuper
  @override
  Future<void> onSend(LiveActivityBaseModel? model, bool isCreate) async {
    if (!isCreate) {
      return;
    }
    LiveActivityTrackEvent.realTimeActivitySend(type);
  }

  @mustCallSuper
  @override
  Future<void> onAppResume() async {
    LiveActivityTrackEvent.realTimeActivityShow(type);
  }

  /// 获取scheme url
  String getSchemeUrl(Map<String, dynamic>? params) {
    const String separator = "&";
    String paramsStr = "";
    if (params != null && params.isNotEmpty) {
      paramsStr = "$separator${params.entries.map((e) => "${e.key}=${e.value}").join(separator)}";
    }
    return "$liveActivityScheme://$liveActivityHost?$liveActivityTypeKey=${type.value}$paramsStr";
  }
}
import 'dart:async';

import 'package:get/get.dart';
import 'package:live_activities/models/live_activity_file.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/live_activity/model/live_activity_model.dart';
import 'package:playlet/service/live_activity/services/base_handler.dart';
import 'package:playlet/service/live_activity/track_event/live_activity_track_event.dart';
import 'package:playlet/utils/date_time_extension.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/utils/file_storage.dart';
import 'package:playlet/utils/map_extension.dart';

class LiveActivityWatchHandler extends LiveActivityBaseHandler {
  final String _shortPlayIdKey = "shortPlayId";

  @override
  LiveActivityType get type => LiveActivityType.watch;

  /// 当前播放剧信息监听器
  LightSubscription<ShortPlayDetail?>? _shortPlayDetailSubscription;

  /// 监听播放器状态变化
  StreamSubscription<PlayerStatusChangedEvent>? _playerStatusChangedSubscription;

  /// 累加距离上次观看时间的定时器
  Timer? _timer;

  late final LiveActivityWatchModel _watchModel = LiveActivityWatchModel()
    ..logoImage = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_logo.png',
    )
    ..shortsImagePlaceholder = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_cover.png',
    )
    ..playImage = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_play.png',
    );

  @override
  void onInit() {
    super.onInit();

    _addListener();
  }

  @override
  void changeDetailsPageState(bool isIn, DetailsController? detailsController) {
    super.changeDetailsPageState(isIn, detailsController);
    checkTaskValid(currentModel).then((isValid) async {
      if (isValid) {
        await startTask();
      } else {
        await stopTask();
      }
    });
  }
    
  @override
  Future<void> startTask() async {
    await _addShortDetailListener();
    _updateShortsInfo(detailsControllerRef?.target?.viewModel.currentDramaModel.shortPlayDetail);
    _updateStartTime(isReset: true);
    currentModel = _watchModel;
    await super.startTask();
  }

  @override
  Future<void> stopTask() async {
    await _cancelShortDetailListener();
    _cancelTimer();
    await super.stopTask();
  }

  @override
  Future<bool> checkTaskValid(LiveActivityBaseModel? model) async {
    bool isValid = await super.checkTaskValid(model);
    if (!isValid) {
      return isValid;
    }
    if (!isInDetailsPage) {
      return false;
    }
    if (detailsControllerRef?.target == null) {
      return false;
    }
    return true;
  }

  @override
  Future<void> onSend(LiveActivityBaseModel? model, bool isCreate) async {
    await super.onSend(model, isCreate);
    
    if (!isCreate) {
      return;
    }
    final watchModel = model as LiveActivityWatchModel?;
    if (watchModel == null) {
      return;
    }
    int? shortPlayId = watchModel.shortPlayId;
    if (shortPlayId == null) {
      return;
    }
    LiveActivityTrackEvent.reelShow(shortPlayId.toString());
  }

  @override
  Future<void> onClick(Map<String, String> queryParameters) async {
    await super.onClick(queryParameters);

    if (isInDetailsPage) {
      // 已经在沉浸页，不处理
      return;
    }
    
    final shortPlayId = queryParameters.safeGet<String>(_shortPlayIdKey);
    if (shortPlayId == null) {
      return;
    }
    try {
      await AppNavigator.startDetailsPage(DetailsOptions(
        businessId: int.parse(shortPlayId),
        scene: EventValue.immersion,
        from: EventValue.realTimeActivity,
      ));
    } catch (e) {
      FFLog.error("onClick error: $e");
    }
  }

  void _onPlayStarted() {
    _cancelTimer();
    _updateStartTime(isReset: true);
    updateTask();
  }

  void _onPlayEnded() {
    _updateStartTime(isReset: true);
    _startTimer();
  }

  Future<void> _addListener() async {
    await _cancelListener();

    _playerStatusChangedSubscription = eventBus.on<PlayerStatusChangedEvent>().listen((event) async {
      // 进入沉浸页设置实时活动状态
      if (event.isPlaying) {
        _onPlayStarted();
      } else {
        _onPlayEnded();
      }
    });
  }

  Future<void> _cancelListener() async {
    await _playerStatusChangedSubscription?.cancel();
    _playerStatusChangedSubscription = null;
  }

  Future<void> _addShortDetailListener() async {
    await _cancelShortDetailListener();
    _shortPlayDetailSubscription = detailsControllerRef?.target?.shortPlayDetail.listen((detail) async {
      if (!isInDetailsPage) {
        // 不在沉浸页中，不更新
        return;
      }
      _updateShortsInfo(detail);
      await updateTask();
    }) as LightSubscription<ShortPlayDetail?>?;
  }

  Future<void> _cancelShortDetailListener() async {
    await _shortPlayDetailSubscription?.cancel();
    _shortPlayDetailSubscription = null;
  }

  void _updateShortsInfo(ShortPlayDetail? detail) {
    if (detail == null) {
      return;
    }
    _watchModel.shortPlayName = detail.shortPlayName;
    String? imageUrl = detail.picUrl;
    if (imageUrl != null && imageUrl.isNotEmpty) {
      final options = LiveActivityImageFileOptions(resizeFactor: 0.1);
      _watchModel.shortsImage = LiveActivityFileFromUrl.image(FileStorage.removeAuthKeyFromImageUrl(imageUrl), imageOptions: options);
    } else {
      _watchModel.shortsImage = null;
    }
    
    Map<String, dynamic> params = {};
    final shortPlayId = detail.id;
    _watchModel.shortPlayId = shortPlayId;

    if (shortPlayId != null) {
      params[_shortPlayIdKey] = shortPlayId;
    }
    _watchModel.schemeURL = getSchemeUrl(params);
  }

  void _updateStartTime({bool isReset = false}) {
    int seconds = 0;
    if (!isReset) {
      seconds = _watchModel.startTime ?? 0;
      seconds ++;
    }
    _watchModel.startTime = seconds;
    _watchModel.startTimeText = AppTrans.liveActivityWatchStartTime(DateTimeExtension.convertSecondsToString(seconds));
    _watchModel.title = AppTrans.liveActivityWatchTitle();
    _watchModel.buttonTitle = AppTrans.liveActivityWatchButtonTitle();
  }

  void _startTimer() {
    _cancelTimer();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      _updateStartTime(isReset: false);
      await updateTask();
    });
  }
  
  void _cancelTimer() {
    _timer?.cancel();
    _timer = null;
  }
}
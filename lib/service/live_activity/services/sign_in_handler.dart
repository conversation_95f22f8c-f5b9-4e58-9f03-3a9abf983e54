import 'dart:async';

import 'package:get/get.dart';
import 'package:live_activities/models/live_activity_file.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/live_activity/model/live_activity_model.dart';
import 'package:playlet/service/live_activity/services/base_handler.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/date_time_extension.dart';

class LiveActivitySignInHandler extends LiveActivityBaseHandler {

  @override
  LiveActivityType get type => LiveActivityType.signIn;
  
  /// 签到状态监听
  StreamSubscription<SignInStatus>? _signInStatusSubscription;

  /// 离签到截止剩余时间秒数
  int _todayEndSignRemainSecondsValue = 0;
  int get _todayEndSignRemainSeconds => _todayEndSignRemainSecondsValue;
  set _todayEndSignRemainSeconds(int seconds) {
    _todayEndSignRemainSecondsValue = seconds < 0 ? 0 : seconds;
    _updateRemainTime();
  }

  /// 递减距离下次签到时间的定时器
  Timer? _timer;

  late final LiveActivitySignInModel _signInModel = LiveActivitySignInModel()
    ..logoImage = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_logo.png',
    )
    ..coinsImage = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_coins.png',
    )
    ..bonusImage = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_bonus.png',
    )
    ..schemeURL = getSchemeUrl(null);

  @override
  void onInit() async {
    super.onInit();
    // 开启签到状态监听
    await _addSignInStatusListener();
  }

  @override
  void changeDetailsPageState(bool isIn, DetailsController? detailsController) async {
    super.changeDetailsPageState(isIn, detailsController);
    await _updateTaskState();
  }
  
  @override
  Future<void> startTask() async {
    currentModel = _signInModel;
    _updateRemainTime();
    _startTimer();
    await super.startTask();
  }

  @override
  Future<void> stopTask() async {
    _cancelTimer();
    await super.stopTask();
  }

  @override
  Future<bool> checkTaskValid(LiveActivityBaseModel? model) async {
    bool isValid = await super.checkTaskValid(model);
    if (!isValid) {
      return isValid;
    }
    if (isInDetailsPage) {
      // 在沉浸页中，不显示活动
      return false;
    }
    SignInStatus status = _getUserSerivce().signInStatus.value;
    if (status == SignInStatus.unknown) {
      // 未获取到签到状态，不显示活动
      return false;
    }
    if (status == SignInStatus.signed) {
      // 当天已签到，不显示活动
      return false;
    }
    return true;
  }

  @override
  Future<void> onClick(Map<String, String> queryParameters) async {
    await super.onClick(queryParameters);
    AppNavigator.startRewardsPage(from: EventValue.realTimeActivity);
  }

  /// 监听签到状态
  Future<void> _addSignInStatusListener() async {
    await _cancelSignInStatusListener();
    _signInStatusSubscription = _getUserSerivce().signInStatus.listen(_onSignInStatusDidChanged);
  }

  /// 取消签到状态监听
  Future<void> _cancelSignInStatusListener() async {
    await _signInStatusSubscription?.cancel();
    _signInStatusSubscription = null;
  }

  /// 签到状态改变回调
  Future<void> _onSignInStatusDidChanged(SignInStatus status) async {
    int? todayEndSignTime = _getUserSerivce().todayEndSignTime;
    if (todayEndSignTime != null) {
      _todayEndSignRemainSeconds = ((todayEndSignTime - AppDeviceService().getCurrentCalibratedTime()) / 1000).floor();
    } else {
      _todayEndSignRemainSeconds = 0;
    }
    await _updateTaskState();
  }

  /// 更新任务状态
  Future<void> _updateTaskState() async {
    bool isValid = await checkTaskValid(currentModel);
    if (isValid) {
      await startTask();
    } else {
      await stopTask();
    }
  }

  void _updateRemainTime() {
    _signInModel.title = AppTrans.liveActivitySigninTitle();
    _signInModel.buttonTitle = AppTrans.liveActivitySigninButtonTitle();
    _signInModel.remainTime = _todayEndSignRemainSeconds;
    _signInModel.remainText = AppTrans.liveActivitySigninRemain(DateTimeExtension.convertSecondsToString(_todayEndSignRemainSeconds));
  }

  void _startTimer() {
    _cancelTimer();
    if (_todayEndSignRemainSeconds <= 0) {
      return;
    }
    _timer = Timer.periodic(const Duration(seconds: 1), _onTimerEvent);
  }
  
  void _cancelTimer() {
    _timer?.cancel();
    _timer = null;
  }

  void _onTimerEvent(Timer timer) async {
    _todayEndSignRemainSeconds --;
    if (_todayEndSignRemainSeconds <= 0) {
      // 签到时间已到，重置签到状态
      UserService userService = _getUserSerivce();
      userService.signInStatus.value = SignInStatus.unknown;
      // 重新获取签到状态
      unawaited(userService.getSignInStatus());
      return;
    }
    _updateRemainTime();
    await updateTask();
  }

  UserService _getUserSerivce() {
    return Get.find<UserService>();
  }
}
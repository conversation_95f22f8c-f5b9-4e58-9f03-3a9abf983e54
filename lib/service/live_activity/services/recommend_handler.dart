import 'dart:async';

import 'package:get/get.dart';
import 'package:live_activities/models/live_activity_file.dart';
import 'package:playlet/api/live_activity_api.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/i18n/translation_service.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/live_activity/model/live_activity_model.dart';
import 'package:playlet/service/live_activity/model/live_activity_top_shorts.dart';
import 'package:playlet/service/live_activity/services/base_handler.dart';
import 'package:playlet/service/live_activity/track_event/live_activity_track_event.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/file_storage.dart';
import 'package:playlet/utils/map_extension.dart';

class LiveActivityTopShortsLoader {
  /// 推荐的剧
  List<LiveActivityTopShorts>? topShortsList;

  /// 推荐剧是否正在加载
  Completer<void>? _isLoadingCompleter;

  Future<void> loadIfNeeded() async {
    final completer = _isLoadingCompleter;
    if (completer != null && !completer.isCompleted) {
      // 正在请求推荐剧，不重复请求
      return;
    }
    if (topShortsList != null) {
      // 已经加载过了，不需要再次加载
      return;
    }
    _isLoadingCompleter = Completer();
    List<LiveActivityTopShorts>? list = await LiveActivityApi.getTopRechargeShortPlays();
    topShortsList = list;
    if (list == null || list.isEmpty) {
      _isLoadingCompleter?.complete();
      _isLoadingCompleter = null;
      return;
    }
    _isLoadingCompleter?.complete();
    _isLoadingCompleter = null;
  }
}

class LiveActivityRecommendHandler extends LiveActivityBaseHandler {
  final String _shortPlayIdKey = "shortPlayId";
  final String _episodeNumKey = "episodeNum";

  @override
  LiveActivityType get type => LiveActivityType.recommend;

  /// 签到状态监听
  StreamSubscription<SignInStatus>? _signInStatusSubscription;

  /// 当前显示的推荐剧索引
  int? _currentShowIndex;
  /// 当前推荐剧是否已展示过
  bool _isCurrentRecommendShown = false;

  /// 保存语言对应的推荐的剧
  final Map<String, LiveActivityTopShortsLoader> _topShortsForLanguage = {};

  LiveActivityTopShortsLoader get _topShortsLoader {
    String language = _currentLanguage;
    LiveActivityTopShortsLoader? loader = _topShortsForLanguage[language];
    if (loader == null) {
      loader = LiveActivityTopShortsLoader();
      _topShortsForLanguage[language] = loader;
    }
    return loader;
  }

  /// 当前语言
  String get _currentLanguage {
    final translationService = Get.find<TranslationService>();
    String language = translationService.getCurrentLanguage();
    return language;
  }

  late final LiveActivityRecommendModel _recommendModel = LiveActivityRecommendModel()
    ..logoImage = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_logo.png',
    )
    ..shortsImagePlaceholder = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_cover.png',
    )
    ..playImage = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_play.png',
    );

  @override
  void onInit() async {
    super.onInit();
    // 开启签到状态监听
    await _addSignInStatusListener();
  }

  @override
  void changeDetailsPageState(bool isIn, DetailsController? detailsController) async {
    super.changeDetailsPageState(isIn, detailsController);
    await _updateTaskState();
  }
  
  @override
  Future<void> startTask() async {
    currentModel = _recommendModel;
    await _topShortsLoader.loadIfNeeded();
    final index = _currentShowIndex;
    if (index == null) {
      _toNextTopShorts();
      return;
    }
    _updateRecommendInfo(index);
    await super.startTask();
  }

  @override
  Future<bool> checkTaskValid(LiveActivityBaseModel? model) async {
    bool isValid = await super.checkTaskValid(model);
    if (!isValid) {
      return isValid;
    }
    final topShorts = _topShortsLoader.topShortsList;
    if (topShorts == null || topShorts.isEmpty) {
      // 没有推荐剧，不显示活动
      return false;   
    }
    if (isInDetailsPage) {
      // 在沉浸页中，不显示活动
      return false;
    }
    SignInStatus status = _getUserSerivce().signInStatus.value;
    if (status != SignInStatus.signed) {
      // 当天未签到，不显示活动
      return false;
    }
    // 已签到，显示活动
    return true;
  }

  @override
  Future<void> onSend(LiveActivityBaseModel? model, bool isCreate) async {
    await super.onSend(model, isCreate);
    _isCurrentRecommendShown = true;

    if (!isCreate) {
      return;
    }
    final recommendModel = model as LiveActivityRecommendModel?;
    if (recommendModel == null) {
      return;
    }
    String? shortPlayId = recommendModel.shortPlayId;
    if (shortPlayId == null || shortPlayId.isEmpty) {
      return;
    }
    LiveActivityTrackEvent.reelShow(shortPlayId);
  }

  @override
  Future<void> onAppResume() async {
    super.onAppResume();
    _toNextTopShorts();
  }

  @override
  Future<void> onClick(Map<String, String> queryParameters) async {
    await super.onClick(queryParameters);
    
    final shortPlayId = queryParameters.safeGet<String>(_shortPlayIdKey);
    if (shortPlayId == null) {
      return;
    }
    final episodeNum = queryParameters.safeGet<int>(_episodeNumKey) ?? 1;
    try {
      await AppNavigator.startDetailsPage(DetailsOptions(
        businessId: int.parse(shortPlayId),
        playerEpisodeIndex: episodeNum - 1,
        scene: EventValue.immersion,
        from: EventValue.realTimeActivity,
      ));
    } catch (e) {
      FFLog.error("onClick error: $e");
    }
  }

  /// 监听签到状态
  Future<void> _addSignInStatusListener() async {
    await _cancelSignInStatusListener();
    _signInStatusSubscription = _getUserSerivce().signInStatus.listen(_onSignInStatusDidChanged);
  }

  /// 取消签到状态监听
  Future<void> _cancelSignInStatusListener() async {
    await _signInStatusSubscription?.cancel();
    _signInStatusSubscription = null;
  }

  /// 签到状态改变回调
  Future<void> _onSignInStatusDidChanged(SignInStatus status) async {
    await _updateTaskState();
  }

  /// 更新任务状态
  Future<void> _updateTaskState() async {
    bool isValid = await checkTaskValid(currentModel);
    if (isValid) {
      await startTask();
    } else {
      await stopTask();
    }
  }

  LiveActivityTopShorts? _getCurrentTopShorts() {
    final topShortsList = _topShortsLoader.topShortsList;
    if (topShortsList == null || topShortsList.isEmpty) {
      // 没有推荐短剧，不需要更新
      return null;
    }
    final index = _currentShowIndex;
    if (index == null) {
      return null;
    }
    if (index >= topShortsList.length) {
      // 越界了，一般不会出现这个情况，但还是要防护
      return null;
    }
    final topShorts = topShortsList[index];
    return topShorts;
  }

  void _updateRecommendInfo(int index) {
    LiveActivityTopShorts? topShorts = _getCurrentTopShorts();
    if (topShorts == null) {
      return;
    }
    try {
      _recommendModel.title = topShorts.shortPlayName;
      _recommendModel.buttonTitle = AppTrans.liveActivityRecommendButtonTitle();
      _recommendModel.collectNumber = topShorts.collectNum ?? 0;
      _recommendModel.collectText = AppTrans.liveActivityRecommendCollect(_recommendModel.collectNumber);
      String? imageUrl = topShorts.coverUrl;
      if (imageUrl != null && imageUrl.isNotEmpty) {
        final options = LiveActivityImageFileOptions(resizeFactor: 0.1);
        _recommendModel.shortsImage = LiveActivityFileFromUrl.image(FileStorage.removeAuthKeyFromImageUrl(imageUrl), imageOptions: options);
      } else {
        _recommendModel.shortsImage = null;
      }

      Map<String, dynamic> params = {};
      final shortPlayId = topShorts.shortPlayId;
      _recommendModel.shortPlayId = shortPlayId;
      
      if (shortPlayId != null && shortPlayId.isNotEmpty) {
        params[_shortPlayIdKey] = shortPlayId;
      }
      final episodeNum = topShorts.episodeNum ?? 1;
      params[_episodeNumKey] = episodeNum;
      _recommendModel.schemeURL = getSchemeUrl(params);

    } catch (e) {
      FFLog.error("updateRecommendInfo error: $e");
    }
  }

  /// 轮询下一个推荐短剧
  Future<void> _toNextTopShorts() async {
    final topShortsList = _topShortsLoader.topShortsList;
    if (topShortsList == null || topShortsList.isEmpty) {
      // 没有推荐短剧
      await _updateTaskState();
      return;
    }
    int? index = _currentShowIndex;
    if (index == null) {
      index = 0;
    } else if (_isCurrentRecommendShown == true) {
      // 已经展示过了，才会轮询下一个
      index++;
    }
    if (index >= topShortsList.length) {
      // 重新轮询
      index = 0;
    }
    _updateCurrentShowIndex(index);
    await _updateTaskState();
  }

  void _updateCurrentShowIndex(int index) {
    if (_currentShowIndex == index) {
      return;
    }
    _currentShowIndex = index;
    // 此处要把当前推荐短剧置为未展示过
    _isCurrentRecommendShown = false;
  }

  UserService _getUserSerivce() {
    return Get.find<UserService>();
  }
}
import 'package:live_activities/models/live_activity_file.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/service/live_activity/model/live_activity_model.dart';
import 'package:playlet/service/live_activity/services/base_handler.dart';

class LiveActivityDefaultHandler extends LiveActivityBaseHandler {
  @override
  LiveActivityType get type => LiveActivityType.defaultType;

  late final LiveActivityDefaultModel _defaultModel = LiveActivityDefaultModel()
    ..logoImage = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_logo.png',
    )
    ..shortsImagePlaceholder = LiveActivityFileFromAsset.image(
      'assets/live_activity/live_activity_cover.png',
    )
    ..schemeURL = getSchemeUrl(null);
    
  @override
  Future<void> startTask() async {
    _defaultModel.title = AppTrans.liveActivityDefaultTitle();
    _defaultModel.buttonTitle = AppTrans.liveActivityDefaultButtonTitle();
    currentModel = _defaultModel;
    await super.startTask();
  }
}
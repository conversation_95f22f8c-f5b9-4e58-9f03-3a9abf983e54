import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/service/live_activity/model/live_activity_model.dart';

abstract class LiveActivityHandlerProtocol {
  /// 开始实时活动任务
  Future<void> startTask();

  /// 更新实时活动任务
  Future<void> updateTask();

  /// 停止实时活动任务
  Future<void> stopTask();
}

abstract class LiveActivityHandlerPrivateProtocol extends LiveActivityHandlerProtocol {
  /// 实时活动开关
  bool enable = true;

  LiveActivityType get type;

  /// 实时活动的优先级
  int? priority;

  /// 初始化回调
  void onInit();

  /// 切换沉浸页状态
  void changeDetailsPageState(bool isInDetailsPage, DetailsController? detailsController);
  
  /// 实时活动开始的回调
  Function(LiveActivityBaseModel model)? onStartTask;
  
  /// 实时活动更新的回调
  Function(LiveActivityBaseModel model)? onUpdateTask;

  /// 实时活动结束的回调
  Function(LiveActivityType type, LiveActivityBaseModel? model)? onStopTask;

  /// 检查实时活动任务是否有效
  Future<bool> checkTaskValid(LiveActivityBaseModel? model);

  /// 点击实时活动任务
  Future<void> onClick(Map<String, String> queryParameters);

  /// 实时活动任务已发送
  Future<void> onSend(LiveActivityBaseModel? model, bool isCreate);

  /// app进入前台，处于激活状态
  Future<void> onAppResume();
}
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';

abstract class IOSAppDelegateHandlerProtocol {
  Future<void> onAppLaunch(Map<dynamic, dynamic>? launchOptions);
}

class IOSAppDelegateService extends GetxService {
  late final MethodChannel _appDelegateChannel = const MethodChannel('com.flareflow.ios/appDelegate');
  late final List<WeakReference<IOSAppDelegateHandlerProtocol>> _handlerList = [];

  void registerLaunchHandler(IOSAppDelegateHandlerProtocol handler) {
    _handlerList.add(WeakReference(handler));
  }

  void removeLaunchHandler(IOSAppDelegateHandlerProtocol handler) {
    _handlerList.removeWhere((element) => element.target == handler);
  }

  void clearLaunchHandlers() {
    _handlerList.clear();
  }

  Future<void> launchForIOS() async {
    if (!Platform.isIOS) {
      return;
    }
    try {
      Map? launchOptions = await _appDelegateChannel.invokeMethod('getLaunchOptions');
      for (var handler in _handlerList) {
        await handler.target?.onAppLaunch(launchOptions);
      }
    } catch (e) {
      FFLog.error('获取app启动参数失败: $e');
    }
  }

  Future<void> notifyIOSCanOpenURL() async {
    if (!Platform.isIOS) {
      return;
    }
    try {
      await _appDelegateChannel.invokeMethod('canOpenURL');
    } catch (e) {
      FFLog.error('通知iOS原生可以跳转，失败: $e');
    }
  }
}
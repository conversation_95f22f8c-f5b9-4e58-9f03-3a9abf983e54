import 'dart:convert';

import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/utils/app_language.dart';
import 'package:playlet/utils/file_storage.dart';
import 'package:playlet/utils/safe_storage.dart';

class LastResourceBitInfo {
  // 资源位场景
  final ResourceBitScene scene;
  // 上次展示的资源位id
  final int id;
  // 上次展示的时间
  final DateTime? time;

  LastResourceBitInfo(this.scene, this.id, this.time);
}

class ResourceBitStorage {
  static const String _resourceBitStorageName = 'ResourceBit';
  static const String _lastResourceBitIdKey = 'lastResourceBitId';
  static const String _lastResourceBitShowTimeKey = 'lastResourceBitShowTime';

  /// 从本地获取资源位配置
  static Future<ResourceBitConfig?> getConfigFromCache() async {
    final jsonString = await _getConfigJsonString();
    if (jsonString == null || jsonString.isEmpty) {
      return null;
    }
    try {
      final config = ResourceBitConfig.fromJson(jsonDecode(jsonString));
      return config;
    } catch (e) {
      return null;
    }
  }

  /// 保存资源位配置到本地
  static Future<bool> saveConfigToCache(String json) async {
    final path = await _getConfigFilePath();
    if (path == null) {
      return false;
    }
    return await FileStorage.write(json, path);
  }

  /// 获取资源位配置文件路径
  static Future<String?> _getConfigFilePath() async {
    String? directoryPath = await FileStorage.getApplicationSupportDirectoryPath();
    if (directoryPath == null) {
      return null;
    }
    String path = '$directoryPath/resourceBit/${getCountryLanguageKey()}/config.json';
    FFLog.debug('保存资源位配置路径: $path');
    return path;
  }
  
  /// 获取本地配置json
  static Future<String?> _getConfigJsonString() async {
    final path = await _getConfigFilePath();
    if (path == null) {
      return null;
    }
    final jsonString = await FileStorage.read(path);
    return jsonString;
  }

  /// 获取国家语言key
  static String getCountryLanguageKey() {
    String countryCode = AppDeviceService().getFormatSystemCountryCode();
    if (countryCode.isEmpty) {
      countryCode = 'default';
    }
    String language = AppLanguage.getCurrentFormatLanguage();
    if (language.isEmpty) {
      language = 'default';
    }
    return '${countryCode}_$language';
  }

  /// 获取上次展示的资源位id
  static Future<LastResourceBitInfo?> getLastResourceBitInfo(ResourceBitScene scene) async {
    try {
      SafeStorage? storage = await SafeStorage.safeInstance(_resourceBitStorageName);
      if (storage == null) {
        return null;
      }
      String rootKey = getCountryLanguageKey();
      final data = storage.read(rootKey);
      if (data == null) {
        return null;
      }
      final map = data[scene.name];
      if (map == null) {
        return null;
      }
      final lastResourceBitId = map[_lastResourceBitIdKey];
      if (lastResourceBitId == null) {
        return null;
      }
      if (lastResourceBitId is! int) {
        return null;
      }
      final lastResourceBitShowTime = map[_lastResourceBitShowTimeKey];
      if (lastResourceBitShowTime != null && lastResourceBitShowTime is! int) {
        return null;
      }
      DateTime? time;
      if (lastResourceBitShowTime != null) {
        time = DateTime.fromMillisecondsSinceEpoch(lastResourceBitShowTime);
      }
      return LastResourceBitInfo(scene, lastResourceBitId, time);
    } catch (e) {
      FFLog.error('获取上次展示的资源位信息失败：$e');
      return null;
    }
  }

  /// 保存最近一次展示的资源位信息
  static Future<void> saveLastResourceBitInfo(
    ResourceBitScene scene, 
    ResourceBitModel model,
    ) async {
    final id = model.id;
    if (id == null) {
      return;
    }
    try {
      final time = AppDeviceService().getCurrentCalibratedTime();
      String rootKey = getCountryLanguageKey();

      SafeStorage? storage = await SafeStorage.safeInstance(_resourceBitStorageName);
      if (storage == null) {
        return;
      }
      final data = storage.read(rootKey);
      Map newData;
      if (data == null) {
        newData = {};
      } else {
        newData = data;
      }
      Map map = newData[scene.name] ?? {};
      map[_lastResourceBitIdKey] = id;
      map[_lastResourceBitShowTimeKey] = time;
      newData[scene.name] = map;
      await storage.write(rootKey, newData);
    } catch (e) {
      FFLog.error('保存最近一次展示的资源位信息失败：$e');
    }
  }

  static Future<void> clearLastResourceBitInfo(ResourceBitScene scene) async {
    try {
      String rootKey = getCountryLanguageKey();

      SafeStorage? storage = await SafeStorage.safeInstance(_resourceBitStorageName);
      if (storage == null) {
        return;
      }
      final data = storage.read(rootKey);
      Map newData;
      if (data == null) {
        newData = {};
      } else {
        newData = data;
      }
      newData.remove(scene.name);
      await storage.write(rootKey, newData);
    } catch (e) {
      FFLog.error('保存最近一次展示的资源位信息失败：$e');
    }
  }
}
import 'dart:async';
import 'dart:convert';

import 'package:playlet/api/resource_bit_api.dart';
import 'package:playlet/common/http/result.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/storage/resource_bit_storage.dart';

class ResourceBitConfigLoader {
  /// 是否正在加载
  Completer<void>? _isLoadingCompleter;
  Future<void> Function(bool success, ResourceBitConfig? config) onLoadCompleted;

  ResourceBitConfig? currentConfig;

  ResourceBitConfigLoader(this.onLoadCompleted);

  /// 加载资源位配置
  /// 如果本地有缓存，优先使用本地缓存
  /// 如果本地没有缓存，请求网络配置
  Future<void> load() async {
    final config = await ResourceBitStorage.getConfigFromCache();
    if (config != null) {
      FFLog.debug('先用本地资源位配置');
      await _loadCompleted(true, config);
    }
    unawaited(_requestConfig());
  }

  /// 请求资源位配置
  Future<void> _requestConfig() async {
    final completer = _isLoadingCompleter;
    if (completer != null && !completer.isCompleted) {
      // 正在请求配置，不重复请求
      return;
    }
    _isLoadingCompleter = Completer();

    FFLog.debug('开始请求资源位配置');
    Result result = await ResourceBitApi.getResourceList();
    if (!result.isSuccess) {
      await _loadCompleted(false, null);
      return;
    }
    dynamic data = result.data;
    if (data == null) {
      await _loadCompleted(true, null);
      return;
    }
    try {
      String? jsonString;
      ResourceBitConfig? config;
      if (data is String) {
        jsonString = data;
        final jsonObj = jsonDecode(data);
        config = ResourceBitConfig.fromJson(jsonObj);
      } else if (data is Map<String, dynamic>) {
        jsonString = jsonEncode(data);
        config = ResourceBitConfig.fromJson(data);
      }
      if (jsonString == null || config == null) {
        await _loadCompleted(true, null);
        return;
      }
      
      FFLog.debug('保存资源位配置到本地');
      await ResourceBitStorage.saveConfigToCache(jsonString);
      await _loadCompleted(true, config);
    } catch (e) {
      FFLog.error('解析资源位配置失败: $e json: ${result.data}}');
      await _loadCompleted(false, null);
    }
  }

  Future<void> _loadCompleted(bool success, ResourceBitConfig? config) async {
    _isLoadingCompleter?.complete();
    _isLoadingCompleter = null;

    await onLoadCompleted(success, config);

    if (success) {
      currentConfig = config;
    }
  }
}
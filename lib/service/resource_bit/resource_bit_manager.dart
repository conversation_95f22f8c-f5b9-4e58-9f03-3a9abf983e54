import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/i18n/translation_service.dart';
import 'package:playlet/service/resource_bit/config/resource_bit_config_loader.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';
import 'package:playlet/service/resource_bit/services/bottom_tab_service.dart';
import 'package:playlet/service/resource_bit/services/feed_service.dart';
import 'package:playlet/service/resource_bit/services/home_bottom_float_service.dart';
import 'package:playlet/service/resource_bit/services/home_popup_service.dart';
import 'package:playlet/service/resource_bit/services/my_list_banner_service.dart';
import 'package:playlet/service/resource_bit/services/my_profile_banner_service.dart';
import 'package:playlet/service/resource_bit/services/open_screen_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/storage/resource_bit_storage.dart';

class ResourceBitManager extends GetxService {
  static ResourceBitManager getInstance() {
    return Get.find<ResourceBitManager>();
  }

  /// 语言变化监听
  StreamSubscription<String>? _languageSubscription;

  /// 保存语言对应的推荐的剧
  final Map<String, ResourceBitConfigLoader> _configLoaderForLanguage = {};

  /// 获取配置加载器
  ResourceBitConfigLoader get _configLoader {
    String languageKey = ResourceBitStorage.getCountryLanguageKey();
    if (_configLoaderForLanguage.isNotEmpty) {
      // 把其他语言的加载器清除
      _configLoaderForLanguage.removeWhere((key, value) => key != languageKey);
    }
    ResourceBitConfigLoader? loader = _configLoaderForLanguage[languageKey];
    if (loader == null) {
      loader = ResourceBitConfigLoader(_onLoadCompleted);
      _configLoaderForLanguage[languageKey] = loader;
    }
    return loader;
  }
  
  /// 场景对应的资源位服务
  late final Map<ResourceBitScene, ResourceBitBaseService> _serviceForScene = Map.unmodifiable({
    ResourceBitScene.openScreen : ResourceBitOpenScreenService(ResourceBitScene.openScreen),
    ResourceBitScene.homePopup : ResourceBitHomePopupService(ResourceBitScene.homePopup),
    ResourceBitScene.homeBottomFloat : ResourceBitHomeBottomFloatService(ResourceBitScene.homeBottomFloat),
    ResourceBitScene.bottomTab : ResourceBitBottomTabService(ResourceBitScene.bottomTab),
    ResourceBitScene.myProfileBanner : ResourceBitMyProfileBannerService(ResourceBitScene.myProfileBanner),
    ResourceBitScene.myListBanner : ResourceBitMyListBannerService(ResourceBitScene.myListBanner),
    ResourceBitScene.feed : ResourceBitFeedService(ResourceBitScene.feed),
  });

  @override
  void onInit() {
    super.onInit();
    _addLanguageListner();
  }

  @override
  void onClose() {
    _cancelLanguageListner();
    super.onClose();
  }

  /// 根据场景获取资源位服务
  ResourceBitBaseService? getService(ResourceBitScene scene) {
    return _serviceForScene[scene];
  }

  /// 加载资源位配置
  Future<void> loadConfig() async {
    await _configLoader.load();
  }

  /// 资源位配置加载完成回调
  Future<void> _onLoadCompleted(bool success, ResourceBitConfig? config) async {
    if (!success) {
      return;
    }
    await _parseConfigs(config);
  }

  /// 解析资源位配置
  Future<void> _parseConfigs(ResourceBitConfig? newConfig) async {
    Map<ResourceBitScene, List<ResourceBitModel>> newConfigForScene = _getConfigForScene(newConfig);
    Map<ResourceBitScene, List<ResourceBitModel>> oldConfigForScene = _getConfigForScene(_configLoader.currentConfig);

    // 没有旧配置，则认为是初始化服务
    bool isInit = oldConfigForScene.isEmpty;
    
    Map<ResourceBitScene, ResourceBitBaseService> willRemovedServiceForScene = {};
    willRemovedServiceForScene.addAll(_serviceForScene);

    for (var scene in newConfigForScene.keys) {
      final newResourceList = newConfigForScene[scene];
      if (newResourceList == null || newResourceList.isEmpty) {
        continue;
      }
      ResourceBitBaseService? service = getService(scene);
      if (service == null) {
        // 不存在该服务
        continue;
      }

      willRemovedServiceForScene.remove(scene);
      if (isInit) {
        // 当前没数据，直接更新服务
        await service.updateResourceBitModels(newResourceList, isInit);
      } else {
        List<ResourceBitModel>? oldResourceList = oldConfigForScene[scene];
        if (ResourceBitConfig.isResourceModelsEqual(oldResourceList, newResourceList) == false) {
          // 配置有变化，更新服务
          await service.updateResourceBitModels(newResourceList, isInit);
        }
      }
    }

    // 清空没有配置的服务
    for (var scene in willRemovedServiceForScene.keys) {
      final service = willRemovedServiceForScene[scene];
      if (service == null) {
        continue;
      }
      List<ResourceBitModel>? oldResourceList = oldConfigForScene[scene];
      if (ResourceBitConfig.isResourceModelsEqual(oldResourceList, null) == false) {
        // 配置没变化
        continue;
      }
      // 配置有变化，更新服务
      await service.clear();
    }
  }

  Map<ResourceBitScene, List<ResourceBitModel>> _getConfigForScene(ResourceBitConfig? config) {
    Map<ResourceBitScene, List<ResourceBitModel>> configForScene = {};
    if (config == null) {
      return configForScene;
    }
    
    // 处理开屏资源位
    final openScreenList = config.openScreenList;
    if (openScreenList != null && openScreenList.isNotEmpty) {
      configForScene[ResourceBitScene.openScreen] = openScreenList;
    }
    
    // 处理首页弹窗资源位
    final homePopupList = config.homePopupList;
    if (homePopupList != null && homePopupList.isNotEmpty) {
      configForScene[ResourceBitScene.homePopup] = homePopupList;
    }
    
    // 处理首页底部悬浮窗资源位
    final homeBottomFloatList = config.homeBottomFloatList;
    if (homeBottomFloatList != null && homeBottomFloatList.isNotEmpty) {
      configForScene[ResourceBitScene.homeBottomFloat] = homeBottomFloatList;
    }
    
    // 处理额外资源位
    final extraResourceList = config.extraResourceList;
    if (extraResourceList != null) {
      for (var extraConfig in extraResourceList) {
        final type = extraConfig.type;
        if (type == null) {
          continue;
        }
        List<ResourceBitModel>? resourceList = extraConfig.resourceList;
        if (resourceList == null || resourceList.isEmpty) {
          continue;
        }
        
        ResourceBitScene scene;
        switch (type) {
          case ResourceBitExtraType.bottomTab:
            scene = ResourceBitScene.bottomTab;
            break;
          case ResourceBitExtraType.myProfileBanner:
            scene = ResourceBitScene.myProfileBanner;
            break;
          case ResourceBitExtraType.myListBanner:
            scene = ResourceBitScene.myListBanner;
            break;
          case ResourceBitExtraType.feed:
            scene = ResourceBitScene.feed;
            break;
        }
        
        configForScene[scene] = resourceList;
      }
    }
    return configForScene;
  }

  Future<void> _addLanguageListner() async {
    await _cancelLanguageListner();
    final translationService = Get.find<TranslationService>();
    _languageSubscription = translationService.currentLanguage.listen((language) async {
      await loadConfig();
    });
  }

  Future<void> _cancelLanguageListner() async {
    if (_languageSubscription == null) {
      return;
    }
    await _languageSubscription?.cancel();
    _languageSubscription = null;
  }
}
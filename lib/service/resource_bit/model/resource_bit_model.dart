
import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/http/http_header.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/modules/resource_bit/webview/resource_bit_webview_context.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/resource_bit/track_event/resource_bit_track_event.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/file_storage.dart';
import 'package:playlet/utils/limited_time.dart';
import 'package:url_launcher/url_launcher.dart';

enum ResourceBitScene {
  /// 开屏
  openScreen("bf_appopen_show"),
  /// 首页弹窗
  homePopup("bf_discover_window"),
  /// 首页底部悬浮窗
  homeBottomFloat("bf_icon_tab"),
  /// 底部TAB栏
  bottomTab("tab"),
  /// 我的底部横幅
  myProfileBanner("mylist_banner"),
  /// 追剧横幅
  myListBanner("profile_banner"),
  /// feed流
  feed("feed_1");

  final String value;
  const ResourceBitScene(this.value);

  static ResourceBitScene? fromInt(String? value) {
    return ResourceBitScene.values.firstWhereOrNull(
      (scene) => scene.value == value,
    );
  }
} 

enum ResourceBitExtraType {
  bottomTab(3),       // 底部TAB栏
  myProfileBanner(4), // 我的底部横幅
  myListBanner(5),    // 追剧横幅
  feed(7);            // feed流

  final int value;
  const ResourceBitExtraType(this.value);

  static ResourceBitExtraType? fromInt(int? value) {
    return ResourceBitExtraType.values.firstWhereOrNull(
      (type) => type.value == value,
    );
  }

  int toInt() {
    return value;
  }
}

enum ResourceBitSkipType {
  // 短剧
  shorts(0),
  // h5
  h5(1),
  // 端外h5，预留
  outsideBrowser(2),
  // 端外浏览器（官方）
  officialBrowser(3),
  // 端外浏览器（非官方）
  otherBrowser(4);

  final int value;
  const ResourceBitSkipType(this.value);

  static ResourceBitSkipType? fromInt(int? value) {
    return ResourceBitSkipType.values.firstWhereOrNull(
      (type) => type.value == value,
    );
  }

  int toInt() {
    return value;
  }

  /// 获取埋点事件参数值
  String? getTrackEventParamName() {
    String? name;
    switch (this) {
      case ResourceBitSkipType.shorts:
        name = "reel";
        break;
      case ResourceBitSkipType.h5:
        name = "h5_inapp_open";
        break;
      case ResourceBitSkipType.outsideBrowser:
      case ResourceBitSkipType.officialBrowser:
      case ResourceBitSkipType.otherBrowser:
        name = "h5_external_official_open";
        break;
    }
    return name;
  }

  String? getH5TrackEventParamName() {
    String? name;
    switch (this) {
      case ResourceBitSkipType.h5:
        name = "app";
        break;
      case ResourceBitSkipType.outsideBrowser:
        name = "app_out";
        break;
      case ResourceBitSkipType.officialBrowser:
        name = "official_web";
        break;
      default:
        name = "other";
        break;
    }
    return name;
  }
}

class ResourceBitConfig {
  List<ResourceBitModel>? openScreenList;
  List<ResourceBitModel>? homePopupList;
  List<ResourceBitModel>? homeBottomFloatList;
  List<ResourceBitExtra>? extraResourceList;

  ResourceBitConfig({
    this.openScreenList,
    this.homePopupList,
    this.homeBottomFloatList,
    this.extraResourceList,
  });

  factory ResourceBitConfig.fromJson(Map<String, dynamic> json) => ResourceBitConfig(
    openScreenList: (json['openScreenList'] as List?)?.map((e) => ResourceBitModel.fromJson(e)).toList(),
    homePopupList: (json['homePopupList'] as List?)?.map((e) => ResourceBitModel.fromJson(e)).toList(),
    homeBottomFloatList: (json['homeBottomFloatList'] as List?)?.map((e) => ResourceBitModel.fromJson(e)).toList(),
    extraResourceList: (json['extraResourceList'] as List?)?.map((e) => ResourceBitExtra.fromJson(e)).toList(),
  );

  bool isEqualTo(ResourceBitConfig? other) {
    if (!isResourceModelsEqual(openScreenList, other?.openScreenList)) {
      return false;
    }
    if (!isResourceModelsEqual(homePopupList, other?.homePopupList)) {
      return false;
    }
    if (!isResourceModelsEqual(homeBottomFloatList, other?.homeBottomFloatList)) {
      return false;
    }
    if (!_isResourceBitExtrasEqual(extraResourceList, other?.extraResourceList)) {
      return false;
    }
    return true;
  }

  static bool isResourceModelsEqual(List<ResourceBitModel>? oldModels, List<ResourceBitModel>? newModels) {
    if (oldModels == null && newModels == null) {
      // 两个都为空，相等
      return true;
    }
    if (oldModels == null || newModels == null) {
      // 其中一个为空，不等
      return false;
    }
    if (oldModels.length != newModels.length) {
      // 长度不一致，不等
      return false;
    }
    for (var oldModel in oldModels) {
      bool found = false;
      for (var newModel in newModels) {
        if (oldModel.id == newModel.id) {
          found = true;
          break;
        }
      }
      if (!found) {
        // 旧模型中找不到对应的新模型，不等
        return false;
      }
    }
    return true;
  }

  bool _isResourceBitExtrasEqual(List<ResourceBitExtra>? oldExtras, List<ResourceBitExtra>? newExtras) {
    if (oldExtras == null && newExtras == null) {
      // 两个都为空，相等
      return true;
    }
    if (oldExtras == null || newExtras == null) {
      // 其中一个为空，不等
      return false;
    }
    if (oldExtras.length != newExtras.length) {
      // 长度不一致，不等
      return false;
    }
    for (var oldExtra in oldExtras) {
      bool found = false;
      for (var newExtra in newExtras) {
        if (oldExtra.type == newExtra.type) {
          found = true;
          if (!isResourceModelsEqual(oldExtra.resourceList, newExtra.resourceList)) {
            // 模型数组不等
            return false;
          }
          break;
        }
      }
      if (!found) {
        // 旧模型中找不到对应的新模型，不等
        return false;
      }
    }
    return true;
  }
}

class ResourceBitExtra {
  ResourceBitExtraType? type;
  List<ResourceBitModel>? resourceList;
  
  ResourceBitExtra({
    this.type,
    this.resourceList,
  });

  factory ResourceBitExtra.fromJson(Map<String, dynamic> json) => ResourceBitExtra(
    type: ResourceBitExtraType.fromInt(json['type'] as int?),
    resourceList: (json['resourceList'] as List?)?.map((e) => ResourceBitModel.fromJson(e)).toList(),
  );
}

class ResourceBitModel {
  String? buttonColour;
  String? buttonText;
  String? deviceType;
  int? endDatetimeLong;
  int? id;
  int? isLongTerm;
  String? languageCode;
  int? rawSkipType;
  String? recommendName;
  String? resourceMap;
  String? resourceMapShrink;
  int? resourceType;
  int? shortPlayId;
  int? showType;
  ResourceBitSkipType? skipType;
  String? skipValue;
  int? startDatetimeLong;
  int? weight;

  ResourceBitModel({
    this.buttonColour,
    this.buttonText,
    this.deviceType,
    this.endDatetimeLong,
    this.id,
    this.isLongTerm,
    this.languageCode,
    this.rawSkipType,
    this.recommendName,
    this.resourceMap,
    this.resourceMapShrink,
    this.resourceType,
    this.shortPlayId,
    this.showType,
    this.skipType,
    this.skipValue,
    this.startDatetimeLong,
    this.weight,
  });

  factory ResourceBitModel.fromJson(Map<String, dynamic> json) => ResourceBitModel(
    buttonColour: json['buttonColour'],
    buttonText: json['buttonText'],
    deviceType: json['deviceType'],
    endDatetimeLong: json['endDatetimeLong'],
    id: json['id'],
    isLongTerm: json['isLongTerm'],
    languageCode: json['languageCode'],
    rawSkipType: json['rawSkipType'],
    recommendName: json['recommendName'],
    resourceMap: json['resourceMap'],
    resourceMapShrink: json['resourceMapShrink'],
    resourceType: json['resourceType'],
    shortPlayId: json['shortPlayId'],
    showType: json['showType'],
    skipType: ResourceBitSkipType.fromInt(json['skipType']),
    skipValue: json['skipValue'],
    startDatetimeLong: json['startDatetimeLong'],
    weight: json['weight'],
  );

    /// 加载资源，返回资源本地缓存路径
  Future<String?> loadImage() async {
    return await FileStorage.loadImageUrl(resourceMap);
  }

  void onResourceBitClick(ResourceBitScene scene) {
    final type = skipType;
    if (type == null) {
      FFLog.error('资源位skipType为空，点击无跳转');
      return;
    }
    ResourceBitTrackEvent.bfActEntryClickAll(scene, this);
    switch (type) {
      case ResourceBitSkipType.shorts:
        _toShortsPlay();
        break;
      case ResourceBitSkipType.h5:
        _toH5Page(scene);
        break;
      case ResourceBitSkipType.outsideBrowser:
        _toBrowser(scene, true);
        break;
      case ResourceBitSkipType.officialBrowser:
        _toBrowser(scene, true);
        break;
      case ResourceBitSkipType.otherBrowser:
        _toBrowser(scene, false);
        break;
    }
  }

  Future<Map<String, dynamic>> getCookies(ResourceBitScene scene, {bool isLoadSuccess = false}) async {
    UserResponse? userInfo = Get.find<UserService>().userInfo.value;

    Map<String, dynamic> cookies = await HttpHeader.getHeaders();
    cookies["fromPosition"] = scene.value;
    cookies["resourceBitId"] = id ?? "";
    cookies["screenWidth"] = "${Get.width}";
    cookies["screenHeight"] = "${Get.height}";
    // cookies["preferDisplayStyle"] = "\(preferDisplayStyle.rawValue)" // xjy TODO:
    if (isLoadSuccess) {
      cookies["loadingTimestamp"] = "${DateTime.now().millisecondsSinceEpoch}";
    }
    cookies["userId"] = userInfo?.userId ?? "";
    cookies["nickName"] = userInfo?.nickName ?? "";

    ResourceBitSkipType? type = skipType;
    if (type != null) {
      cookies["skipType"] = type.value;
      cookies["scene"] = type.getH5TrackEventParamName();
    }

    if (LimitedTime.getPopupShow()) {
      cookies["retentionTimestamp"] = LimitedTime.getEndTime() ?? 0;
      cookies["skuId"] = LimitedTime.getSkuId()?? "";
      cookies["skuProductId"] = LimitedTime.getSkuProductId()?? "";
    }

    return cookies;
  }
}

extension ResourceBitModelClickExtension on ResourceBitModel {
  /// 跳转到沉浸页
  void _toShortsPlay() {
    int? shortId = shortPlayId;
    if (shortId == null) {
      return;
    }
    AppNavigator.startDetailsPage(DetailsOptions(
      businessId: shortId,
      scene: EventValue.resourceBitScene,
      from: EventValue.fromResourceBit,
      resourceBitId: id.toString(),
    ));
  }

  /// 跳转到H5页面
  void _toH5Page(ResourceBitScene scene) {
    AppNavigator.startResourceBitWebviewPage(
      ResourceBitWebViewContext(
        scene: scene, 
        resourceBitModel: this
      )
    );
  }

  Future<void> _toBrowser(ResourceBitScene scene, bool shouldAddCookies) async {
    final url = skipValue;
    if (url == null || url.isEmpty) {
      FFLog.error('跳转链接为空');
      return;
    }
    try {
      Uri uri = Uri.parse(url);
      if (shouldAddCookies) {
        Map<String, dynamic> headers = await getCookies(scene);
        final queryParameters = Map<String, String>.from(uri.queryParameters);
        headers.forEach((key, value) {
          String encodeValue;
          if (value is String) {
            encodeValue = value;
          } else {
            encodeValue = value.toString();
          }
          encodeValue = Uri.encodeComponent(encodeValue);
          queryParameters[key] = encodeValue;
        });
        
        // 提取原始hash部分（不包含#号）
        String hash = uri.fragment;
        // 构建查询参数字符串
        String queryString = queryParameters.entries
            .map((e) => '${e.key}=${e.value}')
            .join('&');
        // 组合新的URL，将查询参数放在hash后面
        String newUrl = '${uri.origin}${uri.path}#$hash${queryString.isEmpty ? '' : '?$queryString'}';
        uri = Uri.parse(newUrl);
      }
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      }
    } catch (e) {
      FFLog.error('打开外部浏览器失败：$e');
    }
  }
}
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/utils/track_event.dart';

class ResourceBitTrackEvent {

  /// 资源位曝光剧
  static void reelShow(ResourceBitModel model) {
    final skipType = model.skipType;
    if (skipType != ResourceBitSkipType.shorts) {
      return;
    }
    final id = model.id;
    if (id == null) {
      return;
    }
    final shortPlayId = model.shortPlayId;
    if (shortPlayId == null) {
      return;
    }
    
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: shortPlayId.toString(),
      TrackEvent.scene: EventValue.resourceBitScene,
      EventKey.resourceBitId: id.toString(),
    });
  }   

  /// 活动入口展示-汇总
  /// 每个活动入口展示都汇总上报到该点
  static void bfActEntryShowAll(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.bfActEntryShowAll, [
      EventKey.scene,
      EventKey.resourceBitId,
      EventKey.moduleId,
      EventKey.positionId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);

    switch (scene) {
      case ResourceBitScene.openScreen:
        bfAppOpenShow(scene, model);
        break;
      case ResourceBitScene.homePopup:
        bfDiscoverWindowShow(scene, model);
        break;
      case ResourceBitScene.homeBottomFloat:
        bfIconTabShow(scene, model);
        break;
      case ResourceBitScene.bottomTab:
        activityTabShow(scene, model);
        break;
      case ResourceBitScene.myProfileBanner:
        profileBannerShow(scene, model);
        break;
      case ResourceBitScene.myListBanner:
        mylistBannerShow(scene, model);
        break;
      case ResourceBitScene.feed:
        feedOnePositionShow(scene, model);
        break;
    }
  }

  /// 活动入口点击-汇总
  static void bfActEntryClickAll(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.bfActEntryClickAll, [
      EventKey.scene,
      EventKey.resourceBitId,
      EventKey.moduleId,
      EventKey.positionId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);

    switch (scene) {
      case ResourceBitScene.openScreen:
        bfAppOpenClick(scene, model);
        break;
      case ResourceBitScene.homePopup:
        bfDiscoverWindowClick(scene, model);
        break;
      case ResourceBitScene.homeBottomFloat:
        bfIconTabClick(scene, model);
        break;
      case ResourceBitScene.bottomTab:
        activityTabClick(scene, model);
        break;
      case ResourceBitScene.myProfileBanner:
        profileBannerClick(scene, model);
        break;
      case ResourceBitScene.myListBanner:
        mylistBannerClick(scene, model);
        break;
      case ResourceBitScene.feed:
        feedOnePositionClick(scene, model);
        break;
    }
  }

  /// 开屏资源位展示
  /// 开屏资源位展示成功上报
  static void bfAppOpenShow(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.bfAppOpenShow, [
      EventKey.resourceBitId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 开屏资源位点击
  /// 点击进入活动页面时再上报
  static void bfAppOpenClick(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.bfAppOpenClick, [
      EventKey.resourceBitId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 首页弹窗展示
  /// 首页弹窗展示成功上报
  static void bfDiscoverWindowShow(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.bfDiscoverWindowShow, [
      EventKey.resourceBitId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 首页弹窗点击
  /// 点击进入活动页面时再上报
  static void bfDiscoverWindowClick(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.bfDiscoverWindowClick, [
      EventKey.resourceBitId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 底部悬浮图入口展示
  /// 底部悬浮图展示成功上报
  static void bfIconTabShow(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.bfIconTabShow, [
      EventKey.resourceBitId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 底部悬浮图入口点击
  /// 点击进入活动页面时再上报
  static void bfIconTabClick(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.bfIconTabClick, [
      EventKey.resourceBitId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 底部tab曝光
  /// 底部活动tab曝光上报
  static void activityTabShow(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.activityTabShow, [
      EventKey.resourceBitId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 底部tab点击
  static void activityTabClick(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.activityTabClick, [
      EventKey.resourceBitId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 追剧页面横幅曝光
  static void mylistBannerShow(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.mylistBannerShow, [
      EventKey.resourceBitId,
      EventKey.positionId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 追剧页面横幅点击
  static void mylistBannerClick(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.mylistBannerClick, [
      EventKey.resourceBitId,
      EventKey.positionId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }
  
  /// 我的页面横幅曝光
  static void profileBannerShow(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.profileBannerShow, [
      EventKey.resourceBitId,
      EventKey.positionId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// 我的页面横幅点击
  static void profileBannerClick(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.profileBannerClick, [
      EventKey.resourceBitId,
      EventKey.positionId,
      EventKey.skipType,
      EventKey.reelId,
    ], scene, model);
  }

  /// Feed流首位资源位曝光
  /// Feed流页面第1个位置作为资源位展示成功时上报
  static void feedOnePositionShow(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.feedOnePositionShow, [
      EventKey.resourceBitId,
      EventKey.skipType,
    ], scene, model);
  }

  /// Feed流首位资源位点击
  static void feedOnePositionClick(ResourceBitScene scene, ResourceBitModel model) {
    _addEvent(TrackEvent.feedOnePositionClick, [
      EventKey.resourceBitId,
      EventKey.skipType,
    ], scene, model);
  }

  static void _addEvent(String eventName, List<String> paramNames, ResourceBitScene scene, ResourceBitModel model) {
    Map<String, String> extra = {};
    for (String paramName in paramNames) {
      String? paramValue = _getParamValueForName(scene, model, paramName);
      if (paramValue != null) {
        extra[paramName] = paramValue;
      }
    }
    useTrackEvent(eventName, extra: extra);
  }

  static String? _getParamValueForName(ResourceBitScene scene, ResourceBitModel model, String paramName) {
    // 场景
    if (paramName == EventKey.scene) {
      return scene.value; 
    }
    // 资源位id
    if (paramName == EventKey.resourceBitId) {
      return model.id?.toString();
    }
    // 首页各模块的位置id，（针对首页资源位横幅、宣传图轮播等场景）
    if (paramName == EventKey.moduleId) {
      return null;
    }
    // 传入模块的位置id或者资源位中的排序值。例如：每个模块中剧/营销活动/品牌广告的位置：1、2、3；或者资源位中的排序值：0、1、2……
    if (paramName == EventKey.positionId) {
      return null;
    }
    // 对应cms的跳转类型/内容类型
    if (paramName == EventKey.skipType) {
      return model.skipType?.getTrackEventParamName();
    }
    // 剧id
    if (paramName == EventKey.reelId) {
      return model.shortPlayId?.toString();
    }
    return null;
  }
}
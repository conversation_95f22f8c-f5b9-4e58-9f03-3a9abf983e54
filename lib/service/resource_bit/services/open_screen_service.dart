import 'dart:async';

import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';

class ResourceBitOpenScreenService extends ResourceBitBaseService {
  ResourceBitOpenScreenService(super.scene);

  @override
  Future<void> updateResourceBitModels(List<ResourceBitModel>? models, bool isInit) async {
    await super.updateResourceBitModels(models, isInit);
    // 预加载开屏页图片
    unawaited(_preload());
  }

  @override
  Future<ResourceBitModel?> getResourceModel() async {
    if (!isColdOpen) {
      // 非冷启动，不展示开屏页
      setCurrentModel(null);
      return null;
    }
    ResourceBitModel? model = getCurrentModel();
    if (model != null) {
      return model;
    }
    model = await super.getResourceModel();
    setCurrentModel(model);
    return model;
  }

  @override
  Future<String?> loadResource(ResourceBitModel model) async {
    String? path = await super.loadResource(model);
    if (path == null) {
      return null;
    }
    unawaited(_preloadAllImages(getResourceImageUrl(model)));
    return path;
  }

  Future<void> _preload() async {
    final model = await getResourceModel();
    if (model == null) {
      return;
    }
    await loadResource(model);
  }

  Future<void> _preloadAllImages(String? loadedImageUrl) async {
    final models = resourceBitModels;
    if (models == null || models.isEmpty) {
      return;
    }
    for (var model in models) {
      final resourceMap = getResourceImageUrl(model);
      if (resourceMap == null || resourceMap.isEmpty) {
        continue;
      }
      if (resourceMap == loadedImageUrl) {
        continue;
      }
      // 此处要调super.loadResource，否则会递归
      await super.loadResource(model);
    }
  }
}
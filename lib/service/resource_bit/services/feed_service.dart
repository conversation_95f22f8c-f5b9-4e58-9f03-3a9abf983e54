import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';
import 'package:playlet/service/resource_bit/storage/resource_bit_storage.dart';
import 'package:playlet/utils/date_time_extension.dart';

class ResourceBitFeedService extends ResourceBitBaseService {
  ResourceBitFeedService(super.scene);

  /// 每天冷启动展示1次，取权重最大的展示
  @override
  Future<ResourceBitModel?> getResourceModel() async {
    ResourceBitModel? model;
    LastResourceBitInfo? lastInfo = await getLastResourceBitInfo();
    if (lastInfo == null) {
      // 没有记录，展示权重最大的
      model = _getValidResourceModel();
      setCurrentModel(model);
      return model;
    }
    final lastTime = lastInfo.time;
    if (lastTime == null) {
      // 上次展示的时间为空，展示权重最大的
      model = _getValidResourceModel();
      setCurrentModel(model);
      return model;
    }
    final now = DateTime.fromMillisecondsSinceEpoch(AppDeviceService().getCurrentCalibratedTime());
    if (!lastTime.isSameDay(now)) {
      // 上次展示的时间和今天不是同一天，展示权重最大的
      model = _getValidResourceModel();
      setCurrentModel(model);
      return model;
    }
    // 上次展示的时间和今天是同一天
    model = getCurrentModel();
    if (model != null) {
      // 非冷启动，要展示
      return model;
    }
    // 非当天第一次冷启动，不展示
    return null;
  }

  ResourceBitModel? _getValidResourceModel() {
    final models = resourceBitModels;
    if (models == null || models.isEmpty) {
      return null;
    }
    for (var model in models) {
      ResourceBitSkipType? skipType = model.skipType;
      if (skipType == null) {
        continue;
      }
      if (skipType == ResourceBitSkipType.h5) {
        return model;
      }
      if (skipType == ResourceBitSkipType.shorts) {
        int? shortPlayId = model.shortPlayId;
        if (shortPlayId == null) {
          continue;
        }
        return model;
      }
      continue;
    }
    return null;
  }
}
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';

class ResourceBitBottomTabService extends ResourceBitBaseService {
  ResourceBitBottomTabService(super.scene);

  @override
  Future<ResourceBitModel?> getResourceModel() async {
    ResourceBitModel? model = getCurrentModel();
    if (model != null) {
      return model;
    }
    model = await super.getResourceModel();
    setCurrentModel(model);
    return model;
  }
}
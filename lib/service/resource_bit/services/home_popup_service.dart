import 'dart:async';

import 'package:flutter/material.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain_manager.dart';
import 'package:playlet/modules/resource_bit/popup/home_popup_view.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';
import 'package:playlet/service/resource_bit/storage/resource_bit_storage.dart';
import 'package:playlet/utils/date_time_extension.dart';

class ResourceBitHomePopupService extends ResourceBitBaseService {
  ResourceBitHomePopupService(super.scene);

  @override
  Future<void> updateResourceBitModels(List<ResourceBitModel>? models, bool isInit) async {
    await super.updateResourceBitModels(models, isInit);
    // 预加载首页弹窗图片
    unawaited(_preload());
  }

  Future<void> _preload() async {
    final model = await getResourceModel();
    if (model == null) {
      return;
    }
    String? path = await loadResource(model);
    if (path == null) {
      return;
    }
    // 加入到再次弹框
    DialogChainManager.instance.addOpportunityChain(
      DialogChain(
          HomePopupView(
            resourceBitModel: model,
            imagePath: path,
          ),
          DialogChainManager.homePageResource, onShow: () async {
        await onShown(model);
      }, dialogOptions: DialogOptions(maskColor: Colors.black.withValues(alpha: 0.8))),
    );
  }

  /// 每天只弹1次
  @override
  Future<ResourceBitModel?> getResourceModel() async {
    if (!isColdOpen) {
      // 非冷启动
      setCurrentModel(null);
      return null;
    }
    ResourceBitModel? model;
    LastResourceBitInfo? lastInfo = await getLastResourceBitInfo();
    if (lastInfo == null) {
      // 没有记录
      model = await super.getResourceModel();
      setCurrentModel(model);
      return model;
    }
    final lastTime = lastInfo.time;
    if (lastTime == null) {
      // 上次展示的时间为空
      model = await super.getResourceModel();
      setCurrentModel(model);
      return model;
    }
    final now = DateTime.fromMillisecondsSinceEpoch(AppDeviceService().getCurrentCalibratedTime());
    if (lastTime.isSameDay(now)) {
      // 上次展示的时间和今天是同一天，不再展示
      setCurrentModel(null);
      return null;
    }
    // 上次展示的时间和今天不是同一天，展示
    model = getCurrentModel();
    if (model != null) {
      return model;
    }
    model = await super.getResourceModel();
    setCurrentModel(model);
    return model;
  }
}

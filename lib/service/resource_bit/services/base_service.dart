import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/app_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/storage/resource_bit_storage.dart';
import 'package:playlet/service/resource_bit/track_event/resource_bit_track_event.dart';
import 'package:playlet/utils/date_time_extension.dart';
import 'package:playlet/utils/file_storage.dart';

/// 资源位变化监听回调函数类型
typedef ResourceBitModelsChangedCallback = void Function(List<ResourceBitModel>? models);

class ResourceBitBaseService {
  static const String _tag = 'ResourceBitBaseService';
  /// 资源位场景
  final ResourceBitScene resourceBitScene;

  /// 资源位列表，已过滤掉无效的资源位，并按权重从大到小排序
  List<ResourceBitModel>? resourceBitModels;

  /// 当前国家和语言对应的展示的资源位，key是ResourceBitStorage.getCountryLanguageKey()
  final Map<String, ResourceBitModel> _currentModels = {};
  
  /// 使用 StreamController 替代监听器列表
  final StreamController<List<ResourceBitModel>?> _modelsStreamController = StreamController<List<ResourceBitModel>?>.broadcast();
  
  /// 提供 Stream 供外部监听
  Stream<List<ResourceBitModel>?> get modelsStream => _modelsStreamController.stream;

  /// 构造函数
  ResourceBitBaseService(ResourceBitScene scene) 
    : resourceBitScene = scene;

  /// 是否是冷启动
  bool get isColdOpen => Get.find<AppService>().isColdOpen;

  /// 更新资源位模型列表
  @mustCallSuper
  Future<void> updateResourceBitModels(List<ResourceBitModel>? models, bool isInit) async {
    FFLog.debug('更新资源位模型列表: ${resourceBitScene.name}', tag: _tag);
    resourceBitModels = ResourceBitBaseServiceExtension._getValidPeriodModels(models);
    if (!isInit) {
      await ResourceBitStorage.clearLastResourceBitInfo(resourceBitScene);
    }
    // 通过 Stream 通知监听者
    _modelsStreamController.add(resourceBitModels);
  }

  /// 当前展示的资源位
  ResourceBitModel? getCurrentModel() {
    return _currentModels[ResourceBitStorage.getCountryLanguageKey()];
  }

  /// 设置当前展示的资源位
  void setCurrentModel(ResourceBitModel? model) {
    String key = ResourceBitStorage.getCountryLanguageKey();
    if (model == null) {
      _currentModels.remove(key);
    } else {
      _currentModels[key] = model;
    }
  }

  /// 清空资源
  Future<void> clear() async {
    setCurrentModel(null);
    resourceBitModels?.clear();
    resourceBitModels = null;
    await ResourceBitStorage.clearLastResourceBitInfo(resourceBitScene);
    // 通过 Stream 通知监听者
    _modelsStreamController.add(resourceBitModels);
  }

  /// 获取本次要展示的资源位
  /// 默认实现：每次只会展示1个，每个自然日启动次数依权重大小轮询展示。如：同时存在A、B、C，用户当天第一次启动App展示A，第二次启动展示B，第三次启动展示C
  Future<ResourceBitModel?> getResourceModel() async {
    final models = resourceBitModels;
    if (models == null || models.isEmpty) {
      return null;
    }
    LastResourceBitInfo? lastInfo = await getLastResourceBitInfo();
    if (lastInfo == null) {
      // 没有记录，取第一个
      return models.first;
    }
    final lastTime = lastInfo.time;
    final now = DateTime.fromMillisecondsSinceEpoch(AppDeviceService().getCurrentCalibratedTime());
    if (lastTime != null && !lastTime.isSameDay(now)) {
      // 上次展示的时间和今天不是同一天，取第一个
      return models.first;
    }
    int lastIndex = -1;
    for (var i = 0; i < models.length; i++) {
      final model = models[i];
      if (model.id != lastInfo.id) {
        continue;
      }
      lastIndex = i;
      break;
    }
    if (lastIndex < 0) {
      // 找不到记录，取第一个
      return models.first;
    }
    if (lastIndex >= models.length - 1) {
      // 最后一个，从第一个开始新的循环
      return models.first;
    }
    return models[lastIndex + 1];
  }

  /// 加载资源，返回资源本地缓存路径
  Future<String?> loadResource(ResourceBitModel model) async {
    return await model.loadImage();
  }

  /// 获取资源本地缓存路径
  Future<String?> getResourceCachePath(ResourceBitModel model) async {
    String? imageUrl = getResourceImageUrl(model);
    if (imageUrl == null || imageUrl.isEmpty) {
      return null;
    }
    String? path = await FileStorage.getImageCachePath(imageUrl);
    return path;
  }
  
  /// 资源位已展示
  @mustCallSuper
  Future<void> onShown(ResourceBitModel model) async {
    await ResourceBitStorage.saveLastResourceBitInfo(resourceBitScene, model);
    ResourceBitTrackEvent.bfActEntryShowAll(resourceBitScene, model);
  }

  /// 获取上次展示的资源位信息
  Future<LastResourceBitInfo?> getLastResourceBitInfo() async {
     return await ResourceBitStorage.getLastResourceBitInfo(resourceBitScene);
  }

  /// 获取资源图片url
  String? getResourceImageUrl(ResourceBitModel model) {
    String? imageUrl = model.resourceMap;
    // if (imageUrl == null || imageUrl.isEmpty) {
    //   imageUrl = model.resourceMapShrink;
    // }
    return imageUrl;
  }
}

extension ResourceBitBaseServiceExtension on ResourceBitBaseService {
  /// 获取在有效期范围内的资源位列表
  static List<ResourceBitModel>? _getValidPeriodModels(List<ResourceBitModel>? list) {
    if (list == null) {
      return null;
    }
    int now = AppDeviceService().getCurrentCalibratedTime();
    List<ResourceBitModel> newList = list.where((e) {
      final isLongTerm = e.isLongTerm;
      if (isLongTerm != null && isLongTerm == 1) {
        // 长期有效
        return true;
      }
      final startTime = e.startDatetimeLong ?? 0;
      final endTime = e.endDatetimeLong;
      if (now < startTime) {
        return false;
      }
      if (endTime == null) {
        return true;
      }
      return now <= endTime;
    }).toList();

    // 按权重排序
    newList.sort((a, b) {
      final weightA = a.weight?? 0;
      final weightB = b.weight?? 0;
      return weightB.compareTo(weightA);
    });
    return newList;
  }
}
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';

class ResourceBitHomeBottomFloatService extends ResourceBitBaseService {
  bool isClosed = false;

  ResourceBitHomeBottomFloatService(super.scene);

  void onCloseFloat() {
    isClosed = true;
  }

  /// 关闭后，本次启动期间不再展示该资源位，下次冷启动时再次展示
  @override
  Future<ResourceBitModel?> getResourceModel() async {
    if (isClosed) {
      // 关闭后，本次启动周期不再展示，下次冷启动再展示
      return null;
    }
    ResourceBitModel? model = getCurrentModel();
    if (model != null) {
      return model;
    }
    model = await super.getResourceModel();
    setCurrentModel(model);
    return model;
  }
}
import 'dart:io';

import 'package:advertising_id/advertising_id.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dlink_fingerprint/fingerprint_core.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get_storage/get_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:playlet/api/report.dart';
import 'package:playlet/api/server_time_api.dart';
import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/http/result.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:safe_device/safe_device.dart';
import 'package:vpn_connection_detector/vpn_connection_detector.dart';

class AppDeviceService {
// 私有构造函数，防止外部实例化
  AppDeviceService._internal();

  // 静态变量保存单例实例
  static final AppDeviceService instance = AppDeviceService._internal();

  // 工厂构造函数，返回单例实例
  factory AppDeviceService() => instance;

  // 存储键常量
  static const String advertisingIdKey = "advertising_id";

  /// 初始化设备信息相关
  Future<void> init() async {
    // 获取设备信息
    await getDeviceInfo();
    // 获取包信息
    await getPackageInfo();
    //  获取设备指纹
    await getFingerprint();
    //  获取广告id
    await getAdvertisingId();
    //  获取当前时区
    await getTimeZone();
    // 获取mcc，只支持android
    await fetchMCC();
    //  注册生命周期回调
    _registerLifeCycleHandler();
  }

  PackageInfo? _packageInfo;
  BaseDeviceInfo? _baseDeviceInfo;

  /// 广告ID
  String? advertisingId;

  /// 是否限制广告跟踪
  bool? isLimitAdTrackingEnabled;

  /// 设备指纹
  String? fingerprint;
  String? _fingerprintError;

  String? currentTimeZone;

  /// 与服务器的时间差
  int? _diffServerTime;

  _LifecycleEventHandler? _lifecycleHandler;

  String mcc = "";

  final String _tag = "AppDeviceService";

  Future<String?> getFingerprint() async {
    try {
      if (fingerprint != null) return fingerprint;
      fingerprint = await FingerprintCore.instance.generate();
      if (fingerprint != null) {
        _fingerprintError = null;
        FFLog.info("Fingerprint $fingerprint", tag: _tag);
        return fingerprint;
      } else {
        _fingerprintError = 'Failed to get fingerprint.';
        return null;
      }
    } on PlatformException {
      _fingerprintError = 'Failed to get fingerprint.';
      return null;
    }
  }

  Future<String?> getFingerprintError() async {
    return _fingerprintError;
  }

  /// 获取AppName
  Future<String> getAppName() async {
    PackageInfo packageInfo = await getPackageInfo();
    return packageInfo.appName;
  }

  /// 获取PackageName
  Future<String> getPackageName() async {
    PackageInfo packageInfo = await getPackageInfo();
    return packageInfo.packageName;
  }

  /// 获取Version
  Future<String> getVersion() async {
    PackageInfo packageInfo = await getPackageInfo();
    return packageInfo.version;
  }

  /// 获取BuildNumber
  Future<String> getBuildNumber() async {
    PackageInfo packageInfo = await getPackageInfo();
    return packageInfo.buildNumber;
  }

  // 获取包信息
  Future<PackageInfo> getPackageInfo() async {
    _packageInfo ??= await PackageInfo.fromPlatform();
    return _packageInfo!;
  }

  // 获取设备信息
  Future<BaseDeviceInfo> getDeviceInfo() async {
    _baseDeviceInfo ??= await DeviceInfoPlugin().deviceInfo;
    return _baseDeviceInfo!;
  }

  // 获取 IOS系统版本
  Future<String?> getIosSystemVersion() async {
    if (Platform.isIOS) {
      BaseDeviceInfo deviceInfo = await getDeviceInfo();
      IosDeviceInfo iosDeviceInfo = IosDeviceInfo.fromMap(deviceInfo.data);
      return iosDeviceInfo.systemVersion;
    }
    return null;
  }

  Future<void> fetchMCC() async {
    if (Platform.isAndroid) {
      try {
        const platform = MethodChannel('com.flareflow.android/device');
        mcc = await platform.invokeMethod('mcc');
        FFLog.info('fetchMCC: $mcc');
      } catch (e) {
        FFLog.error('fetchMCC error: $e');
      }
    }
  }

  /// 获取Android SDK Version
  Future<int?> getAndroidSdkVersion() async {
    if (Platform.isAndroid) {
      BaseDeviceInfo deviceInfo = await getDeviceInfo();
      AndroidDeviceInfo androidDeviceInfo = AndroidDeviceInfo.fromMap(deviceInfo.data);
      return androidDeviceInfo.version.sdkInt;
    }
    return null;
  }

  /// 判断设备是否连接网络
  Future<bool> isNetworkAvailable() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    return !connectivityResult.contains(ConnectivityResult.none);
  }

  /// 判断设备是否连接移动网络
  Future<bool> isConnectedToMobile() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult.contains(ConnectivityResult.mobile);
  }

  /// 判断设备是否连接WiFi
  Future<bool> isConnectedToWiFi() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult.contains(ConnectivityResult.wifi);
  }

  /// 获取设备型号
  Future<String?> getDeviceModel() async {
    BaseDeviceInfo deviceInfo = await getDeviceInfo();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidDeviceInfo = AndroidDeviceInfo.fromMap(deviceInfo.data);
      return '${androidDeviceInfo.brand} ${androidDeviceInfo.model}';
    }
    if (Platform.isIOS) {
      IosDeviceInfo iosDeviceInfo = IosDeviceInfo.fromMap(deviceInfo.data);
      return iosDeviceInfo.utsname.machine;
    }
    return null;
  }

  /// 获取广告Id
  Future<String?> getAdvertisingId() async {
    // 先从SafeStorage中查找
    final storage = SafeStorage();
    final storedAdId = storage.read<String>(advertisingIdKey);

    // 如果存储中已有广告ID，则直接返回
    if (storedAdId != null && storedAdId.isNotEmpty) {
      advertisingId = storedAdId;
      FFLog.debug("从存储获取gaid: $advertisingId", tag: _tag);
      return advertisingId;
    }

    // 否则从设备获取广告ID
    try {
      // 记录开始时间
      final startTime = DateTime.now().millisecondsSinceEpoch;

      advertisingId = await AdvertisingId.id(true);

      // 获取成功后保存到存储中
      if (advertisingId != null) {
        await storage.write(advertisingIdKey, advertisingId);

        // 计算耗时
        final endTime = DateTime.now().millisecondsSinceEpoch;
        final duration = endTime - startTime;

        FFLog.info("获取并存储新gaid: $advertisingId, 耗时: ${duration}ms", tag: _tag);
      }
    } on PlatformException {
      advertisingId = null;
    }

    return advertisingId;
  }

  /// 获取是否限制广告跟踪
  Future<bool?> getIsLimitAdTrackingEnabled() async {
    try {
      isLimitAdTrackingEnabled = await AdvertisingId.isLimitAdTrackingEnabled;
    } on PlatformException {
      isLimitAdTrackingEnabled = false;
    }
    return isLimitAdTrackingEnabled;
  }

  /// 系统国家代码 格式化
  String getFormatSystemCountryCode() {
    final localeNames = Platform.localeName.split('_');
    if (localeNames.length < 2) {
      return '';
    }
    var localName = localeNames[1];
    if (localName == "Hans") {
      return "CN";
    }
    return localName;
  }

  /// 系统语言
  String getSystemLanguage() {
    return Platform.localeName;
  }

  /// 系统语言 格式语言
  String getFormatSystemLanguage() {
    var localeName = Platform.localeName;
    if (localeName == "zh_Hans_CN" || localeName == "zh_CN") {
      return "zh-Hans";
    }
    if (localeName == "zh_Hant_TW" || localeName == "zh_Hant") {
      return "zh-Hant";
    }
    return localeName;
  }

  Future<bool> isVpnActive() async {
    try {
      bool isVpnConnected = await VpnConnectionDetector.isVpnActive();
      return isVpnConnected;
    } on PlatformException {
      return false;
    }
  }

  //timeZone
  Future<String> getTimeZone() async {
    final String timeZone = await FlutterTimezone.getLocalTimezone();
    currentTimeZone = timeZone;
    return timeZone;
  }

  /// 检查 iOS/Android 设备是否已越狱
  Future<bool> isRootedDevice() async {
    try {
      bool isJailBroken = await SafeDevice.isJailBroken;
      return isJailBroken;
    } on PlatformException {
      return false;
    }
  }

  /// 检查设备是真实设备还是模拟器
  Future<bool> isRealDevice() async {
    try {
      bool isRealDevice = await SafeDevice.isRealDevice;
      return isRealDevice;
    } on PlatformException {
      return false;
    }
  }

  /// 检查设备是否处于开发者模式（仅限 Android）
  Future<bool> isDeveloperMode() async {
    try {
      if (Platform.isIOS) {
        return false;
      }
      bool isDevelopmentModeEnable = await SafeDevice.isDevelopmentModeEnable;
      return isDevelopmentModeEnable;
    } on PlatformException {
      return false;
    }
  }

  Future<String> getSystemUserAgent() async {
    try {
      // todo by shawn 有更好的做法?
      PackageInfo packageInfo = await getPackageInfo();
      String platform = Platform.isAndroid ? 'Android' : (Platform.isIOS ? 'iOS' : 'Unknown');
      String? model = await getDeviceModel();

      return 'Mozilla/5.0 ($platform; $model) ${packageInfo.appName}/${packageInfo.version}';
    } catch (e) {
      return 'Mozilla/5.0 (Unknown)';
    }
  }

  /// 刷新服务器时间
  Future<void> refreshServerTime() async {
    if (!HttpService().isInitialized) {
      // http服务未初始化
      return;
    }
    Result result = await ServerTimeApi.getServerTime();
    if (!result.isSuccess) {
      return;
    }
    final data = result.data;
    if (data == null) {
      return;
    }
    if (data is! Map) {
      return;
    }
    _diffServerTime = data['diffTimeLong'];
    FFLog.info('本地时间: ${data['localTimeLong']} 服务器时间: ${data['serviceTimeLong']} 时间差: $_diffServerTime', tag: _tag);
  }

  /// 获取与服务器时间校准后的当前时间，单位毫秒
  int getCurrentCalibratedTime() {
    int time = DateTime.now().millisecondsSinceEpoch + (_diffServerTime ?? 0);
    FFLog.info('当前校准后的时间: $time', tag: _tag);
    return time;
  }
}

extension AppDeviceServiceLifeCycleExtension on AppDeviceService {
  /// 注册生命周期回调
  void _registerLifeCycleHandler() {
    if (_lifecycleHandler != null) {
      return;
    }
    final handler = _LifecycleEventHandler(resumeCallBack: () {
      // 应用从后台回到前台，刷新服务器时间
      refreshServerTime();
    });
    _lifecycleHandler = handler;
    WidgetsBinding.instance.addObserver(handler);
  }

  /// 注销生命周期回调
  void _unregisterLifeCycleHandler() {
    final handler = _lifecycleHandler;
    if (handler == null) {
      return;
    }
    WidgetsBinding.instance.removeObserver(handler);
  }
}

class _LifecycleEventHandler extends WidgetsBindingObserver {
  final VoidCallback? resumeCallBack;
  final VoidCallback? pausedCallBack;

  _LifecycleEventHandler({
    this.resumeCallBack,
    this.pausedCallBack,
  });

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      resumeCallBack?.call();
    } else if (state == AppLifecycleState.paused) {
      pausedCallBack?.call();
    }
  }
}

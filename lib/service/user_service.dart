import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/api/report.dart';
import 'package:playlet/api/rewards_api.dart';
import 'package:playlet/api/user.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/utils/auth.dart';
import 'package:playlet/utils/firebase_util.dart';
import 'package:playlet/utils/safe_storage.dart';

enum SignInStatus {
  /// 未获取到签到状态
  unknown,
  /// 已签到
  signed,
  /// 未签到
  notSigned,
}

class UserService extends GetxService {
  static const String tag = "user_service";
  static const String userInfoKey = "userInfoKey";
  Rxn<UserResponse> userInfo = Rxn<UserResponse>();
  bool get isLogin => userInfo.value != null && userInfo.value!.role == 2;
  /// 签到状态
  final Rx<SignInStatus> signInStatus = SignInStatus.unknown.obs;
  /// 当天签到的截止时间戳，单位毫秒
  int? todayEndSignTime;
  StreamSubscription<UserResponse?>? _userInfoSubscription;

  RxnString metaLoginBonus = RxnString();

  // 可以获得多少奖励
  RxInt rewardsTotalBonus = RxInt(0);


  @override
  void onInit() {
    super.onInit();
    _addUserInfoListener();
  }

  @override
  void onClose() {
    _cancelUserInfoListener();
    super.onClose();
  }

  void setMetaLoginBonus(String metaLoginBonus) {
    this.metaLoginBonus.value = metaLoginBonus;
  }

  Future setUserInfo(UserResponse userResponse) async {
    FFLog.info("设置用户信息: ${userResponse.userId}", tag: tag);
    userInfo.value = userResponse;
    FirebaseUtil.setUserId(userResponse.userId ?? "");
    ReportApi.reportGaid();
    await SafeStorage().write(userInfoKey, userResponse.toJson());
  }

  Future<UserResponse> getUserInfo() async {
    if (userInfo.value != null) {
      FFLog.debug("获取用户信息: 直接返回已有用户", tag: tag);
      return userInfo.value!;
    }

    FFLog.debug("获取用户信息: 等待用户信息设置", tag: tag);
    final completer = Completer<UserResponse>();

    final subscription = userInfo.listen((value) {
      if (value != null && !completer.isCompleted) {
        FFLog.debug("获取用户信息: 监听到用户信息更新", tag: tag);
        completer.complete(value);
      }
    });

    return completer.future.then((value) {
      subscription.cancel();
      FFLog.debug("获取用户信息: 已获取用户信息", tag: tag);
      return value;
    });
  }

  Future<void> updateCoins(int coins) async {
    Map<String, dynamic>? userInfoMap = userInfo.value?.toJson();
    if (userInfoMap != null) {
      UserResponse? newUserInfo = UserResponse.fromJson(userInfoMap);
      newUserInfo.coins = coins;
      userInfo.value = newUserInfo;
      await SafeStorage().write(userInfoKey, userInfo.toJson());
    }
  }

  Future<void> updateBonus(int bonus) async {
    Map<String, dynamic>? userInfoMap = userInfo.value?.toJson();
    if (userInfoMap != null) {
      UserResponse? newUserInfo = UserResponse.fromJson(userInfoMap);
      newUserInfo.bonus = bonus;
      userInfo.value = newUserInfo;
      SafeStorage().write(userInfoKey, userInfo.toJson());
    }
  }

  Future<void> updateAutoUnlockEpisode(bool value) async {
    bool result = await ApiUser.updateUserAutoUnlockResult(value: value);
    if (result == true) {
      Map<String, dynamic>? userInfoMap = userInfo.value?.toJson();
      if (userInfoMap != null) {
        UserResponse? newUserInfo = UserResponse.fromJson(userInfoMap);
        newUserInfo.autoUnlock = value;
        newUserInfo.autoUnlockEpisode = value;
        userInfo.value = newUserInfo;
        SafeStorage().write(userInfoKey, userInfo.toJson());
      }
    }
  }

  Future<void> updateAutoUnlockEpisodeValue(bool value) async {
    Map<String, dynamic>? userInfoMap = userInfo.value?.toJson();
    if (userInfoMap != null) {
      UserResponse? newUserInfo = UserResponse.fromJson(userInfoMap);
      newUserInfo.autoUnlock = value;
      newUserInfo.autoUnlockEpisode = value;
      userInfo.value = newUserInfo;
      SafeStorage().write(userInfoKey, userInfo.toJson());
    }
  }

  Future<void> fetchUserInfo() async {
    UserResponse? response = await ApiUser.fetchUserInfo();
    if (response != null) {
      userInfo.value = response;
      SafeStorage().write(userInfoKey, userInfo.toJson());
    }
  }

  /// 用户活跃上报
  /// "1. App打开的时候上报.
  /// 2. 后续切换 tab 时发现距上次上报一次超2小时再次上报"
  void reportActiveTime(bool force) async {
    var lastReportTime = await Auth.getUserReportActiveTime();
    // 距上次上报一次超2小时再次上报
    if (force ||
        (DateTime.now().millisecondsSinceEpoch - lastReportTime) > 7200000) {
      var result = await ApiUser.reportActiveTime();
      if (result) {
        // 记录上报时间
        Auth.setUserReportActiveTime();
      }
    }
  }

    /// 获取签到状态
  Future<void> getSignInStatus() async {
    var result = await ApiRewards.getSignRecord();
    if (result == null) {
      signInStatus.value = SignInStatus.unknown;
      return;
    }
    final signRecords = result.signRecords;
    todayEndSignTime = result.todayEndSignTime;
    if (signRecords == null || signRecords.isEmpty) {
      // 签到记录为空
      signInStatus.value = SignInStatus.notSigned;
      return;
    }
    for (var record in signRecords) {
      bool? isSigned = record.isSign;
      bool? isToday = record.isToday;
      if (isSigned == true && isToday == true) {
        // 今天已签到
        signInStatus.value = SignInStatus.signed;
        return;
      }
    }
    // 今天未签到
    signInStatus.value = SignInStatus.notSigned;
  }

  /// 监听用户信息
  Future<void> _addUserInfoListener() async {
    await _cancelUserInfoListener();
    _userInfoSubscription = userInfo.listen(_onUserInfoChanged);
  }

  /// 取消用户信息监听
  Future<void> _cancelUserInfoListener() async {
    await _userInfoSubscription?.cancel();
    _userInfoSubscription = null;
  }

  void _onUserInfoChanged(UserResponse? user) {
    // 用户信息改变，重置，重新获取签到状态
    signInStatus.value = SignInStatus.unknown;
    unawaited(getSignInStatus());
  }

  /// 获取所有可获得的金币
  Future<void> fetchRewardsTotalBonus() async {
    var result = await ApiRewards.getBonusTotal();
    rewardsTotalBonus.value = result??0;
  }

}

import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/modules/notification/notification_select_view.dart';
import 'package:playlet/utils/safe_storage.dart';

class NotificationLoginService extends GetxService {
  final storage = SafeStorage();
  DateFormat format = DateFormat('yyyy-dd-MM');

  void writeNotificationDialogShow(int type) {
    String currentDate = format.format(DateTime.now());
    storage.write('first_dialog_last_shown_$type', currentDate);
  }

  // 判断通知弹框 在feed流中显示
  bool isShowNotificationDialogByReels(int type) {
    String currentDate = format.format(DateTime.now());
    String? lastShownDate = storage.read('first_dialog_last_shown_$type');
    // 如果当天已经显示过，返回 false
    if (lastShownDate == currentDate) {
      return false;
    }
    return true;
  }

  /// 检查并弹出通知弹窗
  Future<void> checkAndShowNotificationAlert(int type, {Function(bool)? onShowChange}) async {
    PermissionStatus status = await Permission.notification.status;
    if (status.isGranted == false &&
        isShowNotificationDialogByReels(type) == true) {
          onShowChange?.call(true);
      await SmartDialog.show(
        tag: 'first_dialog_last_shown_$type',
        clickMaskDismiss: false,
        onDismiss: () {
          onShowChange?.call(false);
        },
        builder: (context) => NotificationSelectView(type: type),
      );
      FFLog.debug("弹出通知弹框。");
    }
  }
}

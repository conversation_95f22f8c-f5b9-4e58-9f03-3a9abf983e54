import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:deeplink_dev/attribution_sdk_core.dart';
import 'package:deeplink_dev/sdk_callback.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/utils/api_limit_manager.dart';
import 'package:playlet/config/index.dart';
import 'package:flutter/material.dart';
import 'package:playlet/utils/attribute_evens_util.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/api/attribute_api.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/routers/route_observer_custom.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:playlet/common/http/encryption_utils.dart';
import 'package:playlet/common/http/secret_repo.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';

class AttributeService extends GetxService {
  static const tag = "AttributeService";
  static const String _storageKeyIsReported = "attribute_reported";
  static const String _storageKeyLpReported = "lp_reported";
  static const String _storageKeyAttributionReturned = "attribution_returned";
  static const String _storageKeyDeeplinkShortPlayId = "deeplink_shortplay_id";
  static const String _storageKeyDeeplinkSource = "deeplink_source";
  static const String _storageKeyAttributionPlayed = "attribution_played";

  // 添加归因开始时间
  DateTime? _attributionStartTime;

  // 添加解析成功到跳转成功的时间
  DateTime? _parseSuccessTime;

  final UserService _userService = Get.find<UserService>();
  final AppDeviceService _deviceService = AppDeviceService();

  String? clientIpForApi;
  String? _deeplinkData;

  bool _isReportingTask = false;
  _LifecycleEventHandler? _lifecycleHandler;
  StreamSubscription? _routeSubscription;

  // ===== 只有iOS需要 begin =====
  bool _isSDKInitialized = false;
  StreamSubscription? _connectivitySubscription;
  bool _oldHasNetwork = false;
  final _pendingTasks = <Function>[];
  // ===== 只有iOS需要 end =====

  _initSdk() {
    // 记录归因开始时间
    _attributionStartTime = DateTime.now();

    // 初始化归因SDK
    const accountId = Config.dLinkAccountId;
    const metaAppId = Config.dLinkMetaAppId;
    final appsFlyerAppId = Platform.isAndroid ? Config.dLinkAppsFlyerAppIdAndroid : Config.dLinkAppsFlyerAppIdIOS;
    const appsFlyerDevKey = Config.dLinkAppsFlyerDevKey;
    AttributionSdkCore.instance.initSdk(accountId,
        metaAppId: metaAppId,
        appsFlyerAppId: appsFlyerAppId,
        appsFlyerDevKey: appsFlyerDevKey);
    AttributionSdkCore.instance.launch();
  }

  Future<void> init() async {
    // 设置归因开始时间
    _attributionStartTime = DateTime.now();
    FFLog.info("设置归因开始时间: ${_attributionStartTime}", tag: tag);

    // 先监听iOS网络状态
    _listenNetworkForIOS();

    // 设置API限制管理器的冷启动状态
    ApiLimitManager.setColdBoot(true);

    // 设置自定义设备ID
    AttributionSdkCore.instance.setWaitForDeviceId(true);
    //AttributionSdkCore.instance.enableLog();
    AttributionSdkCore.instance.enableAttributionListener();
    AttributionSdkCore.instance.enableIpListener();
    AttributionSdkCore.instance.setPackageSource(Config.getPackageSource());

    // 获取设备指纹作为设备ID
    final deviceId = await _deviceService.getFingerprint();
    if (deviceId != null && deviceId.isNotEmpty) {
      AttributionSdkCore.instance.setDeviceId(deviceId);
      FFLog.info("设置设备ID: $deviceId", tag: tag);
    } else {
      FFLog.error("获取设备ID失败", tag: tag);
    }

    // 设置账户ID
    AttributionSdkCore.instance.setWaitForAccountId(true);
    try {
      // 获取用户信息
      final userInfo = await _userService.getUserInfo();
      if (userInfo.userId != null && userInfo.userId!.isNotEmpty) {
        AttributionSdkCore.instance.setAccountId(userInfo.userId!);
        FFLog.info("设置账户ID: ${userInfo.userId}", tag: tag);
      } else {
        FFLog.error("获取账户ID失败: userId为空", tag: tag);
      }
    } catch (e) {
      FFLog.error("获取账户ID失败: $e", tag: tag);
    }

    // 配置SDK回调
    AttributionSdkCore.instance.setSdkCallback(SdkCallback(
      onSdkInitCompleted: (int code) {
        if (code == 0) {
          FFLog.info("SDK初始化成功", tag: tag);
          _isSDKInitialized = true;
          AttributeEventsUtil.isSdkInitialized = true;
          // 初始化成功后，检查是否需要上报归因信息
          _addReportTask();

          // 如果有 deeplink_data 且在 Android 平台，尝试调用 trackAppReEngagement
          if (Platform.isAndroid &&
              _deeplinkData != null &&
              _deeplinkData!.isNotEmpty) {
            _trackAppReEngagement();
          }
        } else {
          FFLog.error("SDK初始化失败，错误码: $code", tag: tag);
          _isSDKInitialized = false;
          AttributeEventsUtil.isSdkInitialized = false;
        }
      },
      onAttributionSuccess: (Map<String, dynamic> attribution) {
        FFLog.info("成功获取归因信息", tag: tag);
        // 标记归因信息已成功返回
        markAttributionAsReturned();
        // 检查是否需要上报
        _addReportTask();

        // 获取短剧ID
        final reelId = _getShortIdFromAttribution(attribution);

        // 上报归因结果
        _reportAttributionResult(reelId != null && reelId.isNotEmpty, reelId);

        // 获取归因短剧ID并尝试播放
        getAttributionShortPlayIdAndNavigate();
      },
      onAttributionFailed: (int code) {
        FFLog.error("获取归因信息失败，错误码: $code", tag: tag);
        // 上报归因失败
        _reportAttributionResult(false, null);
      },
      onIpDetected: (ipList) {
        FFLog.info("获取到IP列表: $ipList", tag: tag);
        clientIpForApi = _encryptIpList(ipList);
      },
    ));

    _initSdk();

    // 检查并上报LP信息（首次安装）
    await checkAndReportLp();
    // 更新用户信息

    // 检查API调用是否超过限制
    const apiKey = "update_user_info";
    if (ApiLimitManager.isLimited(apiKey)) {
      FFLog.info("API调用次数超过限制，跳过获取用户信息", tag: "updateUserInfo");
    } else {
      // 记录API调用
      ApiLimitManager.checkShouldLimit(apiKey);
      await updateUserInfoAndReport();
    }

    // 始终注册生命周期监听
    _registerLifecycleObserver();
  }

  /// 检查并上报LP信息
  Future<void> checkAndReportLp() async {
    if (_isLpReported()) {
      FFLog.debug("LP信息已上报，不需要再次上报", tag: tag);
      return;
    }

    try {
      FFLog.info("开始上报LP信息（首次安装）", tag: tag);
      final success = await ApiAttribute.reportLp();

      if (success) {
        FFLog.info("LP信息上报成功", tag: tag);
        _markLpAsReported();
      } else {
        FFLog.error("LP信息上报失败", tag: tag);
      }
    } catch (e) {
      FFLog.error("上报LP信息异常: $e", tag: tag);
    }
  }

  // ========== 生命周期监听相关函数 ==========

  /// 注册应用生命周期观察者
  void _registerLifecycleObserver() {
    // 避免重复注册
    if (_lifecycleHandler != null) {
      return;
    }

    _lifecycleHandler = _LifecycleEventHandler(resumeCallBack: () {
      // 应用从后台回到前台，检查是否有任务需要执行
      bool hasTasks = !_isAttributionReported() ||
          (!AttributeService.isShortPlayIdEmpty() && isAttributionReturned());

      // 只有在有任务时才记录日志
      if (hasTasks) {
        FFLog.info("应用从后台回到前台，有待处理的任务", tag: tag);
      }

      // 应用从后台回到前台，检查归因信息上报
      _addReportTask();

      // 检查是否需要跳转归因剧
      _checkAndNavigateAttributionShortPlay();
    });
    WidgetsBinding.instance.addObserver(_lifecycleHandler!);
  }

  /// 检查并跳转归因剧
  void _checkAndNavigateAttributionShortPlay() {
    // 如果有短剧ID，尝试播放
    if (!AttributeService.isShortPlayIdEmpty()) {
      getAttributionShortPlayIdAndNavigate();
    }
  }

  /// 移除应用生命周期观察者
  void _removeLifecycleObserver() {
    if (_lifecycleHandler != null) {
      WidgetsBinding.instance.removeObserver(_lifecycleHandler!);
      _lifecycleHandler = null;
    }
  }

  /// 添加上报任务
  Future<void> _addReportTask() async {
    if (!Platform.isIOS) {
      // 安卓直接检查并上报
      checkAndReportAttribution();
      return;
    }
    if (!_isSDKInitialized) {
      // SDK未初始化或者初始化失败，无法回传剪贴板信息
      return;
    }
    // iOS从剪贴板获取归因信息
    await _setClipboardTextToSDKForIOS();
    // 先保存在任务队列，等有网再执行
    _pendingTasks.add(checkAndReportAttribution);
    if (_oldHasNetwork) {
      // 有网，立即执行
      _executePendingTasksForIOS();
    }
  }

  /// 检查并上报归因信息
  Future<void> checkAndReportAttribution() async {
    if (_isReportingTask || _isAttributionReported()) {
      return;
    }

    _isReportingTask = true;

    try {
      // 获取归因信息
      final attributionInfo =
          await AttributionSdkCore.instance.getAttributionInfo();

      if (attributionInfo != null) {
        // 上报归因信息到服务端
        await _reportAttributionToServer(attributionInfo);
      }
    } catch (e) {
      FFLog.error("上报归因信息异常: $e", tag: tag);
    } finally {
      _isReportingTask = false;
    }
  }

  /// 上报归因信息到服务器
  Future<void> _reportAttributionToServer(
      Map<String, dynamic> attribution) async {
    try {
      FFLog.info("开始上报归因信息", tag: tag);

      // 调用API上报归因信息
      final success = await ApiAttribute.reportAttribution(attribution);

      if (success) {
        FFLog.info("归因信息上报成功", tag: tag);
        FFLog.info("归因信息数据 ${jsonEncode(attribution)}", tag: tag);

        // 标记为已上报
        _markAttributionAsReported();

        // 上报成功后，获取归因短剧ID并尝试播放
        getAttributionShortPlayIdAndNavigate();
      } else {
        FFLog.error("归因信息上报失败", tag: tag);
      }
    } catch (e) {
      FFLog.error("上报归因信息异常: $e", tag: tag);
    }
  }

  /// 获取归因信息
  Future<Map<String, dynamic>?> getAttributionInfo() async {
    return await AttributionSdkCore.instance.getAttributionInfo();
  }

  // ========== 三方登录更新用户信息并且上报用户信息 ==========
  Future<void> updateUserInfoAndReport() async {
    FFLog.info("开始获取并上报三方登录用户信息", tag: tag);

    try {
      // 获取Facebook用户信息
      final fbUserData = await ApiAttribute.getFBUserInfo();

      if (fbUserData == null) {
        FFLog.error("获取Facebook用户信息失败", tag: tag);
        return;
      }

      FFLog.debug("获取到Facebook用户信息: ${jsonEncode(fbUserData)}", tag: tag);

      // 按照SDK要求格式构造用户信息
      final Map<String, dynamic> userInfo = {
        'countryName': fbUserData['country'],
        'city': fbUserData['city'],
        'firstName': fbUserData['firstName'],
        'lastName': fbUserData['lastName'],
      };

      // 添加email（如果有）
      final email = fbUserData['email'];
      if (email != null && email.toString().isNotEmpty) {
        userInfo['emails'] = [email];
      }

      // 添加phone（如果有）
      final phone = fbUserData['phone'];
      if (phone != null && phone.toString().isNotEmpty) {
        userInfo['phones'] = [phone];
      }

      FFLog.debug("格式化后的用户信息: ${jsonEncode(userInfo)}", tag: tag);
      // 设置用户信息到归因SDK
      AttributionSdkCore.instance.setUserInfo(userInfo);
      FFLog.info("成功设置用户信息到归因SDK", tag: tag);
    } catch (e) {
      FFLog.error("获取并上报三方登录用户信息异常: $e", tag: tag);
    }
  }

  // ========== 短剧播放相关函数 ==========

  /// 获取归因短剧ID并跳转到播放页面
  Future<void> getAttributionShortPlayIdAndNavigate() async {
    // 从存储中获取短剧ID
    final shortPlayId = AttributeService.getDeeplinkShortPlayId();
    final source = AttributeService.getDeeplinkSource();

    // 如果没有短剧ID，从归因信息获取
    if (shortPlayId == null || shortPlayId.isEmpty) {
      if (isAttributionReturned()) {
        // 从归因信息获取短剧ID
        final attributionShortPlayId = await _getShortPlayCode();
        if (attributionShortPlayId != null) {
          // 归因来源的短剧，检查是否已经播放过
          if (_isAttributionShortPlayPlayed()) {
            FFLog.info("归因短剧已经播放过，不再播放", tag: tag);
            return;
          }

          // 保存从归因获取的短剧ID
          AttributeService.saveDeeplinkShortPlayId(
              attributionShortPlayId.toString(), "attribution");
          // 递归调用以使用新保存的ID
          return getAttributionShortPlayIdAndNavigate();
        } else {
          FFLog.info("没有获取到有效的归因短剧ID", tag: tag);
          return;
        }
      } else {
        FFLog.info("归因信息尚未返回", tag: tag);
        return;
      }
    }

    // 有短剧ID，尝试跳转播放
    try {
      // 检查当前路由是否为首页
      final currentRoute = Get.currentRoute;
      FFLog.info("当前路由: $currentRoute", tag: tag);

      // 检查应用是否在前台
      final isAppInForeground =
          WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;

      // 只有当前路由是首页且应用在前台时才跳转
      if (currentRoute == Routes.mainPage && isAppInForeground) {
        FFLog.info("跳转到短剧播放页面，ID: $shortPlayId，来源: $source", tag: tag);
        // 转换短剧ID为整数
        final shortPlayIdInt = int.tryParse(shortPlayId);
        if (shortPlayIdInt != null) {

          AppNavigator.startDetailsPage(DetailsOptions(
              businessId: shortPlayIdInt,
              playerEpisodeIndex: 0, // 默认从第一集开始播放
              scene: EventValue.campaign, 
              from: EventValue.campaign, // 归因剧来源
              ));

          // 如果是归因短剧，标记为已播放并清空ID
          if (source == "attribution") {
            _reportParseToJump(shortPlayId);
            _markAttributionShortPlayAsPlayed();
            AttributeService.clearDeeplinkShortPlayId();
          } else if (source != null && source != "attribution") {
            // 如果是其他来源，也清空ID（避免重复播放）
            AttributeService.clearDeeplinkShortPlayId();
          }
        } else {
          FFLog.error("短剧ID转换为整数失败: $shortPlayId", tag: tag);
        }
      } else {
        if (!isAppInForeground) {
          FFLog.info("应用在后台，不进行跳转", tag: tag);
        } else {
          FFLog.info("当前不在首页，不进行跳转", tag: tag);
        }
      }
    } catch (e) {
      FFLog.error("跳转到短剧播放页面异常: $e", tag: tag);
    }
  }

  @override
  void onInit() {
    super.onInit();

    // 监听路由变化，当返回首页时检查是否需要播放归因短剧
    _routeSubscription =
        eventBus.on<RouteObserverCustomEvent>().listen((event) {
      if (event.to == Routes.mainPage) {
        // 当路由变为首页时，检查是否需要播放归因短剧
        if (isAttributionReturned() && !AttributeService.isShortPlayIdEmpty()) {
          getAttributionShortPlayIdAndNavigate();
        }
      }
    });

    // 设置平台通道监听深度链接
    _setupPlatformChannels();

    // 检查初始路由是否包含深度链接信息
    _checkInitialRouteForDeeplink();
  }

  /// 设置平台通道以接收来自Android和iOS的短剧ID
  void _setupPlatformChannels() {
    FFLog.debug("正在设置平台通道以接收深度链接数据", tag: tag);

    // 监听Android平台通道
    const androidChannel = MethodChannel('com.flareflow.android/deeplink');
    androidChannel.setMethodCallHandler((call) async {
      FFLog.debug("收到Android平台通道调用: ${call.method}", tag: tag);
      if (call.method == 'setDeepLinkShortPlayId') {
        final shortId = call.arguments as String?;
        FFLog.info("Android深度链接参数: $shortId", tag: tag);
        if (shortId != null && shortId.isNotEmpty) {
          FFLog.info("收到Android深度链接短剧ID: $shortId", tag: tag);
          AttributeService.saveDeeplinkShortPlayId(shortId, "android");
        }
      } else if (call.method == 'setDeeplinkData') {
        // 处理 setDeeplinkData 方法调用
        final data = call.arguments as String?;
        FFLog.info("Android深度链接数据: $data", tag: tag);
        if (data != null && data.isNotEmpty) {
          FFLog.info("收到Android再互动深度链接数据", tag: tag);
          _deeplinkData = data;

          // 如果SDK已初始化，立即调用trackAppReEngagement
          if (_isSDKInitialized) {
            _trackAppReEngagement();
          }
          // 如果SDK未初始化，会在初始化成功后通过回调调用
        }
      }
      return null;
    });

    // 监听iOS平台通道
    const iosChannel = MethodChannel('com.flareflow.ios/deeplink');
    iosChannel.setMethodCallHandler((call) async {
      FFLog.info("收到iOS平台通道调用: ${call.method}", tag: tag);
      if (call.method == 'handleDeepLink') {
        final arguments = call.arguments as Map<dynamic, dynamic>?;
        FFLog.info("iOS深度链接参数: $arguments", tag: tag);
        final shortId = arguments?['shortid'] as String?;
        if (shortId != null && shortId.isNotEmpty) {
          FFLog.info("收到iOS深度链接短剧ID: $shortId", tag: tag);
          AttributeService.saveDeeplinkShortPlayId(shortId, "ios");
        }
        return true;
      }
      return null;
    });

    FFLog.info("平台通道设置完成", tag: tag);

    // 通知原生端Flutter通道已就绪
    _notifyNativeChannelsReady();
  }

  /// 通知原生端Flutter通道已就绪
  void _notifyNativeChannelsReady() {
    try {
      // 短暂延迟，确保通道注册完成
      Future.delayed(const Duration(milliseconds: 100), () {
        FFLog.debug("通知原生端Flutter通道已就绪", tag: tag);

        // 通知Android平台
        const androidChannel = MethodChannel('com.flareflow.android/deeplink');
        androidChannel.invokeMethod('flutterChannelReady').then((_) {
          FFLog.debug("已通知Android平台Flutter通道就绪", tag: tag);
        }).catchError((error) {
          // 忽略错误，可能原生端未实现此方法
          FFLog.debug("通知Android平台失败: $error", tag: tag);
        });

        // 通知iOS平台
        const iosChannel = MethodChannel('com.flareflow.ios/deeplink');
        iosChannel.invokeMethod('flutterChannelReady').then((_) {
          FFLog.debug("已通知iOS平台Flutter通道就绪", tag: tag);
        }).catchError((error) {
          // 忽略错误，可能原生端未实现此方法
          FFLog.debug("通知iOS平台失败: $error", tag: tag);
        });
      });
    } catch (e) {
      FFLog.error("通知原生端Flutter通道就绪失败: $e", tag: tag);
    }
  }

  @override
  void onClose() {
    // 确保在服务销毁时移除监听
    _removeLifecycleObserver();
    _routeSubscription?.cancel();
    _connectivitySubscription?.cancel();
    super.onClose();
  }

  /// 处理归因短剧展示逻辑
  Future<int?> _getShortPlayCode() async {
    FFLog.info("开始处理归因短剧展示逻辑", tag: tag);

    // 获取最新的归因信息
    final attributionInfo = await AttributionSdkCore.instance.getAttributionInfo();

    // 如果没有归因信息，则结束流程
    if (attributionInfo == null) {
      FFLog.error("没有获取到归因信息", tag: tag);
      return null;
    }

    FFLog.info("获取到归因信息: ${jsonEncode(attributionInfo)}", tag: tag);

    // 使用通用函数获取短剧ID
    final shortPlayId = _getShortIdFromAttribution(attributionInfo);

    // 如果短剧ID为空，标记为已播放并结束流程
    if (shortPlayId == null || shortPlayId.isEmpty) {
      FFLog.error("短剧ID为空", tag: tag);
      return null;
    }

    // 将字符串ID转换为整数
    final int? shortPlayIdInt = int.tryParse(shortPlayId);
    if (shortPlayIdInt == null) {
      FFLog.error("短剧ID转换为整数失败: $shortPlayId", tag: tag);
      return null;
    }

    FFLog.info("成功获取归因短剧ID: $shortPlayIdInt", tag: tag);
    // 返回有效的短剧ID
    return shortPlayIdInt;
  }

  // ==========  SafeStorage 相关函数 ==========

  /// 检查归因信息是否已上报
  bool? _isReportedCache; // 内存缓存
  /// 检查归因信息是否已上报
  bool _isAttributionReported() {
    if (_isReportedCache == true) {
      return true;
    }
    final result = SafeStorage().read<bool>(_storageKeyIsReported) ?? false;
    if (result) {
      _isReportedCache = true; // 一旦为 true 就缓存
    }
    return result;
  }

  /// 标记归因信息为已上报
  void _markAttributionAsReported() {
    SafeStorage().write(_storageKeyIsReported, true);
  }

  /// 检查归因信息是否成功返回过
  bool isAttributionReturned() {
    return SafeStorage().read<bool>(_storageKeyAttributionReturned) ?? false;
  }

  /// 标记归因信息已成功返回
  void markAttributionAsReturned() {
    SafeStorage().write(_storageKeyAttributionReturned, true);
  }

  /// 检查LP信息是否已上报
  bool _isLpReported() {
    return SafeStorage().read<bool>(_storageKeyLpReported) ?? false;
  }

  /// 标记LP信息为已上报
  void _markLpAsReported() {
    SafeStorage().write(_storageKeyLpReported, true);
  }

  /// 加密IP列表
  String? _encryptIpList(List<String> ipList) {
    try {
      final joinedIps = ipList.join(",");
      FFLog.debug("加密IP列表: $joinedIps", tag: tag);
      // 使用项目现有的加密工具类
      final publicKey = SecretRepo.getPublicKey();
      return EncryptionUtils.encryptWithRSA(joinedIps, publicKey);
    } catch (e) {
      FFLog.error("加密IP列表失败: $e", tag: tag);
      return null;
    }
  }

  /// 获取加密后的IP地址用于HTTP请求头
  static String? getEncryptedIpForHeader() {
    try {
      if (Get.isRegistered<AttributeService>()) {
        final clientIpForApi = Get.find<AttributeService>().clientIpForApi;
        if (clientIpForApi != null && clientIpForApi.isNotEmpty) {
          return clientIpForApi;
        }
        FFLog.error("获取加密IP失败: ip为空", tag: tag);
      } else {
        FFLog.error("获取加密IP失败: AttributeService未注册", tag: tag);
      }
    } catch (e) {
      FFLog.error("获取加密IP失败: $e", tag: tag);
    }
    return null;
  }

  /// 保存深度链接短剧ID和来源
  static void saveDeeplinkShortPlayId(String shortId, String source) {
    FFLog.info("保存深度链接短剧ID: $shortId, 来源: $source", tag: AttributeService.tag);
    SafeStorage().write(_storageKeyDeeplinkShortPlayId, shortId);
    SafeStorage().write(_storageKeyDeeplinkSource, source);

    // 如果AttributeService已经注册，尝试立即导航
    if (Get.isRegistered<AttributeService>()) {
      Get.find<AttributeService>().getAttributionShortPlayIdAndNavigate();
    }
  }

  /// 获取深度链接短剧ID
  static String? getDeeplinkShortPlayId() {
    return SafeStorage().read<String>(_storageKeyDeeplinkShortPlayId);
  }

  /// 获取深度链接来源
  static String? getDeeplinkSource() {
    return SafeStorage().read<String>(_storageKeyDeeplinkSource);
  }

  /// 检查短剧ID是否为空
  static bool isShortPlayIdEmpty() {
    final shortId = getDeeplinkShortPlayId();
    return shortId == null || shortId.isEmpty;
  }

  /// 清空短剧ID（播放后调用）
  static void clearDeeplinkShortPlayId() {
    FFLog.info("清空深度链接短剧ID", tag: AttributeService.tag);
    SafeStorage().remove(_storageKeyDeeplinkShortPlayId);
    SafeStorage().remove(_storageKeyDeeplinkSource);
  }

  /// 检查初始路由是否包含深度链接信息
  void _checkInitialRouteForDeeplink() {
    try {
      // 获取当前路由
      final initialRoute = Get.currentRoute;
      FFLog.debug("检查初始路由: $initialRoute", tag: tag);

      // 检查是否是深度链接路由
      if (initialRoute.startsWith('/deeplink')) {
        FFLog.info("检测到深度链接初始路由: $initialRoute", tag: tag);

        // 解析参数
        final uri = Uri.parse(initialRoute);
        final shortId = uri.queryParameters['shortid'];
        final source = uri.queryParameters['source'] ?? 'initial_route';

        if (shortId != null && shortId.isNotEmpty) {
          FFLog.info("从初始路由获取到短剧ID: $shortId, 来源: $source", tag: tag);

          // 保存短剧ID和来源
          AttributeService.saveDeeplinkShortPlayId(shortId, source);
        }
      }
    } catch (e) {
      FFLog.error("解析初始路由异常: $e", tag: tag);
    }
  }

  // 添加处理 deeplink_data 的函数
  void _trackAppReEngagement() {
    if (_deeplinkData == null || _deeplinkData!.isEmpty) {
      FFLog.info("没有 deeplink_data 可用，跳过 trackAppReEngagement", tag: tag);
      return;
    }

    if (!(_deeplinkData ?? "").startsWith("http")) {
      FFLog.info("deeplink_data 不是 http 链接，跳过 trackAppReEngagement", tag: tag);
      // 处理完后清空数据，避免重复调用
      _deeplinkData = null;
      return;
    }

    try {
      FFLog.info("调用 trackAppReEngagement 处理再互动数据", tag: tag);
      AttributionSdkCore.instance.trackAppReEngagement(_deeplinkData!);
      FFLog.info("trackAppReEngagement 调用成功，数据: $_deeplinkData", tag: tag);
      // 处理完后清空数据，避免重复调用
      _deeplinkData = null;
    } catch (e) {
      FFLog.error("调用 trackAppReEngagement 失败: $e", tag: tag);
    }
  }

  /// 检查归因短剧是否已播放
  bool _isAttributionShortPlayPlayed() {
    return SafeStorage().read<bool>(_storageKeyAttributionPlayed) ?? false;
  }

  /// 标记归因短剧为已播放
  void _markAttributionShortPlayAsPlayed() {
    FFLog.info("标记归因短剧为已播放", tag: tag);
    SafeStorage().write(_storageKeyAttributionPlayed, true);
  }

  /// 从归因信息中获取短剧ID
  String? _getShortIdFromAttribution(Map<String, dynamic> attribution) {
    try {
      if (attribution.containsKey('campaignName')) {
        final campaignName = attribution['campaignName'] as String?;
        if (campaignName != null && campaignName.isNotEmpty) {
          // 查找 _shortid- 后面的数字
          final RegExp regex = RegExp(r'_shortid-([0-9]+)_');
          final match = regex.firstMatch(campaignName);

          if (match != null && match.groupCount >= 1) {
            final shortId = match.group(1);
            FFLog.info("从归因信息中获取到短剧ID: $shortId", tag: tag);
            // 记录解析成功的时间
            _parseSuccessTime = DateTime.now();
            return shortId;
          }
        }
      }
      FFLog.info("归因信息中未找到短剧ID", tag: tag);
      return null;
    } catch (e) {
      FFLog.info("获取短剧ID异常: $e", tag: tag);
      return null;
    }
  }

  /// 判断是否为自然用户
  /// 如果 _getShortIdFromAttribution 返回 null，则为自然用户
  Future<bool> isNaturalUser() async {
    try {
      if (isAttributionReturned()) {
        final shortId = await _getShortPlayCode();
        return shortId == null;
      }
      return false;
    }  catch (e) {
      FFLog.error("判断自然用户异常: $e", tag: tag);
      // 发生异常时，默认为自然用户
      return false;
    }
  }

  // 添加上报归因结果的方法
  void _reportAttributionResult(bool isSuccess, String? reelId) {
    if (_attributionStartTime == null) {
      FFLog.error("归因开始时间为空，无法计算持续时间", tag: tag);
      return;
    }

    final duration = DateTime.now().difference(_attributionStartTime!).inSeconds;
    // 如果时长小于1秒，按1秒计算
    final finalDuration = duration < 1 ? 1 : duration;

    FFLog.info("归因结果上报 - 状态: ${isSuccess ? "成功" : "失败"}, 短剧ID: $reelId, 持续时间: $finalDuration秒", tag: tag);

    useTrackEvent(EventName.start_to_parse, extra: {
      EventKey.reelId: reelId ?? '',
      EventKey.status: (isSuccess ? 1 : 0).toString(),
      EventKey.duration: finalDuration.toString(),
    });

    // 重置开始时间
    _attributionStartTime = null;
    FFLog.debug("重置归因开始时间", tag: tag);
  }

  // 添加上报解析成功到跳转成功的时间
  void _reportParseToJump(String reelId) {
    if (_parseSuccessTime == null) return;

    final duration = DateTime.now().difference(_parseSuccessTime!).inSeconds;
    // 如果时长小于1秒，按1秒计算
    final finalDuration = duration < 1 ? 1 : duration;

    useTrackEvent(EventName.parse_to_jump, extra: {
      EventKey.reelId: reelId,
      EventKey.duration: finalDuration.toString(),
    });

    // 重置开始时间
    _parseSuccessTime = null;
  }
}

/// 生命周期事件处理类
class _LifecycleEventHandler extends WidgetsBindingObserver {
  final Function resumeCallBack;

  _LifecycleEventHandler({required this.resumeCallBack});

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      resumeCallBack();
    }
  }
}

extension AttributeServiceIOSExtension on AttributeService {
  /// iOS监听网络
  Future<void> _listenNetworkForIOS() async {
    if (!Platform.isIOS) {
      // 安卓不需要监听网络
      return;
    }
    // 初始化网络状态
    final connectivity = await Connectivity().checkConnectivity();
    _oldHasNetwork = _checkHasNetworkForIOS(connectivity);

    // 监听网络状态变化
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> result) {
      final oldNetwork = _oldHasNetwork;
      final newNetwork = _checkHasNetworkForIOS(result);
      _oldHasNetwork = newNetwork;

      if (!oldNetwork && newNetwork) {
        // 从无网到有网
        _executePendingTasksForIOS();
      }
    });
  }

  // 检查iOS网络状态
  bool _checkHasNetworkForIOS(List<ConnectivityResult> result) {
    if (!Platform.isIOS) {
      return true;
    }
    if (result.contains(ConnectivityResult.wifi)) {
      return true;
    }
    if (result.contains(ConnectivityResult.ethernet)) {
      return true;
    }
    if (result.contains(ConnectivityResult.mobile)) {
      return true;
    }
    if (result.contains(ConnectivityResult.vpn)) {
      return true;
    }
    if (result.contains(ConnectivityResult.other)) {
      return true;
    }
    return false;
  }

  /// 执行iOS任务队列
  void _executePendingTasksForIOS() {
    if (!Platform.isIOS) {
      return;
    }
    while (_pendingTasks.isNotEmpty) {
      final task = _pendingTasks.removeAt(0);
      task();
    }
  }

  /// iOS从剪贴板读取文本并设置到SDK
  Future<void> _setClipboardTextToSDKForIOS() async {
    if (!Platform.isIOS) {
      return;
    }
    if (_isReportingTask || _isAttributionReported()) {
      return;
    }
    if (kDebugMode) {
      // debug时频繁弹出剪贴板弹窗影响开发，所以此处不执行
      return;
    }
    final clipboardText = await getClipboardTextForIOS();
    FFLog.info("读取到剪贴板内容: $clipboardText", tag: AttributeService.tag);
    if (clipboardText.isEmpty) {
      return;
    }

    // 检查剪贴板内容是否包含关键字
    final String lowerText = clipboardText.toLowerCase();
    if (!lowerText.contains('clipuuid') &&
        !lowerText.contains('shortplayid') &&
        !lowerText.contains('contentId')) {
      return;
    }
    FFLog.info("剪贴板内容包含目标关键字", tag: AttributeService.tag);
    AttributionSdkCore.instance.setClipboardInfo(clipboardText);
  }

  // iOS读取剪贴板文本
  Future<String> getClipboardTextForIOS() async {
    final data = await Clipboard.getData(Clipboard.kTextPlain);
    final text = data?.text ?? '';
    if (text.isNotEmpty) {
      await Clipboard.setData(const ClipboardData(text: ""));
    }
    return text;
  }
}

import 'package:get/get.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/ff_userdefault_util.dart';

/// 广告触发器接口
abstract class AdTriggerCase {
  /// 判断是否可以触发广告
  bool canTrigger();
  
  /// 重置触发器状态
  void reset();
}

/// 基于计数的触发器实现
class CountTriggerCase implements AdTriggerCase {
  final int totalCount;
   int currentCount;
  final bool Function()? extraCase;
  
  CountTriggerCase({required this.totalCount, this.currentCount = 0, this.extraCase});
  
  /// 尝试触发并增加计数
  bool tryToTrigger({required int count}) {
    currentCount += count;
    return canTrigger();
  }
  
  @override
  bool canTrigger() {
    return currentCount >= totalCount && extraCasePassed();
  }
  
  /// 如果计数已达到，则重置计数
  void resetIfCountPassed() {
    if (currentCount >= totalCount) {
      currentCount = 0;
    }
  }
  
  @override
  void reset() {
    currentCount = 0;
  }
  
  /// 检查额外条件是否通过
  bool extraCasePassed() {
    if (extraCase != null) {
      return extraCase!();
    }
    return true;
  }
  
  /// 没有金币时的触发器
  static CountTriggerCase noMoney({required int count, int currentCount = 0}) {
    return CountTriggerCase(
      totalCount: count,
      currentCount: currentCount,
      extraCase: () {
        final userService = Get.find<UserService>();
        if (userService.userInfo.value == null) {
          return false;
        }
        
        return userService.userInfo.value?.coins == 0;
      },
    );
  }
  
  /// 已付费但没有金币时的触发器
  static CountTriggerCase paidButNoMoney({required int count}) {
    return CountTriggerCase(
      totalCount: count,
      extraCase: () {
        final userService = Get.find<UserService>();
        if (userService.userInfo.value == null) {
          return false;
        }
        
        if (userService.userInfo.value?.coins != 0) {
          return false;
        }
        
        if (userService.userInfo.value?.isRecharged != true) {
          return false;
        }
        
        return true;
      },
    );
  }
  
  /// 从未充值的触发器
  static CountTriggerCase neverRecharge({required int count}) {
    return CountTriggerCase(
      totalCount: count,
      extraCase: () {
        final userService = Get.find<UserService>();
        if (userService.userInfo.value == null) {
          return false;
        }
        
        return userService.userInfo.value?.isRecharged != true;
      },
    );
  }
}
import 'package:get/get.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/ad/adtrigger/ad_trigger_case.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/ff_userdefault_util.dart';

// 事件枚举
enum Event {
  closeEpisodeDetail,
  closePaySceneWithoutPay,
  watchRewardAdList
}

class AdTriggerEventCollector {
  // 单例模式
  static final AdTriggerEventCollector shared = AdTriggerEventCollector._();

  // 监听用户信息变化
  final userService = Get.find<UserService>();

  // 退出详情页触发器
  late final CountTriggerCase existDetailPageCase = CountTriggerCase.noMoney(
    count: 3,
    currentCount: FFUserDefault.exitDetailPageCount.value,
  );
  
  // 关闭付费场景触发器
  late final CountTriggerCase closePaySceneWithoutPayCase = CountTriggerCase.noMoney(
    count: 1,
  );

  // 观看广告触发器
  late final CountTriggerCase watchAdCase = CountTriggerCase.noMoney(
    count: 2,
    currentCount: FFUserDefault.watchAdRewardCount.value,
  );
  
  DateTime? lastPresentDate;
  bool _triggerCaseCompleted = false;
  
  // 触发条件完成状态，带持久化
  bool get triggerCaseCompleted => _triggerCaseCompleted;
  set triggerCaseCompleted(bool value) {
    _triggerCaseCompleted = value;
    FFUserDefault.isAdDisplayTriggered.value = value;
  }
  
  // 默认是可以收集
  bool canCollect = true;
  
  // 是否可以触发提前看广告
  bool get canTrigger => (canCollect && triggerCaseCompleted) 
                           && userService.userInfo.value?.coins == 0;
                          
  
  // 私有构造函数
  AdTriggerEventCollector._() {
    _triggerCaseCompleted = FFUserDefault.isAdDisplayTriggered.value;
    
    ever(userService.userInfo, (_) {
      updateCollectStatus();
    });
    
    updateCollectStatus();
  }
  
  // 更新收集状态
  void updateCollectStatus() {
    final hasCoins = (userService.userInfo.value?.coins ?? 0) > 0;
    
    if (canCollect && hasCoins) {
      // 原来能收集事件，现在有钱了，那么就要终止收集，触发条件也全都清零
      // 其实就是从没钱变有钱
      canCollect = false;
      reset();
    } else if (!canCollect && !hasCoins) {
      // 原来不收集事件，现在又没钱了，那么就要重新收集事件
      // 其实就是从有钱变没钱
      canCollect = true;
      reset();
    }
  }
    
  // 收集事件
  void collect(Event event) {
    if (!canCollect) {
      return;
    }
    
    // 满足触发条件后，其他行为就不处理了
    if (triggerCaseCompleted) {
      return;
    }
    
    bool triggered = false;
    switch (event) {
      case Event.closeEpisodeDetail:
        FFUserDefault.exitDetailPageCount.value += 1;
        triggered = existDetailPageCase.tryToTrigger(count: 1);
        break;
      case Event.closePaySceneWithoutPay:
        triggered = closePaySceneWithoutPayCase.tryToTrigger(count: 1);
        break;
      case Event.watchRewardAdList:
        FFUserDefault.watchAdRewardCount.value += 1;
        triggered = watchAdCase.tryToTrigger(count: 1);
        break;
    }
    
    // 埋点的时候同时触发预取
    // AdManager().prefetchAds();
    
    if (triggered) {
      triggerCaseCompleted = true;
    }
  }
  
  // 重置触发器
  void reset() {
    existDetailPageCase.resetIfCountPassed();
    closePaySceneWithoutPayCase.resetIfCountPassed();
    watchAdCase.resetIfCountPassed();
    
    triggerCaseCompleted = false;
  }
  
  // 更新最后展示时间
  void updateExitLastInteruptADPrsentDate() {
    lastPresentDate = DateTime.now();
  }
  
  // 检查触发间隔是否满足
  bool checkExitDetailPageGapFilled() {
    bool dateCheckPassed = true;
    const int minDurationSecond = 60;
    
    if (lastPresentDate != null) {
      dateCheckPassed = DateTime.now().difference(lastPresentDate!).inSeconds > minDurationSecond;
    }

    return dateCheckPassed;
  }
}

// AdManager扩展
extension AdManagerExtension on AdManager {
  static void collectTriggerEvent(Event event) {
    AdTriggerEventCollector.shared.collect(event);
  }
}


class CountChecker {
  final int maxCount;
  final bool enabled;
  int currentCount = 0;
  
  CountChecker({required this.maxCount, required this.enabled});
  
  bool check() {
    if (!enabled) {
      return false;
    }
    
    currentCount = currentCount + 1;
    return currentCount >= maxCount;
  }
  
  void reset() {
    currentCount = 0;
  }
}

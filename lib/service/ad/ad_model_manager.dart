import 'package:get/get.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_interstitial_model.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_native_model.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'dart:math';
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_native_model.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_reward_model.dart';

import 'ad_track_event.dart';

class AdModelManager {
  late final AdScene _adScene;
  late final AdUnitCacheQueue _normalAdCacheQueue;
  late final AdUnitCacheQueue _defaultAdCacheQueue;
  
  AdModelManager(AdScene adScene, List<AdUnit> normalList, List<AdUnit> defaultList) {
    _adScene = adScene;
    
    _normalAdCacheQueue = AdUnitCacheQueue(adScene, normalList, true);
    _defaultAdCacheQueue = AdUnitCacheQueue(adScene ,defaultList, true);
    
    FFLog.debug('初始化 AdModelManager: 场景=${adScene.toString()}}', tag: 'AdModelManager');
    
    // 启动时预缓存
    prefetchAd();
  }

  // 预缓存
  void prefetchAd() {
    _normalAdCacheQueue.prefetchAd();
    _defaultAdCacheQueue.prefetchAd();
  }

  /// 从缓存取出广告模型
  AdModel? getAdModelFromCache() {
    AdModel? adModel = _normalAdCacheQueue.popFromCache();
    if (adModel != null) {
      return adModel;
    }
    adModel = _defaultAdCacheQueue.popFromCache();
    return adModel;
  }

  // 加载广告模型，实现2秒限时加载逻辑
  Future<AdModel?> loadAdModel() async {

    const timeout = Duration(seconds: 2);
    final startTime = DateTime.now();
    
    // 清除无效缓存
    FFLog.debug('清除无效缓存', tag: 'AdModelManager');
    _normalAdCacheQueue._clearInvalidAdUnits();
    _defaultAdCacheQueue._clearInvalidAdUnits();
    
    // 检查正常广告缓存
    FFLog.debug('检查正常广告缓存', tag: 'AdModelManager');
    AdModel? cachedNormalAd = _normalAdCacheQueue.popFromCache();
    if (cachedNormalAd != null) {
      FFLog.trace('使用正常广告缓存 ${cachedNormalAd.adUnit.adUnitID}', tag: 'AdModelManager');
      return cachedNormalAd;
    }
    
    // 正常广告缓存未命中，尝试加载正常广告（2秒超时）
    AdModel? result;
    FFLog.debug('正常广告缓存未命中，开始加载正常广告', tag: 'AdModelManager');
    try {
      result = await _normalAdCacheQueue.loadAdModel().timeout(timeout, onTimeout: () => null);
      if (result != null) {
        FFLog.trace('正常广告加载成功 ${result.adUnit.adUnitID}', tag: 'AdModelManager');
        await _delayAfterLoaded(startTime);
        return result;
      }
    } catch (e) {
      FFLog.error('正常广告加载异常: $e', tag: 'AdModelManager');
    }
    
    // 正常广告加载失败，检查兜底广告缓存
    FFLog.debug('正常广告加载失败，检查兜底广告缓存', tag: 'AdModelManager');
    AdModel? cachedDefaultAd = _defaultAdCacheQueue.popFromCache();
    if (cachedDefaultAd != null) {
      FFLog.trace('使用兜底广告缓存 ${cachedDefaultAd.adUnit.adUnitID}', tag: 'AdModelManager');
      return cachedDefaultAd;
    }
    
    // 兜底广告缓存也未命中，同时请求两种广告
    FFLog.debug('兜底广告缓存未命中，同时请求两种广告', tag: 'AdModelManager');
    final normalAdFuture = _normalAdCacheQueue.loadAdModel();
    final defaultAdFuture = _defaultAdCacheQueue.loadAdModel();
    
    try {
      // 等待2秒，看哪个先返回
      FFLog.debug('同时请求正常广告和兜底广告,等待2秒', tag: 'AdModelManager');
      final futures = await Future.wait([
        normalAdFuture.timeout(timeout, onTimeout: () => null),
        defaultAdFuture.timeout(timeout, onTimeout: () => null),
      ]);
      
      final normalAd = futures[0];
      final defaultAd = futures[1];
      
      // 优先展示正常广告，其次是兜底广告
      if (normalAd != null) {
        FFLog.trace('正常广告请求成功，使用正常广告 ${normalAd.adUnit.adUnitID}', tag: 'AdModelManager');
        result = normalAd;
      } else if (defaultAd != null) {
        FFLog.error('正常广告请求失败，兜底广告请求成功，使用兜底广告 ${defaultAd.adUnit.adUnitID}', tag: 'AdModelManager');
        result = defaultAd;
      } else {
        FFLog.error('正常广告和兜底广告请求均失败', tag: 'AdModelManager');
      }
    } catch (e) {
      FFLog.error('广告请求发生异常: $e', tag: 'AdModelManager');
    }
    
    // 在返回结果前检查是否需要等待
    if (result != null) {
      await _delayAfterLoaded(startTime);
      return result;
    }
    
    // todo
    // 把失败的aduinitid数组传给正常广告的cachequeue，并告诉他加载下一个ad，递归，直到cachequeue没有可加载的广告为止，如果都失败，再用兜底广告的cachequeue执行一次这个流程。
    return result;
  }

  Future<void> _delayAfterLoaded(DateTime startTime) async {
    const timeout = Duration(seconds: 2);

    final elapsedTime = DateTime.now().difference(startTime);
    if (elapsedTime < timeout) {
      FFLog.debug('广告加载时间不足2秒，等待剩余时间', tag: 'AdModelManager');
      await Future.delayed(timeout - elapsedTime);
    }
  }
}

class AdUnitCacheQueue {
  final List<AdUnit> _allAdUnitList;
  final Map<bool, AdCacheContext> _adCacheContextForOrientation = {};
  final AdScene _adScene;
  // 是否应该缓存
  final bool _shouldCache;

  bool get _isLandscape {
    BuildContext? context = Get.context;
    if (context == null) {
      return false;
    }
    FFLog.info("广告这边判断是 isLandscape:${ScreenUtils.isLandscape(context)}");
    return ScreenUtils.isLandscape(context);
  }

  AdUnitCacheQueue(AdScene adScene, List<AdUnit> adUnitList, bool shouldCache) : 
    _adScene = adScene, _allAdUnitList = adUnitList, _shouldCache = shouldCache;

  // 从缓存中获取广告，并触发预加载
  AdModel? popFromCache() {
    _clearInvalidAdUnits();

    if (_adCacheContextForOrientation.isEmpty) {
      return null;
    }
    bool isLandscape = _isLandscape;
    final adCacheContext = _adCacheContextForOrientation[isLandscape];
    if (adCacheContext == null) {
      return null;
    }
    final cachedAdModel = adCacheContext.cachedAdModel;
    if (cachedAdModel == null) {
      return null;
    }
    _adCacheContextForOrientation.remove(isLandscape); // 消耗完缓存后立即预加载下一个
    FFLog.debug('从缓存中取出广告，并触发新的预加载 ${cachedAdModel.adUnit.adUnitID}', tag: 'AdModelManager');
    if (_shouldCache) {
      prefetchAd(); 
    }
    return cachedAdModel;
  }

  // 预加载广告到缓存
  Future<void> prefetchAd() async {
    if (_shouldCache == false) {
      return;
    }
    bool isLandscape = _isLandscape;
    final adCacheContext = _adCacheContextForOrientation[isLandscape];
    final cachedAdModel = adCacheContext?.cachedAdModel;
    if (cachedAdModel != null) {
      FFLog.trace('已有缓存，跳过预加载 ${cachedAdModel.adUnit.adUnitID}', tag: 'AdModelManager');
      return;
    }
    final isLoading = adCacheContext?.isLoading ?? false;
    if (isLoading) {
      FFLog.trace('正在加载中，跳过预加载', tag: 'AdModelManager');
      return;
    }
    if (_allAdUnitList.isEmpty) {
      FFLog.trace('广告单元列表为空，跳过预加载', tag: 'AdModelManager');
      return;
    }
    
    final adModel = await _loadAdModel();
    if (adModel == null) {
      return;
    }
    // 缓存成功埋点
    AdTrackEvent.adFill(adModel);
  }

  // 加载广告模型
  Future<AdModel?> loadAdModel() async {
    FFLog.debug('开始加载广告模型', tag: 'AdModelManager');
    final result = await _loadAdModel();
    _adCacheContextForOrientation.remove(_isLandscape); // 每次消费后都清空缓存
    return result;
  }
  
  // 内部加载广告逻辑
  Future<AdModel?> _loadAdModel() async {
    _clearInvalidAdUnits();

    bool isLandscape = _isLandscape;
    AdCacheContext adCacheContext = _adCacheContextForOrientation[isLandscape] ?? AdCacheContext();
    final cachedAdModel = adCacheContext.cachedAdModel;
    if (cachedAdModel != null) {
      // 有缓存还未使用
      FFLog.trace('使用现有缓存 ${cachedAdModel.adUnit.adUnitID}', tag: 'AdModelManager');
      return cachedAdModel;
    }
    
    if (_allAdUnitList.isEmpty) {
      // 没有广告单元
      FFLog.debug('没有可用的广告单元', tag: 'AdModelManager');
      return null;
    }
    
    if (adCacheContext.isLoading) {
      // 正在加载中
      FFLog.debug('已有加载请求正在进行中', tag: 'AdModelManager');
      return null;
    }

    _adCacheContextForOrientation[isLandscape] = adCacheContext;
    _updateLoadingState(true);
    
    try {
      final random = Random();
      final randomIndex = random.nextInt(_allAdUnitList.length);
      
      final adUnit = _allAdUnitList[randomIndex];
      
      AdModel? adModel;
      if (adUnit.type == AdType.native) {
        adModel = AdAmNativeModel(adUnit: adUnit);
      } else if (adUnit.type == AdType.interstitial) {
        adModel = AdAmInterstitialModel(adUnit: adUnit);
      } else if (adUnit.type == AdType.reward) {
        adModel = AdAmRewardModel(adUnit: adUnit);
      } else {
        FFLog.error('未知的广告类型: ${adUnit.type}', tag: 'AdModelManager');
        _updateLoadingState(false);
        return null;
      }
      FFLog.trace('开始请求广告 ${adModel.adUnit.adUnitID}', tag: 'AdModelManager');
      AdTrackEvent.adRequest(adUnit);
      //这边增加个待优化,判断native的加载是用哪种
      bool userMediunTemple = false;
      if (_adScene == AdScene.shortsNative) {
        userMediunTemple = true;
      }
      await adModel.loadAd(userMediunTemple: userMediunTemple);
      if (adModel.statusModel.status == AdLoadStatus.failed) {
        FFLog.error('广告加载失败 ${adModel.adUnit.adUnitID}', tag: 'AdModelManager');
        _updateLoadingState(false);
        return null;
      }
      
      // 加载成功
      FFLog.trace('广告加载成功 ${adModel.adUnit.adUnitID}', tag: 'AdModelManager');

      adCacheContext.cachedAdModel = adModel;
      adCacheContext.cachedAdModel?.statusModel.successDate = DateTime.now();  // 记录加载时间
      _updateLoadingState(false);
      return adCacheContext.cachedAdModel;
    } catch (e) {
      FFLog.error('广告加载失败: $e', tag: 'AdModelManager');
      _updateLoadingState(false);
      return null;
    }
  }

  // 清除无效的广告单元
  void _clearInvalidAdUnits() {
    bool isLandscape = _isLandscape;
    final adCacheContext = _adCacheContextForOrientation[isLandscape];
    final adModel = adCacheContext?.cachedAdModel;
    if (adModel == null) {
      return;
    }
    final successDate = adModel.statusModel.successDate;
    if (successDate == null) {
      return;
    }
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final loadTime = successDate.millisecondsSinceEpoch;
    final timeDiff = currentTime - loadTime;
    if (timeDiff > 60 * 60 * 1000) {
      // 缓存时间超过1小时，清除缓存
      FFLog.trace('缓存时间超过1小时，清除缓存', tag: 'AdModelManager');
      _adCacheContextForOrientation.remove(isLandscape);
    }
  }

  void _updateLoadingState(bool isLoading) {
    bool isLandscape = _isLandscape;
    final context = _adCacheContextForOrientation[isLandscape];
    if (context == null) {
      return;
    }
    context.isLoading = isLoading;
    _adCacheContextForOrientation[isLandscape] = context;
  }
}

class AdCacheContext {
  AdModel? cachedAdModel;
  bool isLoading = false;
}
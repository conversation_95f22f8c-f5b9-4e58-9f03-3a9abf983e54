import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:path_provider/path_provider.dart';
import 'package:playlet/api/ad_api.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/service/ad/ad_model_manager.dart';
import 'package:playlet/service/ad/ad_toast_custom.dart';
import 'package:playlet/service/ad/ad_track_event.dart';
import 'package:playlet/service/ad/admodel/ad_config.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/admodel/admob/ad_am_native_model.dart';
import 'package:playlet/service/ad/admodel/am_admob_model.dart';
import 'package:playlet/service/ad/adtrigger/ad_trigger_eventcollector.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/file_storage.dart';

/// 广告上下文
class AdContext {
  /// 事件参数
  Map<String, String>? eventParam;
}

class AdManager extends GetxService {
  // Private constructor
  AdManager._();

  // Singleton instance
  static final AdManager _instance = AdManager._();

  // Factory constructor to return singleton instance
  factory AdManager() {
    return _instance;
  }

  static const String _tag = 'AdManager';
  late final Map<AdScene, AdModelManager> _modelManagerForScene = {};

  RxBool shouldShowAd = false.obs;

  Future<void> init() async {
    MobileAds.instance.initialize();
    _modelManagerForScene.clear();

    // 添加对 userInfo 的监听
    final UserService userService = Get.find<UserService>();
    ever(userService.userInfo, (_) {
      _updateShouldShowAd();
    });

    // 初始化时更新一次广告状态
    _updateShouldShowAd();

    AdConfig? adConfig = await _getConfig();
    final adSwitchList = adConfig?.adSwitch;
    if (adSwitchList == null || adSwitchList.isEmpty) {
      return;
    }

    Map<AdScene, List<AdUnit>> adUnitForScene = {};
    List<AdUnit>? defaultList = [];
    for (var adSwitch in adSwitchList) {
      if (adSwitch.isOpen == false) {
        continue;
      }
      final adScene = adSwitch.adSceneCode;
      final adType = adSwitch.adType;
      final adUnitID = adSwitch.adUnitID;
      if (adScene == null || adType == null || adUnitID == null || adUnitID.isEmpty) {
        continue;
      }
      final adUnit = AdUnit(adUnitID: adUnitID, type: adType, name: adSwitch.adUnitName, providerIdentifier: 'Admob');

      List<AdUnit> list = adUnitForScene[adScene] ?? [];
      list.add(adUnit);
      adUnitForScene[adScene] = list;

      if (adType == AdType.interstitial) {
        // 插页广告作为兜底广告
        defaultList.add(adUnit);
      }
    }
    for (var entry in adUnitForScene.entries) {
      final adScene = entry.key;
      final adUnits = entry.value;
      if (adUnits.isEmpty) {
        continue;
      }
      final modelManager = AdModelManager(adScene, adUnits, defaultList);
      _modelManagerForScene[adScene] = modelManager;
    }
  }

  // 增加场景内容判断是否可以展示广告
  bool shouldShowAdWithScene(AdScene scene) {
    if (shouldShowAd.value == false){
      return false;
    }
    if (scene == AdScene.exitImmersionPage) {
      // 判断上次是否展示了超过60s
      return AdTriggerEventCollector.shared.checkExitDetailPageGapFilled();
    }
    return true;
  }

  // 预加载广告
  void prefetchAds() {
    _modelManagerForScene.forEach((scene, manager) {
      manager.prefetchAd();
    });
  }

  // 展示原生信息流广告
  // scene：场景
  // context: 广告上下文
  Future<Widget?> getNativeAdView(AdScene scene, {AdContext? context}) async {
    if (shouldShowAd.value == false) {
      return null;
    }
    final adModel = await getAdModel(scene, context: context);
    if (adModel == null) {
      return null;
    }
    return await getNativeAdViewForAdModel(scene, adModel);
  }

  // 展示原生信息流广告
  // scene：场景
  // adModel: 广告模型
  Future<Widget?> getNativeAdViewForAdModel(AdScene scene, AdModel adModel) async {
    if (shouldShowAd.value == false) {
      return null;
    }
    final nativeModel = adModel as AdAmNativeModel?;
    if (nativeModel == null) {
      return null;
    }
    if (adModel.statusModel.status != AdLoadStatus.success) {
      return null;
    }
    
    Widget? widget;
    if (scene == AdScene.homeFeed) {
      widget = nativeModel.smallWidget;
    } else if (scene == AdScene.listFeed) {
      widget = nativeModel.listFeedWidget;
    } else if (scene == AdScene.shortsNative) {
      widget = nativeModel.shortsFeedWidget;
    }
    if (widget == null) {
      return null;
    }
    return widget;
  }

  // 展示全屏广告(插页、激励)
  // scene：场景
  // forceShowAd：是否强制展示广告
  // context: 广告上下文
  // 返回值：关闭时是否领取了奖励
  Future<bool> showFullScreenAd(AdScene scene, {bool forceShowAd = false, AdContext? context}) async {
    if (forceShowAd == false && shouldShowAdWithScene(scene) == false) {
      if (scene != AdScene.exitImmersionPage) {
        SmartDialog.showToast(AppTrans.adWaitingTry());
      }
      return false;
    }
    AdTrackEvent.watchAdClick(scene, param: context?.eventParam);
    
    if (scene != AdScene.exitImmersionPage) {
      bool isNetworkAvailable = await AppDeviceService().isNetworkAvailable();
      if (!isNetworkAvailable) {
        AdCutomToast.showNoNetWorkToast();
        return false;
      }

      SmartDialog.showLoading(msg: AppTrans.adLoading());
    }

    final adModel = await getAdModel(scene, context: context);
    if (adModel == null) {
      if (scene != AdScene.exitImmersionPage) {
        SmartDialog.dismiss();
        SmartDialog.showToast(AppTrans.adWaitingTry());
      }
      return false;
    }
    
    if (adModel.statusModel.status != AdLoadStatus.success) {
      if (scene != AdScene.exitImmersionPage) {
        SmartDialog.dismiss();
        SmartDialog.showToast(AppTrans.adWaitingTry());
      }
      return false;
    }

    if (scene != AdScene.exitImmersionPage) {
      SmartDialog.dismiss();
    }
    PresentableAdModel presentAdModel = adModel as PresentableAdModel;
    if (scene != AdScene.exitImmersionPage &&
        scene != AdScene.checkInDouble &&
        scene != AdScene.dailyReward &&
        scene != AdScene.adsUnlock &&
        scene != AdScene.continuousAdPop &&
        scene != AdScene.continuousAdRetentionPop) {
      return false;
    }

    Completer<bool> completer = Completer();

    presentAdModel.present("", (model, success) {
      final fullModel = model as AMFullScreenAdModel?;
      final fullAdInfo = fullModel?.adInfo;
      if (fullAdInfo != null) {
        AdTrackEvent.adImpressions(model, scene, param: context?.eventParam);
        AdTrackEvent.adImpressionsDuration(model, scene, success, param: context?.eventParam);
      }
      // 退出沉浸页的广告展示后需要更新时间
      if (scene == AdScene.exitImmersionPage) {
        AdTriggerEventCollector.shared.updateExitLastInteruptADPrsentDate();
      }

      completer.complete(success);
    });
    return completer.future;
  }

  Future<AdModel?> getAdModel(AdScene scene, {AdContext? context}) async {
    final modelManager = _modelManagerForScene[scene];
    if (modelManager == null) {
      return null;
    }
    final adModel = await modelManager.loadAdModel();
    _listenAdModel(adModel, scene, context: context);
    return adModel;
  }

  AdModel? getAdModelFromCache(AdScene scene, {AdContext? context}) {
    final modelManager = _modelManagerForScene[scene];
    if (modelManager == null) {
      return null;
    }
    final adModel = modelManager.getAdModelFromCache();
    _listenAdModel(adModel, scene, context: context);
    return adModel;
  }

  void _listenAdModel(AdModel? adModel, AdScene scene, {AdContext? context}) {
    if (adModel == null) {
      return;
    }
    if (adModel.statusModel.status != AdLoadStatus.success) {
      return;
    }
    adModel.didImpression = (model, success) {
      AdTrackEvent.adRealImpressions(model, scene, param: context?.eventParam);
    };
    adModel.onClick = (model) {
      AdTrackEvent.adClicks(model, scene);
    };
    adModel.onPaidValueUpdate = (model) {
      AdTrackEvent.adRevenue(model, scene, param: context?.eventParam);
    };
  }

  Future<AdConfig?> _getConfig() async {
    AdConfig? adConfig = await _getConfigFromCache();
    if (adConfig == null) {
      return await _getConfigFromNetwork();
    }
    unawaited(_getConfigFromNetwork());
    return adConfig;
  }

  Future<AdConfig?> _getConfigFromNetwork() async {
    final result = await AdApi.getConfig();
    if (result.isSuccess == false) {
      return null;
    }
    AdConfig? adConfig;
    try {
      adConfig = AdConfig.fromJson(jsonDecode(result.data));
    } catch (e) {
      return null;
    }
    await _saveConfigToCache(result.data);
    return adConfig;
  }

  Future<AdConfig?> _getConfigFromCache() async {
    final path = await _getConfigFilePath();
    if (path == null) {
      return null;
    }
    final jsonString = await FileStorage.read(path);
    if (jsonString == null || jsonString.isEmpty) {
      return null;
    }
    try {
      final config = AdConfig.fromJson(jsonDecode(jsonString));
      return config;
    } catch (e) {
      return null;
    }
  }

  Future<void> _saveConfigToCache(String json) async {
    final path = await _getConfigFilePath();
    if (path == null) {
      return;
    }
    await FileStorage.write(json, path);
  }

  Future<String?> _getConfigFilePath() async {
    String? directoryPath = await FileStorage.getApplicationSupportDirectoryPath();
    if (directoryPath == null) {
      return null;
    }
    String path = '$directoryPath/ad/ad_config.json';
    FFLog.debug('广告配置保存路径: $path', tag: _tag);
    return path;
  }

  // 更新广告展示状态
  void _updateShouldShowAd() {
    final UserService userService = Get.find<UserService>();
    final userInfo = userService.userInfo.value;
    if (userInfo == null) {
      shouldShowAd.value = true;
      return;
    }
    if (userInfo.isSubscription != null && userInfo.isSubscription == true) {
      shouldShowAd.value = false;
      return;
    }
    final coins = userInfo.coins;
    if (coins != null && coins > 100) {
      shouldShowAd.value = false;
      return;
    }
    shouldShowAd.value = true;
  }
}

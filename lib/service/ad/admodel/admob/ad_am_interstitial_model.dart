import 'dart:async';

import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/ad/admodel/am_admob_model.dart';

class AdAmInterstitialModel extends AMFullScreenAdModel {
  AdAmInterstitialModel({required super.adUnit});  

  @override
  Future<(Ad?, String?)> performRealAdLoad() {
    Completer<(Ad?, String?)> completer = Completer();
     InterstitialAd.load(
      adUnitId: adUnit.adUnitID,
      request: super.getAdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          FFLog.debug("InterstitialAd 广告加载成功${ad.responseInfo?.responseExtras}, ==${ad.responseInfo?.loadedAdapterResponseInfo}");
          ad.setImmersiveMode(true);
          ad.fullScreenContentCallback = FullScreenContentCallback(
            onAdShowedFullScreenContent: (ad) {
              didImpression?.call(this, true);
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              FFLog.error("InterstitialAd 广告展示失败 === $error.message");

              onClose?.call(this, false);
              didImpression?.call(this, false);
              (ad as Ad).dispose();
            },
            onAdDismissedFullScreenContent: (ad) {
              onClose?.call(this, isSuccess);
              (ad as Ad).dispose();
            },
            onAdClicked: (ad) {
              onClick?.call(this);
            },       
          );
          ad.onPaidEvent = (adValue, valueMicros, precision, currencyCode) {
            // adValue.currencyCode;
            // adValue.valueMicros;
            // adValue.precision;
            PaidEventHandler(adValue, valueMicros, precision, currencyCode);
          };
           updateNetworkName(ad);
           completer.complete((ad, null));       
          },
        onAdFailedToLoad: (error) {
          FFLog.error("InterstitialAd 广告加载失败 === $error.message");

          completer.complete((null, error.message));
        },
      ),
    );
    return completer.future;
  }

  @override
  void performRealAdPresent() {

    if (adInfo is InterstitialAd) {
      beginTime = DateTime.now();
      final interstitialAd = adInfo as InterstitialAd;
      interstitialAd.show();
    } 

  }
  
  
  
}
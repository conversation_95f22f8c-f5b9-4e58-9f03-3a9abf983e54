import 'dart:async';

import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:google_mobile_ads/src/ad_containers.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/ad/admodel/am_admob_model.dart';

class AdAmRewardModel extends AMFullScreenAdModel {
  
  AdAmRewardModel({required super.adUnit});  
  double? _beginTime;

  @override
  Future<(Ad?, String?)> performRealAdLoad() {
    isSuccess = false;
    Completer<(Ad?, String?)> completer = Completer();
     RewardedAd.load(
      adUnitId: adUnit.adUnitID,
      request: super.getAdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
        FFLog.debug("reward 广告加载成功${ad.responseInfo?.responseExtras}, ==${ad.responseInfo?.loadedAdapterResponseInfo}");

          ad.setImmersiveMode(true);
          ad.fullScreenContentCallback = FullScreenContentCallback(
            onAdShowedFullScreenContent: (ad) { 
              didImpression?.call(this, true);
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              FFLog.error("RewardedAd present error: ${error.message}");
              isSuccess = false;
              onClose?.call(this, false);
              didImpression?.call(this, false);
              (ad as Ad).dispose();
            },
            onAdDismissedFullScreenContent: (ad) {
              onClose?.call(this, isSuccess);
              (ad as Ad).dispose();
            },
            onAdClicked: (ad) {
              print("reward 点击了广告ad === $ad");
              onClick?.call(this);
            },       
          );

          ad.onPaidEvent = (adValue, valueMicros, precision, currencyCode) {
            PaidEventHandler(adValue, valueMicros, precision, currencyCode);
          };
           updateNetworkName(ad);
           completer.complete((ad, null));       
        },
        onAdFailedToLoad: (error) {
           completer.complete((null, error.message));     
        },
      ),
    );
    return completer.future;
  }

  @override
  void performRealAdPresent() {

    if (adInfo is RewardedAd) {
      final rewardedAd = adInfo as RewardedAd;
      beginTime = DateTime.now();
      rewardedAd.show(onUserEarnedReward: (_, __) {
        isSuccess = true;
      });
    } 
    
  }

}
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/admodel/admob/native_ad_view/admob_native_view.dart';
import 'package:playlet/service/ad/admodel/am_admob_model.dart';

class AdAmNativeModel extends AdMobModel {
  late final bool useMediumTemplete;
  late final NativeAd? _nativeAd;
  AdAmNativeModel({required super.adUnit});
  bool _isDisposed = false;

  // 添加安全的dispose方法
  void safeDispose() {
    if (_isDisposed) return;
    _isDisposed = true;
    
    // 延迟一小段时间再dispose，确保不会影响正在使用的广告
    Future.delayed(Duration(milliseconds: 200), () {
      if (_nativeAd != null) {
        _nativeAd.dispose();
      }
    });
  }

  @override
  Future<void> loadAd({bool? userMediunTemple}) async {
    useMediumTemplete = userMediunTemple ?? false;
    Completer completer = Completer();
    statusModel.status = AdLoadStatus.loading;
    final NativeAdOptions nativeAdOptions = NativeAdOptions(
      adChoicesPlacement: (userMediunTemple == true) ? AdChoicesPlacement.bottomRightCorner: AdChoicesPlacement.topRightCorner// 可选值见下方
    );
    _nativeAd = NativeAd(
      factoryId: "CustomTemplateView",
        adUnitId: adUnit.adUnitID,
        listener: NativeAdListener(
          onAdLoaded: (ad) {
            FFLog.debug("native 广告加载成功, ad: $ad");
            updateNetworkName(ad);
            statusModel.successDate = DateTime.now();
            statusModel.status = AdLoadStatus.success;
            completer.complete(statusModel.status);
          },
          onAdFailedToLoad: (ad, error) {
            FFLog.error("native 广告加载失败, error: $error.message");
            statusModel.status = AdLoadStatus.failed;
            statusModel.error = error;
            completer.complete(statusModel.status);
            ad.dispose();
          },
          // Called when a click is recorded for a NativeAd.
          onAdClicked: (ad) {
            onClick?.call(this);
          },
          // Called when an impression occurs on the ad.
          onAdImpression: (ad) {
            didImpression?.call(this, true);
          },
          // Called when an ad removes an overlay that covers the screen.
          onAdClosed: (ad) {},
          // Called when an ad opens an overlay that covers the screen.
          onAdOpened: (ad) {},
          // For iOS only. Called before dismissing a full screen view
          onAdWillDismissScreen: (ad) {
            // Called before dismissing a full screen view.
          },
          // Called when an ad receives revenue value.
          onPaidEvent: (ad, valueMicros, precision, currencyCode) {
            // 回调收益的内容
            PaidEventHandler(ad, valueMicros, precision, currencyCode);
          },
        ),
        nativeAdOptions: nativeAdOptions,
        request: super.getAdRequest(),
        customOptions: {"isUselarge": useMediumTemplete},

      // nativeTemplateStyle: NativeTemplateStyle(
      //   templateType: useMediumTemplete ? TemplateType.medium : TemplateType.small,
      //   mainBackgroundColor: const Color(0xfffffbed),
      //   callToActionTextStyle: NativeTemplateTextStyle(
      //     textColor: Colors.white,
      //     style: NativeTemplateFontStyle.monospace,
      //     size: 16.0,
      //   ),
      //   primaryTextStyle: NativeTemplateTextStyle(
      //     textColor: Colors.black,
      //     style: NativeTemplateFontStyle.bold,
      //     size: 16.0,
      //   ),
      //   secondaryTextStyle: NativeTemplateTextStyle(
      //     textColor: Colors.black,
      //     style: NativeTemplateFontStyle.italic,
      //     size: 16.0,
      //   ),
      //   tertiaryTextStyle: NativeTemplateTextStyle(
      //     textColor: Colors.black,
      //     style: NativeTemplateFontStyle.normal,
      //     size: 16.0,
      //   ),
      // ),
                )
      ..load();
    return completer.future;
  }
}

extension AdAmNativeModelExtension on AdAmNativeModel {
  Widget? get smallWidget {
    if (_nativeAd == null) return null;
    return AdmobNativeView.getSmallNativeViewWith(_nativeAd);
  }

  Widget? get largeWidget {
    if (_nativeAd == null) return null;
    return AdmobNativeView.getLargeNativeViewWith(_nativeAd);
  }

  Widget? get listFeedWidget {
    if (_nativeAd == null) return null;
    return AdmobNativeView.getListFeedNativeViewWith(_nativeAd);
  }

  Widget? get shortsFeedWidget {
    if (_nativeAd == null) return null;
    return AdmobNativeView.getShortsFeedNativeViewWith(_nativeAd);
  }
}

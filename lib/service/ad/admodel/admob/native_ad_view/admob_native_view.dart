import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:get/utils.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdmobNativeView {
  static Widget getSmallNativeViewWith(NativeAd nativeAd) {
    return NativeAdView(
      nativeAd: nativeAd,
      width: 320.sp,
      height: 120.sp,
      isfullNative: false,
    );
  }

  static Widget getLargeNativeViewWith(NativeAd nativeAd) {
    return NativeAdView(
      nativeAd: nativeAd,
      width: 0,
      height: 0,
      isfullNative: true,
    );
  }

  static Widget getListFeedNativeViewWith(NativeAd nativeAd) {

    return NativeAdView(
      nativeAd: nativeAd,
      width: 320.sp,
      height: 120.sp,
      isfullNative: false,
    );
  }

  static Widget getShortsFeedNativeViewWith(NativeAd nativeAd) {
    return NativeAdView(
      nativeAd: nativeAd,
      width: 0,
      height: 0,
      isfullNative: true,
    );
  }

}

class NativeAdView extends StatefulWidget {

  final NativeAd nativeAd;
  final double width;
  final double height;
  final bool isfullNative;

  NativeAdView({
    required this.nativeAd,
    required this.width,
    required this.height,
    required this.isfullNative,
  });

  @override
  _NativeAdViewState createState() => _NativeAdViewState();
}

class _NativeAdViewState extends State<NativeAdView> {

  @override
  Widget build(BuildContext context) {
    if(widget.isfullNative) {
      return Container(
        width: Get.width,
        height: Get.height,
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: 320.sp,
              minHeight: 320.sp,
              maxWidth: Get.width,
              maxHeight: Get.height,
            ),
            child: AdWidget(ad: widget.nativeAd),
          ),
        ),
      );
    }
    // 全屏广告布局保持不变...
    double height = Platform.isAndroid ? 108 : 120;
    return Container(
      width: Get.width,
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minWidth: Get.width - 32.sp,
            minHeight: height,
            maxWidth: Get.width - 32.sp,
            maxHeight: height,
          ),
          child: AdWidget(ad: widget.nativeAd),
        ),
      ),
    );
  }

  @override
  void dispose() {
    // if (!widget.isfullNative) {
    //   widget.nativeAd.dispose();
    // }
    super.dispose();
  }
}
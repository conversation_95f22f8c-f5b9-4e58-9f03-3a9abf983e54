import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// 广告付费值模型
class AdPaidValue {
  final double? price;
  final String? currency;
  final String? precisionType;

  AdPaidValue({
    this.price,
    this.currency,
    this.precisionType,
  });
}

// 广告单元模型
class AdUnit {
  final String adUnitID; // 广告位id
  final AdType type;
  final String? name;
  final String providerIdentifier; // 广告商

  AdUnit({
    required this.adUnitID,
    required this.type,
    this.name,
    required this.providerIdentifier,
  });

  static dummyAdUnit(AdType interstitial, String identifier) {}

}

enum AdScene {
  homeFeed('Home_Feed'),
  listFeed('List_Feed'),
  shortsNative('Shorts_Native'),
  exitImmersionPage('exit_immersion_page'),
  checkInDouble('check_in_double'),
  dailyReward('daily_reward'),
  adsUnlock('ads_unlock'),
  continuousAdPop('continuous_ad_pop'),
  continuousAdRetentionPop('continuous_ad_retention_pop');
  
  final String value;
  const AdScene(this.value);

  static AdScene? fromString(String? value) {
    return AdScene.values.firstWhereOrNull(
      (scene) => scene.value == value,
    );
  }
}

// 广告类型
enum AdType {
  native('NATIVE'),
  interstitial('INTERSTITIAL'),
  reward('REWARD');

  final String value;
  const AdType(this.value);

  static AdType? fromString(String? value) {
    if (value == null) {
      return null;
    }
    return AdType.values.firstWhereOrNull(
      (type) => type.value == value,
    );
  }
}

// 广告加载状态
enum AdLoadStatus {
  idle,
  loading,
  success,
  failed,
}

// 广告状态观察者类型定义
typedef AdStatusObserver = void Function(AdLoadStatus status);

// 广告加载状态模型
class AdLoadStatusModel {
  AdLoadStatus status;
  AdStatusObserver? observer;
  DateTime? successDate; // 加载成功时间
  dynamic error; // 加载失败信息

  AdLoadStatusModel({
    this.status = AdLoadStatus.idle,
    this.observer,
    this.successDate,
    this.error,
  });
}

// 广告模型基类
abstract class AdModel {
  AdUnit get adUnit;
  AdLoadStatusModel statusModel = AdLoadStatusModel();
  
  AdPaidValue? get paidValue;
  // 用于统计时长,开始播放时间
  DateTime? beginTime;
  /// 当paidValue更新时，调用此函数，用于更新广告的价格，例如在adn
  void Function(AdModel)? onPaidValueUpdate;
  
  String? get adNetworkName;
  
  /// 是否要埋点ad_real_impressions
  bool shouldCollectAdRealImpressions;
  
  void Function(AdModel)? onClick;
  void Function(AdModel, bool)? onClose;
  void Function(AdModel, bool)? didImpression;
  
  VoidCallback? get eventAction;

  AdModel({
    this.shouldCollectAdRealImpressions = false,
  });

  Future<void> loadAd({bool? userMediunTemple});


  double getAdPlayTime(DateTime? beginTime) {
    beginTime ??= DateTime.now();
    final endTime = DateTime.now();
    var duration = endTime.difference(beginTime).inSeconds;
    if (duration > 0) {
      duration += 1; // 不足1秒按1秒算
    } else {
      duration = 1;
    }
    return duration.toDouble();
  }
}

// 可展示的广告模型
abstract class PresentableAdModel extends AdModel {
  PresentableAdModel();

  void present(String scene, void Function(AdModel, bool)? onClose);
}

// AdModel 扩展
extension AdModelExtension on AdModel {
  // 加载状态相关
  AdStatusObserver? get statusObserver => statusModel.observer;
  set statusObserver(AdStatusObserver? value) {
    statusModel.observer = value;
    // 立即通知当前状态
    _notifyCurrentStatus();
  }

  void _notifyCurrentStatus() {
    statusModel.observer?.call(statusModel.status);
  }

  // 状态判断
  bool get isSuccess => statusModel.status == AdLoadStatus.success;
  
  // 获取可用广告
  AdModel? getAvailableAd(Duration duration) {
    if (statusModel.status == AdLoadStatus.success) {
      final successDate = statusModel.successDate;
      if (successDate != null) {
        final difference = DateTime.now().difference(successDate);
        if (difference <= duration) {
          return this;
        }
      }
    }
    return null;
  }
}

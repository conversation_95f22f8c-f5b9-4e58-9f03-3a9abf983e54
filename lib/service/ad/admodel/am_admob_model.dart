import 'dart:async';
import 'dart:ui';

import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';

typedef OnAdImpression = void Function(AdModel, bool);
typedef OnAdClick = void Function(AdModel);
typedef OnAdClose = void Function(AdModel, bool);
typedef OnPaidValueUpdate = void Function(AdModel);

class AdMobModel extends AdModel {
  // 回调类型定义

  // 广告回调
  @override
  OnAdImpression? didImpression; // 已展示，可以上报impression埋点
  @override
  OnAdClick? onClick;
  @override
  OnAdClose? onClose;
  OnPaidValueUpdate? onPaidValueUpdate;

  // 广告属性
  @override
  final AdUnit adUnit;
  @override
  AdPaidValue? paidValue;
  @override
  String? adNetworkName;
  
  bool shouldCollectAdRealImpressions = true;
  String? adScene;

  // 付费事件处理
  void PaidEventHandler(Ad ad, double valueMicros, PrecisionType precision, String currencyCode)  {
    final paidValue = AdPaidValue(
      currency: currencyCode,
      price: valueMicros / 1000000, // 需要根据实际情况获取
      precisionType: precision.name,
    );
    this.paidValue = paidValue;
    
    onPaidValueUpdate?.call(this);
  }

  AdMobModel({required this.adUnit});

  @override
  Future<void> loadAd({bool? userMediunTemple}) async {
    return ; // 默认返回null，子类需要重写此方法
  }

  bool canLoadAd() {
    if (statusModel.status != AdLoadStatus.idle) {
      FFLog.debug("不要重复加载广告");
      return false;
    }
    return true;
  }

  void updateNetworkName(Ad ad) {
    adNetworkName = ad.responseInfo?.mediationAdapterClassName;
        FFLog.debug("adNetworkName ==: $adNetworkName");
  }

  @override
  void updateAdScene(String scene) {
    adScene = scene;
  }

  AdRequest getAdRequest() {
    return AdRequest();
  }

  @override
  // todo: implement eventAction
  VoidCallback? get eventAction => throw UnimplementedError();
}

abstract class AMFullScreenAdModel extends AdMobModel implements PresentableAdModel {
  Ad? adInfo;
  bool isSuccess = true; // 默认是true，奖励等特殊类型需要自己一开始设置为false

  AMFullScreenAdModel({required super.adUnit});

  @override
  Future<void> loadAd({bool? userMediunTemple}) async {
    if (!canLoadAd()) {
      return ;
    }
    Completer completion = Completer();
    statusModel.status = AdLoadStatus.loading;
    try {
      final (ad, error) = await performRealAdLoad();
        if (error == null) {
          adInfo = ad;
          statusModel.status = AdLoadStatus.success;
          statusModel.successDate = DateTime.now();
          completion.complete();
        } else {
          statusModel.status = AdLoadStatus.failed;
          statusModel.error = error;
          completion.complete();
        }
      } catch (error) {
      statusModel.status = AdLoadStatus.failed;
      statusModel.error = error;
      completion.complete();
    }
    return completion.future;
  }

  // 实际加载广告的方法，子类必须实现
  Future<(Ad?, String? error)> performRealAdLoad();

  @override
  void present(String scene, void Function(AdModel, bool)? onClose) {
    this.onClose = onClose;
    if (adInfo == null) {
      this.onClose?.call(this, false);
      return;
    }

    performRealAdPresent();
  }

  // 实际展示广告的方法，子类必须实现
  void performRealAdPresent();

  @override
  VoidCallback? get eventAction => null;
}



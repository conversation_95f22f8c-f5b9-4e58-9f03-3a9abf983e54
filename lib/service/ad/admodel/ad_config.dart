
// BEGIN: Generated code
import 'package:playlet/service/ad/admodel/ad_model.dart';

class AdConfig {
  List<AdSwitch>? adSwitch;

  AdConfig({
    this.adSwitch,
  });

  factory AdConfig.fromJson(Map<String, dynamic> json) => AdConfig (
    adSwitch: (json['adSwitch'] as List).map((e) => AdSwitch.fromJson(e)).toList(),
  );

  Map<String, dynamic> toJson() => {
    'adSwitch': adSwitch?.map((e) => e.toJson()).toList() ?? [],
  };
}

class AdSwitch {
  String? adUnitID;
  AdScene? adSceneCode;
  String? adUnitName;
  bool? isOpen;
  AdType? adType;
  int? type;

  AdSwitch({
    this.adUnitID,
    this.adUnitName,
    this.adSceneCode,
    this.isOpen,
    this.adType,
    this.type,
  });

  factory AdSwitch.fromJson(Map<String, dynamic> json) => AdSwitch(
    adUnitID: json['adUnitID'],
    adSceneCode: AdScene.fromString(json['adSceneCode']),
    adUnitName: json['adUnitName'],
    isOpen: json['isOpen'],
    adType: AdType.fromString(json['adFormat']),
    type: json['type'],
  );

  Map<String, dynamic> toJson() => {
    'adUnitID': adUnitID,
    'adSceneCode': adSceneCode?.value,
    'adUnitName': adUnitName,
    'isOpen': isOpen,
    'adFormat': adType?.value,
    'type': type,
  };
}


// END: Generated code

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:playlet/components/loading/ffloading.dart';
import 'package:playlet/i18n/trans.dart';

class AdCutomToast {
  static Future<void> showNoNetWorkToast() async {
    SmartDialog.showLoading(
      msg: AppTrans.adLoading(), 
      displayTime: const Duration(seconds: 2), 
      alignment: Alignment.center, 
      builder: (_) => _buildToastWidget(),
        maskColor: Colors.transparent,  // 添加透明背景
        backDismiss: false,  // 允许点击背景关闭
    );

    // 这边延迟2.1s
    await Future.delayed(const Duration(milliseconds: 2100)); 
    SmartDialog.showToast(
      AppTrans.adWaitingTry(),
      displayTime: const Duration(seconds: 2),
      alignment: Alignment.bottomCenter,
      builder: (context) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 5.sp),
          margin: EdgeInsets.only(bottom: 34.sp),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.8),
            borderRadius: BorderRadius.circular(4.sp),
          ),
          child: Text(
            AppTrans.adWaitingTry(),
            style: TextStyle(
              color: Colors.white,
              fontSize: 14.sp,
            ),
          ),
        );
      },
    );
  }
}

Widget _buildToastWidget() {
  return Container(
    constraints: BoxConstraints(
      maxWidth: 150.sp,
      maxHeight: 150.sp,
    ),
    decoration: BoxDecoration(
      color: Colors.black,
      borderRadius: BorderRadius.circular(8),
    ),
    padding: EdgeInsets.all(16.w),
    child: const FFLoadingWidget(),
  );
}
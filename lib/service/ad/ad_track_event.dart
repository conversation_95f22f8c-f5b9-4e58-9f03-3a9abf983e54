import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/ad/admodel/admob/native_ad_view/admob_native_view.dart';
import 'package:playlet/service/ad/admodel/am_admob_model.dart';
import 'package:playlet/utils/track_event.dart';
import 'package:playlet/common/event/track_event.dart';

class AdTrackEvent {
  
  /// 请求广告埋点
  static void adRequest(AdUnit adUnit) {
    useTrackEvent(TrackEvent.ad_request, extra: {
      TrackEvent.type: adUnit.type.toString().toLowerCase(),
      EventKey.ad_unit_name: adUnit.name ?? "",
      EventKey.ad_platform: adUnit.providerIdentifier,
      EventKey.ad_unit_id: adUnit.adUnitID,
    });
  }

  /// 广告缓存成功埋点
  static void adFill(AdModel adModel) {
    useTrackEvent(TrackEvent.ad_fill, extra: {
      TrackEvent.type: adModel.adUnit.type.toString().toLowerCase(),
      EventKey.ad_unit_name: adModel.adUnit.name ?? "",
      EventKey.ad_platform: adModel.adUnit.providerIdentifier,
      EventKey.ad_mediation_name: adModel.adNetworkName ?? "",
      EventKey.ad_unit_id: adModel.adUnit.adUnitID,
    });
  }

  /// 广告展示成功埋点
  static void adRealImpressions(AdModel adModel, AdScene scene, {Map<String, String>? param}) {
    Map<String, String> extra = {
      TrackEvent.type: adModel.adUnit.type.toString().toLowerCase(),
      EventKey.ad_placement: scene.value.toString(),
      EventKey.ad_unit_name: adModel.adUnit.name ?? "",
      EventKey.ad_unit_id: adModel.adUnit.adUnitID,
      EventKey.ad_platform: adModel.adUnit.providerIdentifier,
      EventKey.ad_mediation_name: adModel.adNetworkName ?? "",
    };
    if (param != null) {
      extra.addAll(param);
    }
    useTrackEvent(TrackEvent.ad_real_impressions, extra: extra);
  }

  /// 广告展示后关闭埋点
  static void adImpressions(AdModel adModel, AdScene scene, {Map<String, String>? param}) {
    Map<String, String> extra = {
      TrackEvent.type: adModel.adUnit.type.toString().toLowerCase(),
      EventKey.ad_placement: scene.value.toString(),
      EventKey.ecpm: (adModel.paidValue?.price ?? 0).toString(),
      EventKey.ad_unit_name: adModel.adUnit.name ?? "",
      EventKey.ad_unit_id: adModel.adUnit.adUnitID,
      EventKey.ad_platform: adModel.adUnit.providerIdentifier,
      EventKey.ad_mediation_name: adModel.adNetworkName ?? "",
      EventKey.impressions_duration: adModel.getAdPlayTime(adModel.beginTime).toString(),
    };
    if (param != null) {
      extra.addAll(param);
    }
    useTrackEvent(TrackEvent.ad_impressions, extra: extra);
  }
  
  /// 广告展示时长埋点
  static void adImpressionsDuration(AdModel adModel, AdScene scene, bool success, {Map<String, String>? param}) {
    Map<String, String> extra = {
      TrackEvent.type: adModel.adUnit.type.toString().toLowerCase(),
      EventKey.ad_placement: scene.value.toString(),
      EventKey.ad_unit_name: adModel.adUnit.name ?? "",
      EventKey.ad_unit_id: adModel.adUnit.adUnitID,
      EventKey.ad_platform: adModel.adUnit.providerIdentifier,
      EventKey.ad_mediation_name: adModel.adNetworkName ?? "",
      EventKey.impressions_duration: adModel.getAdPlayTime(adModel.beginTime).toString(),
      TrackEvent.action: success ? 'impressions_success' : 'impressions_close_midway',
    };
    if (param != null) {
      extra.addAll(param);
    }
    useTrackEvent(TrackEvent.ad_impressions_duration, extra: extra);
  }

  /// 广告展示后有收益回传
  static void adRevenue(AdModel adModel, AdScene scene, {Map<String, String>? param}) {
    Map<String, String> extra = {
      TrackEvent.type: adModel.adUnit.type.toString().toLowerCase(),
      EventKey.ad_placement: scene.value.toString(),
      EventKey.ecpm: (adModel.paidValue?.price ?? 0).toString(),
      EventKey.ad_unit_name: adModel.adUnit.name ?? "",
      EventKey.ad_unit_id: adModel.adUnit.adUnitID,
      EventKey.ad_platform: adModel.adUnit.providerIdentifier,
      EventKey.ad_mediation_name: adModel.adNetworkName ?? "",
    };
    if (param != null) {
      extra.addAll(param);
    }
    useTrackEvent(TrackEvent.ad_revenue, extra: extra);
  }

  /// 点击广告内容
  static void adClicks(AdModel adModel, AdScene scene) {
    Map<String, String> extra = {
      TrackEvent.type: adModel.adUnit.type.toString().toLowerCase(),
      EventKey.ad_placement: scene.value.toString(),
      EventKey.ad_unit_name: adModel.adUnit.name ?? "",
      EventKey.ad_unit_id: adModel.adUnit.adUnitID,
      EventKey.ad_platform: adModel.adUnit.providerIdentifier,
      EventKey.ad_mediation_name: adModel.adNetworkName ?? "",
    };
    useTrackEvent(TrackEvent.ad_clicks, extra: extra);
  }

  /// 触发广告场景的页面展示时打点(没有广告展示但是到达该场景也要上报)
  static void adPlacementShow(AdType type, AdScene scene, {Map<String, String>? param}) {
    Map<String, String> extra = {
      TrackEvent.type: type.value.toString().toLowerCase(),
      EventKey.ad_placement: scene.value.toString(),
    };
    if (param != null) {
      extra.addAll(param);
    }
    useTrackEvent(TrackEvent.ad_placement_show, extra: extra);
  }

  /// 用户点击观看广告按钮时打点
  static void watchAdClick(AdScene scene, {Map<String, String>? param}) {
    Map<String, String> extra = {
      EventKey.ad_active: scene.value.toString(),
    };
    if (param != null) {
      extra.addAll(param);
    }
    useTrackEvent(TrackEvent.watch_ad_click, extra: extra);
  }
}
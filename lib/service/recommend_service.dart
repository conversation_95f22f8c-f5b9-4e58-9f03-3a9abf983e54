import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/api/recommend.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';

class RecommendService extends GetxService {
  final SafeStorage storage = SafeStorage();
  final String recommendKey = "recommend_countdown_end_time"; // 存储倒计时结束时间戳
  final String recommendPaySucKey = "recommend_pay_suc"; // 存储支付成功
  final String recommendDeepleepPlayletKey = "recommend_deepleep_playlet";

  RxBool isShow = false.obs;

  RxBool isMoveShow = false.obs;

  Worker? _worker;

  RxInt endTime = 0.obs;
  RxBool isNewUser = false.obs;

  CountdownTimer? _countdownTimer;

  Rx<TimeRemaining> timeRemaining = TimeRemaining(
    hours: '00',
    minutes: '00',
    seconds: '00',
  ).obs;

  @override
  void onInit() {
    init();
    _worker = ever(isShow, (val) {
      if (isShow.value) {
        onTrackEvent();
      }
    });
    super.onInit();
  }

  Future<void> onGetNewUser() async {
    bool val = await ApiRecommend.checkNewUserRecommend();
    isNewUser.value = val;
    if (isNewUser.value == false) {
      await onGetTimeShow();
    }
  }

  @override
  void onClose() {
    _countdownTimer?.dispose();
    _worker?.dispose();
    super.onClose();
  }

  Future<void> init() async {
    await onGetNewUser();
    await getRemainingTime();
  }

  /// 用于处理账号切换
  /// 切换账号后,先判断是否进入过兜底剧，如果进入过则直接触发获取倒计时
  Future<void> onSwitchUser() async {
    FFLog.info("触发切换了用户", tag: "RecommendService");
    final isDeepleepPlaylet = storage.read<bool?>(recommendDeepleepPlayletKey);
    if (isDeepleepPlaylet != null && isDeepleepPlaylet == true) {
      FFLog.info("用户已经进入过兜底剧,直接获取时间", tag: "RecommendService");
      await onGetTimeShow();
    }
  }

  bool getPaySuccess() {
    final isPaySuccess = storage.read<bool?>(recommendPaySucKey);
    Get.log("RecommendService 是否发生了支付成功:$isPaySuccess");
    return isPaySuccess == true ? true : false;
  }

  //进入归因剧集
  Future<void> onSaveDeepleepPlaylet() async {
    await storage.write(recommendDeepleepPlayletKey, true);
  }

  //发生支付成功
  Future<void> onSavePaySuccess() async {
    await storage.write(recommendPaySucKey, true);
  }

  /// 获取当前倒计时剩余毫秒数，若不存在或已过期返回 null
  Future<void> getRemainingTime() async {
    if (endTime.value > 0) return;
    final isDeepleepPlaylet = storage.read<bool?>(recommendDeepleepPlayletKey);
    if (isDeepleepPlaylet != null && isDeepleepPlaylet == true) {
      await onGetTimeShow();
    }
  }

  Future<void> onGetTimeShow() async {
    final res = await ApiRecommend.getTimeData();
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    endTime.value = res?.endTime ?? 0;
    final endTims = endTime.value - currentTime;
    Get.log("RecommendService 当前时间 $currentTime 大于结束时间 $endTims");
    //当前时间大于结束时间，则倒计时结束
    if (endTims > 0) {
      _countdownTimer?.dispose();
      _countdownTimer = CountdownTimer(endTimeMillis: endTime.value);
      // 启动倒计时
      _countdownTimer?.start();
      // 监听倒计时更新
      _countdownTimer?.onTick.listen((remaining) {
        Get.log(
            'RecommendService 剩余时间: ${remaining.hours}时${remaining.minutes}分${remaining.seconds}秒');
        timeRemaining.value = remaining;
        timeRemaining.refresh();
      });
      // 监听倒计时结束
      _countdownTimer?.onFinished.listen((_) {
        Get.log("RecommendService 倒计时结束");
        isShow.value = false;
        //倒计时结束返回上一页
        if (Get.currentRoute == Routes.recommendPage) {
          Get.back();
        }
      });
      Future.delayed(const Duration(milliseconds: 600), () {
        Get.log("RecommendService 倒计时开始");
        isShow.value = true;
      });
    } else {
      isShow.value = false;
    }
  }

  bool getIsRecommendPage() {
    if (isNewUser.value) {
      return true;
    }
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final endTims = endTime.value - currentTime;
    if (endTims > 0) {
      return true;
    }
    return false;
  }

  double dx = 0;
  double dy = 0;
  Timer? _moveTimer;
  void onPointerDown(PointerDownEvent event) {
    dx = event.position.dx;
    dy = event.position.dy;
  }

  void onPointerMove(PointerMoveEvent event) {
    if (event.position.dx != dx || event.position.dy != dy) {
      isMoveShow.value = true;
    }
  }

  void onPointerUp(PointerUpEvent event) {
    if (isMoveShow.value) {
      _moveTimer?.cancel();
      // 启动一个新的定时器，在3秒后隐藏
      _moveTimer = Timer(const Duration(seconds: 3), () {
        isMoveShow.value = false;
      });
    }
  }

  void onTrackEvent() {
    useTrackEvent(TrackEvent.suspensionButtonShow);
  }
}

class CountdownTimer {
  // 截止时间（毫秒）
  final int endTimeMillis;
  Timer? _timer;
  final _tickController = StreamController<TimeRemaining>.broadcast();
  final _finishController = StreamController<void>.broadcast();

  // 获取时间更新的流
  Stream<TimeRemaining> get onTick => _tickController.stream;

  // 获取倒计时结束的流
  Stream<void> get onFinished => _finishController.stream;

  // 构造函数，接收截止时间的毫秒数
  CountdownTimer({required this.endTimeMillis});

  // 启动倒计时
  void start() {
    if (_timer != null) {
      _timer!.cancel();
    }

    // 立即发送一次当前状态
    _emitCurrentTime();

    // 创建定时器，每秒触发一次
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_getRemainingMillis() <= 0) {
        stop();
        _finishController.add(null);
        return;
      }

      _emitCurrentTime();
    });
  }

  // 停止倒计时
  void stop() {
    _timer?.cancel();
    _timer = null;
  }

  // 获取剩余毫秒数
  int _getRemainingMillis() {
    return endTimeMillis - DateTime.now().millisecondsSinceEpoch;
  }

  // 发送当前时间状态
  void _emitCurrentTime() {
    final remainingMillis = _getRemainingMillis();
    if (remainingMillis <= 0) {
      _tickController
          .add(TimeRemaining(hours: "00", minutes: "00", seconds: "00"));
      return;
    }

    final remaining = TimeRemaining.fromMilliseconds(remainingMillis);
    _tickController.add(remaining);
  }

  // 释放资源
  void dispose() {
    stop();
    _tickController.close();
    _finishController.close();
  }
}

// 剩余时间数据类
class TimeRemaining {
  final String hours;
  final String minutes;
  final String seconds;

  TimeRemaining({
    required this.hours,
    required this.minutes,
    required this.seconds,
  });

  // 从毫秒数创建TimeRemaining对象
  factory TimeRemaining.fromMilliseconds(int milliseconds) {
    final totalSeconds = milliseconds ~/ 1000;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;

    // 确保时分秒为两位数字，个位数前补0
    final hoursStr = hours.toString().padLeft(2, '0');
    final minutesStr = minutes.toString().padLeft(2, '0');
    final secondsStr = seconds.toString().padLeft(2, '0');

    return TimeRemaining(
      hours: hoursStr,
      minutes: minutesStr,
      seconds: secondsStr,
    );
  }
}

// 初始化App服务
import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/common/http/http.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/translation_service.dart';
import 'package:playlet/service/ad/ad_manager.dart';
import 'package:playlet/service/dialog_opportunity_service.dart';
import 'package:playlet/service/good_review_service.dart';
import 'package:playlet/service/ios_app_delegate_service.dart';
import 'package:playlet/service/live_activity/live_activity_service.dart';
import 'package:playlet/service/mmkv_service.dart';
import 'package:playlet/service/other_task/get_watch_ad_task_service.dart';
import 'package:playlet/service/recommend_service.dart';
import 'package:playlet/service/recently_watch_service.dart';
import 'package:playlet/service/resource_bit/resource_bit_manager.dart';
import 'package:playlet/service/notification/notification_service.dart';
import 'package:playlet/service/shorts_service.dart';
import 'package:playlet/utils/firebase_util.dart';
import 'package:playlet/utils/safe_storage.dart';

import 'analytics_service.dart';
import 'app_device_service.dart';
import 'app_service.dart';
import 'attribute_service.dart';
import 'fallback_drama_service.dart';
import 'fingerprint_service.dart';
import 'notificaiton_dialog_service.dart';
import 'payment/payment_service.dart';
import 'player_service.dart';
import 'user_service.dart';

// 日志标签常量
const String tagInitServices = 'initServices';

Future<void> initServices() async {
  // 记录初始化开始时间
  final startTime = DateTime.now().millisecondsSinceEpoch;

  FFLog.info('===== 开始初始化所有服务 =====', tag: tagInitServices);
  Get.log('starting services ...');

  try {
    // 初始化持久化存储
    try {
      await SafeStorage.init();
      FFLog.info('SafeStorage初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('SafeStorage初始化失败: $e', tag: tagInitServices);
    }

    // 初始化设备服务
    try {
      await AppDeviceService.instance.init();
      FFLog.info('设备服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('设备服务初始化失败: $e', tag: tagInitServices);
    }

    // 注册基础服务
    Get.lazyPut(() => IOSAppDelegateService());
    Get.lazyPut(() => AppService());
    Get.lazyPut(() => UserService());
    Get.lazyPut(() => ShortsService());
    Get.lazyPut(() => RecommendService());
    Get.lazyPut(() => RecentlyWatchService(), fenix: true);
    Get.lazyPut(() => AttributeService());
    Get.lazyPut(() => FallbackDramaService());

    // 初始化弹窗次数服务
    Get.lazyPut(() => DialogOpportunityService());

    // 初始化通知弹窗次数服务
    Get.lazyPut(() => NotificationLoginService());


    // 初始化指纹服务
    try {
      await Get.put<FingerprintService>(FingerprintService()).init();
      FFLog.info('指纹服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('指纹服务初始化失败: $e', tag: tagInitServices);
    }

    // 初始化播放器服务
    try {
      await Get.put<PlayerService>(PlayerService()).init();
      FFLog.info('播放器服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('播放器服务初始化失败: $e', tag: tagInitServices);
    }

    // 初始化翻译服务
    try {
      await Get.put<TranslationService>(TranslationService()).init();
      FFLog.info('翻译服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('翻译服务初始化失败: $e', tag: tagInitServices);
    }

    // 初始化分析服务
    try {
      Get.put<AnalyticsService>(AnalyticsService());
      FFLog.info('分析服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('分析服务初始化失败: $e', tag: tagInitServices);
    }

    // 初始化通知服务
    try {
      await Get.put<NotificationService>(NotificationService()).init();
      FFLog.info('通知服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('通知服务初始化失败: $e', tag: tagInitServices);
    }

    // 初始化HTTP服务
    try {
      HttpService().init();
      FFLog.info('HTTP服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('HTTP服务初始化失败: $e', tag: tagInitServices);
    }

    // 初始化广告服务
    try {
      await AdManager().init();
      FFLog.info('广告服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('广告服务初始化失败: $e', tag: tagInitServices);
    }

    // 初始化归因服务
    try {
      FFLog.info('归因服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('归因服务初始化失败: $e', tag: tagInitServices);
    }

    // 初始化支付服务
    try {
      Get.put<PaymentService>(PaymentService());
      FFLog.info('支付服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('支付服务初始化失败: $e', tag: tagInitServices);
    }

    // 初始化MMKV服务
    try {
      Get.put<MMKVService>(MMKVService());
      FFLog.info('MMKV服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('MMKV服务初始化失败: $e', tag: tagInitServices);
    }

    Get.put(GoodReviewService());

    // 初始化资源位服务
    try {
      Get.put<ResourceBitManager>(ResourceBitManager());
      FFLog.info('资源位服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('资源位服务初始化失败: $e', tag: tagInitServices);
    }
    
    // 初始化实时活动服务
    try {
      Get.put<LiveActivityService>(LiveActivityService());
      FFLog.info('实时活动服务初始化成功', tag: tagInitServices);
    } catch (e) {
      FFLog.error('实时活动服务初始化失败: $e', tag: tagInitServices);
    }
  } catch (e, stackTrace) {
    FFLog.error('服务初始化过程中发生崩溃: $e\n$stackTrace', tag: tagInitServices);
  } finally {
    // 计算总时间
    final endTime = DateTime.now().millisecondsSinceEpoch;
    final totalTime = endTime - startTime;

    FFLog.info('===== 所有服务初始化完成，总耗时: ${totalTime}ms =====',
        tag: tagInitServices);
    Get.log('All services started...');
  }
}

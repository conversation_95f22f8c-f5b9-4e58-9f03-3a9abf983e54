import 'dart:io';

import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:playlet/service/payment/payment_model.dart';
import 'package:playlet/service/payment/payment_service.dart';

import '../../common/event/event_key.dart';
import '../../common/event/event_value.dart';
import '../../common/event/track_event.dart';
import '../../common/log/ff_log.dart';
import '../../utils/track_event.dart';
import '../user_service.dart';

class PaymentEvent {
  static submitOrderCreate(
      {required Payment payment,
      required ProductDetails productDetails,
      required PurchaseDetails purchase,
      required bool isSubscriptionProduct}) {
    String orderNo = "";

    if (Platform.isIOS) {
      orderNo = (purchase as AppStorePurchaseDetails)
              .skPaymentTransaction
              .transactionIdentifier ??
          "";
    } else {
      orderNo = (purchase as GooglePlayPurchaseDetails)
              .billingClientPurchase.orderId;
    }

    useTrackEvent(TrackEvent.order_create, extra: {
      TrackEvent.scene: payment.strSource.toString(),
      TrackEvent.amount: productDetails.rawPrice.toString(),
      EventKey.currency: productDetails.currencyCode,
      TrackEvent.product_id: payment.skuProductId,
      TrackEvent.sku: payment.skuId,
      TrackEvent.reel_id: payment.shortPlayCode.toString(),
      TrackEvent.episode: payment.episode.toString(),
      TrackEvent.template_id: payment.skuModelConfigId.toString(),
      TrackEvent.platform: Platform.isIOS ? "Apple" : "Google play",
      TrackEvent.sort: isSubscriptionProduct ? "subscribe" : 'inapp',
      TrackEvent.payment_method: Platform.isIOS ? "Apple" : "Google play",
      TrackEvent.lock_begin: payment.lockBegin ?? "",
      EventKey.playDirection: payment.playDirection ?? "",
      EventKey.resourceBitId: payment.resourceBitId ?? "",
      TrackEvent.order_no: orderNo,
    });
  }

  static submitOrderCreateFail(Payment payment, String currencyCode, String errorCode,
      {required bool isSubscriptionProduct}) {
    // 查找指定对象

    useTrackEvent(TrackEvent.order_create_fail, extra: {
      TrackEvent.scene: payment.strSource.toString(),
      TrackEvent.link_tag: '',
      TrackEvent.type: errorCode,
      TrackEvent.platform: Platform.isIOS ? "Apple" : "Google play",
      TrackEvent.payment_method: Platform.isIOS ? "Apple" : "Google play",
      TrackEvent.sort: isSubscriptionProduct ? "subscribe" : 'inapp',
      TrackEvent.amount: payment.amount ?? "",
      // 传入本地化金额 与 货币代码 USD
      TrackEvent.currency: currencyCode,
      TrackEvent.sku: payment.skuId,
      TrackEvent.product_id: payment.skuProductId,
      TrackEvent.template_id: payment.skuModelConfigId.toString(),

      EventKey.playDirection: payment.playDirection ?? "",
      EventKey.resourceBitId: payment.resourceBitId ?? "",
    });
  }

  static submitOrderCreateCancel(
    Payment payment,
    bool isSubscriptionProduct,
  ) {
    final PaymentService paymentService = Get.find<PaymentService>();
    // 订单取消埋点
    useTrackEvent(TrackEvent.order_create_cancel, extra: {
      TrackEvent.scene: payment.strSource.toString(),
      TrackEvent.platform: Platform.isIOS ? 'Apple' : 'Google play',
      TrackEvent.payment_method: Platform.isIOS ? 'Apple' : 'Google play',
      TrackEvent.amount: payment.amount ?? "",
      // 传入本地化金额 与 货币代码 USD
      TrackEvent.currency: paymentService.currencyCode.value,
      TrackEvent.sku: payment.skuId,
      TrackEvent.product_id: payment.skuProductId,
      TrackEvent.template_id: payment.skuModelConfigId.toString(),
      TrackEvent.episode: payment.episode.toString(),
      TrackEvent.action: TrackEvent.other,

      EventKey.playDirection: payment.playDirection ?? "",
      EventKey.resourceBitId: payment.resourceBitId ?? "",
    });
  }

  /// 商店支付成功 入账接口调用成功
  static submitOrderPaySuccess(
    Payment payment,
    bool isSubscriptionProduct,
    PurchaseDetails purchase,
  ) {
    String orderNo = "";

    if (Platform.isIOS) {
      orderNo = (purchase as AppStorePurchaseDetails)
              .skPaymentTransaction
              .transactionIdentifier ??
          "";
    } else {
      orderNo = (purchase as GooglePlayPurchaseDetails)
              .billingClientPurchase.orderId;
    }

    FFLog.debug("submitOrderPaySuccess  $orderNo");

    var userInfo = Get.find<UserService>();
    final PaymentService paymentService = Get.find<PaymentService>();
    // 订单支付成功埋点
    useTrackEvent(TrackEvent.pay_success, extra: {
      TrackEvent.scene: payment.strSource.toString(),
      // 传入本地化金额 与 货币代码 USD
      TrackEvent.amount: payment.amount ?? "",
      TrackEvent.currency: paymentService.currencyCode.value,
      TrackEvent.reel_id: payment.shortPlayCode.toString(),
      TrackEvent.episode: payment.episode.toString(),
      TrackEvent.is_first:
          (userInfo.userInfo.value?.isRecharged == true) ? "0" : "1",
      TrackEvent.platform: Platform.isIOS ? 'Apple' : 'Google play',
      TrackEvent.sku: payment.skuId,
      TrackEvent.is_main: ((payment.skuType == 0) ? 0 : 1).toString(),
      TrackEvent.product_id: payment.skuProductId,
      TrackEvent.template_id: payment.skuModelConfigId.toString(),
      TrackEvent.sort: isSubscriptionProduct ? 'subscribe' : 'inapp',
      TrackEvent.order_no: orderNo,
      TrackEvent.payment_method: Platform.isIOS ? 'Apple' : 'Google play',
      TrackEvent.lock_begin: payment.lockBegin ?? "",

      EventKey.playDirection: payment.playDirection ?? "",
      EventKey.resourceBitId: payment.resourceBitId ?? "",
    });
  }

  /// 商店支付成功 但没有调用入账接口时上传
  static submitStoreOrderPaySuccess(
    Payment payment,
    bool isSubscriptionProduct,
    PurchaseDetails purchase,
  ) {
    String orderNo = "";

    if (Platform.isIOS) {
      orderNo = (purchase as AppStorePurchaseDetails)
              .skPaymentTransaction
              .transactionIdentifier ??
          "";
    } else {
      orderNo = (purchase as GooglePlayPurchaseDetails)
              .billingClientPurchase.orderId;
    }

    FFLog.debug("submitOrderPaySuccess  $orderNo");

    var userInfo = Get.find<UserService>();
    final PaymentService paymentService = Get.find<PaymentService>();
    // 查找指定对象
    // 订单支付成功埋点
    useTrackEvent(TrackEvent.pay_success, extra: {
      TrackEvent.scene: payment.strSource.toString(),
      // 传入本地化金额 与 货币代码 USD
      TrackEvent.amount: payment.amount ?? "",
      TrackEvent.currency: paymentService.currencyCode.value,
      TrackEvent.reel_id: payment.shortPlayCode.toString(),
      TrackEvent.episode: payment.episode.toString(),
      TrackEvent.is_first:
          (userInfo.userInfo.value?.isRecharged == true) ? "0" : "1",
      TrackEvent.platform: Platform.isIOS ? 'Apple' : 'Google play',
      TrackEvent.sku: payment.skuId,
      TrackEvent.is_main: ((payment.skuType == 0) ? 0 : 1).toString(),
      TrackEvent.product_id: payment.skuProductId,
      TrackEvent.template_id: payment.skuModelConfigId.toString(),
      TrackEvent.sort: isSubscriptionProduct ? 'subscribe' : 'inapp',
      TrackEvent.order_no: orderNo,
      TrackEvent.payment_method: Platform.isIOS ? 'Apple' : 'Google play',
      TrackEvent.lock_begin: payment.lockBegin ?? "",

      EventKey.playDirection: payment.playDirection ?? "",
      EventKey.resourceBitId: payment.resourceBitId ?? "",
    });
  }

  // static String getAmount(bool isSubscriptionProduct,
  //     StoreProductResult storeProductResult, Payment payment) {
  //   String amount = "";
  //   if (isSubscriptionProduct) {
  //     if (storeProductResult.subscribeSkuResponses != null) {
  //       for (var element in storeProductResult.subscribeSkuResponses!) {
  //         if (element.skuId == payment.skuId) {
  //           amount = element.firstAmount;
  //           break;
  //         }
  //       }
  //     }
  //     if (amount.isEmpty) {
  //       FFLog.error("查找失败 ${payment.skuId}");
  //     }
  //   } else {
  //     if (storeProductResult.skuInfoResponses?.isNotEmpty == true) {
  //       for (var element in storeProductResult.skuInfoResponses!) {
  //         if (element.gpSkuId == payment.skuId ||
  //             element.iosSkuId == payment.skuId) {
  //           amount = element.recharge;
  //           break;
  //         }
  //       }
  //       if (storeProductResult.retainSkuInfoResponses != null &&
  //           (storeProductResult.retainSkuInfoResponses!.iosSkuId ==
  //                   payment.skuId ||
  //               storeProductResult.retainSkuInfoResponses!.gpSkuId ==
  //                   payment.skuId)) {
  //         amount = storeProductResult.retainSkuInfoResponses!.recharge;
  //       }
  //       if (amount.isEmpty) {
  //         FFLog.error("查找失败 ${payment.skuId}");
  //       }
  //     }
  //   }
  //   return amount;
  // }

  // 用户点击购买按钮 拉起平台支付时埋一次
  static submitOrderShow({
    required String amount,
    required String skuProductId,
    required String skuId,
    required String skuModelConfigId,
    required String strScene,
    required String lockBegin,
    required bool isSubscriptionProduct,
    required String coins,
    required String bonus,
    required String reelId,
    required String episode,
    required String playDirection,
    required String resourceBitId,
  }) {
    final PaymentService paymentService = Get.find<PaymentService>();
    useTrackEvent(TrackEvent.order_show, extra: {
      /*
      剧集解锁-沉浸页的待解锁弹窗：ads_coins
      剧集解锁-付费挽留弹窗：pay_retain
      剧集解锁-付费挽留商品：pay_retain_commodity
      剧集解锁-订阅：coin_subscribe
      订阅详情页：subscribe
      充值页：recharge
      充值页-订阅：recharge_subscribe
      订阅到期弹窗：subscribe_expire_popup
       */
      TrackEvent.scene: strScene,
      TrackEvent.amount: amount,
      TrackEvent.currency: paymentService.currencyCode.value,
      TrackEvent.product_id: skuProductId,
      TrackEvent.sku: skuId,
      TrackEvent.reel_id: reelId,
      TrackEvent.episode: episode,
      TrackEvent.template_id: skuModelConfigId,
      TrackEvent.platform: Platform.isIOS ? "Apple" : "Google play",
      TrackEvent.sort: isSubscriptionProduct ? "subscribe" : 'inapp',
      TrackEvent.payment_method: Platform.isIOS ? "Apple" : "Google play",
      TrackEvent.coins: coins,
      TrackEvent.bonus: bonus,
      TrackEvent.lock_begin: lockBegin,
      EventKey.playDirection: playDirection,
      EventKey.resourceBitId: resourceBitId,
    });
  }

  // 用户点击购买按钮 拉起平台支付时埋一次
  static submitOrderClick({
    required String amount,
    required String skuProductId,
    required String skuId,
    required String skuModelConfigId,
    required String strScene,
    required String lockBegin,
    required bool isSubscriptionProduct,
    required String coins,
    required String bonus,
    required String reelId,
    required String episode,
    required String playDirection,
    required String resourceBitId,
  }) {
    final PaymentService paymentService = Get.find<PaymentService>();
    useTrackEvent(TrackEvent.orderClick, extra: {
      /*
      剧集解锁-沉浸页的待解锁弹窗：ads_coins
      剧集解锁-付费挽留弹窗：pay_retain
      剧集解锁-付费挽留商品：pay_retain_commodity
      剧集解锁-订阅：coin_subscribe
      订阅详情页：subscribe
      充值页：recharge
      充值页-订阅：recharge_subscribe
      订阅到期弹窗：subscribe_expire_popup
       */
      TrackEvent.scene: strScene,
      TrackEvent.amount: amount,
      TrackEvent.currency: paymentService.currencyCode.value,
      TrackEvent.product_id: skuProductId,
      TrackEvent.sku: skuId,
      TrackEvent.reel_id: reelId,
      TrackEvent.episode: episode,
      TrackEvent.template_id: skuModelConfigId,
      TrackEvent.platform: Platform.isIOS ? "Apple" : "Google play",
      TrackEvent.sort: isSubscriptionProduct ? "subscribe" : 'inapp',
      TrackEvent.payment_method: Platform.isIOS ? "Apple" : "Google play",
      TrackEvent.coins: coins,
      TrackEvent.bonus: bonus,
      TrackEvent.lock_begin: lockBegin,
      EventKey.playDirection: playDirection,
      EventKey.resourceBitId: resourceBitId,
    });
  }

  // 进入商品展示页 埋一次
  static submitRechargeShow({
    required String strScene,
    required String reelId,
    required String episode,
    required String action,
    required String lockBegin,
    required String playDirection,
    String? resourceBitId,
  }) {
    /*
      剧集解锁-沉浸页的待解锁弹窗：ads_coins
      剧集解锁-付费挽留弹窗：pay_retain
      剧集解锁-付费挽留商品：pay_retain_commodity
      剧集解锁-订阅：coin_subscribe
      订阅详情页：subscribe
      充值页：recharge
      充值页-订阅：recharge_subscribe
      订阅到期弹窗：subscribe_expire_popup
       */
    
    Map<String, String> extra = {
      TrackEvent.scene: strScene,
      TrackEvent.reel_id: reelId,
      TrackEvent.episode: episode,
      TrackEvent.action: action,
      TrackEvent.lock_begin: lockBegin,
      EventKey.playDirection: playDirection,
    };

    if (resourceBitId != null && resourceBitId.isNotEmpty) {
      extra[EventKey.resourceBitId] = resourceBitId;
    }

    useTrackEvent(TrackEvent.recharge_show, extra: extra);
  }

  // 退出商品展示页 埋一次
  static submitRechargeShowEnd({
    required String strScene,
    required String reelId,
    required String episode,
    required String action,
    required String lockBegin,
    required String playDirection,
    String? resourceBitId,
  }) {
    /*
      剧集解锁-沉浸页的待解锁弹窗：ads_coins
      剧集解锁-付费挽留弹窗：pay_retain
      剧集解锁-付费挽留商品：pay_retain_commodity
      剧集解锁-订阅：coin_subscribe
      订阅详情页：subscribe
      充值页：recharge
      充值页-订阅：recharge_subscribe
      订阅到期弹窗：subscribe_expire_popup
       */
    
    Map<String, String> extra = {
      TrackEvent.scene: strScene,
      TrackEvent.reel_id: reelId,
      TrackEvent.episode: episode,
      TrackEvent.action: action,
      TrackEvent.lock_begin: lockBegin,
      EventKey.playDirection: playDirection,
    };
    if (resourceBitId != null && resourceBitId.isNotEmpty) {
      extra[EventKey.resourceBitId] = resourceBitId;
    }

    useTrackEvent(TrackEvent.recharge_show_end, extra: extra);
  }

  // 展示每个订阅商品 埋一次
  static submitSubscribeThingShow(
    String amount,
    String strScene,
    String productId,
  ) {
    final PaymentService paymentService = Get.find<PaymentService>();
    useTrackEvent(TrackEvent.subscribe_thing_show, extra: {
      TrackEvent.amount: amount,
      // 传入本地化金额 与 货币代码 USD
      TrackEvent.currency: paymentService.currencyCode.value,
      TrackEvent.scene: strScene,
      // 沉浸页的待解锁弹窗：ads_coins 充值页：recharge 订阅详情页 subscrib
      TrackEvent.product_id: productId,
      // 传入后台该位置的商品id
    });
  }

// 提交恢复购买结果的方法
// 参数说明：
// type: 触发补偿类型，可选值：auto（自动触发），manual（用户手动触发），默认为空字符串
// result: 结果，可选值：0（未漏单），1（漏单），默认为空字符串
// coins: 漏单补偿金币，传入补偿的金币数值，默认为空字符串
// genre: 付费类型，可选值：inapp（内购），subscribe（订阅），默认为空字符串
// bonus: 漏单补偿奖励，传入补偿的奖励数值，默认为空字符串
// scene: 场景，可选值：topup（充值页），默认为空字符串
  static submitRestoreResult({
    String type = EventValue.manual,
    String result = '1',
    String coins = '',
    String genre = EventValue.subscribe,
    String bonus = '',
    String strScene = EventValue.topUp,
  }) {
    // 调用 useTrackEvent 方法，触发恢复结果的事件跟踪
    // 并传递包含各种参数的额外信息
    useTrackEvent(TrackEvent.restore_result, extra: {
      // 触发补偿类型
      TrackEvent.type: type,
      // 恢复结果，0 表示未漏单，1 表示漏单
      TrackEvent.result: result,
      // 漏单补偿的金币数值
      TrackEvent.coins: coins,
      // 付费类型，如内购或订阅
      TrackEvent.genre: genre,
      // 漏单补偿的奖励数值
      TrackEvent.bonus: bonus,
      // 场景，如充值页
      TrackEvent.scene: strScene,
      // 后台该位置的商品id
    });
  }
}

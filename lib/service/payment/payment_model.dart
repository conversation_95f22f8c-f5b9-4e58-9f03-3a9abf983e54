// Payment 类用于封装支付相关的信息
import 'dart:async';

class SourceType {
  static const int subscriptionAggregationPage = 1; // 订阅详情
  static const int unlockDialogPage = 2; // 解锁弹框
  static const int rechargePage = 3; // 充值页
}

class Payment {
  // 用于完成异步操作的 Completer，返回布尔值表示支付操作的结果
  Completer<bool>? completer;

  // 必须参数 - Google 商店的商品 ID
  String skuId;

  // 必须参数 - 服务器下发的产品 ID
  String skuProductId;

  // 非必须参数 - 订阅场景，可能为 null 服务器下发的 sourceType
  int? source;

  // 非必须参数 - 是否进行补单操作，可能为 null
  bool? recover;

  // 非必须参数 - 短剧的 ID，可能为 null 长码
  int? shortPlayCode;

  // 非必须参数 - 短剧的 ID，可能为 null
  int? shortPlayId;

  // 非必须参数 - 模版ID，可能为 null
  String? skuModelConfigId;

  // 非必须参数 - 剧集的集数，可能为 null
  int? episode;

  // 非必须参数 - 剧集的集数，可能为 null
  bool? isRetain;

// 非必须参数 - Sku 类型
  int? skuType;

  // 非必须参数 - 卡点集数
  String? lockBegin;

  // 非必须参数 - 订阅场景，埋点所需的场景
  String? strSource;

  // 资源位id
  String? resourceBitId;

  // 屏幕方向
  String? playDirection;

  // 本次支付金额
  String? amount;

  // 构造函数，用于初始化 Payment 类的实例
  Payment({
    // 必须传入的 Completer 实例
    this.completer,
    // 必须传入的 skuId
    required this.skuId,
    // 必须传入的 productId
    required this.skuProductId,
    // 必须传入的 本次支付金额
    required this.amount,

    // 可选的订阅场景
    this.source,
    // 可选的是否补单标志
    this.recover,
    // 可选的短剧 ID 长码
    this.shortPlayCode,
    // 可选的短剧 ID 长码
    this.shortPlayId,
    // 可选的 当前剧集集数
    this.episode,
    //模版ID
    this.skuModelConfigId,
    // 是否膨胀商品
    this.isRetain = false,
    // sku类型
    this.skuType,
    // 卡点集数
    this.lockBegin,
    // 非必须参数 - 订阅场景，埋点所需的场景
    this.strSource,
    // 资源位id
    this.resourceBitId,

    // 屏幕方向
    this.playDirection,
  });

  @override
  String toString() {
    return '支付参数 Payment{completer: $completer, skuId: $skuId, productId: $skuProductId, source: $source, recover: $recover, shortPlayId: $shortPlayCode, skuModelConfigId: $skuModelConfigId, episode: $episode lockBegin $lockBegin resourceBitId $resourceBitId playDirection $playDirection}';
  }
}

/// 定义支付回调类型枚举
enum PaymentCallbackType {
  /// 购买成功
  purchaseSuccess,

  /// 获取商品详情成功
  fetchProductDetail,

  /// 购买取消
  purchaseCancel,

  /// 购买过程中发生错误
  purchaseError,

  /// 订阅成功
  subscriptionSuccess,

  /// 订阅失败
  subscriptionError,

  /// 订阅取消
  subscriptionCancel,
}

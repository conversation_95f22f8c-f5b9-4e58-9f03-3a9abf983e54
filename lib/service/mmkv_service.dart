import 'package:get/get.dart';
import 'package:mmkv/mmkv.dart';
import 'package:playlet/common/log/ff_log.dart';

class MMKVService extends GetxService {
  late MMKV _episodeKv;
  bool _isInitialized = false;
  static const String _tag = 'MMKVService';

  @override
  void onInit() {
    super.onInit();
    _initMMKV();
  }

  Future<void> _initMMKV() async {
    try {
      await MMKV.initialize();
      _episodeKv = MMKV('EpisodeDuration');
      _isInitialized = true;
    } catch (e) {
      FFLog.debug('MMKV初始化失败: $e', tag: _tag);
    }
  }

  // 保存播放进度
  bool setPlayDuration(int episodeId, Duration duration) {
    try {
      if (!_isInitialized) return false;
      return _episodeKv.encodeInt(episodeId.toString(), duration.inMilliseconds);
    } catch (e) {
      FFLog.debug('MMKV encodeInt失败: $e', tag: _tag);
      return false;
    }
  }

  // 获取播放进度
  Duration getPlayDuration(int episodeId) {
    try {
      if (!_isInitialized) return Duration.zero;
      int milliseconds = _episodeKv.decodeInt(episodeId.toString());
      return Duration(milliseconds: milliseconds);
    } catch (e) {
      FFLog.debug('MMKV decodeInt失败: $e', tag: _tag);
      return Duration.zero;
    }
  }

  // 删除播放进度
  void removePlayDuration(int episodeId) {
    try {
      if (!_isInitialized) return;
      _episodeKv.removeValue(episodeId.toString());
    } catch (e) {
      FFLog.debug('MMKV removeValue失败: $e', tag: _tag);
    }
  }
}
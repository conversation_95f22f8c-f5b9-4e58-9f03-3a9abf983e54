import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/controller/bottom_nav_controller.dart';

class BottomNavWidget extends GetView<BottomNavController> {
  const BottomNavWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        return SafeArea(
          child: Container(
            height: 59.sp,
            color: const Color(0xFF171717),
            child: Row(
              children: [
                SizedBox(width: 10.sp),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(
                      controller.displayItems.length,
                      (displayIndex) {
                        final item = controller.displayItems[displayIndex];
                        // 因为controller.displayItems含有非tabItem的元素，所以此处要去查找item在_tabbarItems里的索引，要以这个索引为准，不能用displayIndex。
                        // 设计不合理，不应该太依赖索引，而是要让tabItem和页面一一绑定，但是改造量太大，所以先这样处理。
                        final realIndex = controller.tabbarItems.indexOf(item);
                        return Expanded(
                          child: item.buildTabItem(
                            isSelected: realIndex == controller.pageIndex.value,
                            onTap: () => {
                              // 如果按照tabItem和页面一一对应的设计，这里就不用处理点击事件，可以全部交给item自己处理
                              if (realIndex >= 0)
                                {
                                  // TabbarItem切换
                                  controller.changePage(realIndex)
                                }
                              else
                                {
                                  // 非tab切换，此处不处理，交给item自己处理
                                }
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ),
                SizedBox(width: 10.sp),
              ],
            ),
          ),
        );
      },
    );
  }
}

class TabbarItem implements TabItemProtocol {
  final String title;
  final String icon;
  final String activeIcon;

  TabbarItem({
    required this.title,
    required this.icon,
    required this.activeIcon,
  });

  @override
  Widget buildTabItem({required bool isSelected, VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            isSelected ? activeIcon : icon,
            width: 26.sp,
            height: 26.sp,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              color: isSelected ? Colors.white : const Color(0xFF999999),
              fontSize: 11.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}

class TabResourceBitImageItem implements TabItemProtocol {
  final String imagePath;
  final VoidCallback onTap;
  TabResourceBitImageItem({required this.imagePath, required this.onTap});

  @override
  Widget buildTabItem({required bool isSelected, VoidCallback? onTap}) {
    return GestureDetector(
      onTap: this.onTap,
      behavior: HitTestBehavior.opaque,
      child: Image.file(
        File(imagePath),
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
      ),
    );
  }
}

abstract class TabItemProtocol {
  /// 构建tabItem
  Widget buildTabItem({required bool isSelected, VoidCallback? onTap});
}

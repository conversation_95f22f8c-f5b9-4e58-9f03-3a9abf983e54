import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/*
示例：
// 显示返回按钮和标题居中,显示右侧组件
FFNavBar(
  showBackIcon: true, // 默认false
  title: "Playlet",
  rightWidgets: [Text("fasd"), Text("123123")],
)

// 显示返回按钮和标题不居中
FFNavBar(
  showBackIcon: true,
  title: "Playlet",
  titleInCenter: false,
)

// 自定义左侧组件
FFNavBar(
  leadingWidget: Text("fasd"),
  title: "Playlet",
)
*/

class FFNavBar extends StatelessWidget implements PreferredSizeWidget {
  
  /// 是否显示返回按钮
  final bool showBackIcon;
  
  /// 自定义的左侧组件，当 [showBackIcon] 为 false 时生效
  final Widget? leadingWidget;
  
  /// 右侧组件列表
  final List<Widget>? rightWidgets;
  
  /// 标题文本
  final String? title;
  
  /// 标题是否居中显示，为 true 时居中，为 false 时靠左
  final bool titleInCenter;
  
  /// 导航栏背景颜色
  final Color? backgroundColor;
  
  /// 背景颜色透明度，取值范围 0.0-1.0
  final double alpha;
  
  /// 默认的返回按钮点击回调,即showbackicon为true时，不设置时默认调用 Get.back()
  final VoidCallback? onBackPressed;
  
  /// 标题文本样式
  final TextStyle? titleStyle;

  const FFNavBar({
    Key? key,
    this.showBackIcon = false,
    this.leadingWidget,
    this.rightWidgets,
    this.title,
    this.titleInCenter = true,
    this.backgroundColor = Colors.transparent,
    this.alpha = 1.0,
    this.onBackPressed,
    this.titleStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: Get.mediaQuery.padding.top,
      ),
      width: Get.width,
      color: backgroundColor?.withOpacity(alpha),
      height: kToolbarHeight + Get.mediaQuery.padding.top,
      child: Stack(  // 使用Stack替代Row
        children: [
          // 左侧部分
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: _buildLeftSection(),
          ),
          
          // 居中标题
          if (titleInCenter)
            Positioned.fill(
              child: Align(
                alignment: Alignment.center,
                child: _buildCenterTitle(),
              ),
            ),
          
          // 右侧部分
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: _buildRightSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterTitle() {
    if (title == null) return SizedBox();
    
    return Text(
      title!,
      style: titleStyle ?? TextStyle(
        color: Colors.white,
        fontSize: 18.sp,
        fontWeight: FontWeight.w600,
      ),
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildLeftSection() {
    List<Widget> leftWidgets = [];
    
    // 添加返回按钮或自定义的leadingWidget
    if (showBackIcon) {
      Widget backIcon = IconButton(
        padding: EdgeInsets.zero,
        constraints: BoxConstraints(),
        onPressed: onBackPressed ?? () => Get.back(),
        icon: Image.asset(
          'assets/back_icon/white_back_icon.png',  // 替换为你的自定义图片路径
          width: 24.sp,
          height: 24.sp,
          color: Colors.white,  // 可选，如果需要改变图片颜色
        ),
      );
      leftWidgets.add(backIcon);
    } else if (leadingWidget != null) {
      leftWidgets.add(leadingWidget!);
    }
    
    // 如果标题不居中，则添加到左侧
    if (!titleInCenter && title != null) {
      leftWidgets.add(SizedBox(width: 6.sp));
      leftWidgets.add(
        Text(
          title!,
          style: titleStyle ?? TextStyle(
            color: Colors.white,
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }
    
    return Row(
      children: leftWidgets,
    );
  }

  Widget _buildRightSection() {
    List<Widget> right_widgets = [];

    rightWidgets?.forEach((element) {
      right_widgets.add(element);
      right_widgets.add(SizedBox(width: 12.sp));
    });

    return Row(
      children: right_widgets
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);
}
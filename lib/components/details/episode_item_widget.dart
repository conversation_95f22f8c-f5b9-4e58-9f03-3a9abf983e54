import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/details/episode_funs_widget.dart';
import 'package:playlet/components/details/landscape_button_widget.dart';
import 'package:playlet/components/details/player/episode_player_view.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/details/details_controller.dart';

class EpisodeItemWidget extends StatefulWidget {
  const EpisodeItemWidget({
    super.key,
    required this.index,
    required this.episode,
    required this.onPlayStatusChanged,
    required this.onEnded,
    required this.onTimeChange,
    required this.detailsController,
    this.shortPlayCode,
    this.shortPlayType,
  });

  final int index;
  final int? shortPlayCode;
  final int? shortPlayType;
  final Episode episode;
  final Function(Duration tiem) onTimeChange;
  final Function(bool isPlaying) onPlayStatusChanged;
  final Function() onEnded;
  final DetailsController detailsController;

  @override
  State<EpisodeItemWidget> createState() => _EpisodeItemWidgetState();
}

class _EpisodeItemWidgetState extends State<EpisodeItemWidget>
    with SingleTickerProviderStateMixin {
  DetailsController get detailsController => widget.detailsController;
  bool isMovePlayer = false;

  bool isLastOne = false;
  late Duration playPosition;

  @override
  void initState() {
    playPosition = const Duration(seconds: 0);
    super.initState();
  }

  void _initScreenUtil(Orientation orientation) {
    if (orientation == Orientation.portrait) {
      ScreenUtil.init(
        context,
        designSize: const Size(393, 852), // 竖屏设计尺寸
      );
    } else {
      ScreenUtil.init(
        context,
        designSize: const Size(852, 393), // 横屏设计尺寸
      );
    }
  }

  Future<void> _toggleFullScreen() async {
    detailsController.toggleLandscapeScreen();
  }


  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    _initScreenUtil(mediaQuery.orientation); // 确保每次build都检查方向
    Get.log("isUnlockEpisodId EpisodeItemWidget $isLastOne");
    return Obx(() {
      isLastOne = widget.index == detailsController.viewModel.currentDramaModel.episodeList.length - 1;
      if (detailsController.viewModel.currentDramaModel.allLock.value) {
        isLastOne = false;
      }

      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            EpisodePlayerView(
              index: widget.index,
              isLastOne: isLastOne,
              aspectRatio: widget.episode.info.aspectRatio ?? 0,
              shortPlayCode: widget.shortPlayCode,
              shortPlayType: widget.shortPlayType,
              episodeNum: widget.episode.info.episodeNum,
              key: widget.episode.playerKey,
              videoUrl: widget.episode.videoUrl,
              onPlayStatusChanged: widget.onPlayStatusChanged,
              onEnded: widget.onEnded,
              onTimeChange: (tiem) {
                playPosition = tiem;
                widget.onTimeChange(tiem);
              },
              detailsController: detailsController,
              onMoveStart: () {
                setState(() {
                  isMovePlayer = true;
                });
              },
              onMoveEnd: () {
                setState(() {
                  isMovePlayer = false;
                });
              },
            ),

            // 竖屏状态下显示横屏按钮
            !detailsController.isLandscapeScreen.value &&
                    (widget.episode.info.aspectRatio ?? 0) >= 1 && !isLastOne
                ? Builder(
                    builder: (context) {               
                      final screenWidth = Get.width;
                      final videoHeight =
                          screenWidth / (widget.episode.info.aspectRatio ?? 1);
                      final videoTop =
                          (Get.height - videoHeight) / 2; // 播放区域距离屏幕顶部的高度
                      return LandscapeButtonWidget(
                          top: videoTop + videoHeight + 20.sp,
                          onTap: _toggleFullScreen);
                    },
                  )
                : Builder(
                    builder: (context) {                    
                      return const SizedBox();
                    },
                  ),

            Obx(() => detailsController.viewModel.currentDramaModel.isLastOneView.value
                ? _buildUnlockView()
                : const SizedBox()),
            isMovePlayer
                ? const SizedBox()
                : EpisodeFunsWidget(
                    info: widget.episode,
                    detailsController: detailsController,
                  ),
          ],
        ),
      );
    });
  }

  Widget _buildUnlockView() {
    return isLastOne == true
        ? Positioned.fill(
            child: Container(
              width: Get.width,
              height: Get.height,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.sp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    AppTrans.unlockMoreEpisodes(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: const Color(0XFFFFFFFF),
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(height: 30.sp),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () => detailsController.onUnLock(widget.index,isClickButton:true),
                    child: Container(
                      width: 250.sp,
                      height: 50.sp,
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFCD00),
                        borderRadius: BorderRadius.all(
                          Radius.circular(24.r),
                        ),
                      ),
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Assets.video.unlock.image(
                            width: 20.sp,
                            height: 20.sp,
                          ),
                          SizedBox(width: 8.sp),
                          Text(
                            AppTrans.unlockNow(),
                            style: TextStyle(
                              color: const Color(0XFF121212),
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
        : const SizedBox();
  }
}

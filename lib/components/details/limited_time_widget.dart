import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/store/store_product_result.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/service/payment/payment_service.dart';
import 'package:playlet/utils/limited_time.dart';
import 'package:slide_countdown/slide_countdown.dart';

import '../../service/user_service.dart';

class LimitedTimeWidget extends StatefulWidget {
  const LimitedTimeWidget({
    super.key,
    required this.skuInfo,
    required this.detailsController,
    this.onStartPay,
  });

  final SkuInfoResponses skuInfo;
  final DetailsController detailsController;
  final VoidCallback? onStartPay;

  @override
  State<LimitedTimeWidget> createState() => _LimitedTimeWidgetState();
}

class _LimitedTimeWidgetState extends State<LimitedTimeWidget> {
  final PaymentService paymentService = Get.find<PaymentService>();
  DetailsController get detailsController => widget.detailsController;
  final UserService userServices = Get.find<UserService>();
  bool isSelect = true;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    isSelect = userServices.userInfo.value?.autoUnlock ?? true;
  }

  @override
  Widget build(BuildContext context) {
    final time = LimitedTime.getLeftTime();
    final isLandscape = ScreenUtils.isLandscape(context);
    return Padding(
      /// 设置底部偏移量，竖屏时为超出弹窗顶部的高度的一半
      padding: EdgeInsets.only(bottom: isLandscape ? 0.0 : 57.sp),
      child: SizedBox(
        height: isLandscape ? 336.sp : 450.sp,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              children: [
                SizedBox(height: isLandscape ? 64.sp : 114.sp),
                Container(
                  width: isLandscape ? 280.sp : 300.sp,
                  decoration: BoxDecoration(
                    color: const Color(0XFF25252E),
                    borderRadius: BorderRadius.circular(12.sp),
                  ),
                  padding: EdgeInsets.only(bottom: 15.sp),
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: ClipRRect(
                          // 添加左右上角12的圆角
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(12.sp),
                          ),
                          child: Assets.details.limitedTimeBgBack.image(
                            width: isLandscape ? 280.sp : 300.sp,
                            height: double.infinity,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: double.infinity,
                        child: Stack(
                          children: [
                            Column(
                              children: [
                                SizedBox(height: isLandscape ? 81.sp : 117.sp),
                                Container(
                                  width: isLandscape ? 200.sp : 241.sp,
                                  height: isLandscape ? 68.sp : 82.sp,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage(
                                        Assets.details.limitedTimeBack.path,
                                      ),
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      SizedBox(
                                        width: isLandscape ? 83.sp : 100.sp,
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Row(
                                              children: [
                                                SizedBox(
                                                    width: isLandscape
                                                        ? 9.sp
                                                        : 11.sp),
                                                Assets.details.coins.image(
                                                  width: isLandscape
                                                      ? 20.sp
                                                      : 24.sp,
                                                  height: isLandscape
                                                      ? 20.sp
                                                      : 24.sp,
                                                ),
                                                Text(
                                                  "${widget.skuInfo.coins ?? 0}",
                                                  style: TextStyle(
                                                    fontSize: isLandscape
                                                        ? 14.sp
                                                        : 16.sp,
                                                    fontWeight: FontWeight.w700,
                                                    color:
                                                        const Color(0XffFCB11F),
                                                  ),
                                                )
                                              ],
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                SizedBox(
                                                    width: isLandscape
                                                        ? 8.sp
                                                        : 10.sp),
                                                Text(
                                                  AppTrans.coins(),
                                                  style: TextStyle(
                                                    fontSize: isLandscape
                                                        ? 9.sp
                                                        : 11.sp,
                                                    fontWeight: FontWeight.w600,
                                                    color:
                                                        const Color(0XFF4E4E4E),
                                                  ),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                          width: isLandscape ? 20.sp : 27.sp),
                                      Expanded(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Row(
                                              children: [
                                                Assets.details.bouns.image(
                                                  width: isLandscape
                                                      ? 26.sp
                                                      : 27.sp,
                                                  height: isLandscape
                                                      ? 26.sp
                                                      : 27.sp,
                                                ),
                                                SizedBox(
                                                    width: isLandscape
                                                        ? 3.sp
                                                        : 4.sp),
                                                Text(
                                                  "${widget.skuInfo.keepGiveCoins ?? 0}",
                                                  style: TextStyle(
                                                      color: const Color(
                                                          0xFFF93F22),
                                                      fontSize: isLandscape
                                                          ? 21.sp
                                                          : 25.sp,
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      shadows: [
                                                        Shadow(
                                                          color: const Color(
                                                              0xFFF93F22),
                                                          offset: Offset(
                                                              0, 1.04.sp),
                                                          blurRadius: 2.08.sp,
                                                        ),
                                                      ]),
                                                )
                                              ],
                                            ),
                                            Text(
                                              AppTrans.bonus(),
                                              style: TextStyle(
                                                color: const Color(0xFFF93F22),
                                                fontSize:
                                                    isLandscape ? 11.sp : 13.sp,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 14.sp),
                                SizedBox(
                                  height: isLandscape ? 23.sp : 29.sp,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Assets.details.timeDowm.image(
                                        width: isLandscape ? 13.sp : 15.sp,
                                        height: isLandscape ? 13.sp : 15.sp,
                                      ),
                                      6.sp.horizontalSpace,
                                      Text(
                                        AppTrans.countdown(),
                                        style: TextStyle(
                                          fontSize: isLandscape ? 11.sp : 12.sp,
                                          fontWeight: FontWeight.w400,
                                          color: const Color(0xFF999999),
                                        ),
                                      ),
                                      6.sp.horizontalSpace,
                                      SlideCountdown(
                                        decoration: const BoxDecoration(
                                          color: Colors.transparent,
                                        ),
                                        shouldShowHours: (p0) => true,
                                        shouldShowMinutes: (p0) => true,
                                        shouldShowSeconds: (p0) => true,
                                        duration: Duration(seconds: time),
                                        padding: const EdgeInsets.all(0),
                                        style: TextStyle(
                                          color: const Color(0XFFFF4500),
                                          fontSize: isLandscape ? 13.sp : 16.sp,
                                          fontWeight: FontWeight.w600,
                                          decoration: TextDecoration.none,
                                        ),
                                        separatorPadding:
                                            const EdgeInsets.all(0),
                                        separatorType: SeparatorType.symbol,
                                        separatorStyle: TextStyle(
                                          color: const Color(0XFFFF4500),
                                          fontSize: isLandscape ? 13.sp : 16.sp,
                                          fontWeight: FontWeight.w600,
                                          decoration: TextDecoration.none,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 4.sp),
                                GestureDetector(
                                  onTap: widget.onStartPay,
                                  child: Container(
                                    width: 200.sp,
                                    height: isLandscape ? 36.sp : 44.sp,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(40.r),
                                      ),
                                      gradient: const LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: [
                                          Color(0xFFFF4500),
                                          Color(0xFFFFFF15)
                                        ],
                                      ),
                                    ),
                                    alignment: Alignment.center,
                                    child: Text(
                                      "${paymentService.currency.value} ${widget.skuInfo.recharge}",
                                      style: TextStyle(
                                        fontSize: 18.sp,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 12.sp),
                                // Row(
                                //   mainAxisAlignment: MainAxisAlignment.center,
                                //   children: [
                                //     GestureDetector(
                                //       onTap: () {
                                //         setState(() async {
                                //           isSelect = !isSelect;
                                //           await userServices.updateAutoUnlockEpisode(isSelect);
                                //         });
                                //       },
                                //       child: Row(
                                //         mainAxisAlignment:
                                //             MainAxisAlignment.center,
                                //         children: [
                                //           isSelect
                                //               ? Assets.store.iconAgreementSelect
                                //                   .image(
                                //                       width: 13.r, height: 13.r)
                                //               : Assets.store.iconAgreementUnselect
                                //                   .image(
                                //                       width: 13.r, height: 13.r),
                                //           SizedBox(width: 4.w),
                                //           Text(
                                //             AppTrans.automaticEpisodeUnlock(),
                                //             style: TextStyle(
                                //               color: const Color(0x99999999),
                                //               fontSize: 10.sp,
                                //               fontWeight: FontWeight.w400,
                                //             ),
                                //           ),
                                //         ],
                                //       ),
                                //     ),
                                //   ],
                                // )
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              top: 0,
              child: Assets.details.limitedTimeTop.image(
                width: isLandscape ? 209.sp : 311.sp,
                height: isLandscape ? 197.sp : 292.sp,
              ),
            ),
            Positioned(
              top: isLandscape ? 78.sp : 125.sp,
              right: isLandscape ? 18.sp : 14.sp,
              child: InkWell(
                onTap: detailsController.onLimitedTimePanelClose,
                child: Container(
                  width: isLandscape ? 20.sp : 24.sp,
                  height: isLandscape ? 20.sp : 24.sp,
                  decoration: BoxDecoration(
                    color: const Color(0XFF464646),
                    borderRadius: BorderRadius.all(
                      Radius.circular(10.r),
                    ),
                  ),
                  padding: EdgeInsets.all(3.sp),
                  child: Assets.video.close.image(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

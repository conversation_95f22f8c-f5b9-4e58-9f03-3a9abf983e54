import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/api/details.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/components/details/drama_retention/drama_retention_popup.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/drama_retention.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/track_event.dart';

enum LoadingStatus {
  loading,
  success,
  failed,
}

class DramaRetentionController extends GetxController {
  static const String tag = 'DramaRetentionController';

  RxInt currentIndex = 0.obs;
  RxBool isScrolled = false.obs;

  RxList<DramaRetentionItem> list = <DramaRetentionItem>[].obs;

  // 加载状态
  late Rx<LoadingStatus> loadingStatus = LoadingStatus.loading.obs;

  RxBool isAutoPlay = false.obs;

  int? shortPlayId;

  init(int shortPlayId) {
    loadingStatus.value = LoadingStatus.loading;
    this.shortPlayId = shortPlayId;
    currentIndex.value = 0;
    list.clear();
    isScrolled.value = false;
    isAutoPlay.value = false;
    getData();
  }

  onAutoPlay() {
    Future.delayed(const Duration(milliseconds: 1000), () {
      isAutoPlay.value = true;
    });
  }

  Future<void> getData() async {
    final result = await ApiDetails.getRetainList(shortPlayId!);
    list.addAll(result);
    loadingStatus.value = LoadingStatus.success;
  }

  void onPageChanged(int index) {
    useTrackEvent(TrackEvent.logoutWindowClick, extra: {"action": "slide"});
    currentIndex.value = index;
    isAutoPlay.value = true;
  }

  void onScrollChanged(double value) {
    isScrolled.value = value > 0;
  }

  String getVideoUrlByResolution(VideoInfoMap videoInfoMap, String resolution) {
    switch (resolution) {
      case '1080p':
        return videoInfoMap.video1080 ??
            videoInfoMap.video720 ??
            videoInfoMap.video480!;
      case '720p':
        return videoInfoMap.video720 ??
            videoInfoMap.video480 ??
            videoInfoMap.video1080!;
      case '480p':
        return videoInfoMap.video480 ??
            videoInfoMap.video720 ??
            videoInfoMap.video1080!;
      default:
        return videoInfoMap.video720 ??
            videoInfoMap.video480 ??
            videoInfoMap.video1080!;
    }
  }

  void useTrackEventReelShow(DramaRetentionItem item) {
    useTrackEvent(TrackEvent.reel_show, extra: {
      TrackEvent.reel_id: item.shortPlayCode?.toString() ?? "",
      TrackEvent.scene: EventValue.immersionBack,
    });
  }

  void onToDetail(DramaRetentionItem item, Duration playTime,
      {bool isNext = false}) async {
    final DetailsController controller =
        Get.find<DetailsController>(tag: "details_controller");
    int? episodeNum = item.episodeNum ?? 1;
    controller.onToInfo(DetailsOptions(
      businessId: item.shortPlayId!,
      scene: EventValue.immersionBack,
      from: EventValue.immersionBack,
      playerEpisodeIndex: isNext ? episodeNum : episodeNum - 1,
      playerEpisodePosition: playTime,
      videoDuration: item.videoDuration,
    ));
    await SmartDialog.dismiss(tag: DramaRetentionPopup.tag, result: true);
  }

  Future<void> onCollectOrUnCollect(DramaRetentionItem item) async {
    final isCollect = item.isCollect == 1 ? true : false;
    if (isCollect == true) {
      useTrackEvent(TrackEvent.logoutWindowClick,
          extra: {"action": "collect_cancel"});
      ApiDetails.cancelCollect(
        businessId: item.shortPlayId!,
        scene: "immersion_back",
        colletType: 1,
        collectSource: 1,
      ).then((value) {
        if (value) {
          item.isCollect = 2;
        }
        list.refresh();
      });
    } else {
      useTrackEvent(TrackEvent.logoutWindowClick, extra: {"action": "collect"});
      ApiDetails.collectOp(
        businessId: item.shortPlayId!,
        scene: "immersion_back",
        colletType: 1,
        collectSource: 1,
        watchTime: 0,
        dramaId: item.id!,
      ).then((value) {
        if (value) {
          item.isCollect = 1;
          Get.toast(AppTrans.dramaRetentionFavoriteTig());
        }
        list.refresh();
      });
    }
  }
}

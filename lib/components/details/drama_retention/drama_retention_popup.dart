import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/components/details/drama_retention/drama_retention_controller.dart';
import 'package:playlet/components/details/drama_retention/drama_retention_player.dart';
import 'package:playlet/components/empty/empty.dart';
import 'package:playlet/components/loading/ffloading.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/drama_retention.dart';
import 'package:playlet/utils/track_event.dart';

class DramaRetentionPopup extends StatefulWidget {
  static const String tag = 'DramaRetentionPopup';

  const DramaRetentionPopup({super.key});

  @override
  State<DramaRetentionPopup> createState() => _DramaRetentionPopupState();
}

class _DramaRetentionPopupState extends State<DramaRetentionPopup> {
  final DramaRetentionController controller =
      Get.find(tag: DramaRetentionController.tag);
  final PageStorageBucket _bucket = PageStorageBucket();

  @override
  void initState() {
    useTrackEvent(TrackEvent.logoutWindowShow);
    controller.onAutoPlay();
    super.initState();
  }

  bool isClose = false;

  void onClose() {
    if (isClose) {
      return;
    }
    isClose = true;
    SmartDialog.dismiss<bool>(tag: DramaRetentionPopup.tag, result: false);
    useTrackEvent(TrackEvent.logoutWindowClick, extra: {"action": "close"});
    Future.delayed(const Duration(milliseconds: 500), () {
      isClose = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return SizedBox(
        width: Get.width,
        height: Get.height,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              AppTrans.dramaRetentionRecommendedForYou(),
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
            _buildSwiper(),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 39.sp),
              child: Column(
                children: [
                  _buildInfo(),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: onClose,
                    child: Assets.details.retentionClose.image(
                      width: 30.sp,
                      height: 30.sp,
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildSwiper() {
    if (controller.loadingStatus.value == LoadingStatus.loading) {
      // 加载中
      return const Center(child: FFLoadingWidget());
    }
    if (controller.loadingStatus.value == LoadingStatus.failed) {
      // 加载失败
      return EmptyWidget(
        pageFrom: EmptyPageFrom.shorts,
        type: EmptyType.noNetwork,
        onRefresh: () {
          controller.getData();
        },
      );
    }
    List<Widget> items = [
      for (int i = 0; i < controller.list.length; i++)
        DramaRetentionItemWidget(
          index: i,
          item: controller.list[i],
        ),
    ];
    return SizedBox(
      width: Get.width,
      height: 450.sp,
      child: PageStorage(
        bucket: _bucket,
        child: CarouselSlider(
          options: CarouselOptions(
            height: 434.sp,
            viewportFraction: 0.7,
            enlargeCenterPage: true,
            enlargeFactor: 0.2,
            enableInfiniteScroll: controller.list.length > 3 ? true : false,
            onPageChanged: (index, reason) {
              controller.onPageChanged(index);
            },
            onScrolled: (value) {
              if (value != null) {
                controller.onScrollChanged(value);
              }
            },
          ),
          items: items,
        ),
      ),
    );
  }

  Widget _buildInfo() {
    return Obx(() {
      if (controller.loadingStatus.value != LoadingStatus.success ||
          controller.list.isEmpty) {
        return const SizedBox();
      }
      return Column(
        children: [
          Text(
            controller.list[controller.currentIndex.value].shortPlayName ?? "",
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.white,
              shadows: [
                Shadow(
                  blurRadius: 1.2,
                  color: Colors.black.withValues(alpha: 0.25),
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
          SizedBox(height: 6.sp),
          Text(
            controller.list[controller.currentIndex.value].recommendContent ??
                "",
            style: TextStyle(
              fontSize: 13.sp,
              fontWeight: FontWeight.w400,
              color: const Color.fromRGBO(158, 158, 158, 1),
              decoration: TextDecoration.none,
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 3,
          ),
          SizedBox(height: 24.sp),
        ],
      );
    });
  }
}

class DramaRetentionItemWidget extends StatefulWidget {
  const DramaRetentionItemWidget({
    super.key,
    required this.index,
    required this.item,
  });

  final int index;
  final DramaRetentionItem item;

  @override
  State<DramaRetentionItemWidget> createState() =>
      _DramaRetentionItemWidgetState();
}

class _DramaRetentionItemWidgetState extends State<DramaRetentionItemWidget> {
  final DramaRetentionController controller =
      Get.find(tag: DramaRetentionController.tag);

  Duration playTime = const Duration(seconds: 0);

  @override
  Widget build(BuildContext context) {
    controller.useTrackEventReelShow(widget.item);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: Container(
                    width: 266.sp,
                    height: 354.sp,
                    decoration: const BoxDecoration(color: Colors.black),
                    child: DramaRetentionPlayer(
                      key: ValueKey(widget.item.id),
                      videoUrl: controller.getVideoUrlByResolution(
                          widget.item.videoUrl!, '720p'),
                      index: widget.index,
                      item: widget.item,
                      shortPlayCode: widget.item.shortPlayCode,
                      episodeNum: widget.item.episodeNum,
                      shortPlayType: widget.item.shortPlayType,
                      aspectRatio: widget.item.aspectRatio ?? 0.75,
                      onTimeChange: (p0) {
                        playTime = p0;
                      },
                      cover: widget.item.coverId!,
                      onClick: () {
                        useTrackEvent(TrackEvent.logoutWindowClick,
                            extra: {"action": "focu_pic"});
                        controller.onToDetail(widget.item, playTime);
                      },
                      onEnded: () => controller.onToDetail(
                        widget.item,
                        playTime,
                        isNext: true,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 28.sp),
              ],
            ),
            Obx(() => controller.currentIndex.value == widget.index
                ? Positioned(
                    left: 0,
                    right: 0,
                    child: _buildFuns(),
                  )
                : const SizedBox()),
          ],
        ),
      ],
    );
  }

  Widget _buildFuns() {
    final playWidth = widget.item.isCollect == 2 ? 126.sp : 208.sp;
    final followWidth = widget.item.isCollect == 2 ? 126.sp : 44.sp;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        GestureDetector(
          onTap: () {
            useTrackEvent(TrackEvent.logoutWindowClick,
                extra: {"action": "play_now"});
            controller.onToDetail(widget.item, playTime);
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: playWidth,
            height: 38.sp,
            decoration: BoxDecoration(
              color: const Color.fromRGBO(255, 205, 0, 1),
              borderRadius: BorderRadius.circular(4.r),
            ),
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Assets.details.retentionPlay.image(
                  width: 18.sp,
                  height: 18.sp,
                ),
                SizedBox(width: 8.sp),
                Text(
                  AppTrans.dramaRetentionPlayNow(),
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color.fromRGBO(30, 30, 30, 1),
                  ),
                ),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () => controller.onCollectOrUnCollect(widget.item),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: followWidth,
            height: 38.sp,
            decoration: BoxDecoration(
              color: const Color.fromRGBO(66, 69, 75, 1),
              borderRadius: BorderRadius.circular(4.r),
            ),
            clipBehavior: Clip.hardEdge,
            alignment: Alignment.center,
            child: widget.item.isCollect == 1
                ? Assets.mylist.mylistAlreadyCollected.image(
                    width: 18.sp,
                    height: 18.sp,
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Assets.mylist.favorites.image(
                        width: 18.sp,
                        height: 18.sp,
                      ),
                      SizedBox(width: 8.sp),
                      Text(
                        AppTrans.dramaRetentionFavorite(),
                        style: TextStyle(
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      )
                    ],
                  ),
          ),
        ),
      ],
    );
  }
}

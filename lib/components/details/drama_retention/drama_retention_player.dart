import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/player/cover_info.dart';
import 'package:playlet/common/player/player_view_core.dart';
import 'package:playlet/common/player/player_view_core_track_event.dart';
import 'package:playlet/components/details/drama_retention/drama_retention_controller.dart';
import 'package:playlet/model/drama_retention.dart';

class DramaRetentionPlayer extends StatefulWidget {
  const DramaRetentionPlayer({
    super.key,
    required this.videoUrl,
    required this.index,
    required this.cover,
    this.onEnded,
    required this.onTimeChange,
    required this.aspectRatio,
    this.onClick,
    this.shortPlayType,
    this.shortPlayCode,
    this.episodeNum,
    required this.item,
  });

  final DramaRetentionItem item;
  final String videoUrl;
  final String cover;
  final double aspectRatio;
  final int? shortPlayType;
  final int? shortPlayCode;
  final int? episodeNum;
  final int index;
  final Function()? onEnded;
  final Function()? onClick;
  final Function(Duration) onTimeChange;

  @override
  State<DramaRetentionPlayer> createState() => _DramaRetentionPlayerState();
}

class _DramaRetentionPlayerState extends State<DramaRetentionPlayer>
    with WidgetsBindingObserver {
  final DramaRetentionController controller =
      Get.find(tag: DramaRetentionController.tag);

  bool isPlaying = false;
  bool isInitialized = false;

  late Worker everCurrentIndex;
  late Worker everAutoPlay;

  final GlobalKey<PlayerViewCoreState> _playerCoreKey =
      GlobalKey<PlayerViewCoreState>();

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    everCurrentIndex = ever(controller.currentIndex, (value) async {
      await onPlayOrPause();
    });
    everAutoPlay = ever(controller.isAutoPlay, (value) async {
      if (controller.isAutoPlay.value) {
        await onPlayOrPause();
      }
    });
    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.inactive ||
        state == AppLifecycleState.paused) {
      await onPause();
    } else if (state == AppLifecycleState.resumed) {
      await onPlay();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    everCurrentIndex.dispose();
    everAutoPlay.dispose();
    super.dispose();
  }

  // 创建埋点事件处理对象
  PlayerViewCoreTrackEvent? _trackEvent;
  PlayerViewCoreTrackEvent _createTrackEvent() {
    if (_trackEvent != null) {
      return _trackEvent!;
    }

    _trackEvent = PlayerViewCoreTrackEvent(
      scene: EventValue.immersionBackPop,
      shortPlayCode: widget.shortPlayCode,
      episodeNum: widget.episodeNum,
    );
    return _trackEvent!;
  }

  /// 播放视频
  Future<void> onPlay() async {
    if (controller.currentIndex.value == widget.index &&
        controller.isAutoPlay.value &&
        !isPlaying) {
      await _playerCoreKey.currentState?.onPlay();
    }
  }

  /// 暂停视频
  Future<void> onPause() async {
    await _playerCoreKey.currentState?.onPause();
  }

  Future<void> onPlayOrPause() async {
    if (!isPlaying) {
      await onPlay();
    } else {
      await onPause();
    }
  }

  @override
  Widget build(BuildContext context) {
    // 定义固定尺寸
    final playerSize = Size(266.sp, 354.sp);
    return Stack(
      children: [
        SizedBox(
          width: playerSize.width,
          height: playerSize.height,
          child: PlayerViewCore(
            key: _playerCoreKey,
            playerViewType: PlayerViewType.retention,
            videoUrl: widget.videoUrl,
            onTimeChange: widget.onTimeChange,
            coverInfo: CoverInfo(
              thumbUrl: widget.cover,
              fit: BoxFit.cover,
            ),
            hideLoading: true,
            aspectRatio: widget.aspectRatio,
            containerSize: playerSize, // 使用Size参数传递尺寸
            shortPlayCode: widget.shortPlayCode,
            shortPlayType: widget.item.shortPlayType,
            trackEvent: _createTrackEvent(),
            onPlayerInitialized: (player) async {
              isInitialized = true;
              await onPlayOrPause();
              setState(() {});
            },
            onReadyToDisplay: () {
              if (widget.item.watchTime != null) {
                final videoDuration = widget.item.videoDuration ?? 0;
                if (videoDuration > 0 &&
                    widget.item.watchTime! > 0 &&
                    widget.item.watchTime! < videoDuration - 1) {
                  Get.log("开始跳转 ${widget.item.watchTime}");
                  _playerCoreKey.currentState?.onSeekTo(Duration(
                    seconds: widget.item.watchTime!,
                  ));
                }
              }
            },
            onPlayStatusChanged: (val) {
              isPlaying = val;
              setState(() {});
            },
            onEnded: () {
              widget.onEnded?.call();
            },
          ),
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            widget.onClick?.call();
          },
        )
      ],
    );
  }
}

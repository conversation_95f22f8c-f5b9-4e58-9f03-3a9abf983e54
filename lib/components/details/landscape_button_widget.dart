import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';

class LandscapeButtonWidget extends StatelessWidget {
  final double top;
  final VoidCallback onTap;

  const LandscapeButtonWidget({
    super.key,
    required this.top,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: top,
      left: 0,
      right: 0,
      child: Center(
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            decoration: ShapeDecoration(
              color: const Color(0xFF1E1E1E),
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  width: 0.70,
                  strokeAlign: BorderSide.strokeAlignOutside,
                  color: Colors.white.withValues(alpha: 0.4),
                ),
                borderRadius: BorderRadius.circular(57),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  Assets.fullscreen.icChangeScreen.path,
                  width: 20.sp,
                  height: 20.sp,
                ),
                const SizedBox(width: 5),
                Text(
                  AppTrans.fullscreen(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                    height: 1.15,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/api/details.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/notificaiton_dialog_service.dart';
import 'package:playlet/utils/index.dart';

class EpisodeFunsWidget extends StatefulWidget {
  const EpisodeFunsWidget({
    super.key,
    required this.info,
    required this.detailsController,
  });

  final Episode info;
  final DetailsController detailsController;

  @override
  State<EpisodeFunsWidget> createState() => _ReelFunsWidgetState();
}

class _ReelFunsWidgetState extends State<EpisodeFunsWidget> {
  DetailsController get detailsController => widget.detailsController;
  bool isCollecting = false;

  Future<void> onCollectOrUnCollect() async {
    if (isCollecting == true) return;
    isCollecting = true;
    bool isCollect = detailsController.shortsService.collectionShortsIds
        .contains(detailsController.viewModel.currentDramaModel.shortPlayDetail.shortPlayCode);
    if (isCollect == true) {
      ApiDetails.cancelCollect(
        businessId: widget.info.info.shortPlayId!,
        scene: detailsController.detailsOptions.scene,
        colletType: 1,
        collectSource: 1,
      ).then((value) {
        if (value) {
          // detailsController.isCollect.value = false;
          detailsController.onCancelCollect();
          detailsController.collectNum.value =
              detailsController.collectNum.value - 1;
        }
        isCollecting = false;
      });
    } else {
      ApiDetails.collectOp(
        businessId: widget.info.info.shortPlayId!,
        scene: detailsController.detailsOptions.scene,
        dramaId: widget.info.info.id!,
        colletType: 1,
        collectSource: 1,
        watchTime: widget.info.playPosition.inSeconds,
      ).then((value) {
        if (value) {
          // detailsController.isCollect.value = true;
          detailsController.onConfirmCollect();
          detailsController.collectNum.value =
              detailsController.collectNum.value + 1;
        }
        isCollecting = false;
      });
    }

    /// 检查并弹出通知弹窗
    var notificationLoginService = Get.find<NotificationLoginService>();
    await notificationLoginService.checkAndShowNotificationAlert(2,
        onShowChange: (isShow) {
      // 这边弹窗的时候需要暂停视频播放
      if (isShow == true) {
        detailsController.onCurrentVideoPause();
      } else {
        // 关闭弹窗的时候需要继续播放视频
        detailsController.onCurrentVideoPlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 定义横屏和竖屏的底部间距
    final double portraitBottomPadding = Get.mediaQuery.padding.bottom + 155.sp;
    final double landscapeBottomPadding = 94.sp;
    return Positioned(
      bottom: detailsController.isLandscapeScreen.value
          ? landscapeBottomPadding
          : portraitBottomPadding,
      right: detailsController.isLandscapeScreen.value ? 48.sp : 8.sp,
      child: Column(
        children: [
          Obx(
            () => _buildFun(
              detailsController.shortsService.collectionShortsIds.contains(
                          detailsController.
                              viewModel.currentDramaModel.shortPlayDetail.shortPlayCode) ==
                      true
                  ? Assets.video.liveActive.image(width: 35.sp, height: 35.sp)
                  : Assets.video.like.image(width: 35.sp, height: 35.sp),
              Utils.formatNumberK(
                  detailsController.shortsService.getShortsCollectionNumberBy(
                      detailsController.viewModel.currentDramaModel.shortPlayDetail.shortPlayCode)),
              onTap: () {
                unawaited(onCollectOrUnCollect());
              },
            ),
          ),
          // 添加是否显示订阅入口
          Obx(() {
            return detailsController
                        .userService.userInfo.value?.hasSubscription ==
                    true
                ? _buildFun(
                    Assets.video.vip.image(width: 35.sp, height: 35.sp),
                    AppTrans.vip(),
                    onTap: () async {
                      if (detailsController.isLandscapeScreen.value) {
                        await detailsController.toggleLandscapeScreen();
                      }
                      AppNavigator.startSubscriptionPage(from: "immersion");
                    },
                  )
                : const SizedBox();
          }),
          _buildFun(
            Assets.video.list.image(width: 35.sp, height: 35.sp),
            AppTrans.list(),
            onTap: detailsController.onEpisodePanelShow,
          ),
        ],
      ),
    );
  }

  Widget _buildFun(Widget icon, String text, {VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(top: 20.sp, left: 8.sp, right: 8.sp),
        child: Column(
          children: [
            icon,
            Text(
              text,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                shadows: [
                  Shadow(
                    offset: const Offset(0, 1),
                    color: const Color(0x00000040).withValues(alpha: 0.25),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

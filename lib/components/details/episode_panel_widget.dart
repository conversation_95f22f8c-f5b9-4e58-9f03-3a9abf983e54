import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/i18n/trans.dart';

class EpisodePanelWidget extends StatefulWidget {
  const EpisodePanelWidget({
    super.key,
    required this.onItem,
    required this.detailsController,
  });

  final Function(EpisodeTab episodeVideoData) onItem;
  final DetailsController detailsController;

  @override
  State<EpisodePanelWidget> createState() => _EpisodePanelWidgetState();
}

class _EpisodePanelWidgetState extends State<EpisodePanelWidget> {
  DetailsController get detailsController => widget.detailsController;
  int get lastEpisodeNum => widget.detailsController.viewModel.currentDramaModel.currentUpdatedDramaLastEpisode?.episodeNum ?? 0;
  @override
  Widget build(BuildContext context) {
     // 获取屏幕方向
    final isLandscape = ScreenUtils.isLandscape(context);
    return Container(
      width: isLandscape ? (Platform.isAndroid ? 350.sp : 320.sp) : Get.width,
      height: isLandscape ? double.infinity : 474.sp,
      decoration: BoxDecoration(
        color: const Color(0xFF1E1E1E),
        borderRadius: isLandscape
            ? BorderRadius.zero
            : BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
      ),
      child: DefaultTabController(
        length: detailsController.viewModel.currentDramaModel.episodeTabList.length,
        initialIndex: (detailsController.viewModel.currentDramaModel.currentIndex.value / 30).toInt(),
        child: Stack(
          children: [
            GetBuilder<DetailsController>(
              id: detailsController.episodePanelId,
              tag: "details_controller",
              builder: (_) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTopInfo(isLandscape),
                    _buildTabs(isLandscape),
                    Expanded(
                      child: TabBarView(
                        children: _buildTabViews(isLandscape),
                      ),
                    ),
                  ],
                );
              },
            ),
            Positioned(
              top: isLandscape? 26.sp : 18.sp,
              right: (isLandscape && Platform.isAndroid)? 46.sp: 18.sp,
              child: InkWell(
                onTap: detailsController.onEpisodePanelClose,
                 child: Assets.store.iconStorePopExit.image(
              width: 24.r,
              height: 24.r,
            ),                
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopInfo(bool isLandscape) {
    return Padding(
      padding: EdgeInsets.only(
        top: isLandscape ? 42.sp : 33.sp,
        left: 16.sp,
        right: (isLandscape && Platform.isAndroid)? 46.sp: 16.sp,
        bottom: 0.sp,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
              right: isLandscape ? 16.sp : 36.sp ,
            ),
            child: Text(
              detailsController.viewModel.currentDramaModel.shortPlayDetail.shortPlayName ?? "",
              style: TextStyle(
                color: Colors.white,
                fontSize: isLandscape ? 16.sp : 18.sp,
                fontWeight: FontWeight.w600,
                shadows: [
                  Shadow(
                    color: const Color(0xFF000000).withValues(alpha: 0.2),
                    offset: Offset(0, 1.2.sp),
                    blurRadius: 1.2.sp,
                  ),
                ],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          7.sp.verticalSpace,
          _buildNumLabel(),
        ],
      ),
    );
  }

  Widget _buildNumLabel() {
    if ((detailsController.viewModel.currentDramaModel.shortPlayDetail.updateEpisode ?? 0) <
        (detailsController.viewModel.currentDramaModel.shortPlayDetail.totalEpisodes ?? 0)) {
      return Row(
        children: [
          Text(
            "${AppTrans.update()} ${detailsController.viewModel.currentDramaModel.shortPlayDetail.updateEpisode ?? 0} ${AppTrans.ep()}",
            style: TextStyle(
              color: const Color(0XFF9F9FA2),
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
          Container(
            width: 2.sp,
            height: 2.sp,
            margin: EdgeInsets.symmetric(horizontal: 4.sp),
            decoration: BoxDecoration(
              color: const Color(0XFF9F9FA2),
              borderRadius: BorderRadius.all(
                Radius.circular(1.sp),
              ),
            ),
          ),
          Text(
            "${AppTrans.all()} ${detailsController.viewModel.currentDramaModel.shortPlayDetail.totalEpisodes ?? 0} ${AppTrans.ep()}",
            style: TextStyle(
              color: const Color(0XFF9F9FA2),
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      );
    }
    return Text(
      "${AppTrans.all()} ${detailsController.viewModel.currentDramaModel.shortPlayDetail.totalEpisodes ?? 0} ${AppTrans.ep()}",
      style: TextStyle(
        color: const Color(0XFF9F9FA2),
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  Widget _buildTabs(bool isLandscape) {
    return Theme(
      data: ThemeData(
        useMaterial3: false,
      ),
      child: TabBar(
        isScrollable: true,
        indicator: const BoxDecoration(),
        padding: EdgeInsets.zero,
        labelPadding: EdgeInsets.zero,
        unselectedLabelStyle: TextStyle(
          color: Colors.white,
          fontSize: isLandscape?  14.sp : 16.sp,
          fontWeight: FontWeight.w500,
          shadows: [
            Shadow(
              color: const Color(0xFF000000).withValues(alpha: 0.2),
              offset: Offset(0, 1.2.sp),
              blurRadius: 1.2.sp,
            ),
          ],
        ),
        labelStyle: TextStyle(
          color: const Color(0XFFFFCD00),
          fontSize: isLandscape?  14.sp : 16.sp,
          fontWeight: FontWeight.w500,
          shadows: [
            Shadow(
              color: const Color(0xFF000000).withValues(alpha: 0.2),
              offset: Offset(0, 1.2.sp),
              blurRadius: 1.2.sp,
            ),
          ],
        ),
        tabs: _buildTabList(),
      ),
    );
  }

  List<Widget> _buildTabList() {
    List<Widget> tabList = [];
    for (int i = 0; i < detailsController.viewModel.currentDramaModel.episodeTabList.length; i++) {
      tabList.add(
        _buildTabRightBorder(
          Tab(
            child: Text(
              detailsController.viewModel.currentDramaModel.episodeTabList[i].label,
            ),
          ),
          isDivider: i < detailsController.viewModel.currentDramaModel.episodeTabList.length - 1,
        ),
      );
    }
    return tabList;
  }

  List<Widget> _buildTabViews(bool isLandscape) {
    List<Widget> tabViews = [];
    for (EpisodeTabInfo item in detailsController.viewModel.currentDramaModel.episodeTabList) {
      tabViews.add(_buildTabView(item.list,isLandscape));
    }
    return tabViews;
  }

  Widget _buildTabRightBorder(Widget child,
      {bool isDivider = true, bool isOne = false}) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          padding: EdgeInsets.only(
            left: isOne ? 16.sp : 20.sp,
            right: 20.sp,
          ),
          child: child,
        ),
        if (isDivider)
          Positioned(
            right: 0,
            child: Container(
              width: 1.sp,
              height: 14.sp,
              color: const Color(0xFF9F9FA2),
            ),
          ),
      ],
    );
  }

  Widget _buildTabView(List<EpisodeTab> list,bool isLandscape) {
    return GridView.builder(
      padding: EdgeInsets.only(
        bottom: Get.mediaQuery.padding.bottom + 20.sp,
        left: 16.sp,
        right: (isLandscape && Platform.isAndroid)? 46.sp: 16.sp,
      ),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 6,
        mainAxisSpacing: isLandscape?  7.sp : 9.sp,
        crossAxisSpacing: isLandscape?  7.sp : 9.sp,
        childAspectRatio: 1,
      ),
      itemCount: list.length,
      itemBuilder: (context, index) => _buildEpisodeItem(list[index],isLandscape),
    );
  }

  BoxDecoration unItemBoxDecoration = BoxDecoration(
    color: const Color(0xFF333333),
    borderRadius: BorderRadius.all(
      Radius.circular(6.r),
    ),
  );
  BoxDecoration playItemBoxDecoration = BoxDecoration(
    color: const Color(0xFFFFCD00).withValues(alpha: 0.26),
    border: Border.all(
      color: const Color(0xFFFFCD00),
      width: 1.sp,
    ),
    borderRadius: BorderRadius.all(
      Radius.circular(6.r),
    ),
  );

  Widget _buildEpisodeItem(EpisodeTab item,bool isLandscape){
    final isPlay = item.index == detailsController.viewModel.currentDramaModel.currentIndex.value;
    final isLock = item.info.alreadyLock == 1 ? false : true;
    final isDisable = item.isDisable;
    return Opacity(
      opacity: isDisable == true ? 0.5 : 1,
      child: InkWell(
        onTap: () => widget.onItem(item),
        child: Container(
          decoration: isPlay ? playItemBoxDecoration : unItemBoxDecoration,
          child: Stack(
            alignment: Alignment.center,
            children: [
              _buildEpisodeItemContent(isPlay, item, isLandscape),
              _buildEpisodeItemLock(isLock, isLandscape),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEpisodeItemLock(bool isLock, bool isLandscape) {
    if (isLock) {
      return Positioned(
        top: 3.sp,
        right: 3.sp,
        child: Assets.video.lockEpisode.image(
          width: isLandscape? 9.sp : 12.sp,
          height: isLandscape? 9.sp : 12.sp,
        ),
      );
    }
    return const SizedBox();
  }

  Widget _buildEpisodeItemContent(bool isPlay, EpisodeTab episode, bool isLandscape) {
    if (isPlay) {
      return Assets.video.playEpisode.image(
        width: isLandscape? 15.sp : 18.sp,
        height: isLandscape? 15.sp : 18.sp,
      );
    }
    return Text(
      "${episode.info.episodeNum}",
      style: TextStyle(
        color: Colors.white,
        fontSize: isLandscape? 12.sp :  15.sp,
        fontWeight: FontWeight.w500,
      ),
    );
  }
}

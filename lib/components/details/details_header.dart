import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:get_storage/get_storage.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/details/details_controller.dart';

class DetailsHeader extends StatefulWidget {
  const DetailsHeader({
    super.key,
    required this.onBack,
    required this.detailsController,
  });

  final VoidCallback onBack;
  final DetailsController detailsController;

  @override
  State<DetailsHeader> createState() => _DetailsHeaderState();
}

class _DetailsHeaderState extends State<DetailsHeader> {
  DetailsController get detailsController => widget.detailsController;

  @override
  Widget build(BuildContext context) {
    bool isLandscape = ScreenUtils.isLandscape(context);
    return Positioned(
      top: 0,
      width: Get.width,
      child: Padding(
        padding: EdgeInsets.only(
          top: Get.mediaQuery.padding.top,
          left: isLandscape? 46.sp : 6.sp,
          right: 42.sp,
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: widget.onBack,
              icon: Icon(
                Icons.arrow_back_ios_rounded,
                size: 20.sp,
                color: Colors.white,
              ),
              style: ButtonStyle(
                padding: const WidgetStatePropertyAll(EdgeInsets.zero),
                minimumSize: WidgetStatePropertyAll(Size(36.sp, 36.sp)),
                fixedSize: WidgetStatePropertyAll(Size(36.sp, 36.sp)),
                maximumSize: WidgetStatePropertyAll(Size(36.sp, 36.sp)),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            Flexible(
              child: Obx(
                () => Text(
                  detailsController.shortPlayDetail.value?.shortPlayName ?? "",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                    shadows: [
                      Shadow(
                        offset: const Offset(0, 2),
                        color: const Color(0x00000004).withValues(alpha: 0.15),
                        blurRadius: 1,
                      )
                    ],
                  ),
                  maxLines: 1,
                ),
              ),
            ),
            SizedBox(width: 6.sp),
            Obx(
              () => Text(
                detailsController.viewModel.currentDramaModel.currentEpisode.value == null ? "" :
                 "${AppTrans.ep()}.${detailsController.viewModel.currentDramaModel.currentEpisode.value?.episodeNum}"
                    .toUpperCase(),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  shadows: [
                    Shadow(
                      offset: const Offset(0, 2),
                      color: const Color(0x00000004).withValues(alpha: 0.15),
                      blurRadius: 1,
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

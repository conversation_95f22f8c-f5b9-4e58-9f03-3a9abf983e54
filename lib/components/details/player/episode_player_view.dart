import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/player/cover_info.dart';
import 'package:playlet/common/player/player.dart';
import 'package:playlet/common/player/player_view_core.dart';
import 'package:playlet/common/player/player_view_core_track_event.dart';
import 'package:playlet/components/details/player/episode_player_bottom_widget.dart';
import 'package:playlet/components/details/player/episode_player_center_widget.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/routers/pages.dart';

const String tag = "EpisodePlayerView";

class EpisodePlayerView extends StatefulWidget {
  const EpisodePlayerView({
    super.key,
    required this.index,
    required this.videoUrl,
    required this.onTimeChange,
    required this.isLastOne,
    required this.aspectRatio,
    required this.detailsController,
    this.coverInfo,
    this.onPlayStatusChanged,
    this.onEnded,
    this.onMoveStart,
    this.onMoveEnd,
    this.shortPlayCode,
    this.shortPlayType,
    this.episodeNum,
  });

  final double aspectRatio;
  final int index;
  final int? shortPlayCode;
  final int? shortPlayType;
  final String videoUrl;
  final int? episodeNum;
  final CoverInfo? coverInfo;
  final bool isLastOne;
  final Function? onMoveStart;
  final Function? onMoveEnd;
  final Function(bool isPlaying)? onPlayStatusChanged;
  final Function(Duration tiem) onTimeChange;
  final Function()? onEnded;
  final DetailsController detailsController;

  @override
  State<EpisodePlayerView> createState() => EpisodePlayerViewState();
}

class EpisodePlayerViewState extends State<EpisodePlayerView> {
  DetailsController get detailsController => widget.detailsController;

  final GlobalKey<PlayerViewCoreState> _playerCoreKey =
      GlobalKey<PlayerViewCoreState>();

  /// 外部访问的播放器属性
  Player get player {
    // 使用PlayerViewCore提供的方法获取播放器实例
    if (_playerCoreKey.currentState != null) {
      return _playerCoreKey.currentState!.getPlayer();
    }
    // 如果都没有，抛出友好的错误消息
    throw Exception('Player not initialized yet');
  }

  double get playbackSpeed => _playerCoreKey.currentState?.playbackSpeed ?? 1.0;

  /// 播放视频
  Future<void> onPlay() async {
    await _playerCoreKey.currentState?.onPlay();
  }

  /// 暂停视频
  Future<void> onPause() async {
    await _playerCoreKey.currentState?.onPause();
  }

  /// 定位到指定位置
  Future<void> onSeekTo(Duration position) async {
    await _playerCoreKey.currentState?.onSeekTo(position);
  }

  bool get isInCriticalTimeRange => _playerCoreKey.currentState?.getIsInCriticalTimeRange()?? false;

  // 创建埋点事件处理对象
  PlayerViewCoreTrackEvent? _trackEvent;
  PlayerViewCoreTrackEvent _createTrackEvent() {
    if (_trackEvent != null) {
      return _trackEvent!;
    }

    bool isFree = (detailsController.viewModel.currentDramaModel.currentIndex.value + 1) <
        (detailsController.viewModel.currentDramaModel.shortPlayDetail.lockBegin ?? 0)
        ? true
        : false;

    _trackEvent = PlayerViewCoreTrackEvent(
      scene: EventValue.immersion,
      shortPlayCode: widget.shortPlayCode,
      episodeNum: widget.episodeNum,
      from: detailsController.detailsOptions.from,
      isFree: isFree ? '0' : '1',
      moduleName: detailsController.detailsOptions.moduleName,
      moduleId: detailsController.detailsOptions.moduleId,
      positionId: detailsController.detailsOptions.positionId,
      speedLevel: playbackSpeed.toString(),
      lockBegin: detailsController
          .viewModel.currentDramaModel.shortPlayDetail.lockBegin
          ?.toString(),
      resourceBitId: detailsController.detailsOptions.resourceBitId,
      reelName: detailsController
          .viewModel.currentDramaModel.shortPlayDetail.shortPlayName,
      videoDuration: detailsController
          .viewModel
          .currentDramaModel
          .episodeList[
              detailsController.viewModel.currentDramaModel.currentIndex.value]
          .info
          .videoDuration,
    );
    return _trackEvent!;
  }

  void _onPlayerInitialized(bool initialized) {
    if (initialized &&
        detailsController.viewModel.currentDramaModel.currentIndex.value ==
            widget.index) {
      _playerCoreKey.currentState?.onPlayOrPause();
    }
  }

  void _onReadyToDisplay() {
    detailsController.onPlayerEpisodePosition();
    setState(() {});
  }

  void _onPlayStatusChanged(bool playing) {
    bool allowIsPlay = detailsController.isForeground == false;
    if (allowIsPlay) {
      _playerCoreKey.currentState?.onPause();
    }

    if (widget.index ==
            detailsController.viewModel.currentDramaModel.episodeList.length -
                1 &&
        !detailsController.viewModel.currentDramaModel.allLock.value) {
      _playerCoreKey.currentState?.onPause();
    }

    widget.onPlayStatusChanged?.call(playing);
  }

  void _onTimeChanged(Duration position) {
    bool isNoDialog = detailsController.getNoDialog();
    if (!isNoDialog) {
      _playerCoreKey.currentState?.onPause();
    }
    if (Get.currentRoute != Routes.detailsPage) {
      _playerCoreKey.currentState?.onPause();
    }
    widget.onTimeChange(position);
  }

  void _onMoveStart() {
    _playerCoreKey.currentState?.setPausedByUserState(false);
    widget.onMoveStart?.call();
  }

  void _onMoveEnd() {
    _playerCoreKey.currentState?.setPausedByUserState(false);
    widget.onMoveEnd?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        PlayerViewCore(
          key: _playerCoreKey,
          videoUrl: widget.videoUrl,
          aspectRatio: widget.aspectRatio,
          coverInfo: widget.coverInfo,
          onTimeChange: _onTimeChanged,
          onEnded: widget.onEnded,
          onPlayerInitialized: _onPlayerInitialized,
          onReadyToDisplay: _onReadyToDisplay,
          onPlayStatusChanged: _onPlayStatusChanged,
          playerViewType: PlayerViewType.episode,
          index: widget.index,
          shortPlayCode: widget.shortPlayCode,
          hideLoading: widget.isLastOne,
          shortPlayType: widget.shortPlayType,
          trackEvent: _createTrackEvent(),
          isDailyShowLoading: true,
        ),
        (_playerCoreKey.currentState?.isPlayerAvailable() ?? false) &&
                !widget.isLastOne
            ? EpisodePlayerBottomWidget(
                controller:
                    _playerCoreKey.currentState?.getPlayer() ?? Player(""),
                detailsController: detailsController,
                onMoveStart: _onMoveStart,
                onMoveEnd: _onMoveEnd,
              )
            : const SizedBox.shrink(),
        (_playerCoreKey.currentState?.isPlayerAvailable() ?? false) &&
                !widget.isLastOne
            ? EpisodePlayerCenterWidget(
                controller:
                    _playerCoreKey.currentState?.getPlayer() ?? Player(""))
            : const SizedBox.shrink(),
      ],
    );
  }
}

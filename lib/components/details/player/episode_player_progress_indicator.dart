import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/player/player.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/i18n/translations/de.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/track_event.dart';

import '../../../modules/details/details_controller.dart';
import 'episode_player_duration_widget.dart';
import 'episode_player_scrubber.dart';

class EpisodePlayerProgressIndicator extends StatefulWidget {
  const EpisodePlayerProgressIndicator(
    this.controller, {
    super.key,
    required this.allowScrubbing,
    required this.detailsController,
    this.onMoveStart,
    this.onMoveEnd,
  });

  /// The [Player] that actually associates a video with this
  /// widget.
  final Player controller;
  final bool allowScrubbing;
  final DetailsController detailsController;
  final Function? onMoveStart;
  final Function? onMoveEnd;

  @override
  State<EpisodePlayerProgressIndicator> createState() =>
      _PlayerProgressIndicatorState();
}

class _PlayerProgressIndicatorState
    extends State<EpisodePlayerProgressIndicator> {
  _PlayerProgressIndicatorState() {
    listener = () {
      if (!mounted) {
        return;
      }
      setState(() {});
    };
  }

  late VoidCallback listener;

  Player get controller => widget.controller;
  DetailsController get detailsController => widget.detailsController;

  bool isMove = false;

  OverlayState? overlayState;
  OverlayEntry? overlayEntry;

  @override
  void initState() {
    super.initState();
    overlayState = Overlay.of(context);
    controller.addListener(listener);
  }

  @override
  void deactivate() {
    controller.removeListener(listener);
    closeOverlay();
    super.deactivate();
  }

  void showOverlay() {
    if (overlayEntry != null) return;
    overlayEntry = OverlayEntry(
      builder: (context) {
        return Positioned(
          bottom: Get.mediaQuery.padding.bottom + 68.sp,
          child: Material(
            color: Colors.transparent,
            child: Container(
              width: Get.width,
              height: ScreenUtils.isLandscape(context)? 40.sp : 140.sp,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
              alignment: Alignment.center,
              child: EpisodePlayerDurationWidget(
                controller: controller,
                textStyle: TextStyle(
                  fontSize: 18.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        );
      },
    );
    overlayState?.insert(overlayEntry!);
  }

  void closeOverlay() {
    overlayEntry?.remove();
    overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    Widget progressIndicator;
    bool isLandscape = ScreenUtils.isLandscape(context);
    if (controller.value.isInitialized) {
      progressIndicator = SizedBox(
        width: Get.width,
        height: 24.sp,
        child: Stack(
          children: <Widget>[
            Center(
              child: SizedBox(
                height: isMove ? 6.sp : isLandscape? 4.sp : 2.sp,
                child: Stack(
                  fit: StackFit.passthrough,
                  children: [
                    // 播放进度
                    LinearProgressIndicator(
                      value: controller.value.playbackProgress,
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Colors.white),                    
                      backgroundColor: Colors.white.withValues(alpha: 0.4),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 4,
              left: isLandscape? (controller.value.playbackProgress * (Get.width - 122.sp)) - 4:
                  (controller.value.playbackProgress * (Get.width - 28.sp)) - 4,
              child: SizedBox(
                width: 8.sp,
                height: 16.sp,
                child: isMove
                    ? Container(
                        width: 8.sp,
                        height: 16.sp,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(29.r),
                        ),
                      )
                    : Center(
                        child: Container(
                          width: isLandscape? 8.sp : 4.sp,
                          height: isLandscape? 8.sp : 4.sp,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(180),
                          ),
                        ),
                      ),
              ),
            ),
          ],
        ),
      );
    } else {
      // 初始化占位进度条
      progressIndicator = SizedBox(
        width: Get.width,
        height: 24.sp,
        child: Center(
          child: SizedBox(
            height: 1.sp,
            child: const LinearProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              backgroundColor: Color.fromRGBO(255, 255, 255, 0.3),
            ),
          ),
        ),
      );
    }
    if (widget.allowScrubbing) {
      return EpisodePlayerScrubber(
        controller: controller,
        onMoveStart: () {
          useTrackEvent(TrackEvent.video_drag_start, extra: {
            TrackEvent.reel_id:
                detailsController.viewModel.currentDramaModel.shortPlayDetail.shortPlayCode.toString(),
                EventKey.scene: detailsController.isLandscapeScreen.value? EventValue.horizontal : EventValue.vertical,
          });
          isMove = true;
          widget.onMoveStart?.call();
          showOverlay();
          detailsController.playerBottomWidgetBackgroundColor.value = Colors.black.withValues(alpha: 0.7);                
          /// 使用全局loading会阻止用户事件
          // Get.loadingDot();
        },
        onMoveEnd: () {
          isMove = false;
          widget.onMoveEnd?.call();
          closeOverlay();
          detailsController.playerBottomWidgetBackgroundColor.value = Colors.transparent;
        },
        child: progressIndicator,
      );
    } else {
      return progressIndicator;
    }
  }
}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/common/player/player.dart';

class EpisodePlayerDurationWidget extends StatefulWidget {
  final Player controller;
  final TextStyle? textStyle;

  const EpisodePlayerDurationWidget(
      {super.key, required this.controller, this.textStyle});

  @override
  State<EpisodePlayerDurationWidget> createState() =>
      _PlayerDurationWidgetState();
}

class _PlayerDurationWidgetState extends State<EpisodePlayerDurationWidget> {
  late StreamController<Duration> _positionStreamController;
  bool _isInCriticalTimeRange = false;
  @override
  void initState() {
    super.initState();
    // 初始化 StreamController
    _positionStreamController = StreamController<Duration>.broadcast();
    // 监听 Stream
    widget.controller.addListener(listener);
  }

  void listener() {
    if (!_positionStreamController.isClosed) {
      final position = widget.controller.value.position;
      final duration = widget.controller.value.duration;

      if (duration.inSeconds > 1) {
        // 视频时长需大于1秒才有效
        _isInCriticalTimeRange = position.inSeconds <= 1 || // 开头1秒
            (duration.inSeconds - position.inSeconds) <= 2; // 结尾2秒
      } else {
        _isInCriticalTimeRange = false; // 短于1秒的视频不限制
      }

      widget.controller.isInCriticalTimeRange.value = _isInCriticalTimeRange;

      _positionStreamController.add(position);
    }
  }

  @override
  void dispose() {
    // 关闭 StreamController
    _positionStreamController.close();
    widget.controller.removeListener(listener);
    super.dispose();
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "$twoDigitMinutes:$twoDigitSeconds";
  }

  final textStyle = TextStyle(
    color: Colors.white,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Duration>(
      stream: _positionStreamController.stream,
      initialData: widget.controller.value.position,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox();
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              formatDuration(snapshot.data!),
              style: (widget.textStyle ?? textStyle).copyWith(
                fontFamily: 'monospace',
              ),
            ),
            SizedBox(
              width: 4.sp,
            ),
            Text(
              "/",
              style: widget.textStyle ?? textStyle,
            ),
            SizedBox(
              width: 4.sp,
            ),
            Text(
              formatDuration(widget.controller.value.duration),
              style: (widget.textStyle ?? textStyle).copyWith(
                fontFamily: 'monospace',
              ),
            ),
          ],
        );
      },
    );
  }
}

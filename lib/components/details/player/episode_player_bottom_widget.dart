import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/player/player.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/components/details/portrait_button_widget.dart';
import 'package:playlet/modules/details/details_controller.dart';

import 'episode_player_duration_widget.dart';
import 'episode_player_progress_indicator.dart';
import 'episode_player_speed_widget.dart';

class EpisodePlayerBottomWidget extends StatefulWidget {
  const EpisodePlayerBottomWidget({
    super.key,
    required this.controller,
    required this.detailsController,
    this.onMoveStart,
    this.onMoveEnd,
  });

  /// The [Player] that actually associates a video with this
  /// widget.
  final Player controller;
  final DetailsController detailsController;
  final Function? onMoveStart;
  final Function? onMoveEnd;

  @override
  State<EpisodePlayerBottomWidget> createState() =>
      _EpisodePlayerBottomWidgetState();
}

class _EpisodePlayerBottomWidgetState extends State<EpisodePlayerBottomWidget> {
  DetailsController get detailsController => widget.detailsController;
  @override
  Widget build(BuildContext context) {
    bool isLandscape = ScreenUtils.isLandscape(context);
    return Positioned(
      bottom: 0,
      child: Obx(() {
      return Container(
        width: Get.width,
        padding: EdgeInsets.only(
          left: isLandscape ? 61.sp : 14.sp,
          right: isLandscape ? 61.sp : 14.sp,
        ),
        height: 68.sp + Get.mediaQuery.padding.bottom,
        decoration: BoxDecoration(
          color: detailsController.playerBottomWidgetBackgroundColor.value,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                    padding: EdgeInsets.only(top: 13.sp),
                    child: EpisodePlayerDurationWidget(
                        controller: widget.controller)),
                Expanded(child: Container()),
                Padding(
                  padding: EdgeInsets.only(top: 12.sp),
                  child: EpisodePlayerSpeedWidget(
                    controller: widget.controller,
                    detailsController: detailsController,
                    onMoveStart: widget.onMoveStart,
                    onMoveEnd: widget.onMoveEnd,
                  ),
                ),
                Obx(() => Visibility(
                    visible: detailsController.isLandscapeScreen.value,
                    child: Row(children: [
                      SizedBox(width: 12.sp),
                      Padding(
                        padding: EdgeInsets.only(top: 12.sp),
                        child: PortraitButtonWidget(
                            onTap: detailsController.toggleLandscapeScreen),
                      )
                    ])))         
              ],
            ),
            EpisodePlayerProgressIndicator(
              widget.controller,
              allowScrubbing: true,
              detailsController: detailsController,
              onMoveStart: widget.onMoveStart,
              onMoveEnd: widget.onMoveEnd,
            ),
          ],
        ),
      
      );
    }),
  );
}
}
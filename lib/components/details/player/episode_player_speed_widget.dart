import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/common/player/player.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/utils/get_extension.dart';

class EpisodePlayerSpeedWidget extends StatefulWidget {
  const EpisodePlayerSpeedWidget({
    super.key,
    required this.controller,
    required this.detailsController,
    this.onMoveStart,
    this.onMoveEnd,
  });

  final Player controller;
  final DetailsController detailsController;
  final Function? onMoveStart;
  final Function? onMoveEnd;

  @override
  State<EpisodePlayerSpeedWidget> createState() => _PlayerSpeedWidgetState();
}

class _PlayerSpeedWidgetState extends State<EpisodePlayerSpeedWidget> {
  DetailsController get detailsController => widget.detailsController;

  Player get controller => widget.controller;

  double playbackSpeed = 1.0;

  String layerViewTag = "payer_speed_layer_view_tag";

  bool isShow = false;

  @override
  void initState() {
    playbackSpeed = detailsController.playbackSpeed.value;
    controller.setPlaybackSpeed(playbackSpeed);
    super.initState();
  }

  @override
  void dispose() {
    SmartDialog.dismiss(tag: layerViewTag);
    super.dispose();
  }

  void onShow() {
    final isLandscape = ScreenUtils.isLandscape(context);
    detailsController.playerBottomWidgetBackgroundColor.value = Colors.black.withValues(alpha: 0.7);
    SmartDialog.showAttach(
      targetContext: context,
      alignment: Alignment.topCenter,
      animationType: SmartAnimationType.fade,
      tag: layerViewTag,
      keepSingle: true,
      maskWidget: Column(
        children: [
          Expanded(
            child: Container(
              width: Get.width,
              height: double.infinity,
              color: Colors.transparent,
            ),
          ),
          Container(
            width: Get.width,
            height: 140.sp,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.7),
                ],
              ),
            ),
          ),
          Container(
            width: Get.width,
            height: Get.mediaQuery.padding.bottom + 68.sp,
            color: Colors.transparent,
          ),
        ],
      ),
      builder: (context) => Column(
        children: [
          SpeedLayerView(
            playbackSpeed: playbackSpeed,
            onPlaybackSpeedChange: (playbackSpeed) {
              detailsController.setPlaybackSpeed(playbackSpeed);
              setState(() {
                this.playbackSpeed = playbackSpeed;
                controller.setPlaybackSpeed(playbackSpeed);
                Get.toast(AppTrans.switchedToSpeedTip(playbackSpeed));
                onClose();
              });
            },
          ),
          SizedBox(
              height:
                  isLandscape ? 7.sp : 48.sp),
        ],
      ),
      onDismiss: () {
        onClose();
      },
    );
    isShow = true;
    widget.onMoveStart?.call();
  }

  void onClose() {
    detailsController.playerBottomWidgetBackgroundColor.value = Colors.transparent;
    SmartDialog.dismiss(tag: layerViewTag);
    isShow = false;
    widget.onMoveEnd?.call();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (isShow) {
          onClose();
        } else {
          onShow();
        }
      },
      child: Container(
        height: 24.sp,
        padding: EdgeInsets.symmetric(horizontal: 10.sp),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12.sp),
        ),
        child: Center(
          child: Text(
            "${playbackSpeed}X",
            style: TextStyle(
              color: Colors.white,
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}

class SpeedLayerView extends StatefulWidget {
  const SpeedLayerView({
    super.key,
    required this.playbackSpeed,
    required this.onPlaybackSpeedChange,
  });

  final double playbackSpeed;

  final Function(double playbackSpeed) onPlaybackSpeedChange;

  @override
  State<SpeedLayerView> createState() => _SpeedLayerViewState();
}

class _SpeedLayerViewState extends State<SpeedLayerView> {
  List<double> speeds = [1.0, 1.25, 1.75, 2.0];

  double playbackSpeed = 1.0;

  @override
  void initState() {
    playbackSpeed = widget.playbackSpeed;
    super.initState();
  }

  BoxDecoration unItemBoxDecoration = BoxDecoration(
    color: Colors.white.withValues(alpha: 0.2),
    borderRadius: BorderRadius.all(
      Radius.circular(100.r),
    ),
  );

  BoxDecoration selectItemBoxDecoration = BoxDecoration(
    color: const Color(0xFFFFC001).withValues(alpha: 0.25),
    border: Border.all(
      color: const Color(0xFFFFC001),
      width: 2.sp,
    ),
    borderRadius: BorderRadius.all(
      Radius.circular(100.r),
    ),
  );

  @override
  Widget build(BuildContext context) {
    final isLandscape = ScreenUtils.isLandscape(context);
    return Center(
      child: Container(
        width: isLandscape ? 360.sp : Get.width,
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: speeds.map((e) => _buildSpeedItem(e)).toList(),
        ),
      ),
    );
  }

  Widget _buildSpeedItem(double speed) {
    return InkWell(
      onTap: () {
        setState(() {
          playbackSpeed = speed;
          widget.onPlaybackSpeedChange(speed);
        });
      },
      child: Container(
        width: 74.sp,
        height: 42.sp,
        decoration: playbackSpeed == speed
            ? selectItemBoxDecoration
            : unItemBoxDecoration,
        alignment: Alignment.center,
        child: Text(
          "${speed}X",
          style: TextStyle(
            color:
                playbackSpeed == speed ? const Color(0xFFFFC001) : Colors.white,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}

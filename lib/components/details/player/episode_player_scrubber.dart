import 'package:flutter/material.dart';
import 'package:playlet/common/player/player.dart';

/// A scrubber to control [VideoPlayerController]s
class EpisodePlayerScrubber extends StatefulWidget {
  /// Create a [PlayerScrubber] handler with the given [child].
  ///
  /// [controller] is the [Player] that will be controlled by
  /// this scrubber.
  const EpisodePlayerScrubber({
    super.key,
    required this.child,
    required this.controller,
    required this.onMoveStart,
    required this.onMoveEnd,
  });

  /// The widget that will be displayed inside the gesture detector.
  final Widget child;

  /// The [Player] that will be controlled by this scrubber.
  final Player controller;

  final Function onMoveStart;
  final Function onMoveEnd;

  @override
  State<EpisodePlayerScrubber> createState() => _PlayerScrubberState();
}

class _PlayerScrubberState extends State<EpisodePlayerScrubber> {
  bool _controllerWasPlaying = false;

  Player get controller => widget.controller;

  @override
  Widget build(BuildContext context) {
    void seekToRelativePosition(Offset globalPosition) {
      final RenderBox box = context.findRenderObject()! as RenderBox;
      final Offset tapPos = box.globalToLocal(globalPosition);
      final double relative = tapPos.dx / box.size.width;
      final Duration position = controller.value.duration * relative;
      controller.seekTo(position);
    }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      child: widget.child,
      onHorizontalDragStart: (DragStartDetails details) {
        if (!controller.value.isInitialized) {
          return;
        }
        _controllerWasPlaying = controller.value.isPlaying;
        if (_controllerWasPlaying) {
          controller.pause();
        }
      },
      onHorizontalDragUpdate: (DragUpdateDetails details) {
        if (!controller.value.isInitialized) {
          return;
        }
        widget.onMoveStart();
        seekToRelativePosition(details.globalPosition);
      },
      onHorizontalDragEnd: (DragEndDetails details) {
        if (_controllerWasPlaying &&
            controller.value.position != controller.value.duration) {
          controller.play();
        }
        widget.onMoveEnd();
      },
      onTapDown: (TapDownDetails details) {
        if (!controller.value.isInitialized) {
          return;
        }
        widget.onMoveStart();
        seekToRelativePosition(details.globalPosition);
      },
      onTapUp: (details) {
        widget.onMoveEnd();
      },
      onTapCancel: () {
        widget.onMoveEnd();
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart' hide Trans;
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/model/upgrade/app_upgrade_result.dart';
import 'package:playlet/utils/open_store.dart';
import 'package:playlet/utils/track_event.dart';

class AppUpgradeDialogWidget extends StatefulWidget {
  const AppUpgradeDialogWidget({super.key, required this.upgrade});

  final AppUpgradeResult upgrade;

  @override
  State<AppUpgradeDialogWidget> createState() => _AppUpgradeDialogWidgetState();
}

class _AppUpgradeDialogWidgetState extends State<AppUpgradeDialogWidget> {
  final ScrollController _scrollController = ScrollController();
  void _trackUpdateEvent(String action) {
    useTrackEvent(EventName.update_click, extra: {
      EventKey.type: widget.upgrade.update == AppUpgradeType.force
          ? EventValue.mandatory_updates
          : EventValue.recommended_updates,
      EventKey.action: action
    });
  }

   @override
  void dispose() {
    _scrollController.dispose(); // 销毁控制器防止内存泄漏
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Stack(
          children: [
            Column(
              children: [
                Container(
                  width: 300.sp,
                  constraints: BoxConstraints(
                    minHeight: 270.sp,
                  ),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(37, 37, 46, 1),
                    borderRadius: BorderRadius.circular(12.r),
                    image: DecorationImage(
                      image: Assets.upgrade.upgradeTopBg.provider(),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Stack(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          left: 20.sp,
                          right: 20.sp,
                          bottom: 20.sp,
                        ),
                        child: Center(
                            child: Column(
                          children: [
                            SizedBox(height: 119.sp),
                            Text(
                              AppTrans.appVersionUpgradeTitle(),
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                decoration: TextDecoration.none,
                                height: 1.5,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            Container(
                              constraints: BoxConstraints(
                                maxHeight: 162.sp,
                              ),
                              padding: EdgeInsets.symmetric(
                                vertical: 15.sp,
                              ),
                              child: Scrollbar(
                                thumbVisibility: true,
                                controller: _scrollController,
                                child: SingleChildScrollView(
                                  controller: _scrollController, 
                                  scrollDirection: Axis.vertical,
                                  child: Text(
                                    textAlign: TextAlign.start,                                    
                                    widget.upgrade.updateRemark ?? "",
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: const Color(0xFF999999),
                                      fontWeight: FontWeight.w400,
                                      decoration: TextDecoration.none,
                                      height: 1.8,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 5.sp),
                            GestureDetector(
                              onTap: () {
                                _trackUpdateEvent(EventValue.update);
                                OpenStore.instance.openUrl(
                                  widget.upgrade.storeLink ?? "",
                                );
                              },
                              child: Container(
                                width: 196.sp,
                                height: 40.sp,
                                decoration: BoxDecoration(
                                  color: const Color.fromRGBO(255, 69, 0, 1),
                                  borderRadius: BorderRadius.circular(6.r),
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  AppTrans.update(),
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    decoration: TextDecoration.none,
                                  ),
                                ),
                              ),
                            ),
                            if (widget.upgrade.update == AppUpgradeType.suggest)
                              GestureDetector(
                                onTap: () {
                                  _trackUpdateEvent(EventValue.cancel);
                                  SmartDialog.dismiss();
                                },
                                child: Padding(
                                  padding: EdgeInsets.only(top: 10.sp),
                                  child: SizedBox(
                                    height: 22.sp,
                                    child: Center(
                                      child: Text(
                                        AppTrans.later(),
                                        style: TextStyle(
                                            fontSize: 16.sp,
                                            color: const Color.fromRGBO(
                                                130, 130, 130, 1),
                                            fontWeight: FontWeight.w500,
                                            decoration: TextDecoration.none,
                                            height: 1.38),
                                      ),
                                    ),
                                  ),
                                ),
                              )
                          ],
                        )),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Center(
                child: Assets.upgrade.upgradeIcon.image(
                  width: 118.sp,
                  height: 118.sp,
                ),
              ),
            ),
            if (widget.upgrade.update == AppUpgradeType.suggest)
              Positioned(
                top: 15.sp,
                right: 15.sp,
                child: GestureDetector(
                  onTap: () {
                    _trackUpdateEvent(EventValue.cancel);
                    SmartDialog.dismiss();
                  },
                  child: Container(
                    padding: EdgeInsets.all(4.sp),
                    child: Assets.dialogGrayClose.image(
                      width: 24.sp,
                      height: 24.sp,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }
}

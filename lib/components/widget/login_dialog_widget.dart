import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart' hide Trans;
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/components/ui/login_buttom.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/auth.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/index.dart';
import 'package:playlet/utils/track_event.dart';

class LoginDialogWidget extends StatefulWidget {
  const LoginDialogWidget({
    super.key,
    required this.trackScene,
    required this.fbRewardsTrackFrom,
  });
  final String trackScene;
  final String fbRewardsTrackFrom;

  @override
  State<LoginDialogWidget> createState() => _LoginDialogWidgetState();
}

class _LoginDialogWidgetState extends State<LoginDialogWidget> {
  void _trackLoginClickEvent(String type) {
    useTrackEvent(EventName.login_click, extra: {
      EventKey.scene: widget.trackScene,
      EventKey.type: type,
    });
  }

  void onGoogleLogin() async {
    _trackLoginClickEvent(EventValue.google);
    Get.loading();
    bool isSuccess = await Auth.signInWithGoogle(widget.trackScene);
    Get.dismiss();
    if (isSuccess) {
      SmartDialog.dismiss();
    }
  }

  void onAppleLogin() async {
    _trackLoginClickEvent(EventValue.apple);
    Get.loading();
    bool isSuccess = await Auth.signInWithApple(widget.trackScene);
    Get.dismiss();
    if (isSuccess) {
      SmartDialog.dismiss();
    }
  }

  void onFacebookLogin() async {
    _trackLoginClickEvent(EventValue.facebook);
    Get.loading();
    bool isSuccess = await Auth.signInWithFacebook(
      widget.trackScene,
      fbRewardsTrackFrom: widget.fbRewardsTrackFrom,
    );
    Get.dismiss();
    if (isSuccess) {
      SmartDialog.dismiss();
    }
  }

  @override
  Widget build(BuildContext context) {
    final height = Platform.isAndroid ? 402.sp : 464.sp;
    return Container(
      width: Get.width,
      height: height,
      decoration: BoxDecoration(
        color: const Color.fromRGBO(30, 30, 30, 1),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Column(
            children: [
              _buildTop(),
              _buildLogins(),
              _buildAgreement(),
            ],
          ),
          Positioned(
            top: 6.sp,
            right: 6.sp,
            child: IconButton(
              onPressed: () => SmartDialog.dismiss(),
              icon: Assets.dialogGrayClose.image(
                width: 24.sp,
                height: 24.sp,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildLogins() {
    if (Platform.isIOS) {
      return Column(
        children: [
          SizedBox(height: 18.sp),
          _buildAppleLoginButton(),
          SizedBox(height: 18.sp),
          _buildGoogleLoginButton(),
          SizedBox(height: 12.sp),
          _buildLoginGift(_buildFacebookLoginButton()),
        ],
      );
    }
    return Column(
      children: [
        SizedBox(height: 18.sp),
        _buildLoginGift(_buildFacebookLoginButton()),
        SizedBox(height: 18.sp),
        _buildGoogleLoginButton(),
      ],
    );
  }

  Widget _buildLoginGift(Widget child) {
    return Stack(
      children: [
        Column(
          children: [
            SizedBox(height: 19.sp),
            child,
          ],
        ),
        Positioned(
          top: 0,
          right: 0,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 5.sp),
            height: 26.sp,
            decoration: BoxDecoration(
              color: const Color.fromRGBO(255, 69, 0, 1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(20.r),
                bottomLeft: Radius.circular(1.r),
                bottomRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 添加图标
                Assets.login.iconBonus.image(
                  width: 16.sp,
                  height: 16.sp,
                ),
                SizedBox(width: 4.sp), // 添加图标和文本之间的间距
                Text(
                  AppTrans.signInReward(Get.find<UserService>().metaLoginBonus),
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 12.sp,
                    fontFamily: 'Poppins',
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAppleLoginButton() {
    return LoginButtom(
      width: 312.sp,
      title: AppTrans.signInApple(),
      image: Assets.login.appleBlack.path,
      backgroundColor: Colors.white,
      textColor: Colors.black,
      border: false,
      onTap: onAppleLogin,
    );
  }

  Widget _buildFacebookLoginButton() {
    return LoginButtom(
      width: 312.sp,
      title: AppTrans.signInFacebook(),
      image: Assets.login.facebookWhiteIcon.path,
      backgroundColor: const Color.fromRGBO(62, 103, 181, 1),
      onTap: onFacebookLogin,
    );
  }

  // ignore: unused_element
  Widget _buildGoogleLoginButton() {
    return LoginButtom(
      width: 312.sp,
      title: AppTrans.signInGoogle(),
      image: Assets.login.googleIcon.path,
      backgroundColor: const Color.fromRGBO(85, 85, 85, 1),
      onTap: onGoogleLogin,
    );
  }

  Widget _buildTop() {
    return Column(
      children: [
        SizedBox(height: 25.sp),
        Assets.logo.image(
          width: 56.sp,
          height: 56.sp,
        ),
        SizedBox(height: 8.sp),
        Text(
          Config.appName,
          style: TextStyle(
            fontSize: 24.sp,
            color: Colors.white,
            fontWeight: FontWeight.w600,
            decoration: TextDecoration.none,
          ),
        ),
        SizedBox(height: 8.sp),
        Text(
          AppTrans.signInWelcome(Config.appName),
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildAgreement() {
    return Container(
      width: 313.sp,
      padding: EdgeInsets.only(bottom: 10.sp, top: 26.sp),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          children: [
            TextSpan(
              text: AppTrans.signInTips(),
              style: TextStyle(
                color: const Color(0xFF828282),
                fontSize: 12.sp,
                // fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
                height: 1.5,
              ),
            ),
            TextSpan(
              text: ' ',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
            TextSpan(
              text: AppTrans.userAgreement(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                height: 1.5,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Utils.openUserAgreement();
                },
            ),
            TextSpan(
              text: ' ',
              style: TextStyle(
                fontSize: 12.sp,
              ),
            ),
            TextSpan(
              text: AppTrans.signInAnd(),
              style: TextStyle(
                color: const Color(0xFF828282),
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                height: 1.5,
              ),
            ),
            TextSpan(
              text: ' ',
              style: TextStyle(
                fontSize: 12.sp,
              ),
            ),
            TextSpan(
              text: AppTrans.privacyPolicy(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                height: 1.5,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Utils.openPrivacyPolicy();
                },
            ),
          ],
        ),
      ),
    );
  }
}

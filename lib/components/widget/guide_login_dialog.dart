import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/theme/theme.dart';

class GuideLoginDialog extends StatefulWidget {
  const GuideLoginDialog({super.key});

  @override
  State<GuideLoginDialog> createState() => _GuideLoginDialogState();
}

class _GuideLoginDialogState extends State<GuideLoginDialog> {
  static const _whiteColor = Colors.white;
  static const _orangeColor = Color.fromRGBO(255, 69, 0, 1);
  static final _shadow = Shadow(
    offset: const Offset(0, 4),
    blurRadius: 4,
    color: const Color(0xFF000000).withOpacity(0.20),
  );
  @override
  Widget build(BuildContext context) {
    return Theme(
      data: AppTheme.theme,
      child: Center(
        child: SizedBox(
          width: 300.sp,
          height: 423.sp,
          child: Stack(
            children: [
              Positioned.fill(
                child: Assets.login.loginGuideBg.image( 
                  fit: BoxFit.cover,
                )
              ),
              _buildContent(),
              _buildClose(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildClose() {
    return Positioned(
      top: 128.sp,
      right: 6.sp,
      child: IconButton(
        onPressed: () => Get.back(),
        icon: Assets.dialogGrayClose.image(
          width: 24.sp,
          height: 24.sp,
        ),     
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 153.sp),
        Padding(
          padding: EdgeInsets.only(left: 20.sp),
          child: Text(
            "login.page.label".tr,
            textAlign: TextAlign.left,
            style: TextStyle(
              color: _whiteColor,
              fontSize: 22.sp,
              fontWeight: FontWeight.w700,
              decoration: TextDecoration.none,
              height: 1.50,
              shadows: [_shadow],
            ),
          ),
        ),
        SizedBox(height: 6.sp),
        Padding(
          padding: EdgeInsets.only(left: 20.sp),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              _buildText("+", 32.sp),
              _buildText(" 50", 32.sp),
              SizedBox(width: 5.sp),
              _buildText("Bonus", 20.sp),
            ],
          ),
        ),
        Center(
          child: GestureDetector(
            onTap: () => Get.back<bool>(result: true),
            child: Container(
              margin: EdgeInsets.only(top: 110.sp),
              width: 230.sp,
              height: 40.sp,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(6.r)),
                color: _orangeColor,
              ),
              alignment: Alignment.center,
              child: Text(
                "signInToGet".tr,
                style: TextStyle(
                  color: _whiteColor,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  decoration: TextDecoration.none,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }


  Widget _buildText(String text, double fontSize) {
    return Text(
      text,
      style: TextStyle(
        color: _whiteColor,
        fontSize: fontSize,
        fontWeight: FontWeight.w800,
        decoration: TextDecoration.none,
        fontStyle: FontStyle.italic,
        height: 1.50,
        shadows: [_shadow],
      ),
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/utils/file_storage.dart';
import 'package:playlet/utils/index.dart';

class CoverWidget extends StatelessWidget {
  const CoverWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit,
    this.compress = true,
    this.placeholder,
    this.errorWidget,
    this.imageBuilder,
    this.scale = 1.5,
    this.filterQuality = FilterQuality.medium,
  });

  final String imageUrl;

  final double? width;
  final double? height;
  final double scale;
  final BoxFit? fit;
  final bool compress; // 是否压缩
  final FilterQuality filterQuality; // 图片过滤质量
  final Widget Function(BuildContext, String)? placeholder;
  final Widget Function(BuildContext, String, Object)? errorWidget;
  final Widget Function(BuildContext, ImageProvider<Object>)? imageBuilder;

  @override
  Widget build(BuildContext context) {
    final url = compress ? Utils.getImageRatioScale(
      imageUrl,
      width: width,
      height: height,
      scale: scale,
    ) : imageUrl;
    // 生成自定义缓存键
    final cacheKey = FileStorage.getImageCacheKey(url);
    Get.log("cacheKey: $cacheKey");
    Get.log("CoverWidget url: $url");
    return CachedNetworkImage(
      imageUrl: url,
      cacheKey: cacheKey,
      width: width,
      height: height,
      fit: fit,
      filterQuality: filterQuality,
      placeholder: (context, url) {
        if (placeholder != null) {
          return placeholder!(context, url);
        } else {
          return Container(
            width: width,
            height: height,
            color: const Color(0xFF222222),
          );
        }
      },
      errorListener: (value) {
        FFLog.error(value, tag: "CoverWidget");
      },
      errorWidget: (context, url, error) {
        if (errorWidget != null) {
          return errorWidget!(context, url, error);
        } else {
          return Container(
            width: width,
            height: height,
            color: const Color(0xFF222222),
          );
        }
      },
      imageBuilder: imageBuilder,
    );
  }
}

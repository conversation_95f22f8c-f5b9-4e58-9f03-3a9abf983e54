import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/utils/get_extension.dart';

class GoodReviewPopup extends StatefulWidget {
  const GoodReviewPopup({super.key});

  static const String tag = 'good_review_popup_tag';

  @override
  State<GoodReviewPopup> createState() => _GoodReviewPopupState();
}

class _GoodReviewPopupState extends State<GoodReviewPopup> {
  int rateValue = 5;

  void onSubmit() async {
    if (rateValue < 5) {
      Get.toast(AppTrans.goodReviewTig());
      onClose();
      return;
    }
    // 打开商店评价
    final InAppReview inAppReview = InAppReview.instance;
    if (Platform.isAndroid) {
      if (await inAppReview.isAvailable()) {
        await inAppReview.requestReview();
      }
    } else if (Platform.isIOS) {
      await inAppReview.openStoreListing(appStoreId: Config.appStoreId);
    }
    onClose();
  }

  void onClose() {
    SmartDialog.dismiss(tag: GoodReviewPopup.tag);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      height: 402.sp,
      decoration: BoxDecoration(
        color: const Color.fromRGBO(30, 30, 30, 1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.sp),
          topRight: Radius.circular(20.sp),
        ),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              SizedBox(height: 23.sp),
              Assets.goodReview.goold.image(
                width: 150.sp,
                height: 145.sp,
              ),
              SizedBox(
                width: 231.sp,
                child: Text(
                  AppTrans.goodReviewLabel(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 12.sp),
              Rate(
                value: 5,
                onChange: (value) {
                  rateValue = value.toInt();
                  Get.log("rateValue: $rateValue");
                },
              ),
              SizedBox(height: 28.sp),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: onClose,
                    behavior: HitTestBehavior.translucent,
                    child: Container(
                      width: 112.sp,
                      height: 38.sp,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: const Color.fromRGBO(217, 217, 217, 1),
                          width: 1.sp,
                        ),
                        borderRadius: BorderRadius.circular(4.sp),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        // "Cancel",
                        AppTrans.cancel(),
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 24.sp),
                  GestureDetector(
                    onTap: onSubmit,
                    behavior: HitTestBehavior.translucent,
                    child: Container(
                      width: 112.sp,
                      height: 38.sp,
                      decoration: BoxDecoration(
                        color: const Color.fromRGBO(255, 69, 0, 1),
                        borderRadius: BorderRadius.circular(4.sp),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        // "Submit",
                        AppTrans.submit(),
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
          Positioned(
            top: 18.sp,
            right: 18.sp,
            child: InkWell(
              onTap: onClose,
              child: Container(
                width: 24.sp,
                height: 24.sp,
                decoration: BoxDecoration(
                  color: const Color(0XFF464646),
                  borderRadius: BorderRadius.all(
                    Radius.circular(16.r),
                  ),
                ),
                padding: EdgeInsets.all(3.sp),
                child: Assets.video.close.image(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 评分组件
class Rate extends StatefulWidget {
  const Rate({
    super.key,
    this.allowHalf = false,
    this.color,
    this.count = 5,
    this.gap,
    this.icon,
    this.showText = false,
    this.size = 24.0,
    this.textWidth = 48.0,
    this.builderText,
    this.value = 0,
    this.onChange,
    this.direction = Axis.horizontal,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.min,
    this.iconTextGap,
  });

  /// 是否允许半选
  final bool? allowHalf;

  /// 评分图标的颜色，示例：[选中颜色] / [选中颜色，未选中颜色]，默认：[TDTheme.of(context).warningColor5, TDTheme.of(context).grayColor4]
  final List<Color>? color;

  /// 评分的数量
  final int? count;

  /// 评分图标的间距，默认：TDTheme.of(context).spacer8
  final double? gap;

  /// 自定义评分图标，[选中和未选中图标] / [选中图标，未选中图标]，默认：[TDIcons.star_filled]
  final List<IconData>? icon;

  /// 是否显示对应的辅助文字
  final bool? showText;

  /// 评分图标的大小
  final double? size;

  /// 评分等级对应的辅助文字宽度
  final double? textWidth;

  /// 评分等级对应的辅助文字自定义构建，优先级高于[texts]
  /// 配置后，会忽略[texts],[textWidth],[iconTextGap]
  final Widget Function(BuildContext context, double value)? builderText;

  /// 选择评分的值
  final double? value;

  /// 评分数改变时触发
  final void Function(double value)? onChange;

  /// 评分图标与辅助文字的布局方向
  final Axis? direction;

  /// 评分图标与辅助文字的主轴对齐方式
  final MainAxisAlignment? mainAxisAlignment;

  /// 评分图标与辅助文字的交叉轴对齐方式
  final CrossAxisAlignment? crossAxisAlignment;

  /// 评分图标与辅助文字主轴方向上如何占用空间
  final MainAxisSize? mainAxisSize;

  /// 评分图标与辅助文字的间距，默认：[TDTheme.of(context).spacer16]
  final double? iconTextGap;

  @override
  State<Rate> createState() => _TDRateState();
}

class _TDRateState extends State<Rate> with TickerProviderStateMixin {
  /// 节流
  final _throttle = Throttle(delay: const Duration(milliseconds: 100));

  /// 当前选中的评分值
  late double _activeValue;

  late Map<double, GlobalKey> _globalKeys;

  /// 动画
  late List<AnimationController> _controller;
  late List<Animation<double>> _animation;

  /// 每个评分(Row)的Size:<索引, Size>
  final _rateSize = <int, Size>{};

  /// 每个评分(Row)的Offset:<索引, Offset>
  final _rateOffset = <int, Offset>{};

  /// 根据评分值，获取所在评分的索引
  int _index([double? value]) => ((value ?? _activeValue) - 0.5).floor();

  @override
  void initState() {
    super.initState();
    _activeValue = widget.value ?? 0;
    _globalKeys =
        List.generate((widget.count ?? 5) * 2, (index) => index / 2 + 0.5)
            .asMap()
            .map((index, e) => MapEntry(e, GlobalKey()));
    _controller = List.generate(
        widget.count ?? 5,
        ((index) => AnimationController(
              duration: const Duration(milliseconds: 300),
              vsync: this,
            )));
    _animation = List.generate(
        widget.count ?? 5,
        ((index) =>
            Tween<double>(begin: 1.0, end: 1.33).animate(_controller[index])));
  }

  @override
  void didUpdateWidget(Rate oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value) {
      _activeValue = widget.value ?? 0;
    }
    if (widget.count != oldWidget.count) {
      _globalKeys =
          List.generate((widget.count ?? 5) * 2, (index) => index / 2 + 0.5)
              .asMap()
              .map((index, e) => MapEntry(e, GlobalKey()));
    }
  }

  @override
  void dispose() {
    for (var controller in _controller) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Flex(
      direction: widget.direction ?? Axis.horizontal,
      mainAxisAlignment: widget.mainAxisAlignment ?? MainAxisAlignment.start,
      crossAxisAlignment:
          widget.crossAxisAlignment ?? CrossAxisAlignment.center,
      mainAxisSize: widget.mainAxisSize ?? MainAxisSize.min,
      children: [
        GestureDetector(
          onTapUp: (details) {
            _changeSelect(details.globalPosition, true);
          },
          onHorizontalDragUpdate: (details) {
            _changeSelect(details.globalPosition);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(widget.count ?? 5, (index) {
              final isLast = index == (widget.count ?? 5) - 1;
              return Padding(
                padding: EdgeInsets.only(right: isLast ? 0 : widget.gap ?? 8),
                child: AnimatedBuilder(
                  animation: _animation[index],
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _animation[index].value,
                      child: child,
                    );
                  },
                  child: Center(
                    key: _globalKeys[index + 1.0],
                    child: _getIconActive(value: index + 1.0)
                        ? Assets.goodReview.statAcctive.image(
                            width: 30.45.sp,
                            height: 31.82.sp,
                          )
                        : Assets.goodReview.star.image(
                            width: 30.45.sp,
                            height: 31.82.sp,
                          ),
                  ),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  bool _getIconActive({double? value, bool? isActive}) {
    return (value != null && _activeValue >= value) ||
        (isActive != null && isActive);
  }

  void _changeSelect(Offset globalPosition, [bool? isTap]) {
    _throttle.call(() {
      final newIndex = _fingerInsideContainer(globalPosition);
      if (newIndex == null) {
        return;
      }
      final diff = newIndex != _activeValue;
      if (diff || isTap == true) {
        _activeValue = newIndex;
        _forward();
        if (diff) {
          setState(() {});
          widget.onChange?.call(newIndex);
        }
      }
    });
  }

  void _forward() {
    for (var element in _controller) {
      element.reverse();
    }
  }

  double? _fingerInsideContainer(Offset globalPosition) {
    final rateBox = context.findRenderObject() as RenderBox?;
    if (rateBox == null) {
      return null;
    }
    final rateOffset = rateBox.localToGlobal(Offset.zero);
    if (globalPosition.dx < rateOffset.dx) {
      return 0;
    }
    for (var entry in _globalKeys.entries) {
      final renderBox =
          entry.value.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final localPosition = renderBox.globalToLocal(globalPosition);
        final isIn =
            renderBox.hitTest(BoxHitTestResult(), position: localPosition);
        if (isIn) {
          var value = widget.allowHalf == true
              ? entry.key
              : entry.key.ceil().toDouble();
          var index = _index(value);
          if (!_rateSize.containsKey(index) ||
              !_rateOffset.containsKey(index)) {
            final parentRenderBox = renderBox.parent as RenderBox;
            _rateSize[index] = parentRenderBox.size;
            _rateOffset[index] = parentRenderBox.localToGlobal(Offset.zero);
          }
          return value;
        }
      }
    }

    return null;
  }
}

class Throttle {
  Duration delay;
  Timer? _timer;

  Throttle({required this.delay});

  void call(VoidCallback callback) {
    if (_timer?.isActive ?? false) {
      // 如果 _timer 正在运行，则不执行任何操作
      return;
    }

    // 创建一个新的计时器
    _timer = Timer(delay, () {
      callback();
      _timer = null; // 计时器执行完毕后，将其置为 null
    });
  }
}

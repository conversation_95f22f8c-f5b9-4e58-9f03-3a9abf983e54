import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/rewards/check_in_list_result.dart';

import '../../gen/assets.gen.dart';

// 已领取
final Decoration rewardsDecoration = BoxDecoration(
  color: const Color(0xFF303030),
  borderRadius: BorderRadius.circular(196.r),
);

// 已领取 文字颜色
final TextStyle rewardsTextStyle = TextStyle(
  fontSize: 14.sp,
  fontWeight: FontWeight.w500,
  color: const Color.fromRGBO(153, 153, 153, 1),
);

// 今天
final Decoration rewardsTodayDecoration = BoxDecoration(
  color: const Color(0xFF303030),
  borderRadius: BorderRadius.circular(196.r),
);

final TextStyle rewardsTodayTextStyle = TextStyle(
  fontSize: 10.sp,
  fontWeight: FontWeight.w400,
  color: Colors.white,
);

// 未领取
final Decoration rewardsUnDecoration = BoxDecoration(
  color: const Color(0xFF303030),
  borderRadius: BorderRadius.circular(196.r),
);

final TextStyle rewardsUnTextStyle = TextStyle(
  fontSize: 14.sp,
  fontWeight: FontWeight.w500,
  color: const Color.fromRGBO(255, 205, 0, 1),
);

final TextStyle labelTextStyle = TextStyle(
  fontSize: 10.sp,
  fontWeight: FontWeight.w500,
  color: const Color.fromRGBO(0, 0, 0, 0.38),
);

class RewardsItemWidget extends StatelessWidget {
  final SignRecords? signRecord;

  const RewardsItemWidget(
    this.signRecord, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // 假设 signRecord 不为 null 时，根据其属性来决定样式
    bool isToday = signRecord?.isToday ?? false;
    bool isSign = signRecord?.isSign ?? false;
    String dayText = "${AppTrans.day()}${signRecord?.day}";
    String pointsText = "+${signRecord?.bonus}";

    return Container(
      decoration: isToday
          ? rewardsTodayDecoration
          : (isSign ? rewardsDecoration : rewardsUnDecoration),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 8.sp),
          Text(
            pointsText,
            style: isToday
                ? rewardsUnTextStyle
                : (isSign ? rewardsTextStyle : rewardsUnTextStyle),
          ),
          SizedBox(height: (isToday && isSign) ? 4.sp : 2.sp),
          getImage(isToday, isSign),
          SizedBox(height: (isToday && isSign) ? 4.sp : 2.sp),
          Text(
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            isToday ? AppTrans.today() : dayText,
            style: isToday
                ? rewardsTodayTextStyle
                : TextStyle(
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF999999),
                  ),
          ),
          SizedBox(height: 8.sp),
        ],
      ),
    );
  }

  Widget getImage(bool isToday, bool isSign) {
    if (isToday && isSign) {
      return Assets.rewards.imgRewardsSelect.image(width: 24.r, height: 24.r);
    } else if (isSign) {
      return Opacity(
          opacity: 0.5,
          child: Assets.imgCoinB.image(width: 28.r, height: 28.r));
    } else {
      return Assets.imgCoinB.image(
        width: 28.r,
        height: 28.r,
      );
    }
  }
}

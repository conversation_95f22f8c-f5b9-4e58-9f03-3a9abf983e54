import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';

// 开始按钮组件
class StartTaskButton extends StatelessWidget {
  final VoidCallback onPressed;

  const StartTaskButton({
    Key? key,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFFCD00),
          borderRadius: BorderRadius.circular(41.r),
        ),
        padding: EdgeInsets.fromLTRB(19.w, 5.h, 19.w, 5.h),
        child: Text(
          AppTrans.go(),
          style: TextStyle(
            color: Colors.black,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}



// 开始按钮组件
class NoStartTaskButton extends StatelessWidget {
  final VoidCallback onPressed;

  const NoStartTaskButton({
    Key? key,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey,
          borderRadius: BorderRadius.circular(41.r),
        ),
        padding: EdgeInsets.fromLTRB(19.w, 5.h, 19.w, 5.h),
        child: Text(
          AppTrans.go(),
          style: TextStyle(
            color: Colors.black,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}


// 领取奖励按钮组件
class ReceiveTaskButton extends StatelessWidget {
  final VoidCallback onPressed;

  const ReceiveTaskButton({
    Key? key,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFF4500),
          borderRadius: BorderRadius.circular(41.r),
        ),
        padding: EdgeInsets.fromLTRB(19.w, 5.h, 19.w, 5.h),
        child: Text(
          AppTrans.notificationReceive(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

// 完成按钮组件
class CompleteTaskButton extends StatelessWidget {
  final VoidCallback onPressed;

  const CompleteTaskButton({
    Key? key,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Padding(
        padding: EdgeInsets.only(right: 10.w),
        child: IgnorePointer(
          child: Assets.rewards.imgTaskSelect.image(width: 30.r, height: 30.r),
        ),
      ),
    );
  }
}
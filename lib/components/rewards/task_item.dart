import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/components/rewards/task_item_button.dart';
import 'package:playlet/gen/assets.gen.dart';

import '../../i18n/trans.dart';
import '../../model/rewards/task_type_model.dart';

class ADTaskItem extends StatelessWidget {
  final Widget? image;
  final String bonusText;
  String taskText;
  final String progressText;
  String descriptionText;
  final int initialState;
  final bool showLine;
  final VoidCallback onButtonPressed; // 新增回调参数

  ADTaskItem({
    super.key,
    this.image,
    this.bonusText = '+10',
    this.taskText = 'Bonus',
    this.progressText = '(0/10)',
    this.descriptionText = "",
    this.initialState = 1,
    this.showLine = true,
    required this.onButtonPressed, // 新增回调参数
  }) {
    taskText = taskText.isEmpty ? AppTrans.bonus() : taskText;
    descriptionText =
        descriptionText.isEmpty ? AppTrans.rewardsWatch() : descriptionText;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 12.r, right: 12.r, top: 12.r),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      image ??
                          Assets.imgCoinB.image(
                            width: 28.r,
                            height: 28.r,
                            fit: BoxFit.fill,
                          ),
                      SizedBox(
                        width: 8.w,
                      ),
                      Text(
                        bonusText,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFFFFCD00),
                        ),
                      ),
                      SizedBox(
                        width: 4.w,
                      ),
                      Text(
                        taskText,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(
                        width: 14.w,
                      ),
                      Text(
                        progressText,
                        style: TextStyle(
                            fontSize: 15.sp, fontWeight: FontWeight.w400),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  SizedBox(
                    width: 250.w,
                    child: Text(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      descriptionText,
                      style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF999999),
                          fontWeight: FontWeight.w500),
                    ),
                  )
                ],
              ),
              Positioned(
                  right: 5.w,
                  child: initialState == TaskStatus.complete
                      ? CompleteTaskButton(
                          onPressed: onButtonPressed,
                        )
                      : initialState == TaskStatus.start
                          ? StartTaskButton(
                              onPressed: onButtonPressed,
                            )
                          : initialState == TaskStatus.receive
                              ? ReceiveTaskButton(
                                  onPressed: onButtonPressed,
                                )
                              : NoStartTaskButton(
                                  onPressed: onButtonPressed,
                                ))
            ],
          ),
          SizedBox(height: 12.r),
          if (showLine)
            Container(
              height: 0.7.h,
              decoration: BoxDecoration(
                border: Border.all(
                  color: const Color(0x1AFFFFFF),
                  width: 0.7,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class TaskNewUserItem extends StatelessWidget {
  final String imagePath;
  final String bonusText;
  final String taskText;
  final String progressText;
  final String descriptionText;
  final int initialState;
  final bool showLine;
  final VoidCallback onButtonPressed; // 新增回调参数

  const TaskNewUserItem({
    super.key,
    this.imagePath = "",
    this.bonusText = '+10',
    this.taskText = 'Bonus',
    this.progressText = '(0/10)',
    this.descriptionText = "",
    this.initialState = 1,
    this.showLine = true,
    required this.onButtonPressed, // 新增回调参数
  });

  @override
  Widget build(BuildContext context) {
    return Container(

      padding: EdgeInsets.only(left: 12.r, right: 12.r, top: 12.r),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(
                    imagePath,
                    width: 36.r,
                    height: 36.r,
                    fit: BoxFit.fill,
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            bonusText,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFFFFCD00),
                            ),
                          ),
                          SizedBox(
                            width: 4.w,
                          ),
                          Text(
                            taskText,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5.h,
                      ),
                      SizedBox(
                        width: 250.w,
                        child: Text(
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          descriptionText,
                          style: TextStyle(
                              fontSize: 14.sp,
                              color: const Color(0xFF999999),
                              fontWeight: FontWeight.w500),
                        ),
                      )
                    ],
                  ),
                ],
              ),
              Positioned(
                  right: 5.w,
                  child: initialState == TaskStatus.complete
                      ? CompleteTaskButton(
                          onPressed: onButtonPressed,
                        )
                      : initialState == TaskStatus.start
                          ? StartTaskButton(
                              onPressed: onButtonPressed,
                            )
                          : ReceiveTaskButton(
                              onPressed: onButtonPressed,
                            ))
            ],
          ),
          SizedBox(height: 11.r),
          if (showLine)
            Container(
              height: 0.7.h,
              decoration: BoxDecoration(
                border: Border.all(
                  color: const Color(0x1AFFFFFF),
                  width: 0.7,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lexo_ttplayer_decryption/vod_player_typedef.dart';
import 'package:playlet/common/controller/bottom_nav_controller.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/player/cover_info.dart';
import 'package:playlet/common/player/player.dart';
import 'package:playlet/common/player/player_view_core.dart';
import 'package:playlet/common/player/player_view_core_track_event.dart';
import 'package:playlet/components/reels/player/reel_player_center_widget.dart';
import 'package:playlet/components/reels/player/reel_player_mask_widget.dart';
import 'package:playlet/components/reels/player/reel_player_progress_indicator.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/modules/reels/reels_controller.dart';
import 'package:playlet/routers/pages.dart';

const String tag = "ReelPlayerView";

class ReelPlayerView extends StatefulWidget {
  const ReelPlayerView({
    super.key,
    required this.videoUrl,
    required this.index,
    required this.onTimeChange,
    this.coverInfo,
    this.onEnded,
    this.onMoveStart,
    this.onMoveEnd,
    this.shortPlayCode,
    required this.aspectRatio,
    this.shortPlayType,
    this.startPlayPosition = Duration.zero,
  });

  final double aspectRatio;
  final int index;
  final int? shortPlayCode;
  final int? shortPlayType;
  final String videoUrl;
  final CoverInfo? coverInfo;
  final Duration startPlayPosition;
  final Function(Duration tiem) onTimeChange;
  final Function? onMoveStart;
  final Function? onMoveEnd;
  final Function()? onEnded;

  @override
  State<ReelPlayerView> createState() => ReelPlayerViewState();
}

class ReelPlayerViewState extends State<ReelPlayerView> {
  final GlobalKey<PlayerViewCoreState> _playerCoreKey =
      GlobalKey<PlayerViewCoreState>();

  /// 外部访问的播放器属性
  Player get player {
    // 使用PlayerViewCore提供的方法获取播放器实例
    if (_playerCoreKey.currentState != null) {
      return _playerCoreKey.currentState!.getPlayer();
    }
    // 如果都没有，抛出友好的错误消息
    throw Exception('Player not initialized yet');
  }

  final ReelsController reelsController = Get.find<ReelsController>();
  final BottomNavController bottomNavController =
      Get.find<BottomNavController>();

  // 创建埋点事件处理对象
  PlayerViewCoreTrackEvent? _trackEvent;

  Future<void> onPlay() async {
    FFLog.info(
        'onPlay -> ${bottomNavController.pageIndex.value} ${reelsController.currentIndex.value} currentRoute=${Get.currentRoute}',
        tag: tag);
    if (bottomNavController.pageIndex.value == 1 &&
        reelsController.currentIndex.value == widget.index &&
        Get.currentRoute == Routes.mainPage) {
      _playerCoreKey.currentState?.onPlay();
    } else {
      FFLog.info('onPlay -> 不播放', tag: tag);
      _playerCoreKey.currentState?.onPause();
    }
  }

  /// 暂停视频
  Future<void> onPause() async {
    await _playerCoreKey.currentState?.onPause();
  }

  // 创建埋点事件处理对象
  PlayerViewCoreTrackEvent _createTrackEvent() {
    if (_trackEvent != null) {
      return _trackEvent!;
    }

    ReelItem reelItem = reelsController.reelItems[widget.index];

    if (reelItem.type != ReelType.shorts || reelItem.reel == null) {
      _trackEvent = PlayerViewCoreTrackEvent(
        scene: EventValue.shorts,
        shortPlayCode: widget.shortPlayCode,
      );
      return _trackEvent!;
    }

    Reel reel = reelItem.reel!;
    ForYouShortInfo info = reel.info;

    _trackEvent = PlayerViewCoreTrackEvent(
      scene: EventValue.shorts,
      shortPlayCode: info.shortPlayCode,
      episodeNum: info.episodeNum,
      episodeId: info.id?.toString(),
      from: EventValue.fromShorts,
      isFree: '0',
      logic: 'nature',
      speedLevel: '1',
    );
    return _trackEvent!;
  }

  void _onPlayerInitialized(bool initialized) {
    if (initialized) {
      onPlay();
    }
  }

  void _onPlayStatusChanged(bool playing) {
    bool allowIsPlay = bottomNavController.pageIndex.value != 1 ||
        reelsController.currentIndex.value != widget.index ||
        bottomNavController.currentRoute.value != Routes.mainPage ||
        reelsController.isForeground == false;
    FFLog.info('onPlayStatusChanged -> allowIsPlay=$allowIsPlay', tag: tag);
    if (allowIsPlay) {
      _playerCoreKey.currentState?.onPause();
    }
  }

  void onStartSeekTo() {
    Duration? duration = _playerCoreKey.currentState?.player.value.duration;
    if (duration == null) return;
    Duration startPlayPosition = widget.startPlayPosition;
    // 播放剩余小于1秒，则开始播放
    if (duration.inMilliseconds - widget.startPlayPosition.inMilliseconds <
        1000) {
      startPlayPosition = Duration.zero;
    }
    _playerCoreKey.currentState?.onSeekTo(startPlayPosition);
  }

  void _onTimeChanged(Duration position) {
    widget.onTimeChange(position);
  }

  void _onReadyToDisplay() {
    onStartSeekTo();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        PlayerViewCore(
          key: _playerCoreKey,
          videoUrl: widget.videoUrl,
          aspectRatio: widget.aspectRatio,
          coverInfo: widget.coverInfo,
          onTimeChange: _onTimeChanged,
          onEnded: widget.onEnded,
          onMoveStart: widget.onMoveStart,
          onMoveEnd: widget.onMoveEnd,
          onPlayerInitialized: _onPlayerInitialized,
          onPlayStatusChanged: _onPlayStatusChanged,
          onReadyToDisplay: _onReadyToDisplay,
          scalingMode: widget.aspectRatio < 1
              ? TTVideoEngineScalingMode.TTVideoEngineScalingModeAspectFill
              : TTVideoEngineScalingMode.TTVideoEngineScalingModeAspectFit,
          playerViewType: PlayerViewType.reels,
          index: widget.index,
          shortPlayCode: widget.shortPlayCode,
          shortPlayType: widget.shortPlayType,
          trackEvent: _createTrackEvent(),
          isDailyShowLoading: true,
        ),
        const ReelPlayerMaskWidget(),
        Positioned(
          bottom: 0,
          left: 12.sp,
          right: 12.sp,
          child: _playerCoreKey.currentState?.isPlayerAvailable() ?? false
              ? ReelPlayerProgressIndicator(
                  _playerCoreKey.currentState?.getPlayer() ?? Player(""),
                  allowScrubbing: true,
                  onMoveStart: widget.onMoveStart,
                  onMoveEnd: widget.onMoveEnd,
                )
              : const SizedBox.shrink(),
        ),
        _playerCoreKey.currentState?.isPlayerAvailable() ?? false
            ? ReelPlayerCenterWidget(
                controller:
                    _playerCoreKey.currentState?.getPlayer() ?? Player(""))
            : const SizedBox.shrink(),
      ],
    );
  }
}

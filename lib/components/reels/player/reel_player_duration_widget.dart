import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/common/player/player.dart';

class ReelPlayerDurationWidget extends StatefulWidget {
  final Player controller;
  final TextStyle? textStyle;

  const ReelPlayerDurationWidget(
      {super.key, required this.controller, this.textStyle});

  @override
  State<ReelPlayerDurationWidget> createState() => _PlayerDurationWidgetState();
}

class _PlayerDurationWidgetState extends State<ReelPlayerDurationWidget> {
  late StreamController<Duration> _positionStreamController;

  @override
  void initState() {
    super.initState();
    // 初始化 StreamController
    _positionStreamController = StreamController<Duration>.broadcast();
    // 监听 Stream
    widget.controller.addListener(listener);
  }

  void listener() {
    if (!_positionStreamController.isClosed) {
      _positionStreamController.add(widget.controller.value.position);
    }
  }

  @override
  void dispose() {
    // 关闭 StreamController
    _positionStreamController.close();
    widget.controller.removeListener(listener);
    super.dispose();
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "$twoDigitMinutes:$twoDigitSeconds";
  }

  final textStyle = TextStyle(
    color: Colors.white,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Duration>(
      stream: _positionStreamController.stream,
      initialData: widget.controller.value.position,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox();
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              formatDuration(snapshot.data!),
              style: widget.textStyle ?? textStyle,
            ),
            SizedBox(
              width: 4.sp,
            ),
            Text(
              "/",
              style: widget.textStyle ?? textStyle,
            ),
            SizedBox(
              width: 4.sp,
            ),
            Text(
              formatDuration(widget.controller.value.duration),
              style: widget.textStyle ?? textStyle,
            ),
          ],
        );
      },
    );
  }
}

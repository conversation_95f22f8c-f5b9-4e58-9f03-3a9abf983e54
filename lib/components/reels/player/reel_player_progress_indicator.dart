import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/player/player.dart';
import 'package:playlet/utils/get_extension.dart';

import 'reel_player_duration_widget.dart';
import 'reel_player_scrubber.dart';

class ReelPlayerProgressIndicator extends StatefulWidget {
  const ReelPlayerProgressIndicator(
    this.controller, {
    super.key,
    required this.allowScrubbing,
    this.onMoveStart,
    this.onMoveEnd,
  });

  /// The [Player] that actually associates a video with this
  /// widget.
  final Player controller;

  final bool allowScrubbing;

  final Function? onMoveStart;
  final Function? onMoveEnd;

  @override
  State<ReelPlayerProgressIndicator> createState() =>
      _PlayerProgressIndicatorState();
}

class _PlayerProgressIndicatorState extends State<ReelPlayerProgressIndicator> {
  _PlayerProgressIndicatorState() {
    listener = () {
      if (!mounted) {
        return;
      }
      setState(() {});
    };
  }

  late VoidCallback listener;

  Player get controller => widget.controller;

  bool isMove = false;

  OverlayState? overlayState;
  OverlayEntry? overlayEntry;

  @override
  void initState() {
    super.initState();
    overlayState = Overlay.of(context);
    controller.addListener(listener);
  }

  @override
  void deactivate() {
    controller.removeListener(listener);
    closeOverlay();
    super.deactivate();
  }

  void showOverlay() {
    if (overlayEntry != null) return;
    overlayEntry = OverlayEntry(
      builder: (context) {
        return Positioned(
          bottom: 121.sp,
          child: Material(
            color: Colors.transparent,
            child: SizedBox(
              width: Get.width,
              child: Center(
                child: ReelPlayerDurationWidget(
                  controller: controller,
                  textStyle: TextStyle(
                    fontSize: 18.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
    overlayState?.insert(overlayEntry!);
  }

  void closeOverlay() {
    overlayEntry?.remove();
    overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    Widget progressIndicator;
    if (controller.value.isInitialized) {
      progressIndicator = SizedBox(
        width: Get.width,
        height: 8,
        child: Stack(
          children: <Widget>[
            Center(
              child: SizedBox(
                height: isMove ? 6.sp : 2.sp,
                child: Stack(
                  fit: StackFit.passthrough,
                  children: [
                    // 播放进度
                    LinearProgressIndicator(
                      value: controller.value.playbackProgress,
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Colors.white),
                      backgroundColor: const Color(0XFF7F7F7B),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 0,
              left:
                  (controller.value.playbackProgress * (Get.width - 24.sp)) - 4,
              child: SizedBox(
                width: 8.sp,
                height: 8,
                child: isMove
                    ? Container(
                        width: 8.sp,
                        height: 16.sp,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(29.r),
                        ),
                      )
                    : Center(
                        child: Container(
                          width: 4.sp,
                          height: 4.sp,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(180),
                          ),
                        ),
                      ),
              ),
            ),
          ],
        ),
      );
    } else {
      // 初始化占位进度条
      progressIndicator = SizedBox(
        width: Get.width,
        height: 8,
        child: Center(
          child: SizedBox(
            height: 1.sp,
            child:  LinearProgressIndicator(
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
              backgroundColor: Colors.white.withValues(alpha: 0.4),
            ),
          ),
        ),
      );
    }
    if (widget.allowScrubbing) {
      return ReelPlayerScrubber(
        controller: controller,
        onMoveStart: () {
          isMove = true;
          widget.onMoveStart?.call();
          showOverlay();
          /// 使用全局loading会阻止用户事件
          // Get.loadingDot();
        },
        onMoveEnd: () {
          isMove = false;
          widget.onMoveEnd?.call();
          closeOverlay();
        },
        child: progressIndicator,
      );
    } else {
      return progressIndicator;
    }
  }
}

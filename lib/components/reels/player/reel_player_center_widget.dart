import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/player/player.dart';
import 'package:playlet/gen/assets.gen.dart';

class ReelPlayerCenterWidget extends StatefulWidget {
  const ReelPlayerCenterWidget({super.key, required this.controller});

  final Player controller;

  @override
  State<ReelPlayerCenterWidget> createState() => _ReelPlayerCenterWidgetState();
}

class _ReelPlayerCenterWidgetState extends State<ReelPlayerCenterWidget> {
  bool isPlaying = true;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(listener);
  }

  @override
  void dispose() {
    widget.controller.removeListener(listener);
    super.dispose();
  }

  void listener() {
    if (widget.controller.value.isPlaying && !isPlaying) {
      isPlaying = true;
      setState(() {});
    } else if (!widget.controller.value.isPlaying && isPlaying) {
      isPlaying = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return isPlaying
        ? const SizedBox()
        : Positioned(
            left: Get.width / 2 - 35.sp,
            top: Get.height / 2 - 35.sp,
            width: 70.sp,
            height: 70.sp,
            child: IgnorePointer(
              child: isPlaying
                  ? Assets.video.pause.image(width: 70.sp, height: 70.sp)
                  : Assets.video.play.image(width: 70.sp, height: 70.sp),
            ),
          );
  }
}

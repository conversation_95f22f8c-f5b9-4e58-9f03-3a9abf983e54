import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ReelPlayerMaskWidget extends StatelessWidget {
  const ReelPlayerMaskWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 0.0,
      left: 0.0,
      right: 0.0,
      child: Container(
        height: 140.sp,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0x00000000), // 透明
              Color(0xB3000000), // 70% 黑色
            ],
          ),
        ),
      ),
    );
  }
}

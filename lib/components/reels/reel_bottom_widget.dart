import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/routers/app_navigator.dart';

class ReelBottomWidget extends StatefulWidget {
  const ReelBottomWidget({
    super.key,
    required this.info,
    required this.isShowAllBtnPlay,
  });

  final Reel info;
  final RxBool isShowAllBtnPlay;

  @override
  State<ReelBottomWidget> createState() => _ReelBottomWidgetState();
}

class _ReelBottomWidgetState extends State<ReelBottomWidget> {
  void onToDetail() => AppNavigator.startDetailsPage(
        DetailsOptions(
          businessId: widget.info.info.shortPlayId!,
          scene: EventValue.shorts,
          from: EventValue.fromShorts,
          playerEpisodeIndex: (widget.info.info.episodeNum ?? 1) - 1,
          playerEpisodePosition: widget.info.playPosition,
          videoDuration: widget.info.info.videoDuration,
          openEpisodePanel: true,
        ),
      );

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 8,
      left: 16.sp,
      right: 16.sp,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 276.sp,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.info.info.shortPlayName ?? "",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                6.sp.verticalSpace,
              ],
            ),
          ),
          ReelDetailTextWidget(detailText: widget.info.info.recommendContent),
          15.sp.verticalSpace,
          _buildPlayButton(),
          InkWell(
            onTap: onToDetail,
            child: SizedBox(
              width: Get.width - 32.sp,
              height: 32.sp,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Assets.video.list.image(width: 16.sp, height: 16.sp),
                      6.sp.horizontalSpace,
                      Text(
                        _getNumString(),
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  _buildArrow(),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildPlayButton() {
    return Obx(
      () {
        if (!widget.isShowAllBtnPlay.value) return const SizedBox();
        return Column(
          children: [
            GestureDetector(
              onTap: onToDetail,
              child: Container(
                width: 264.sp,
                height: 40.sp,
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(255, 205, 0, 1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.details.retentionPlay.image(
                      width: 18.sp,
                      height: 18.sp,
                    ),
                    SizedBox(width: 8.sp),
                    Text(
                      "Watch all episode",
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(30, 30, 30, 1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            15.sp.verticalSpace,
          ],
        );
      },
    );
  }

  String _getNumString() {
    String currentNum =
        "${AppTrans.ep()}.${widget.info.info.episodeNum}".toUpperCase();
    String allOrUpdateNum = '';
    if (widget.info.info.updateEpisode! < widget.info.info.totalEpisodes!) {
      allOrUpdateNum =
          "${AppTrans.update()} ${widget.info.info.updateEpisode ?? 0} ${AppTrans.ep()}";
    } else {
      allOrUpdateNum =
          AppTrans.allEpisodes(widget.info.info.totalEpisodes ?? 0);
    }
    return '$currentNum / $allOrUpdateNum';
  }

  Widget _buildArrow() {
    return Icon(
      Icons.arrow_forward_ios_rounded,
      color: Colors.white,
      size: 12.sp,
    );
  }
}

class ReelDetailTextWidget extends StatefulWidget {
  const ReelDetailTextWidget({
    super.key,
    required this.detailText,
  });
  final String? detailText;

  @override
  State<ReelDetailTextWidget> createState() => _ReelDetailTextWidgetState();
}

class _ReelDetailTextWidgetState extends State<ReelDetailTextWidget> {
  bool isMore = false;

  @override
  Widget build(BuildContext context) {
    TextStyle textStyle = TextStyle(
      fontSize: 13.sp,
      fontWeight: FontWeight.w400,
      color: Colors.white,
    );

    double maxWidth = 276.sp;

    final TextPainter textPainter = TextPainter(
      locale: Localizations.localeOf(Get.context!),
      textDirection: TextDirection.ltr,
      text: TextSpan(
        text: widget.detailText,
        style: textStyle,
      ),
    )..layout(minWidth: 0, maxWidth: maxWidth);

    Size textSize = textPainter.size;

    bool hasMore = textSize.height > 40.sp;

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        SizedBox(
          width: 276.sp,
          child: hasMore == false
              ? Text(
                  widget.detailText ?? "",
                  style: textStyle,
                )
              : Text(
                  widget.detailText ?? "",
                  style: textStyle.copyWith(
                    overflow: TextOverflow.ellipsis,
                  ),
                  maxLines: isMore == true ? 6 : 2,
                ),
        ),
        hasMore == false
            ? const SizedBox.shrink()
            : Padding(
                padding: EdgeInsets.only(bottom: 0.sp),
                child: Ink(
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        isMore = !isMore;
                      });
                    },
                    child: Transform.rotate(
                      angle: isMore == true ? math.pi : 0,
                      child: Assets.arrowCircleDown.image(
                        width: 18.sp,
                        height: 18.sp,
                      ),
                    ),
                  ),
                ),
              ),
      ],
    );
  }
}

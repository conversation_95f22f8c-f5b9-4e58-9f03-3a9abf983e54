import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/components/details/landscape_button_widget.dart';
import 'package:playlet/components/reels/player/reel_player_view.dart';
import 'package:playlet/components/reels/reel_bottom_widget.dart';
import 'package:playlet/components/reels/reel_funs_widget.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/modules/reels/reels_track_event.dart';
import 'package:playlet/routers/app_navigator.dart';

class ReelItemWidget extends StatefulWidget {
  const ReelItemWidget({
    super.key,
    required this.info,
    required this.index,
    required this.onEnded,
    required this.onTimeChange,
    this.shortPlayCode,
    this.shortPlayType,
  });

  final int index;
  final int? shortPlayCode;
  final int? shortPlayType;
  final Reel info;
  final Function() onEnded;
  final Function(Duration tiem) onTimeChange;

  @override
  State<ReelItemWidget> createState() => _ReelItemWidgetState();
}

class _ReelItemWidgetState extends State<ReelItemWidget> {
  bool isDragProgress = false;

  RxBool isShowAllPlay = false.obs;

  _toggleFullScreen() {
    AppNavigator.startDetailsPage(
      DetailsOptions(
        businessId: widget.info.info.shortPlayId!,
        scene: EventValue.shorts,
        playerEpisodeIndex: (widget.info.info.episodeNum ?? 1) - 1,
        playerEpisodePosition: widget.info.playPosition,
        videoDuration: widget.info.info.videoDuration,
        from: EventValue.fromShorts,
        isSupportLandscapeMode: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          ReelPlayerView(
            index: widget.index,
            shortPlayCode: widget.shortPlayCode,
            shortPlayType: widget.shortPlayType,
            key: widget.info.playerKey,
            videoUrl: widget.info.videoUrl,
            onEnded: widget.onEnded,
            startPlayPosition: widget.info.playPosition,
            onTimeChange: (tiem) {
              int timeNum = tiem.inSeconds;
              if (timeNum > 10 && !isShowAllPlay.value) {
                isShowAllPlay.value = true;
              }
              widget.onTimeChange(tiem);
            },
            aspectRatio: widget.info.info.aspectRatio ?? 0,
            onMoveStart: () {
              isDragProgress = true;
              setState(() {});
            },
            onMoveEnd: () {
              isDragProgress = false;
              setState(() {});
            },
          ),
          (widget.info.info.aspectRatio ?? 0) >= 1
              ? Builder(
                  builder: (context) {
                    // 计算横屏播放区域的高度和位置
                    final screenWidth = Get.width;
                    final videoHeight =
                        screenWidth / (widget.info.info.aspectRatio ?? 1);
                    final videoTop = (Get.height - 60.sp - videoHeight) /
                        2; // 播放区域距离屏幕顶部的高度,60是底部导航栏的高度
                    return LandscapeButtonWidget(
                        top: videoTop + videoHeight + 20.sp,
                        onTap: _toggleFullScreen);
                  },
                )
              : const SizedBox(),
          isDragProgress ? const SizedBox() : ReelFunsWidget(info: widget.info),
          isDragProgress
              ? const SizedBox()
              : ReelBottomWidget(
                  info: widget.info,
                  isShowAllBtnPlay: isShowAllPlay,
                ),
        ],
      ),
    );
  }

  @override
  void initState() {
    ReelsTrackEvent.reelShow(shortPlayCode: widget.shortPlayCode);
    super.initState();
  }
}

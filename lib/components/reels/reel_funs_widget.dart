import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/api/details.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/modules/reels/reels_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/shorts_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/index.dart';

import '../../service/notificaiton_dialog_service.dart';

class ReelFunsWidget extends StatefulWidget {
  const ReelFunsWidget({super.key, required this.info});

  final Reel info;

  @override
  State<ReelFunsWidget> createState() => _ReelFunsWidgetState();
}

class _ReelFunsWidgetState extends State<ReelFunsWidget> {
  final ReelsController reelsController = Get.find<ReelsController>();
  final ShortsService shortsService = Get.find<ShortsService>();
  final UserService userService = Get.find<UserService>();

  int collectNum = 0;
  bool isCollecting = false;

  Future<void> onCollectOrUnCollect() async {
    if (isCollecting == true) return;
    isCollecting = true;
    bool isCollect = shortsService.collectionShortsIds
        .contains(widget.info.info.shortPlayCode);
    if (isCollect) {
      ApiDetails.cancelCollect(
        businessId: widget.info.info.shortPlayId!,
        scene: "collections",
        colletType: 1,
        collectSource: 1,
      ).then((value) {
        if (value) {
          isCollect = false;
          widget.info.info.collectNum = collectNum - 1;
          widget.info.info.isCollect = 2;
          shortsService.cancelCollection(widget.info.info.shortPlayCode);
          shortsService
              .decreaseShortsCollectionNumber(widget.info.info.shortPlayCode);
          reelsController.reelItems.refresh();
        }
        isCollecting = false;
      });
    } else {
      ApiDetails.collectOp(
        businessId: widget.info.info.shortPlayId!,
        scene: "collections",
        dramaId: widget.info.info.id!,
        colletType: 1,
        collectSource: 1,
        watchTime: widget.info.playPosition.inSeconds,
      ).then((value) {
        if (value) {
          isCollect = true;
          widget.info.info.collectNum = collectNum + 1;
          widget.info.info.isCollect = 1;
          shortsService.confirmCollection(widget.info.info.shortPlayCode);
          shortsService
              .increaseShortsCollectionNumber(widget.info.info.shortPlayCode);
          reelsController.reelItems.refresh();
        }
        isCollecting = false;
      });
    }

    /// 检查并弹出通知弹窗
    var notificationLoginService = Get.find<NotificationLoginService>();
    await notificationLoginService.checkAndShowNotificationAlert(2);
  }

  @override
  void initState() {
    // isCollect = widget.info.info.isCollect == 1 ? true : false;
    collectNum = widget.info.info.collectNum ?? 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 155.sp,
      right: 8.sp,
      child: Column(
        children: [
          Obx(
            () => _buildFun(
              shortsService.collectionShortsIds
                          .contains(widget.info.info.shortPlayCode) ==
                      true
                  ? Assets.video.liveActive.image(width: 35.sp, height: 35.sp)
                  : Assets.video.like.image(width: 35.sp, height: 35.sp),
              Utils.formatNumberK(shortsService
                  .getShortsCollectionNumberBy(widget.info.info.shortPlayCode)),
              onTap: () {
                unawaited(onCollectOrUnCollect());
              },
            ),
          ),
          // 添加是否显示订阅入口
          Obx(() {
            return userService.userInfo.value?.hasSubscription == true
                ? _buildFun(
                    Assets.video.vip.image(width: 35.sp, height: 35.sp),
                    AppTrans.vip(),
                    onTap: () =>
                        AppNavigator.startSubscriptionPage(from: "shorts"),
                  )
                : const SizedBox();
          }),

          _buildFun(
            Assets.video.list.image(width: 35.sp, height: 35.sp),
            AppTrans.list(),
            onTap: () {
              AppNavigator.startDetailsPage(
                DetailsOptions(
                  businessId: widget.info.info.shortPlayId!,
                  scene: EventValue.shorts,
                  playerEpisodeIndex: (widget.info.info.episodeNum ?? 1) - 1,
                  playerEpisodePosition: widget.info.playPosition,
                  videoDuration: widget.info.info.videoDuration,
                  from: EventValue.fromShorts,
                  openEpisodePanel: true,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFun(Widget icon, String text, {VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(top: 20.sp, left: 8.sp, right: 8.sp),
        child: Column(
          children: [
            icon,
            Text(
              text,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                shadows: [
                  Shadow(
                    offset: const Offset(0, 1),
                    color: const Color(0x00000040).withValues(alpha: 0.25),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

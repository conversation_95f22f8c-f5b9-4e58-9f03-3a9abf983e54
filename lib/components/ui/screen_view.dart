import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ScreenView extends StatefulWidget {
  final Widget child;
  const ScreenView({super.key, required this.child});

  @override
  State<ScreenView> createState() => _ScreenViewState();
}

class _ScreenViewState extends State<ScreenView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      height: Get.height,
      decoration: const BoxDecoration(
        color: Colors.black, 
      ),
      child: widget.child,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LoginButtom extends StatefulWidget {
  final String title;
  final String image;
  final GestureTapCallback onTap;
  final bool? isLoading;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final bool? border;

  /// 登录按钮
  const LoginButtom(
      {super.key,
      required this.title,
      required this.image,
      required this.onTap,
      this.isLoading,
      this.backgroundColor,
      this.textColor,
      this.width,
      this.border});

  @override
  State<LoginButtom> createState() => _LoginButtomState();
}

class _LoginButtomState extends State<LoginButtom> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ?? 312.w,
      height: 46.h,      
      decoration: BoxDecoration(
              color: widget.backgroundColor ??
                  Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(27.r),
            ),
      child: Padding(
        padding: EdgeInsets.all(1.w),
        child: GestureDetector(
          onTap: widget.onTap,
          child: Container(
            decoration: BoxDecoration(
              color: widget.backgroundColor ??
                  Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(27.r),
            ),
            child: Stack(
            alignment: Alignment.centerLeft,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 14.sp),
                  child: Image.asset(
                    widget.image,
                    width: 24.w,
                    height: 24.h,
                  ),
                ),
               
                Center( child:  Text(
                  widget.title,
                  style: TextStyle(
                    fontFamily: "Poppins",
                    color: widget.textColor ?? Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.none,
                  ),
                ),)
              ],
            ),
          ),
        ),
      ),
    );
  }
}

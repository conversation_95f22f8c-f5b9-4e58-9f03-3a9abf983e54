import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 一个用于显示短标签列表的可复用组件。
///
/// 该组件可以将一组字符串标签以水平排列的方式展示，每个标签都有统一的样式和布局。
/// 当标签超出可用空间时会自动换行。
///
/// 示例：
/// ```dart
/// ShortTagView(
///   tags: ['标签1', '标签2', '标签3'],
///   backgroundColor: Colors.blue,
///   textColor: Colors.white,
/// )
/// ```
class ShortTagView extends StatelessWidget {
  /// 要显示的标签文本列表
  final List<String> tags;

  /// 标签背景颜色，默认为深灰色
  final Color backgroundColor;

  /// 标签文本颜色，默认为白色
  final Color textColor;

  /// 标签圆角半径，默认为11.6
  final double borderRadius;

  /// 标签内部水平方向的内边距，默认为10
  final double horizontalPadding;

  /// 标签内部垂直方向的内边距，默认为4
  final double verticalPadding;

  /// 标签之间的间距，默认为6
  final double spacing;

  /// 标签行之间的间距，默认为6
  final double runSpacing;

  /// 标签文本字体大小，默认为9
  final double fontSize;

  /// 标签容器的高度，默认为21
  final double height;

  const ShortTagView({
    super.key,
    required this.tags,
    this.backgroundColor = const Color(0xff262726),
    this.textColor = Colors.white,
    this.borderRadius = 11.6,
    this.horizontalPadding = 10,
    this.verticalPadding = 4,
    this.spacing = 6,
    this.runSpacing = 6,
    this.fontSize = 9,
    this.height = 21,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height.sp,
      child: LayoutBuilder(
        builder: (context, constraints) => Wrap(
          spacing: spacing.sp,
          runSpacing: runSpacing.sp,
          clipBehavior: Clip.hardEdge,
          children: tags.map((tag) {
            return Container(
              padding: EdgeInsets.symmetric(
                horizontal: horizontalPadding.sp,
                vertical: verticalPadding.sp,
              ),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(borderRadius.sp),
              ),
              child: Text(
                tag,
                style: TextStyle(
                  fontSize: fontSize.sp,
                  color: textColor,
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/gen/assets.gen.dart';

class FFLoadingWidget extends StatelessWidget {
  final bool isShowText;
  final String? icon;
  const FFLoadingWidget({super.key, this.isShowText = false, this.icon});
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Image.asset(
            //   'assets/loading/ff_loading.webp',
            //   width: 110.sp,
            //   height: 50.sp,
            //   fit: BoxFit.fitWidth,
            // ),
            icon != null
                ? Image.asset(
                    icon!,
                    width: 60.sp,
                    height: 60.sp,
                  )
                : Assets.loading.iconLoading.image(
                    width: 60.sp,
                    height: 60.sp,
                  ),
            _ffLoadingText(),
          ],
        ),
      ),
    );
  }

  Widget _ffLoadingText() {
    return isShowText
        ? Padding(
            padding: EdgeInsets.only(top: 10.sp),
            child: const Text(
              'Loading...',
              style: TextStyle(fontSize: 12),
            ))
        : const SizedBox();
  }
}

import 'dart:async';

import 'package:flutter/material.dart';

class FFButton extends StatelessWidget {
  const FFButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.disable = false,
    this.backgroundColor = const Color(0xFFFFCD00),
    this.disableBackgroundColor = const Color(0xFF434551),
    this.isMaxWidth = true,
    this.padding = const EdgeInsets.all(9.0),
    this.shape = const StadiumBorder(),
  });
  final bool disable;
  final FutureOr Function() onPressed;
  final Color backgroundColor;
  final Color disableBackgroundColor;
  final bool isMaxWidth;
  final Widget child;
  final EdgeInsets padding;
  final OutlinedBorder shape;

  factory FFButton.text({
    required final String text,
    required final FutureOr Function() onPressed,
    final TextStyle textStyle = const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      color: Color(0xFF090909),
    ),
    final bool disable = false,
    final Color backgroundColor = const Color(0xFFFFCD00),
    final Color disableBackgroundColor = const Color(0xFF434551),
    final bool isMaxWidth = true,
    final EdgeInsets padding = const EdgeInsets.all(9.0),
    final OutlinedBorder shape = const StadiumBorder(),
  }) {
    return FFButton(
      onPressed: onPressed,
      disable: disable,
      backgroundColor: backgroundColor,
      disableBackgroundColor: disableBackgroundColor,
      isMaxWidth: isMaxWidth,
      padding: padding,
      shape: shape,
      child: Text(
        text,
        style: textStyle.copyWith(
          color: disable ? Colors.white : textStyle.color,
          height: 2.0,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FilledButton(
      onPressed: onPressed,
      style: ButtonStyle(
        padding: WidgetStatePropertyAll(padding),
        enableFeedback: true,
        visualDensity: VisualDensity.compact,
        shape: WidgetStatePropertyAll(shape),
        splashFactory:
            disable ? NoSplash.splashFactory : InkSparkle.splashFactory,
        fixedSize: WidgetStatePropertyAll(
          isMaxWidth == true
              ? const Size.fromWidth(double.maxFinite)
              : Size.infinite,
        ),
        elevation: const WidgetStatePropertyAll(0.0),
        backgroundColor: WidgetStatePropertyAll(
          disable == false ? backgroundColor : disableBackgroundColor,
        ),
      ),
      child: child,
    );
  }
}

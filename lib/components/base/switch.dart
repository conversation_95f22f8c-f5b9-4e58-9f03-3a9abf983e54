import 'package:flutter/material.dart';

class FFSwitch extends StatefulWidget {
  const FFSwitch({
    super.key,
    required this.initValue,
    required this.onChanged,
    this.activeColor = const Color(0xFF07C160),
    this.trackColor = const Color(0xFF999999),
    this.thumbColor = Colors.white,
    this.height = 22.0,
    this.width = 44.0,
  });
  final bool initValue;
  final ValueChanged<bool> onChanged;
  final Color activeColor;
  final Color trackColor;
  final Color thumbColor;
  final double height;
  final double width;

  @override
  State<FFSwitch> createState() => _FFSwitchState();
}

class _FFSwitchState extends State<FFSwitch> {
  late bool _value;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant FFSwitch oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    _value = widget.initValue;
    return GestureDetector(
      onTap: () {
        setState(() {
          _value = !_value;
          widget.onChanged(_value);
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 100),
        curve: Curves.easeInOut,
        width: widget.width,
        height: widget.height,
        padding: _value ? const EdgeInsets.all(2.0) : const EdgeInsets.all(4.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: _value ? widget.activeColor : widget.trackColor,
        ),
        child: AnimatedAlign(
          alignment: _value ? Alignment.centerRight : Alignment.centerLeft,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            width: _value ? 18 : 14.0,
            height: _value ? 18 : 14.0,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.thumbColor,
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/shimmer/shimmer_widget.dart';

class HomeShimmer extends StatelessWidget {
  const HomeShimmer({super.key});

  static const bgColor = Color(0xFF181818);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: Column(
          children: [
            _buildAppBar(),
            _buildBanner(),
            _buildList(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 24.sp),
      child: ShimmerWidget(
        child: Row(
          children: [
            Container(
              width: 38.sp,
              height: 38.sp,
              color: bgColor,
            ),
            Sized<PERSON>ox(width: 6.sp),
            Expanded(
              child: Container(
                height: 26.sp,
                color: bgColor,
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildBanner() {
    var centerWidth = Get.width * 266 / 393;
    var centerHeight = Get.width * 266 / 393 * 354 / 266;
    double itemW = (Get.width - centerWidth - 24.sp) / 2;
    return Padding(
      padding: EdgeInsets.only(bottom: 24.sp),
      child: Container(
        alignment: Alignment.center,
        height: centerHeight + 17.sp,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Positioned(
              left: 0.0,
              right: 0.0,
              top: 0.0,
              child: ShimmerWidget(
                child: SizedBox(
                  height: centerHeight,
                  width: Get.width,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: bgColor,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(6.r),
                            bottomRight: Radius.circular(6.r),
                          ),
                        ),
                        width: itemW,
                        height: centerHeight * 0.9,
                      ),
                      SizedBox(
                        width: centerWidth,
                        height: centerHeight + 17.sp,
                        child: Container(
                          decoration: BoxDecoration(
                            color: bgColor,
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                          width: centerWidth,
                          height: centerHeight,
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: bgColor,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(6.r),
                            bottomLeft: Radius.circular(6.r),
                          ),
                        ),
                        width: itemW,
                        height: centerHeight * 0.9,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0.0.sp,
              child: ShimmerWidget(
                baseColor: const Color(0xFF222222),
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF222222),
                    borderRadius: BorderRadius.circular(6.r),
                  ),
                  width: 114.sp,
                  height: 34.sp,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildList() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.sp),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShimmerWidget(
            child: Container(
              color: bgColor,
              width: 190.sp,
              height: 30.sp,
            ),
          ),
          SizedBox(height: 14.sp),
          SizedBox(
            height: 200.sp,
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                mainAxisSpacing: 10.sp,
                crossAxisSpacing: 10.sp,
                childAspectRatio: 112 / 192,
              ),
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 3,
              itemBuilder: (context, index) {
                double itemW = (Get.width - 60.sp) / 3;
                return ShimmerWidget(
                  child: Column(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: bgColor,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        width: itemW,
                        height: itemW * 149 / 112,
                      ),
                      SizedBox(height: 8.sp),
                      Container(
                        decoration: BoxDecoration(
                          color: bgColor,
                          borderRadius: BorderRadius.circular(2.r),
                        ),
                        width: itemW,
                        height: 14.sp,
                      ),
                      SizedBox(height: 4.sp),
                      Container(
                        decoration: BoxDecoration(
                          color: bgColor,
                          borderRadius: BorderRadius.circular(2.r),
                        ),
                        width: itemW,
                        height: 14.sp,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

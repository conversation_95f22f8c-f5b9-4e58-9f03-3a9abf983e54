import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/components/shimmer/shimmer_widget.dart';
import 'package:playlet/gen/assets.gen.dart';

class DetailShimmer extends StatelessWidget {
  const DetailShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final isLandscape = ScreenUtils.isLandscape(context);
    return SafeArea(
      top: false,
      child: Stack(
        children: [
          _buildBg(),
          Positioned(
            bottom: 26.sp,
            left: 16.sp,
            right: 16.sp,
            child: _buildText(),
          ),
          Positioned(
            bottom: isLandscape? 94.sp : 155.sp,
            right: isLandscape? 48.sp :  16.sp,
            child: _buildIcons(),
          ),
        ],
      ),
    );
  }

  Widget _buildBg() {
    final isLandscape = ScreenUtils.isLandscape(Get.context!);
    return Padding(
      padding: EdgeInsets.only(top: isLandscape ? 0: kToolbarHeight),
      child: Container(
        width: Get.width,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF222222),
              Color(0xFF000000),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildText() {
    return ShimmerWidget(
      child: Container(
        width: Get.width - 32.sp,
        height: 2.sp,
        decoration: BoxDecoration(
          color: const Color(0xFF222222),
          borderRadius: BorderRadius.circular(2.sp),
        ),
      ),
    );
  }

  Widget _buildIcons() {
    return Column(
      children: [
        _buildIcon(iconName: Assets.video.like.path),
        SizedBox(height: 20.sp),
        _buildIcon(iconName: Assets.video.vip.path),
        SizedBox(height: 20.sp),
        _buildIcon(iconName: Assets.video.list.path),
      ],
    );
  }

  Widget _buildIcon({required String iconName}) {
    return ShimmerWidget(
      child: Column(
        children: [
          Image.asset(
            iconName,
            width: 35.sp,
            height: 35.sp,
            color: const Color(0xFF222222),
          ),
          Container(
            width: 36.sp,
            height: 15.sp,
            color: const Color(0xFF222222),
          ),
        ],
      ),
    );
  }
}

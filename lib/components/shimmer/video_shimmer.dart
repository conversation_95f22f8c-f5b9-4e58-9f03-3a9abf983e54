import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/shimmer/shimmer_widget.dart';
import 'package:playlet/gen/assets.gen.dart';

class VideoShimmer extends StatefulWidget {
  const VideoShimmer({super.key});

  @override
  State<VideoShimmer> createState() => _VideoShimmerState();
}

class _VideoShimmerState extends State<VideoShimmer> {
  bool _show = false;
  final Duration _delayDuration = const Duration(milliseconds: 500);
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showLoading();
    });
    super.initState();
  }

  void _showLoading() {
    if (_show == false) {
      Future.delayed(_delayDuration, () {
        if (mounted) {
          setState(() {
            _show = true;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        child: _show == false
            ? const SizedBox.shrink()
            : Stack(
                children: [
                  Positioned(
                    bottom: 26.sp,
                    left: 16.sp,
                    right: 16.sp,
                    child: _buildText(),
                  ),
                  Positioned(
                    bottom: 155.sp,
                    right: 16.sp,
                    child: _buildIcons(),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildText() {
    return ShimmerWidget(
      child: Container(
        width: Get.width - 32.sp,
        height: 2.sp,
        decoration: BoxDecoration(
          color: const Color(0xFF222222),
          borderRadius: BorderRadius.circular(2.sp),
        ),
      ),
    );
  }

  Widget _buildIcons() {
    return Column(
      children: [
        _buildIcon(iconName: Assets.video.like.path),
        SizedBox(height: 20.sp),
        _buildIcon(iconName: Assets.video.vip.path),
        SizedBox(height: 20.sp),
        _buildIcon(iconName: Assets.video.list.path),
      ],
    );
  }

  Widget _buildIcon({required String iconName}) {
    return ShimmerWidget(
      child: Column(
        children: [
          Image.asset(
            iconName,
            width: 35.sp,
            height: 35.sp,
            color: const Color(0xFF222222),
          ),
          Container(
            width: 36.sp,
            height: 15.sp,
            color: const Color(0xFF222222),
          ),
        ],
      ),
    );
  }
}

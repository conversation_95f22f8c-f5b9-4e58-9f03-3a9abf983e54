import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/components/shimmer/shimmer_widget.dart';

class MylistFullScreenShimmer extends StatelessWidget {
  const MylistFullScreenShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          _buildAppBar(),
          _buildTabs(),
          const Expanded(child: MylistCollectViewShimmer()),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 9.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ShimmerWidget(
            child: Container(
              width: 133.sp,
              height: 26.sp,
              decoration: BoxDecoration(
                color: const Color(0xFF181818),
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
          ShimmerWidget(
            child: Container(
              width: 26.sp,
              height: 26.sp,
              decoration: BoxDecoration(
                color: const Color(0xFF181818),
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabs() {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.sp, 12.sp, 16.sp, 30.sp),
      child: Row(
        children: [
          ShimmerWidget(
            child: Container(
              width: 133.sp,
              height: 34.sp,
              decoration: BoxDecoration(
                color: const Color(0xFF181818),
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
          SizedBox(width: 24.sp),
          ShimmerWidget(
            child: Container(
              width: 133.sp,
              height: 34.sp,
              decoration: BoxDecoration(
                color: const Color(0xFF181818),
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class MylistCollectViewShimmer extends StatelessWidget {
  const MylistCollectViewShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildContent();
  }

  Widget _buildContent() {
    double childAspectRatio = 114 / 219;
    return GridView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.sp),
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 12,
      shrinkWrap: true,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 16.sp,
        crossAxisSpacing: 10.sp,
        childAspectRatio: childAspectRatio,
      ),
      itemBuilder: (context, index) => _buildItem(childAspectRatio),
    );
  }

  Widget _buildItem(double aspectRatio) {
    Widget content = Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCover(),
        SizedBox(height: 11.sp * aspectRatio),
        _buildTitle(),
        SizedBox(height: 7.sp * aspectRatio),
        _buildDetail(),
      ],
    );

    return ShimmerWidget(child: content);
  }

  Widget _buildCover() {
    return AspectRatio(
      aspectRatio: 114 / 152,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4.r),
        clipBehavior: Clip.antiAlias,
        child: Container(
          color: const Color(0xFF181818),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      height: 32.sp,
      color: const Color(0xFF181818),
    );
  }

  Widget _buildDetail() {
    return Container(
      height: 14.sp,
      color: const Color(0xFF181818),
    );
  }
}

class MylistHistoryViewShimmer extends StatelessWidget {
  const MylistHistoryViewShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildContent();
  }

  Widget _buildContent() {
    return ListView.separated(
      padding: EdgeInsets.fromLTRB(16.sp, 0.0, 8.sp, 0.0),
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 6,
      shrinkWrap: true,
      separatorBuilder: (context, index) => Divider(
        height: 16.sp,
        color: Colors.transparent,
      ),
      itemBuilder: (context, index) => _buildItem(),
    );
  }

  Widget _buildItem() {
    Widget content = Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildCover(),
        SizedBox(width: 10.sp),
        Expanded(
          child: SizedBox(
            height: 107.sp,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTitle(),
                SizedBox(height: 8.sp),
                _buildLabel(),
                SizedBox(height: 8.sp),
                _buildEP(),
              ],
            ),
          ),
        ),
        SizedBox(width: 10.sp),
        _buildCollect(),
      ],
    );

    return ShimmerWidget(child: content);
  }

  Widget _buildCover() {
    return Container(
      width: 80.sp,
      height: 107.sp,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4.r),
        color: const Color(0xFF181818),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 14.sp,
          color: const Color(0xFF181818),
        ),
        SizedBox(height: 4.sp),
        FractionallySizedBox(
          widthFactor: 0.75,
          child: Container(
            height: 14.sp,
            color: const Color(0xFF181818),
          ),
        ),
      ],
    );
  }

  Widget _buildLabel() {
    return SizedBox(
      height: 20.sp,
      child: Row(
        children: [
          Container(
            height: 20.sp,
            width: 60.sp,
            color: const Color(0xFF181818),
          ),
          SizedBox(width: 6.sp),
          Container(
            height: 20.sp,
            color: const Color(0xFF181818),
            width: 60.sp,
          ),
        ],
      ),
    );
  }

  Widget _buildEP() {
    return FractionallySizedBox(
      widthFactor: 0.75,
      child: Container(
        height: 14.sp,
        color: const Color(0xFF181818),
      ),
    );
  }

  Widget _buildCollect() {
    return Container(
      height: 26.sp,
      width: 26.sp,
      decoration: BoxDecoration(
        color: const Color(0xFF181818),
        borderRadius: BorderRadius.circular(2.6.sp),
      ),
    );
  }
}

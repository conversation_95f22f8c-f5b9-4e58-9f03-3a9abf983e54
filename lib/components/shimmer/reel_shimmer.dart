import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/shimmer/shimmer_widget.dart';
import 'package:playlet/gen/assets.gen.dart';

class ReelShimmer extends StatelessWidget {
  const ReelShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          _buildBg(),
          Positioned(
            left: 16.sp,
            bottom: 50.sp,
            child: _buildText(),
          ),
          Positioned(
            bottom: 155.sp,
            right: 16.sp,
            child: _buildIcons(),
          ),
        ],
      ),
    );
  }

  Widget _buildBg() {
    return Container(
      width: Get.width,
      height: Get.height,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF222222),
            Color(0xFF000000),
          ],
        ),
      ),
    );
  }

  Widget _buildText() {
    return ShimmerWidget(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 225.sp,
            height: 20.sp,
            decoration: BoxDecoration(
              color: const Color(0xFF222222),
              borderRadius: BorderRadius.circular(2.sp),
            ),
          ),
          SizedBox(height: 6.sp),
          Container(
            width: 294.sp,
            height: 20.sp,
            decoration: BoxDecoration(
              color: const Color(0xFF222222),
              borderRadius: BorderRadius.circular(2.sp),
            ),
          ),
          SizedBox(height: 6.sp),
          Container(
            width: 294.sp,
            height: 20.sp,
            decoration: BoxDecoration(
              color: const Color(0xFF222222),
              borderRadius: BorderRadius.circular(2.sp),
            ),
          ),
          SizedBox(height: 12.sp),
          Container(
            width: 294.sp,
            height: 20.sp,
            decoration: BoxDecoration(
              color: const Color(0xFF222222),
              borderRadius: BorderRadius.circular(2.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIcons() {
    return Column(
      children: [
        _buildIcon(iconName: Assets.video.like.path),
        SizedBox(height: 20.sp),
        _buildIcon(iconName: Assets.video.vip.path),
        SizedBox(height: 20.sp),
        _buildIcon(iconName: Assets.video.list.path),
      ],
    );
  }

  Widget _buildIcon({required String iconName}) {
    return ShimmerWidget(
      child: Column(
        children: [
          Image.asset(
            iconName,
            width: 35.sp,
            height: 35.sp,
            color: const Color(0xFF222222),
          ),
          Container(
            width: 36.sp,
            height: 15.sp,
            color: const Color(0xFF222222),
          ),
        ],
      ),
    );
  }
}

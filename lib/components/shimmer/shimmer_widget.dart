import 'package:flutter/material.dart';
import 'package:playlet/components/shimmer/shimmer.dart';

class ShimmerWidget extends StatelessWidget {
  const ShimmerWidget({
    super.key,
    required this.child,
    this.baseColor = const Color(0xFF181818),
    this.highlightColor = const Color(0xFF222222),
  });
  final Widget child;
  final Color baseColor;
  final Color highlightColor;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      direction: ShimmerDirection.ltr,
      period: const Duration(milliseconds: 2000),
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: child,
    );
  }
}

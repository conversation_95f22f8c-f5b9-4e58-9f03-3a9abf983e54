import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/gen/assets.gen.dart';

class WaterMarkView extends StatelessWidget {
  final int? shortPlayType;
  final double? width;
  final double? height;
  
  const WaterMarkView({
    super.key, 
    required this.shortPlayType, 
    this.width, 
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    if (shortPlayType == 6) {
      // 测试剧，不显示水印
      return const SizedBox.shrink();
    }
    return Assets.video.waterMark.image(
      width: width ?? 86.5.sp,
      height: height ?? 22.sp,
    );
  }
}
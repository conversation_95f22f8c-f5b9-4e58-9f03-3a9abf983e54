import 'package:dlink_analytics/analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/color/color_hex.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/utils/track_event.dart';
import 'package:playlet/i18n/trans.dart';


/*
示例：无网络
EmptyWidget(
  type: EmptyType.nonetwotwork,
  isShowDefaultRefreshWidgetWhenError: true,// 只有在nonetwok时才会展示
  onRefresh: () { // 只有在nonetwok时才会展示 ,并且isShowDefaultRefreshWidgetWhenError为true时才会回调
    print('onRefresh');
  },

)

没有数据
EmptyWidget(
  type: EmptyType.noContent
)

*/

enum EmptyPageFrom {
  discover,
  collections,
  recently,
  shorts,
  immersion,
  topup,
  check_in,
  more,
  rewards,

}

enum EmptyType {
  noContent,
  noNetwork,
  timeout
}

class EmptyModel {
  final String message;
  final String image;

  EmptyModel({required this.message, required this.image});
}

extension EmptyModelExtension on EmptyType {
  EmptyModel get model {
    switch (this) {
      case EmptyType.noContent:
        return EmptyModel(
          message: AppTrans.noMoreContents(),
          image: 'assets/empty/empty_noContent.png',
        );
      case EmptyType.noNetwork:
        return EmptyModel(
          message: AppTrans.noNetwork(),
          image: 'assets/empty/empty_noNetwork.png',
        );
        case EmptyType.timeout:
        return EmptyModel(
          message: AppTrans.timeout(),
          image: 'assets/empty/empty_timeout.png',
        );
    }
  }
}

class EmptyWidget extends StatefulWidget {

  // 页面来源
  final EmptyPageFrom pageFrom;
  final EmptyType type;
  // 是否使用默认的刷新widget，在非空视图状态下默认为true，如果为false，则不展示,配合customChild使用,如果使用了customChild，则isShowDefaultRefreshWidget会失效
  final bool isShowDefaultRefreshWidget;
  // 自定义子widget,如果使用了这个自定义的customChild，则isShowDefaultRefreshWidget会失效,回调也会失效,相应的事件有child外部自己提供
  final Widget? customChild;
  // 添加一个回调函数，用于默认刷新控件回调操作,默认的刷新控件点击回调
  final VoidCallback? onRefresh;

  const EmptyWidget({
    Key? key,
    required this.pageFrom,
    required this.type,
    this.isShowDefaultRefreshWidget = true,
    this.customChild,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<EmptyWidget> createState() => _EmptyWidgetState();
}

class _EmptyWidgetState extends State<EmptyWidget> {

  @override
  Widget build(BuildContext context) {
    final emptyModel = widget.type.model;
    return
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(emptyModel.image, width: 120.sp, height: 120.sp),
              SizedBox(height: 10.sp),
              Text(
                emptyModel.message,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: ColorHex.fromHex('#999999'),
                  fontWeight: FontWeight.w400,
                ),
              ),
              _getEmptyRefreshWidget(),
             ],
          ),
        );
  }

  Widget _getEmptyRefreshWidget() {

              if (widget.customChild != null) {
                return  widget.customChild!;
              } else {
                if (widget.isShowDefaultRefreshWidget == true && widget.type != EmptyType.noContent) {
                 return GestureDetector(
                    onTap: widget.onRefresh,
                    child: _EmptyDefaultRefreshWidget(),
                  );
                }
                return Container();
              }
          }

}

Widget _EmptyDefaultRefreshWidget() {
  return Container(
    width: 154.sp,
    height: 40.sp,
    alignment: Alignment.center,
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(20.sp),
    ),
    margin: const EdgeInsets.only(top: 40),
    child: Text(AppTrans.emptyRefresh(), style: TextStyle(color: Colors.black),)
  );
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain.dart';
import 'package:playlet/modules/home/<USER>/dialog_chain_manager.dart';
import 'package:playlet/modules/login/login_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/theme/theme.dart';
import 'package:playlet/utils/date_extension.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';

/// 展示条件：
/// （1）每日首次启动App
/// （2）若用户为游客模式
/// （3）一天一次

const String _kMetaLoginAlertShowTimestamp = 'kMetaLoginAlertShowTimestamp';

Future<void> showMetaLoginAlert() async {
  UserService userService = Get.find<UserService>();

  /// 判断：已登录不展示
  if (userService.isLogin == true) {
    return;
  }

  /// 判断：今天已展示过就不展示
  int? lastTimestamp = SafeStorage().read<int?>(_kMetaLoginAlertShowTimestamp);
  if (lastTimestamp != null) {
    DateTime lastDate = DateTime.fromMillisecondsSinceEpoch(lastTimestamp);
    if (lastDate.isToday() == true) {
      return;
    }
  }

  DialogChainManager.instance.addOpportunityChain(
    DialogChain(
        _MetaLoginAlertContainer(
          bonus: userService.metaLoginBonus.value ?? '',
          onSignIn: () async {
            return await AppNavigator.startLoginPage(
              params: LoginPageParams(
                trackFrom: EventValue.loginGift,
                trackScene: EventValue.loginGift,
                fbRewardsTrackFrom: EventValue.discoverLogin,
              ),
            );
          },
        ),
        DialogChainManager.accountLoginGuide, onShow: () async {
      await SafeStorage().write(
          _kMetaLoginAlertShowTimestamp, DateTime.now().millisecondsSinceEpoch);
      useTrackEvent(TrackEvent.loginGiftShow);
    },
        markTag: 'metaLogin',
        dialogOptions: DialogOptions(clickMaskDismiss: false)),
  );
}

class _MetaLoginAlertContainer extends StatelessWidget {
  const _MetaLoginAlertContainer({
    required this.onSignIn,
    required this.bonus,
  });

  final Future<void> Function() onSignIn;
  final String? bonus;

  static const _whiteColor = Colors.white;
  static const _redColor = Color(0xFFFF4500);
  static final _shadow = Shadow(
    offset: const Offset(0, 4),
    blurRadius: 4,
    color: const Color(0xFF000000).withValues(alpha: 0.2),
  );

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: AppTheme.theme,
      child: Center(
        child: Padding(
          /// 设置底部偏移量，竖屏时为超出弹窗顶部的高度的一半
          padding: EdgeInsets.only(bottom: 42.sp),
          child: SizedBox(
            width: 300.sp,
            height: 384.sp,
            child: Stack(
              children: [
                Positioned.fill(
                    child: Assets.login.loginGuideBg.image(
                  fit: BoxFit.fitWidth,
                  width: 300.sp,
                  height: 384.sp,
                )),
                _buildContent(),
                _buildClose(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 123.sp),
        _buildTitle(),
        SizedBox(height: 6.sp),
        _buildBonus(),
        const Spacer(),
        _buildLoginButton(),
        SizedBox(height: 20.sp),
      ],
    );
  }

  Widget _buildTitle() {
    return Padding(
      padding: EdgeInsets.only(left: 20.sp),
      child: Text(
        AppTrans.welomeToFlareFlow(),
        textAlign: TextAlign.left,
        style: TextStyle(
          color: _whiteColor,
          fontSize: 22.sp,
          fontWeight: FontWeight.w700,
          decoration: TextDecoration.none,
          height: 1.50,
          shadows: [_shadow],
        ),
      ),
    );
  }

  Widget _buildBonus() {
    return Padding(
      padding: EdgeInsets.only(left: 20.sp),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          _buildText("+", 32.sp),
          _buildText(" $bonus", 32.sp),
          SizedBox(width: 5.sp),
          _buildText(AppTrans.bonus(), 20.sp),
        ],
      ),
    );
  }

  Widget _buildLoginButton() {
    return Align(
      alignment: Alignment.center,
      child: GestureDetector(
        onTap: () async {
          useTrackEvent(TrackEvent.loginGiftClick, extra: {
            EventKey.action: 'button',
          });
          SmartDialog.dismiss();
          return await onSignIn();
        },
        child: Container(
          width: 230.sp,
          height: 40.sp,
          padding: EdgeInsets.symmetric(horizontal: 16.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(6.r)),
            color: _redColor,
          ),
          alignment: Alignment.center,
          child: FittedBox(
            fit: BoxFit.scaleDown,
            alignment: Alignment.center,
            child: Text(
              AppTrans.signInToGet(),
              maxLines: 1,
              style: TextStyle(
                color: _whiteColor,
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                decoration: TextDecoration.none,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClose() {
    return Positioned(
      top: 90.sp,
      right: 6.sp,
      child: IconButton(
        onPressed: () {
          useTrackEvent(TrackEvent.loginGiftClick, extra: {
            EventKey.action: 'close',
          });
          SmartDialog.dismiss();
        },
        icon: Assets.login.loginGuideClose.image(
          width: 24.sp,
          height: 24.sp,
        ),
      ),
    );
  }

  Widget _buildText(String text, double fontSize) {
    return Text(
      text,
      style: TextStyle(
        color: _whiteColor,
        fontSize: fontSize,
        fontWeight: FontWeight.w800,
        decoration: TextDecoration.none,
        fontStyle: FontStyle.italic,
        height: 1.50,
        shadows: [_shadow],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/components/alert/action.dart';
import 'package:playlet/components/alert/index.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/routers/app_navigator.dart';

import '../../common/utils/screen_utils.dart';
import '../../service/payment/payment_events.dart';

void showSubscriptionExpiredAlert({required subscriptionDesc}) {
  FFAlert.show(
      container: FFAlertActionContainer(
        title: AppTrans.subscriptionExpired(),
        detail:
            '${AppTrans.subscriptionYour()} [$subscriptionDesc] ${AppTrans.subscriptionExpiredDetail()}',
        actions: [
          FFAlertActionButton.cancel(
            text: AppTrans.noThanks(),
          ),
          FFAlertActionButton.confirm(
            text: AppTrans.subscriptionOk(),
            onPressed: () async {
              AppNavigator.startSubscriptionPage();
            },
          ),
        ],
      ),
      onDismiss: () {
        PaymentEvent.submitRechargeShowEnd(
            strScene: EventValue.subscribeExpirePopup,
            reelId: "",
            episode: "",
            action: "",
            lockBegin: "",
            playDirection: ScreenUtils.isLandscape(Get.context!)
              ? EventValue.horizontal
              : EventValue.vertical,
        );
      });
}

void showSubscriptionBlockAlert() {
  FFAlert.action(
    container: FFAlertActionContainer(
      topIcon: Assets.profile.subscriptionBlock.path,
      actionAlignment: FFAlertActionLayoutAlignment.vertical,
      detail: AppTrans.subscriptionFeature(),
      actions: [
        FFAlertActionButton.confirm(
          text: AppTrans.confirm(),
          isMaxWidth: true,
          padding: EdgeInsets.symmetric(horizontal: 35.w),
        ),
      ],
    ),
  );
}

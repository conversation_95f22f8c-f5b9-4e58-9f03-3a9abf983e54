import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:playlet/components/alert/index.dart';

enum FFAlertActionLayoutAlignment {
  horizontal,
  vertical,
}

class FFAlertActionContainer extends FFAlertContainer {
  final List<FFAlertActionButton> actions;
  final String? title;
  final String? detail;
  final TextStyle titleStyle;
  final TextStyle? detailStyle;
  final TextAlign? detailTextAlign;
  final FFAlertActionLayoutAlignment actionAlignment;
  final String? topIcon;

  FFAlertActionContainer({
    super.key,
    required this.actions,
    this.title,
    this.detail,
    this.titleStyle = const TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: Colors.white,
    ),
    this.detailStyle = const TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: Color(0xFF999999),
      height: 1.5,
    ),
    this.detailTextAlign = TextAlign.center,
    this.actionAlignment = FFAlertActionLayoutAlignment.horizontal,
    this.topIcon,
    super.close,
    super.padding,
  }) : super(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              /// 顶部图标
              topIcon == null
                  ? const SizedBox.shrink()
                  : Padding(
                      padding: const EdgeInsets.only(bottom: 21),
                      child: Image.asset(topIcon, fit: BoxFit.fitWidth),
                    ),

              /// 标题
              title == null
                  ? const SizedBox.shrink()
                  : Padding(
                      padding: const EdgeInsets.only(bottom: 21),
                      child: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),

              /// 内容
              detail == null
                  ? const SizedBox.shrink()
                  : Padding(
                      padding: const EdgeInsets.only(bottom: 24),
                      child: Text(
                        detail,
                        style: detailStyle,
                        textAlign: TextAlign.center,
                      ),
                    ),

              /// 操作按钮
              actionAlignment == FFAlertActionLayoutAlignment.vertical ||
                      actions.length > 2
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: actions.map((e) {
                        FFAlertActionButton ele = e.copyWith(
                          onPressed: () async {
                            if (e.onPressed != null) {
                              await e.onPressed!();
                            }
                            SmartDialog.dismiss();
                          },
                        );
                        return ele;
                      }).toList(),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      mainAxisSize: MainAxisSize.max,
                      children: actions.map((e) {
                        FFAlertActionButton ele = e.copyWith(
                          onPressed: () async {
                            if (e.onPressed != null) {
                              await e.onPressed!();
                            }
                            SmartDialog.dismiss();
                          },
                        );
                        return ele;
                      }).toList(),
                    ),
            ],
          ),
        );
}

class FFAlertActionButton extends StatelessWidget {
  const FFAlertActionButton({
    super.key,
    required this.text,
    this.textStyle = const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      color: Colors.white,
    ),
    this.onPressed,
    this.backgroundColor = const Color(0xFF383A46),
    this.hasBorder = false,
    this.padding = EdgeInsets.zero,
    this.isMaxWidth = false,
  });
  final String text;
  final TextStyle textStyle;
  final FutureOr<void> Function()? onPressed;
  final Color backgroundColor;
  final bool hasBorder;
  final EdgeInsets padding;
  final bool isMaxWidth;

  factory FFAlertActionButton.text({
    required String text,
    Future<void> Function()? onPressed,
    TextStyle textStyle = const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      color: Color(0xFF828282),
    ),
    final bool isMaxWidth = false,
  }) {
    return FFAlertActionButton(
      text: text,
      onPressed: onPressed,
      textStyle: textStyle,
      backgroundColor: Colors.transparent,
      isMaxWidth: isMaxWidth,
    );
  }

  factory FFAlertActionButton.cancel({
    FutureOr<void> Function()? onPressed,
    EdgeInsets padding = EdgeInsets.zero,
    bool isMaxWidth = false,
    String text = 'Cancel',
  }) {
    return FFAlertActionButton(
      text: text,
      hasBorder: true,
      padding: padding,
      onPressed: onPressed,
      isMaxWidth: isMaxWidth,
    );
  }

  factory FFAlertActionButton.confirm({
    FutureOr<void> Function()? onPressed,
    EdgeInsets padding = EdgeInsets.zero,
    bool isMaxWidth = false,
    String text = 'Confirm',
  }) {
    return FFAlertActionButton(
      text: text,
      hasBorder: false,
      backgroundColor: const Color(0xFFFF4500),
      padding: padding,
      onPressed: onPressed,
      isMaxWidth: isMaxWidth,
    );
  }

  FFAlertActionButton copyWith({
    String? text,
    TextStyle? textStyle,
    FutureOr<void> Function()? onPressed,
    Color? backgroundColor,
    bool? hasBorder,
    EdgeInsets? padding,
    bool? isMaxWidth,
  }) {
    return FFAlertActionButton(
      text: text ?? this.text,
      textStyle: textStyle ?? this.textStyle,
      onPressed: onPressed ?? this.onPressed,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      hasBorder: hasBorder ?? this.hasBorder,
      padding: padding ?? this.padding,
      isMaxWidth: isMaxWidth ?? this.isMaxWidth,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: TextButton(
        onPressed: onPressed,
        style: ButtonStyle(
          backgroundColor: WidgetStatePropertyAll(backgroundColor),
          enableFeedback: true,
          minimumSize: WidgetStatePropertyAll(
              Size(isMaxWidth ? double.infinity : 110.0, 40.0)),
          fixedSize: WidgetStatePropertyAll(
              Size(isMaxWidth ? double.infinity : 110.0, 40.0)),
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6.0),
            ),
          ),
          side: WidgetStatePropertyAll(
            hasBorder == false
                ? BorderSide.none
                : BorderSide(
                    color: Colors.white.withAlpha((255.0 * 0.3).round()),
                    width: 1.0,
                  ),
          ),
        ),
        child: Text(text, style: textStyle),
      ),
    );
  }
}

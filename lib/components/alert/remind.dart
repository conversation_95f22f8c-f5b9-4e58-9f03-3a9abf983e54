import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/components/alert/index.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/date_extension.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';

/// 弹出场景:
/// (1)场景一：付费成功后（剧集内付费成功后、topup页面付费成功后
/// (2)  2. 场景二：广告解锁：看广告的等激励视频播放完成后弹出
/// 弹窗条件:
/// (1)到达指定场景
/// (2)用户为游客模式
/// (3)一天一次
/// 页面说明&交互：
/// 1. 弹窗界面文案“为了保护您的财产，请登录账号以免信息丢失“
/// 2. 点击login，在剧集播放页面拉起登录入口
/// 3. 登录成功/点击关闭登录，仍返回该剧集的播放界面
/// 4. 若用户使用facebook登录，登陆成功后，自动发放奖励，并toast提示 “+100 bonus”，同时新手任务中剔除FB绑定任务
/// 5. 点击下方关闭，弹窗消失继续播放视频
/// 6. 注：弹出登录弹窗时，视频暂停

const String _kProtectProperityRemindAlertShowTimestamp =
    'kProtectProperityRemindAlertShowTimestamp';

bool checkShouldShowProtectProperityRemindAlert() {
  UserService userService = Get.find<UserService>();

  /// 判断：已登录不展示
  if (userService.isLogin == true) {
    return false;
  }

  /// 判断：今天已展示过就不展示
  int? lastTimestamp =
      SafeStorage().read<int?>(_kProtectProperityRemindAlertShowTimestamp);
  if (lastTimestamp != null) {
    DateTime lastDate = DateTime.fromMillisecondsSinceEpoch(lastTimestamp);
    if (lastDate.isToday() == true) {
      return false;
    }
  }
  return true;
}

Future<bool?> showProtectProperityRemindAlert({required bool inDetails}) async {
  await SafeStorage().write(_kProtectProperityRemindAlertShowTimestamp,
      DateTime.now().millisecondsSinceEpoch);

  /// 资产保护提醒弹窗展示埋点
  useTrackEvent(TrackEvent.assetProtectionShow);

  /// 展示
  bool? result = await SmartDialog.show<bool?>(
    clickMaskDismiss: false,
    alignment: Alignment.center,
    animationType: SmartAnimationType.centerFade_otherSlide,
    builder: (context) => _ProtectProperityRemindAlertContainer(),
  );
  return result;
}

class _ProtectProperityRemindAlertContainer extends FFAlertContainer {
  _ProtectProperityRemindAlertContainer()
      : super(
          close: true,
          onClose: () {
            /// 资产保护提醒弹窗点击埋点
            useTrackEvent(
              TrackEvent.assetProtectionClick,
              extra: {'action': 'close'},
            );
          },
          backgroundImage: Assets.alertGradientBg.image(
            width: 300.sp,
            fit: BoxFit.fitWidth,
          ),
          child: SizedBox(
            width: 300.sp,
            child: Column(
              children: [
                Assets.remind.image(
                  width: 84.sp,
                  fit: BoxFit.fitWidth,
                ),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 18.sp, vertical: 30.sp),
                  child: Text(
                    AppTrans.protectProperityRemindContent(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.sp),
                  child: ElevatedButton(
                    onPressed: () {
                      SmartDialog.dismiss(result: true);

                      /// 资产保护提醒弹窗点击埋点
                      useTrackEvent(
                        TrackEvent.assetProtectionClick,
                        extra: {'action': 'button'},
                      );
                    },
                    style: ButtonStyle(
                      padding: const WidgetStatePropertyAll(EdgeInsets.zero),
                      shape: const WidgetStatePropertyAll(StadiumBorder()),
                      elevation: const WidgetStatePropertyAll(0.0),
                      visualDensity: VisualDensity.standard,
                      enableFeedback: true,
                      minimumSize: WidgetStatePropertyAll(
                        Size(double.infinity, 40.sp),
                      ),
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: Container(
                      alignment: Alignment.center,
                      constraints: BoxConstraints(
                        minWidth: double.infinity,
                        minHeight: 40.sp,
                      ),
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFFD9020),
                            Color(0xFFFF4500),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                      child: Text(
                        AppTrans.login(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
}

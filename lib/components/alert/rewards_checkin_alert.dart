import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:playlet/components/alert/index.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';

class RewardsCheckinAlert extends FFAlertContainer {
  final int bonus;
  final VoidCallback onNowLook;

  RewardsCheckinAlert({
    super.key,
    required this.bonus,
    required this.onNowLook,
  }) : super(
          /// 设置底部偏移量，竖屏时为超出弹窗顶部的高度的一半
          bottomOffset: 45.sp,
          close: true,
          backgroundImage: Assets.alertGradientBg.image(
            width: 300.w,
            fit: BoxFit.fitWidth,
          ),
          header: Stack(
            alignment: AlignmentDirectional.bottomCenter,
            children: [
              Assets.rewards.imgCheckinAlertHeader.image(
                width: 300.sp,
                height: 90.sp,
              ),
              Container(
                width: 200.sp,
                height: 46.sp,
                alignment: Alignment.center,
                child: Text(
                  AppTrans.congratulations(),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          child: SizedBox(
            width: 300.sp,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Assets.rewards.imgCheckinAlertAd.image(
                  width: 100.sp,
                  height: 100.sp,
                ),
                SizedBox(height: 8.sp),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Assets.imgCoinB.image(
                      width: 33.sp,
                      height: 33.sp,
                    ),
                    Text(
                      '+',
                      style: TextStyle(
                        fontSize: 26.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFFFFCD00),
                      ),
                    ),
                    Text(
                      bonus.toString(),
                      style: TextStyle(
                        fontSize: 32.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFFFFCD00),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 6.sp),
                      child: Text(
                        AppTrans.bonus(),
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w700,
                          color: const Color(0xFFFFCD00),
                        ),
                      ),
                    ),
                  ],
                ),
                Text(
                  '${AppTrans.watchAnAd()}\n${AppTrans.getDoubleRewards()}',
                  maxLines: 2,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFFFFFFFF),
                    height: 1.5,
                  ),
                ),
                SizedBox(height: 24.sp),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.sp),
                  child: ElevatedButton(
                    onPressed: () {
                      SmartDialog.dismiss();
                      onNowLook();
                    },
                    style: ButtonStyle(
                      padding: const WidgetStatePropertyAll(EdgeInsets.zero),
                      shape: const WidgetStatePropertyAll(StadiumBorder()),
                      elevation: const WidgetStatePropertyAll(0.0),
                      visualDensity: VisualDensity.standard,
                      enableFeedback: true,
                      minimumSize: WidgetStatePropertyAll(
                        Size(double.infinity, 40.sp),
                      ),
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: Container(
                      alignment: Alignment.center,
                      constraints: BoxConstraints(
                        minWidth: double.infinity,
                        minHeight: 40.sp,
                      ),
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFFD9020),
                            Color(0xFFFF4500),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                      child: Text(
                        AppTrans.nowLook(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.sp,
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        );
}

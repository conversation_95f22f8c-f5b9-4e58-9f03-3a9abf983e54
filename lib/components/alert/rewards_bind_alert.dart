import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:playlet/components/alert/index.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';

import '../../model/rewards_bind/bind_options.dart';

class RewardsBindAlert extends FFAlertContainer {
  final int bonus;
  final RewardsBindType type;
  final VoidCallback onClick;

  RewardsBindAlert({
    super.key,
    required this.bonus,
    required this.type,
    required this.onClick,
  }) : super(
          close: true,
          backgroundImage: Assets.alertGradientBg.image(
            width: 300.w,
            fit: BoxFit.fitWidth,
          ),
          child: SizedBox(
            width: 300.w,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height:20.h,
                ),
                Image.asset(
                  type == RewardsBindType.Phone
                      ? Assets.rewardsBind.imgBindPhoneSuccess.path
                      : Assets.rewardsBind.imgBindEmailSuccess.path,
                  fit: BoxFit.fitHeight,
                  height: type == RewardsBindType.Phone ? 80.h : 56.h,
                ),
                SizedBox(
                  height: type == RewardsBindType.Phone ? 11.h : 30.h,
                ),
                Text(
                  type == RewardsBindType.Phone
                      ? AppTrans.phoneBindingSuccess()
                      : AppTrans.emailBindingSuccess(),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                      fontSize: 16.sp),
                ),
                SizedBox(
                  height: type == RewardsBindType.Phone ? 11.h : 16.h,
                ),

                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Assets.imgCoinB
                        .image(width: 20.r, height: 20.r, fit: BoxFit.fill),
                    const SizedBox(width: 4),
                    Text(
                      "+${bonus.toString()}",
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFFFFCD00),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      AppTrans.bonus(),
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: type == RewardsBindType.Phone ? 24.h : 40.h,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: ElevatedButton(
                    onPressed: () {
                      SmartDialog.dismiss();
                      onClick();
                    },
                    style: const ButtonStyle(
                      padding: WidgetStatePropertyAll(EdgeInsets.zero),
                      backgroundColor: WidgetStatePropertyAll(Colors.green),
                      shape: WidgetStatePropertyAll(StadiumBorder()),
                      elevation: WidgetStatePropertyAll(0.0),
                      visualDensity: VisualDensity.standard,
                      enableFeedback: true,
                      minimumSize: WidgetStatePropertyAll(
                        Size(double.infinity, 40),
                      ),
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: Container(
                      alignment: Alignment.center,
                      constraints: const BoxConstraints(
                        minWidth: double.infinity,
                        minHeight: 40,
                      ),
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFFD9020),
                            Color(0xFFFF4500),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                      child: Text(
                        AppTrans.goToReceive(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        );
}

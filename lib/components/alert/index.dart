import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/components/alert/action.dart';
import 'package:playlet/components/alert/input.dart';
import 'package:playlet/components/alert/sheet.dart';

/*
  使用方法1:作为小部件使用
  FFAlert(
    container: FFAlertActionContainer(
      title: 'Alert Test',
      detail:
          'This is just a test for alert. You can use it as a widget. Have a good luck',
      actions: [
        FFAlertActionButton.cancel(),
        FFAlertActionButton.confirm(),
      ],
    ),
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
      child: const Text(
        'Alert Test',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    ),
  ),

  使用方法2:作为方法使用（在方法内部调用如下代码）
  FFAlert.action(
    container: FFAlertActionContainer(
      title: 'Alert Test',
      detail:
          'This is just a test for alert. You can use it as a function. Have a good luck',
      actions: [
        FFAlertActionButton.cancel(),
        FFAlertActionButton.confirm(),
      ],
    ),
  );
*/

class FFAlert extends StatelessWidget {
  const FFAlert({
    super.key,
    required this.child,
    required this.container,
    this.alertAlignment = Alignment.center,
  });
  final Widget? child;
  final FFAlertContainer container;
  final Alignment alertAlignment;

  @override
  Widget build(BuildContext context) {
    return Ink(
      child: InkWell(
        onTap: () {
          dismiss();
          if (Get.focusScope?.hasFocus == true ||
              Get.focusScope?.hasPrimaryFocus == true) {
            Get.focusScope?.unfocus();
          }
          show(container: container, alignment: alertAlignment);
        },
        child: child,
      ),
    );
  }

  static Future<void> show({
    required final Widget container,
    final Alignment alignment = Alignment.center,
    void Function()? onDismiss,
    final bool clickMaskDismiss = false,
    final animationType = SmartAnimationType.fade,
    final backType = SmartBackType.block,
  }) async {
    await SmartDialog.show(
      debounce: true,
      clickMaskDismiss: clickMaskDismiss,
      alignment: alignment,
      onDismiss: onDismiss,
      animationType: animationType,
      backType: backType,
      builder: (context) => container,
    );
  }

  static Future<void> action({
    required FFAlertActionContainer container,
  }) async {
    await FFAlert.show(
      container: container,
      alignment: Alignment.center,
    );
  }

  static Future<void> input({
    required FFAlertInputContainer container,
  }) async {
    await FFAlert.show(
      container: container,
      alignment: Alignment.center,
    );
  }

  static Future<void> sheet({
    required FFAlertSheetContainer container,
  }) async {
    await FFAlert.show(
      container: container,
      alignment: Alignment.bottomCenter,
    );
  }

  static void dismiss() {
    SmartDialog.dismiss();
  }
}

class FFAlertContainer extends StatelessWidget {
  const FFAlertContainer({
    super.key,
    required this.child,
    this.backgroundColor = const Color(0xFF25252E),
    this.backgroundImage,
    this.radius = 12.0,
    this.header,
    this.margin = const EdgeInsets.symmetric(horizontal: 30.0),
    this.padding = const EdgeInsets.fromLTRB(17.0, 24.0, 17.0, 30.0),
    this.close = false,
    this.onClose,
    this.bottomOffset = 0,
  });
  final Widget child;
  final Color backgroundColor;
  final Image? backgroundImage;
  final double radius;
  final Widget? header;
  final EdgeInsets margin;
  final EdgeInsets padding;
  final bool close;
  final VoidCallback? onClose;
  final double bottomOffset;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:  EdgeInsets.only(bottom: bottomOffset),
      child: SizedBox(
        width: 300.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            header ?? const SizedBox.shrink(),
            Container(
              padding: EdgeInsets.zero,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(radius),
              ),
              child: Stack(
                children: [
                  backgroundImage ?? const SizedBox.shrink(),
                  Padding(
                    padding: padding,
                    child: child,
                  ),
                  close == false
                      ? const SizedBox.shrink()
                      : Positioned(
                          top: 0.0,
                          right: 0.0,
                          child: IconButton(
                            onPressed: () {
                              FFAlert.dismiss();
                              onClose?.call();
                            },
                            style: const ButtonStyle(
                              backgroundColor: WidgetStatePropertyAll(
                                Color(0xFF464646),
                              ),
                              minimumSize:
                                  WidgetStatePropertyAll(Size(24.0, 24.0)),
                              fixedSize: WidgetStatePropertyAll(Size(24.0, 24.0)),
                              padding: WidgetStatePropertyAll(EdgeInsets.zero),
                            ),
                            icon: const Icon(Icons.close, size: 16),
                          ),
                        ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

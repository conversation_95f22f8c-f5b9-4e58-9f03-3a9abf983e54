import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:playlet/components/rewards_bind/select_country_code_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/model/rewards_bind/country_count_resultdart.dart';

import '../../i18n/trans.dart';
import 'country_code_widget.dart';

// 定义验证回调函数类型
typedef ValidationCallback = void Function(bool isValid, String? message);

class PhoneInputWidget extends StatefulWidget {
  final ValidationCallback onValidation;
  final List<CountryCodeInfo> countryCodeList;

  const PhoneInputWidget(this.countryCodeList,
      {super.key, required this.onValidation});

  @override
  State<PhoneInputWidget> createState() => _PhoneInputWidgetState();
}

class _PhoneInputWidgetState extends State<PhoneInputWidget> {
  late TextEditingController _controller;
  String? _errorText;
  CountryCodeInfo? selectCountryCode;

  @override
  void initState() {
    super.initState();
    selectCountryCode = widget.countryCodeList.first;

    _controller = TextEditingController();
    _controller.addListener(() {
      if (_controller.text.isNotEmpty) {
        _errorText = _validatePhoneNumber(_controller.text);
        // 仅在验证通过时调用回调
        if (_errorText == null && selectCountryCode != null) {
          //  回调时 拼接国家冠吗
          widget.onValidation(
              true, "+${selectCountryCode?.areaCode} ${_controller.text}");
        } else {
          widget.onValidation(false, _errorText);
        }
      } else {
        _errorText = null;
      }
      setState(() {});
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String? _validatePhoneNumber(String value) {
    final trimmedValue = value.replaceAll(' ', ''); // 去除字符串中的所有空格
    final digitsRegExp = RegExp(r'^\d+$');
    if (!digitsRegExp.hasMatch(trimmedValue)) {
      return AppTrans.checkEnteredContent();
    }
    return null;
  }

  void _deleteOneCharacter() {
    if (_controller.text.isNotEmpty) {
      _controller.text =
          _controller.text.substring(0, _controller.text.length - 1);
      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: _controller.text.length),
      );
    }
  }

  void _deleteAllCharacters() {
    _controller.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.only(left: 16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            color: const Color(0xFF424242),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  showSelectPopup();
                }, // 绑定外部传入的 onTap 回调
                child: Row(
                  children: [
                    CountryCodeWidget(
                      countryCode: selectCountryCode?.countryCode,
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 7.w, right: 12.w),
                      child: Assets.rewardsBind.iconRewadsMore
                          .image(width: 18.r, height: 18.r),
                    ),
                    Container(
                      width: 1.w,
                      height: 22.h,
                      color: Colors.grey,
                    ),
                    SizedBox(
                      width: 7.w,
                    ),
                    Text(
                      '+${selectCountryCode?.areaCode}',
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _controller,
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(20),
                    // PhoneNumberFormatter(),
                  ],
                  decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 16.w,
                      ),
                      hintText: AppTrans.enterPhone(),
                      hintStyle: TextStyle(
                          fontSize: 13.sp, color: const Color(0xFF9E9E9E))),
                ),
              ),
              if (_controller.text.isNotEmpty)
                IconButton(
                  icon: Assets.rewardsBind.iconRewadsDelete.image(
                    width: 22.r,
                    height: 22.r,
                  ),
                  onPressed: _deleteOneCharacter,
                  onLongPress: _deleteAllCharacters,
                  splashRadius: 10,
                ),
            ],
          ),
        ),
        if (_errorText != null)
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: Row(
              children: [
                Assets.rewardsBind.imgInputError
                    .image(width: 16.r, height: 16.r),
                SizedBox(
                  width: 6.w,
                ),
                Text(
                  _errorText!,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.red,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Future<void> showSelectPopup() async {
    FocusScope.of(context).unfocus();

    await SmartDialog.show(
      alignment: Alignment.bottomCenter,
      keepSingle: true,
      builder: (context) => SelectCountryCodeWidget(
        widget.countryCodeList,
        onCountrySelected: (item) {
          setState(() {
            selectCountryCode = item;
            SmartDialog.dismiss();
          });
        },
        onExitClick: () {
          SmartDialog.dismiss();
        },
      ),
    );
  }
}

class PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    String newText = newValue.text.replaceAll(RegExp(r'[^0-9]'), '');
    String formattedText = '';

    for (int i = 0; i < newText.length; i++) {
      if (i == 3 || i == 7) {
        formattedText += ' ';
      }
      formattedText += newText[i];
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}

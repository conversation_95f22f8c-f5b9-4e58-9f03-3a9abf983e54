import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../i18n/trans.dart';

class GetVerificationButton extends StatelessWidget {
  final bool isEnabled;
  final VoidCallback? onPressed;

  const GetVerificationButton({
    super.key,
    required this.isEnabled,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isEnabled ? onPressed : null,
      // onTap:   onPressed ,
      child: Container(
        width:  336.w,
        height: 48.h,
        decoration: BoxDecoration(
          color: isEnabled ? const Color(0xFFFFCD00) : const Color(0xFF424242),
          borderRadius: BorderRadius.circular(80),
        ),
        child: Center(
          child: Text(
            AppTrans.getVerificationCode(),
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 16.sp,
              color: isEnabled ? const Color(0xFF121212) : const Color(0xFF9E9E9E),
            ),
          ),
        ),
      ),
    );
  }
}

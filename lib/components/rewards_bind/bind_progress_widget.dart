import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BindProgressWidget extends StatelessWidget {
  static const int inputState = 1;
  static const int verificationState = 2;
  final int currentStep; // 当前进度步骤，范围为 1 到 3

  const BindProgressWidget({super.key, required this.currentStep});

  @override
  Widget build(BuildContext context) {
    // 定义颜色
    const Color completedColor = Color(0xFFFFCD00);
    const Color incompleteColor = Color(0xFF6E6E6E);
    const Color completedTextColor = Colors.black;
    const Color incompleteTextColor = Colors.white;
    // 定义字体大小
    double fontSize = 15.sp;

    // 构建一个圆形进度项
    Widget buildProgressItem(int step) {
      bool isCompleted = step <= currentStep;
      Color color = isCompleted ? completedColor : incompleteColor;
      Color textColor = isCompleted ? completedTextColor : incompleteTextColor;

      return Container(
        width: 24.r,
        height: 24.r,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            step.toString(),
            style: TextStyle(
              color: textColor,
              fontSize: fontSize,
            ),
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        buildProgressItem(1),
        Expanded(child: ConditionalContainer(condition: currentStep == 1,),),
        buildProgressItem(2),
        Expanded(child: ConditionalContainer(condition: currentStep == 2,),),
        buildProgressItem(3),
      ],
    );



  }
}




class ConditionalContainer extends StatelessWidget {
  final bool condition;

  const ConditionalContainer({super.key, required this.condition});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: condition
          ? Container(
        height: 3.h,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [Color(0xFFFFCD00), Color(0xFF3E3E3E)],
            stops: [0.0, 0.8194],
          ),
        ),
      )
          : Container(
        height: 2.h,
        color: const Color(0xFF6E6E6E),
      ),
    );
  }
}




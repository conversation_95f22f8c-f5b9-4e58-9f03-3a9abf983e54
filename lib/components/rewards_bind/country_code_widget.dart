import 'package:flag/flag_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CountryCodeWidget extends StatelessWidget {
  final String? countryCode;
  final double? width;
  final double? height;

  const CountryCodeWidget({
    super.key,
    required this.countryCode,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return  Flag.fromString(
      countryCode??"",
      height: height?? 20.r,
      width: width?? 26.r,
      fit: BoxFit.fill,
    );


  }
}


import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/model/rewards_bind/country_count_resultdart.dart';

import 'country_code_widget.dart';

class CountryCodeItem extends StatelessWidget {
  final CountryCodeInfo countryCodeInfo;
  final VoidCallback? onSelected; // 新增回调


  const CountryCodeItem(this. countryCodeInfo, {super.key,this.onSelected});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onSelected, // 点击触发回调
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.sp),
        child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
          CountryCodeWidget( countryCode: countryCodeInfo.countryCode,),
              SizedBox(width: 13.w),
              Text(
                '${countryCodeInfo.countryName} (+${countryCodeInfo.areaCode})',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w400),
              ),
        ]),
      ),
    )
    ;
  }
}

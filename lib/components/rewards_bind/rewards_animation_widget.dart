import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/routers/app_navigator.dart';

import '../../common/event/track_event.dart';
import '../../gen/assets.gen.dart';
import '../../modules/rewards/event/rewards_event.dart';

class RewardsAnimationWidget extends StatelessWidget {
  const RewardsAnimationWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: () {
          RewardsEvent.submitHomeRewardClick();
          AppNavigator.startRewardsPage(from:TrackEvent.discover);
        },
        child: Padding(
          padding:  EdgeInsets.only(right: 12.w),
          child: Assets.rewardsBind.imgRewardsAnimation
              .image(width: 36.r, height: 36.r),
        ));
  }
}

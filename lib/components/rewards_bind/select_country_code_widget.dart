import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/model/rewards_bind/country_count_resultdart.dart';

import '../../gen/assets.gen.dart';
import 'country_code_item.dart';

class SelectCountryCodeWidget extends StatelessWidget {
  final VoidCallback? onExitClick;
  final List<CountryCodeInfo> countryCodeList;
  final ValueChanged<CountryCodeInfo>? onCountrySelected; // 新增回调

  const SelectCountryCodeWidget(this.countryCodeList,
      {super.key, this.onExitClick, this.onCountrySelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      constraints: BoxConstraints(
        maxHeight: 0.5 * MediaQuery.of(context).size.height,
      ),
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.only(
                left: 20.sp, right: 20.sp, bottom: 20.sp, top: 15.sp),
            decoration: BoxDecoration(
              color: const Color(0XFF1E1E1E),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30.r),
                topRight: Radius.circular(30.r),
              ),
            ),
            child: ListView.builder(
              itemCount: countryCodeList.length,
              padding: EdgeInsets.zero,
              itemBuilder: (BuildContext context, int index) {
                return CountryCodeItem(
                  countryCodeList[index],
                  onSelected: () =>
                      onCountrySelected?.call(countryCodeList[index]),
                );
              },
            ),
          ),
          // 退出按钮固定在右上角
          Positioned(
            top: 20.sp,
            right: 20.sp,
            child: InkWell(
              onTap: onExitClick,
              child: Assets.store.iconStorePopExit.image(
                width: 24.r,
                height: 24.r,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
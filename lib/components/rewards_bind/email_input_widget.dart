import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/gen/assets.gen.dart';

import '../../i18n/trans.dart';

// 定义验证回调函数类型
typedef ValidationCallback = void Function(bool isValid, String? inputValue);

class EmailInputWidget extends StatefulWidget {
  final ValidationCallback onValidation; // 仅保留验证回调参数

  const EmailInputWidget({super.key, required this.onValidation});

  @override
  State<EmailInputWidget> createState() => _EmailInputWidgetState();
}

class _EmailInputWidgetState extends State<EmailInputWidget> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _controller.addListener(() {
      if (_controller.text.isNotEmpty) {
        _errorText = _validateEmail(_controller.text);
        if (_errorText == null) {
          // 验证通过，传递输入的邮箱地址
          widget.onValidation(true, _controller.text);
        } else {
          widget.onValidation(false, _errorText);
        }
      } else {
        _errorText = null;
      }
      setState(() {});
    });
  }

  @override
  void dispose() {
    // 销毁 TextEditingController
    _controller.dispose();
    super.dispose();
  }

  String? _validateEmail(String value) {
    final emailRegExp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegExp.hasMatch(value)) {
      return AppTrans.checkEnteredContent();
    }
    return null;
  }

  void _deleteOneCharacter() {
    if (_controller.text.isNotEmpty) {
      _controller.text =
          _controller.text.substring(0, _controller.text.length - 1);
      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: _controller.text.length),
      );
    }
  }

  void _deleteAllCharacters() {
    _controller.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.only(left: 16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            color: const Color(0xFF424242),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: TextField(
                  controller: _controller,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: AppTrans.enterEmailAddress(),
                    hintStyle: TextStyle(
                      fontSize: 13.sp,
                      color: const Color(0xFF9E9E9E),
                    ),
                  ),
                ),
              ),
              if (_controller.text.isNotEmpty)
                IconButton(
                  icon: Assets.rewardsBind.iconRewadsDelete.image(
                    width: 22.r,
                    height: 22.r,
                  ),
                  onPressed: _deleteOneCharacter,
                  onLongPress: _deleteAllCharacters,
                  splashRadius: 10,
                ),
            ],
          ),
        ),
        if (_errorText != null)
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: Row(
              children: [
                Assets.rewardsBind.imgInputError
                    .image(width: 16.r, height: 16.r),
                SizedBox(
                  width: 6.w,
                ),
                Text(
                  _errorText!,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.red,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/controller/bottom_nav_controller.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/service/recommend_service.dart';
import 'package:playlet/utils/track_event.dart';

class HomeRecommend extends StatefulWidget {
  const HomeRecommend({super.key});

  @override
  State<HomeRecommend> createState() => _HomeRecommendState();
}

class _HomeRecommendState extends State<HomeRecommend> {
  final RecommendService recommendService = Get.find<RecommendService>();
  final BottomNavController bottomNavController =
      Get.find<BottomNavController>();

  RxBool isShow = false.obs;

  @override
  void initState() {
    onShow();
    bottomNavController.currentRoute.obs.listen((val) {
      onShow();
    });
    bottomNavController.pageIndex.obs.listen((val) {
      onShow();
    });
    super.initState();
  }

  void onShow() {
    if (bottomNavController.pageIndex.value == 0 &&
        bottomNavController.currentRoute.value == Routes.mainPage) {
      isShow.value = true;
    } else {
      isShow.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (isShow.value == false) {
          return const SizedBox();
        }
        if (recommendService.isShow.value == true &&
            !recommendService.isMoveShow.value == true) {
          return Positioned(
            bottom: 70.sp,
            right: 12.sp,
            child: GestureDetector(
              onTap: () {
                useTrackEvent(TrackEvent.suspensionButtonClick);
                AppNavigator.startRecommendPage(from: "suspension_button");
              },
              child: SizedBox(
                width: 86.sp,
                height: 84.sp,
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    Positioned(
                      top: 0,
                      child: Assets.home.homeRecommend.image(
                        width: 76.sp,
                        height: 74.sp,
                      ),
                    ),
                    Container(
                      width: 86.sp,
                      height: 33.sp,
                      decoration: BoxDecoration(
                        color: const Color.fromRGBO(255, 184, 86, 1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: _buildTimeWidget(),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
        return const SizedBox();
      },
    );
  }

  Widget _buildTimeWidget() {
    return Obx(
      () => recommendService.isShow.value == true
          ? _buildDownTimeWidget()
          : const SizedBox.shrink(),
    );
  }

  Widget _buildDownTimeWidget() {
    return FutureBuilder(
      future: Future.delayed(const Duration(milliseconds: 60)),
      builder: (context, snapshot) {
        final textStyle = TextStyle(
          fontSize: 13.sp,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        );
        return Obx(
          () => Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 20.59.sp,
                height: 20.59.sp,
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(49, 49, 49, 1),
                  borderRadius: BorderRadius.circular(2.r),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      recommendService.timeRemaining.value.hours.split("")[0],
                      style: textStyle,
                    ),
                    Text(
                      recommendService.timeRemaining.value.hours.split("")[1],
                      style: textStyle,
                    ),
                  ],
                ),
              ),
              Container(
                width: 6.sp,
                alignment: Alignment.center,
                child: Text(":", style: textStyle),
              ),
              Container(
                width: 20.59.sp,
                height: 20.59.sp,
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(49, 49, 49, 1),
                  borderRadius: BorderRadius.circular(2.r),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      recommendService.timeRemaining.value.minutes.split("")[0],
                      style: textStyle,
                    ),
                    Text(
                      recommendService.timeRemaining.value.minutes.split("")[1],
                      style: textStyle,
                    ),
                  ],
                ),
              ),
              Container(
                width: 6.sp,
                alignment: Alignment.center,
                child: Text(":", style: textStyle),
              ),
              Container(
                width: 20.59.sp,
                height: 20.59.sp,
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(49, 49, 49, 1),
                  borderRadius: BorderRadius.circular(2.r),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      recommendService.timeRemaining.value.seconds.split("")[0],
                      style: textStyle,
                    ),
                    Text(
                      recommendService.timeRemaining.value.seconds.split("")[1],
                      style: textStyle,
                    ),
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

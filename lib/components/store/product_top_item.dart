import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/i18n/trans.dart';

import '../../gen/assets.gen.dart';

class ProductTopItem extends StatelessWidget {
  const ProductTopItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          margin: EdgeInsets.only(top: 12.w),
          padding: EdgeInsets.all(14.r),
          decoration: BoxDecoration(
            color: const Color(0xFF191919),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: Colors.grey,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Assets.imgCoinF
                      .image(width: 24.r, height: 24.r, fit: BoxFit.fill),
                  SizedBox(width: 8.w),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "500 ${AppTrans.coins()}",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      // SizedBox(height: 2.h),
                      Text(
                        "+120 ${AppTrans.bonus()}",
                        style: TextStyle(fontSize: 14.sp, color: Colors.white),
                      ),
                    ],
                  ),
                ],
              ),
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.r),
                    color: Colors.white),
                padding: EdgeInsets.all(10.r),
                child: Text(
                  "\$ 39.99",
                  style: TextStyle(
                      color: Colors.black,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: 0,
          left: 0,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFFFFCD00),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(7.r),
                topRight: Radius.circular(7.r),
                bottomRight: Radius.circular(7.r),
              ),
            ),
            padding:
                EdgeInsets.only(left: 8.w, top: 4.h, bottom: 5.h, right: 10.w),
            child: const Text(
              'New users get 100%',
              style: TextStyle(
                fontSize: 10,
                color: Color(0xFF171717),
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

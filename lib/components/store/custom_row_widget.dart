import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/i18n/trans.dart';

class CustomRow extends StatelessWidget {
  final String imagePath;
  final String numberText;
  String coinsText;
  final double imageSize;
  final double numberFontSize;
  final double coinsFontSize;
  final Color coinsTextColor;
  final double spacing;
  final FontWeight numberFontWeight;
  final FontWeight coinsFontWeight;
  final Color numberTextColor; // 新增参数

  CustomRow({
    super.key,
    required this.imagePath,
    this.numberText = '',
    this.coinsText = "",
    this.imageSize = 26,
    this.numberFontSize = 19,
    this.coinsFontSize = 14,
    this.coinsTextColor = const Color(0xFF999999),
    this.spacing = 10,
    this.numberFontWeight = FontWeight.w600, // 默认值
    this.coinsFontWeight = FontWeight.w500, // 默认值
    this.numberTextColor = Colors.white, // 默认值
  }) {
    coinsText = coinsText.isEmpty ? AppTrans.coins() : coinsText;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Image.asset(
          imagePath,
          width: imageSize.r,
          height: imageSize.r,
        ),
        SizedBox(width: spacing.w),
        Text(
          numberText,
          style: TextStyle(
            color: numberTextColor, // 使用传入的颜色
            fontSize: numberFontSize.sp,
            fontWeight: numberFontWeight, // 使用传入的字重
          ),
        ),
        SizedBox(width: spacing.w),
        Text(
          coinsText,
          style: TextStyle(
            color: coinsTextColor,
            fontSize: coinsFontSize.sp,
            fontWeight: coinsFontWeight, // 使用传入的字重
          ),
        ),
      ],
    );
  }
}

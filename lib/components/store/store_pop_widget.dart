import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/components/store/product_item.dart';
import 'package:playlet/components/store/subscribe_item.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/details/details_controller.dart';
import 'package:playlet/service/payment/payment_events.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/index.dart';

import '../../common/log/ff_log.dart';
import '../../gen/assets.gen.dart';
import '../../model/store/store_product_result.dart';
import '../../modules/store/store_controller.dart';
import '../../service/payment/payment_model.dart';
import '../../service/payment/payment_service.dart';
import '../../utils/limited_time.dart';

class StorePopWidget extends StatefulWidget {
  final int businessId;
  final String scene;
  final int shortPlayId;
  final int shortPlayCode;
  final int dramaId;
  final bool showAdItem; // 新增的 bool 变量，控制是否显示广告 item
  final VoidCallback onAdItemTap; // 新增的广告 item 点击回调
  final ValueChanged<SkuInfoResponses?> onRetainSkuInfoResponses;
  final VoidCallback? onClose;
  final VoidCallback onPlaySuccess;
  final VoidCallback onSubscriptionSuccess;
  final String action; //触发动作
  final String lockBegin; //卡点状态
  final int episode;
  final String episodeCoins; //本地金币
  final DetailsController detailsController;

  const StorePopWidget({
    super.key,
    this.businessId = -1,
    this.shortPlayId = -1,
    this.shortPlayCode = -1,
    this.scene = "",
    this.dramaId = -1,
    this.showAdItem = true, // 设置默认值
    this.episode = 0,
    required this.onAdItemTap,
    required this.onRetainSkuInfoResponses,
    this.onClose,
    required this.onPlaySuccess,
    required this.onSubscriptionSuccess,
    this.action = '',
    this.lockBegin = '',
    this.episodeCoins = '',
    required this.detailsController,
  });

  @override
  State<StorePopWidget> createState() => _StorePopWidgetState();
}

class _StorePopWidgetState extends State<StorePopWidget> {
  final StoreController controller = Get.put(StoreController());
  final PaymentService paymentService = Get.find<PaymentService>();
  var productList = <SkuInfoResponses>[].obs;
  var subProductList = <SubscribeSkuResponses>[].obs;

  DetailsController get detailsController => widget.detailsController;

  @override
  void initState() {
    super.initState();
    initData();

    // 添加补单方法
    paymentService.startRecover(from: EventValue.adsCoins);
  }

  @override
  void dispose() {
    super.dispose();
    // 关闭页面 埋点
    PaymentEvent.submitRechargeShowEnd(
      strScene: EventValue.adsCoins,
      reelId: widget.shortPlayCode.toString(),
      episode: widget.episode.toString(),
      action: widget.action,
      lockBegin: widget.lockBegin,
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );

    controller.cancelTimer();
  }

  Future<void> initData() async {
    //埋点

    Get.loading();

    if (paymentService.currency.value.isEmpty) {
      await paymentService.loadProducts();
    }

    await paymentService.getUnlockProductList(
      widget.businessId,
      widget.scene,
      widget.dramaId,
    );
    await paymentService.refreshUserBalance();

    controller.isShowTimeProduct.value = LimitedTime.getPopupShow();
    if (controller.isShowTimeProduct.value) {
      controller.countDown.value = LimitedTime.getLeftTime();
      controller.startCountDown(() async {
        Get.loading();
        await initData();
        Get.dismiss();
      });
    }
    var skuList =
        paymentService.unLockStoreProductResult.value.skuInfoResponses;
    var subList =
        paymentService.unLockStoreProductResult.value.subscribeSkuResponses;
    var retainSkuInfoResponses =
        paymentService.unLockStoreProductResult.value.retainSkuInfoResponses;
    var isShowTimeProduct = LimitedTime.getPopupShow();

    if (skuList != null) {
      productList.clear();
      skuList.sort((a, b) => a.skuType.compareTo(b.skuType));
      productList.addAll(skuList);

      if (paymentService.unLockStoreProductResult.value.skuPositionType == 1 &&
          retainSkuInfoResponses != null &&
          isShowTimeProduct) {
        FFLog.debug("插入膨胀商品   ${retainSkuInfoResponses.toJson()}");
        productList.insert(1, retainSkuInfoResponses);
      }
    }
    if (subList != null) {
      subProductList.clear();
      subProductList.addAll(subList);
    }
    if (controller.paymentService.unLockStoreProductResult.value
                .skuPositionType ==
            1 &&
        !controller.isShowTimeProduct.value &&
        retainSkuInfoResponses != null) {
      // 调用回调函数，传递 retainSkuInfoResponses
      widget.onRetainSkuInfoResponses(retainSkuInfoResponses);
    }

    var strScene = controller.isShowTimeProduct.value
        ? "${EventValue.adsCoins},${EventValue.payRetainCommodity}"
        : EventValue.adsCoins;

    // 打开页面 埋点
    PaymentEvent.submitRechargeShow(
      strScene: strScene,
      reelId: widget.shortPlayCode.toString(),
      episode: widget.episode.toString(),
      action: widget.action,
      lockBegin: widget.lockBegin,
      playDirection: ScreenUtils.isLandscape(Get.context!)
          ? EventValue.horizontal
          : EventValue.vertical,
    );

    Get.dismiss();
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕方向
    bool isLandscape = ScreenUtils.isLandscape(context);
    if (isLandscape) {
      ScreenUtil.init(
        context,
        designSize: const Size(852, 393),
      );
    } else {
      ScreenUtil.init(
        context,
        designSize: const Size(393, 852),
      );
    }
    return Container(
      width: isLandscape ? (Platform.isAndroid ? 423.sp : 393.sp) : Get.width,
      height: isLandscape ? double.infinity : null,
      // 仅在竖屏时设置最大高度
      constraints: isLandscape
          ? null
          : BoxConstraints(
              maxHeight: 0.8 * MediaQuery.of(context).size.height,
            ),
      child: Obx(() {
        return Container(         
          padding: EdgeInsets.only(left: 16.sp, top: 16.sp, right: (isLandscape && Platform.isAndroid) ? 46.sp : 16.sp, bottom: 16.sp),
          decoration: BoxDecoration(
            color: const Color(0XFF1E1E1E),
            borderRadius: isLandscape
                ? BorderRadius.zero
                : BorderRadius.only(
                    topLeft: Radius.circular(30.r),
                    topRight: Radius.circular(30.r),
                  ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildTop(isLandscape),
              SizedBox(height: 10.h),
              Flexible(
                fit: FlexFit.loose,
                child: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: 10.h),
                      _buildProducts(),
                      SizedBox(height: 20.h),
                      _buildDescriptions(),
                      _buildBottom(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildTop(bool isLandscape) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: isLandscape? 26.sp : 16.sp),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                "${AppTrans.price()}:",
                style: TextStyle(
                  color: const Color(0xFF999999),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Assets.imgCoinF.image(width: 18.r, height: 18.r),
              SizedBox(
                width: 4.w,
              ),
              Text(
                widget.episodeCoins,
                style: TextStyle(
                  color: const Color(0xFF999999),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500, // 使用传入的字重
                ),
              ),
              SizedBox(
                width: 30.w,
              ),
              Text(
                "${AppTrans.balance()}:",
                style: TextStyle(
                  color: const Color(0xFF999999),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Assets.imgCoinF.image(width: 18.r, height: 18.r),
              SizedBox(
                width: 4.w,
              ),
              Text(
                "${paymentService.userBalance.value.coins}",
                style: TextStyle(
                  color: const Color(0xFF999999),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500, // 使用传入的字重
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Assets.imgCoinB.image(width: 20.r, height: 20.r),
              SizedBox(
                width: 4.w,
              ),
              Text(
                "${paymentService.userBalance.value.bonus}",
                style: TextStyle(
                  color: const Color(0xFF999999),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500, // 使用传入的字重
                ),
              )
            ],
          ),
        ),
       Padding(
          padding: EdgeInsets.only(top: isLandscape? 16.sp : 0.sp),
          child: GestureDetector(
            onTap: widget.onClose,
            child: Assets.store.iconStorePopExit.image(
              width: 24.r,
              height: 24.r,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProducts() {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 12.sp,
        crossAxisSpacing: 16.sp,
        childAspectRatio: 172 / 86,
      ),
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: 4,
      itemBuilder: (BuildContext context, int index) {
        if (productList.isEmpty) {
          return const SizedBox();
        }
        var currItem = productList[index];
        if (currItem.skuType == 0) {
          FFLog.info("解锁页显示首推。${currItem.toJson()}");
          return ProductFirstItemWith(
              currItem,
              controller.paymentService.currency.value,
              currItem.subscript.isNotEmpty == true, onTap: () async {
            var result = await controller.onStartPay(
              currItem: currItem,
              isRetain: false,
              strSource: EventValue.adsCoins,
              episode: widget.episode,
              lockBegin: widget.lockBegin,
              shortPlayId: widget.shortPlayId,
              shortPlayCode: widget.shortPlayCode,
              playDirection: ScreenUtils.isLandscape(Get.context!)
                  ? EventValue.horizontal
                  : EventValue.vertical,
            );
            if (result) {
              widget.onPlaySuccess();
            }
          });
        } else if ((currItem.skuType == 7) &&
            controller.isShowTimeProduct.value) {
          FFLog.info("解锁页显示膨胀。${currItem.toJson()}");
          return ProductClockItem(
              currItem,
              controller.paymentService.currency.value,
              controller.countDown, onTap: () async {
            var result = await controller.onStartPay(
              currItem: currItem,
              isRetain: true,
              strSource:
                  "${EventValue.adsCoins},${EventValue.payRetainCommodity}",
              episode: widget.episode,
              lockBegin: widget.lockBegin,
              shortPlayId: widget.shortPlayId,
              shortPlayCode: widget.shortPlayCode,
              playDirection: ScreenUtils.isLandscape(Get.context!)
                  ? EventValue.horizontal
                  : EventValue.vertical,
            );

            if (result) {
              widget.onPlaySuccess();
            }
          });
        } else if (index == 3 && widget.showAdItem) {
          // 添加显示条件
          return AdProductItem(
            showUnlockButton: true,
            onTap: widget.onAdItemTap, // 使用传入的回调
            watchAdResultResponse: controller.paymentService
                .unLockStoreProductResult.value.watchAdResultResponse,
          );
        } else {
          try {
            // 获取当前商品项
            FFLog.info("解锁页显示其他。${currItem.toJson()}");
            return ProductItem(
                currItem, controller.paymentService.currency.value,
                onTap: () async {
              var result = await controller.onStartPay(
                currItem: currItem,
                isRetain: false,
                strSource: EventValue.adsCoins,
                episode: widget.episode,
                lockBegin: widget.lockBegin,
                shortPlayId: widget.shortPlayId,
                shortPlayCode: widget.shortPlayCode,
                playDirection: ScreenUtils.isLandscape(Get.context!)
                    ? EventValue.horizontal
                    : EventValue.vertical,
              );
              if (result) {
                widget.onPlaySuccess();
              }
            });
          } catch (e) {
            return const SizedBox();
          }
        }
      },
    );
  }

  Widget _buildDescriptions() {
    if (controller.paymentService.unLockStoreProductResult.value
            .subscribeSkuResponses?.isNotEmpty ??
        false) {
      return ListView.builder(
        itemCount: subProductList.length,
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          SubscribeSkuResponses product = subProductList[index];

          // 订阅展示埋点
          PaymentEvent.submitSubscribeThingShow(
              product.isFirstBuy ? product.firstAmount : product.payAmount,
              'ads_coins',
              product.productId.toString());

          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: SubscribeItem(
              product: product,
              currency: controller.paymentService.currency.value,
              onTap: () async {
                // 在这里添加点击事件的逻辑
                String? skuId = product.skuId;
                String? productId = product.productId.toString();
                FFLog.debug('发起订阅。skuId $skuId   productId $productId ');

                if (skuId.isNotEmpty) {
                  final result = await controller.startBuySubscription(
                    skuId: skuId,
                    productId: productId,
                    source: SourceType.unlockDialogPage,
                    shortPlayId: widget.shortPlayId,
                    shortPlayCode: widget.shortPlayCode,
                    lockBegin: widget.lockBegin,
                    episode: widget.episode,
                    recover: false,
                    amount: product.isFirstBuy
                        ? product.firstAmount
                        : product.payAmount,
                    strSource: EventValue.rechargeSubscribe,
                    playDirection: ScreenUtils.isLandscape(Get.context!)
                        ? EventValue.horizontal
                        : EventValue.vertical,
                  );

                  if (result) {
                    widget.onSubscriptionSuccess();
                  }
                }
              },
            ),
          );
        },
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildBottom() {
    return Padding(
      padding: EdgeInsets.only(bottom: Get.mediaQuery.padding.bottom + 16.sp),
      child: Column(
        children: [
          // GestureDetector(
          //   behavior: HitTestBehavior.translucent,
          //   onTap: () async {
          //     controller.isSelect.value = !controller.isSelect.value;
          //
          //     await controller.userServices
          //         .updateAutoUnlockEpisode(controller.isSelect.value);
          //   },
          //   child: Padding(
          //     padding: EdgeInsets.symmetric(horizontal: 20.w),
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.center,
          //       children: [
          //         controller.isSelect.value
          //             ? Assets.store.iconAgreementSelect
          //             .image(width: 13.r, height: 13.r)
          //             : Assets.store.iconAgreementUnselect
          //             .image(width: 13.r, height: 13.r),
          //         SizedBox(width: 4.w),
          //         Text(
          //           AppTrans.automaticEpisodeUnlock(),
          //           style: TextStyle(
          //             color: const Color(0xFF999999),
          //             fontSize: 10.sp,
          //             fontWeight: FontWeight.w400,
          //             height: 1.5,
          //           ),
          //         ),
          //         SizedBox(width: 10.w),
          //       ],
          //     ),
          //   ),
          // ),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: AppTrans.rechargeMeansAgree(),
                  style: TextStyle(
                    color: const Color(0xFF999999),
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w400,
                    height: 1.5,
                  ),
                ),
                TextSpan(
                  text: ' ',
                  style: TextStyle(
                    color: const Color(0xFF999999),
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w400,
                    height: 1.5,
                  ),
                ),
                TextSpan(
                  text: AppTrans.rechargeAgreement(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w400,
                    height: 1.5,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () async {
                      if (ScreenUtils.isLandscape(context)) {
                        await detailsController.toggleLandscapeScreen();
                      }
                      Utils.openRechargeAgreement();
                    },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

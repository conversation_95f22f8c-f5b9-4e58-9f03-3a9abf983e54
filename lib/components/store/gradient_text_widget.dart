import 'package:flutter/material.dart';

class GradientText extends StatelessWidget {
  const GradientText(
    this.text, {
    super.key,
    this.style = const TextStyle(
      fontWeight: FontWeight.w600,
      fontSize: 17,
    ),
    this.gradient = const LinearGradient(
      colors: [Color(0xFFFF9937), Color(0xFFFBE840)],
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
    ),
  });

  final String text;
  final TextStyle? style;
  final Gradient gradient;

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: Text(
        text,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: style,
      ),
    );
  }
}

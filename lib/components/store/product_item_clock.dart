import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/model/store/store_product_result.dart';

class ProductItemClock extends StatefulWidget {
  final SkuInfoResponses? retainSkuInfoResponses;
  final RxInt countDown;

  const ProductItemClock(this.retainSkuInfoResponses, this.countDown, {super.key});

  @override
  State<ProductItemClock> createState() => _ProductItemClockState();
}

class _ProductItemClockState extends State<ProductItemClock> {
  @override
  void initState() {
    super.initState();

    FFLog.debug("paddingRight  _ProductItemClockState  ${widget.countDown.value}");

  }

  @override
  Widget build(BuildContext context) {



    return Obx(() {
      int hours = (widget.countDown.value ~/ 3600).clamp(0, 99);
      int minutes = ((widget.countDown.value % 3600) ~/ 60).clamp(0, 59);
      int seconds = (widget.countDown.value % 60).clamp(0, 59);

      // 格式化小时、分钟和秒为两位数
      String formattedHours = hours.toString().padLeft(2, '0');
      String formattedMinutes = minutes.toString().padLeft(2, '0');
      String formattedSeconds = seconds.toString().padLeft(2, '0');

      return Container(
        margin: EdgeInsets.only(right:8.w),

        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              margin: EdgeInsets.only(right:8.w),
              child: Assets.store.imgProductClock.image(width: 14.r, height: 14.r),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 3.8.r, vertical:  3.8.r),
              decoration: BoxDecoration(
                color: const Color(0xFFA96805).withOpacity(0.3), // 设置背景色为 #A96805
                borderRadius: BorderRadius.circular(5.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 小时
                  Container(
                    width: 18.w,
                    alignment: Alignment.center,

                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      formattedHours,
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 2.w),
                    child: Text(
                      ':',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // 分钟
                  Container(
                    width: 18.w,
                    alignment: Alignment.center,

                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      formattedMinutes,
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 2.w),
                    child: Text(
                      ':',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // 秒
                  Container(
                    width: 18.w,
                    alignment: Alignment.center,

                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      formattedSeconds,
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }
}

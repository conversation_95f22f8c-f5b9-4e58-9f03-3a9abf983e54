import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/components/store/custom_row_widget.dart';
import 'package:playlet/components/store/product_item_clock.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/store/store_product_result.dart';
import 'package:playlet/model/store/unlock_store_product_result.dart';

import '../../gen/assets.gen.dart';

// 通用Item
class ProductItem extends StatelessWidget {
  final bool showDiscountTag;
  final VoidCallback? onTap;
  final SkuInfoResponses? skuInfoResponses;
  final String currency;

  const ProductItem(
    this.skuInfoResponses,
    this.currency, {
    super.key,
    this.showDiscountTag = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        child: Stack(
          fit: StackFit.expand,
          children: [
            Container(
              margin: EdgeInsets.only(right: 2.w),
              padding: EdgeInsets.only(left: 12.w, top: 10.h),
              decoration: BoxDecoration(
                color: const Color(0xFF191919),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: CustomRow(
                      imagePath: Assets.imgCoinF.path,
                      numberText: "${skuInfoResponses?.coins ?? "0"}",
                      coinsText: AppTrans.coins(),
                      imageSize: 22,
                      numberFontSize: 16,
                      coinsFontSize: 16,
                      coinsTextColor: Colors.white,
                      spacing: 6,
                      coinsFontWeight: FontWeight.w600,
                      numberTextColor: Colors.white,
                    ),
                  ),
                  SizedBox(
                    height: 2.sp,
                  ),
                  Text(
                    (skuInfoResponses != null &&
                            skuInfoResponses!.productGiveCoins > 0)
                        ? "+${skuInfoResponses?.productGiveCoins ?? "0"} ${AppTrans.bonus()}"
                        : "",
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFFFFDD51),
                    ),
                  ),
                  SizedBox(
                    height: 2.sp,
                  ),
                  Text(
                    "$currency ${skuInfoResponses?.recharge ?? "0"}",
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(
                    height: 2.sp,
                  ),
                ],
              ),
            ),
            if (showDiscountTag &&
                (skuInfoResponses?.subscript ?? "").isNotEmpty)
              buildTag(skuInfoResponses?.subscript ?? ""),
          ],
        ),
      ),
    );
  }
}

// 带时钟Item
class ProductClockItem extends StatefulWidget {
  final bool showDiscountTag;
  final VoidCallback? onTap;
  final SkuInfoResponses? retainSkuInfoResponses;
  final String currency;
  final RxInt countDown;

  const ProductClockItem(
    this.retainSkuInfoResponses,
    this.currency,
    this.countDown, {
    super.key,
    this.showDiscountTag = true,
    this.onTap,
  });

  @override
  State<ProductClockItem> createState() => _ProductClockItemState();
}

class _ProductClockItemState extends State<ProductClockItem> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: widget.onTap,
        child: Stack(
          fit: StackFit.expand,
          children: [
            Container(
              margin: EdgeInsets.only(right: 2.w),
              padding: EdgeInsets.only(left: 12.w, top: 10.h, bottom: 10.h),
              decoration: BoxDecoration(
                color: const Color(0xFF191919),
                borderRadius: BorderRadius.circular(8.r),
                image: DecorationImage(
                  image: AssetImage(Assets.store.imgStoreItemBg.path),
                  fit: BoxFit.fitWidth,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: CustomRow(
                      imagePath: Assets.imgCoinF.path,
                      numberText: "${widget.retainSkuInfoResponses?.coins}",
                      coinsText: AppTrans.coins(),
                      imageSize: 22,
                      numberFontSize: 16,
                      coinsFontSize: 16,
                      coinsTextColor: const Color(0xFF804703),
                      spacing: 6,
                      coinsFontWeight: FontWeight.w600,
                      numberTextColor: const Color(0xFF804703),
                    ),
                  ),
                  (widget.retainSkuInfoResponses != null &&
                          widget.retainSkuInfoResponses!.keepGiveCoins > 0)
                      ? Text(
                          (widget.retainSkuInfoResponses != null &&
                                  widget.retainSkuInfoResponses!.keepGiveCoins >
                                      0)
                              ? "+${widget.retainSkuInfoResponses?.keepGiveCoins ?? "0"} ${AppTrans.bonus()}"
                              : "",
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFF804703),
                          ),
                        )
                      : Text("",
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFF804703),
                          )),
                  Text(
                    "${widget.currency} ${widget.retainSkuInfoResponses?.recharge ?? "0"}",
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF804703),
                    ),
                  ),
                  SizedBox(
                    height: 2.sp,
                  ),
                ],
              ),
            ),
            Positioned(
              bottom: 10,
              right: 0,
              child: ProductItemClock(
                  widget.retainSkuInfoResponses, widget.countDown),
            ),
            if (widget.showDiscountTag &&
                (widget.retainSkuInfoResponses?.subscript ?? "").isNotEmpty)
              buildTag(widget.retainSkuInfoResponses?.subscript ?? ""),
          ],
        ));
  }
}

// 首推活动Item
class ProductFirstItemWith extends StatelessWidget {
  final VoidCallback? onTap;
  final SkuInfoResponses? skuInfoResponses;
  final String currency;
  final bool showDiscountTag;
  const ProductFirstItemWith(
    this.skuInfoResponses,
    this.currency,
    this.showDiscountTag, {
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        child: Stack(
          fit: StackFit.expand,
          children: [
            Container(
                margin: EdgeInsets.only(right: 2.w),
                padding: EdgeInsets.only(left: 12.w, top: 10.h),
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Assets.store.imgFirstItemBg.path),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      height: 22.h,
                      child: CustomRow(
                        imagePath: Assets.imgCoinF.path,
                        numberText: "${skuInfoResponses?.coins ?? "0"}",
                        coinsText: AppTrans.coins(),
                        imageSize: 22,
                        numberFontSize: 16,
                        coinsFontSize: 16,
                        coinsTextColor: Colors.white,
                        spacing: 6,
                        coinsFontWeight: FontWeight.w600,
                        numberTextColor: Colors.white,
                      ),
                    ),
                    SizedBox(
                      height: 2.sp,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (skuInfoResponses != null &&
                                skuInfoResponses!.productGiveCoins > 0)
                              Text(
                                (skuInfoResponses != null &&
                                        skuInfoResponses!.productGiveCoins > 0)
                                    ? "+${skuInfoResponses?.productGiveCoins ?? "0"} ${AppTrans.bonus()}"
                                    : "",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFFFFDD51),
                                ),
                              ),
                            Text(
                              "$currency ${skuInfoResponses?.recharge ?? "0"}",
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.only(right: 14.w),
                          child: Assets.store.iconFirstItem.image(
                            width: 40.r,
                            height: 40.r,
                          ),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 2.sp,
                    ),
                  ],
                )),
            if (showDiscountTag &&
                (skuInfoResponses?.subscript ?? "").isNotEmpty)
              buildTag(skuInfoResponses?.subscript ?? "")
          ],
        ),
      ),
    );
  }
}

// 广告项组件
class AdProductItem extends StatelessWidget {
  final bool showUnlockButton;
  final VoidCallback? onTap;
  final WatchAdResultResponse? watchAdResultResponse;

  const AdProductItem(
      {super.key,
      this.showUnlockButton = true,
      this.onTap,
      this.watchAdResultResponse});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.only(right: 2.w),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Container(
              padding: EdgeInsets.only(top: 6.h),
              decoration: BoxDecoration(
                color: const Color(0xFF191919),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: Colors.grey,
                  width: 1,
                ),
              ),
              child: _buildContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.w),
            child: Column(
              children: [
                SizedBox(
                  height: 20.h,
                  child: Row(
                    children: [
                      Assets.store.imgWatchAd.image(
                        width: 20.r,
                        height: 20.r,
                      ),
                      SizedBox(width: 4.w),
                      Expanded(
                        child: Text(
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          AppTrans.watchAds(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  "${AppTrans.watchAdsToUnlock()} ${watchAdResultResponse?.canWatchAdNum ?? ""}/${watchAdResultResponse?.totalWatchAdNum ?? ""}",
                  style: TextStyle(
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFFCBCBCB),
                  ),
                ),
              ],
            ),
          ),
        ),
        showUnlockButton
            ? Container(
                height: 22.h,
                decoration: BoxDecoration(
                    color: const Color(0xFF4F4836),
                    borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(8.r),
                        bottomRight: Radius.circular(8.r))),
                child: Center(
                  child: Text(
                    AppTrans.unlock(),
                    style: TextStyle(
                      color: const Color(0xFFFFE990),
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              )
            : SizedBox(
                width: 172.w,
                height: 22.h,
              ),
      ],
    );
  }
}

Positioned buildTag(String tagText) {
  return Positioned(
    top: 0,
    right: 0,
    child: Container(
      padding: EdgeInsets.only(
        left: 7.w,
        right: 7.w,
        top: 2.h,
        bottom: 4.h,
      ),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.store.imgStoreTagBg.path),
          fit: BoxFit.fill,
        ),
      ),
      child: Center(
        child: Padding(
          padding: EdgeInsets.only(bottom: 3.sp),
          child: Text(
            '+$tagText',
            style: TextStyle(
              color: Colors.white,
              fontSize: 11.sp,
              fontFamily: "Point",
              fontStyle: FontStyle.italic,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    ),
  );
}

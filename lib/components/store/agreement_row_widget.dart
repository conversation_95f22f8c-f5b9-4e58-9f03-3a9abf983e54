import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/i18n/trans.dart';

class AgreementRow extends StatelessWidget {
  final VoidCallback onPrivacyAgreementTap;
  final VoidCallback onUserAgreementTap;

  const AgreementRow({
    super.key,
    required this.onPrivacyAgreementTap,
    required this.onUserAgreementTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildAgreementButton(
          AppTrans.privacyPolicy(),
          onPrivacyAgreementTap,
        ),
        SizedBox(width: 8.w), // 左侧空隙
        Container(
          width: 1.w,
          height: 8.h, // 你可以根据需要调整高度
          color: const Color(0xFF9F9FA2),
        ),
        SizedBox(width: 8.w), // 右侧空隙
        _buildAgreementButton(
          AppTrans.userAgreement(),
          onUserAgreementTap,
        ),
      ],
    );
  }

  Widget _buildAgreementButton(String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Text(
        label,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 12.sp,
          height: 1.3,
          color: Colors.white,
          letterSpacing: 0,
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/i18n/trans.dart';

import '../../gen/assets.gen.dart';
import 'gradient_text_widget.dart';

class SubscribeItem extends StatefulWidget {
  final dynamic product;
  final String currency;
  final VoidCallback? onTap; // 添加点击回调函数

  const SubscribeItem({
    super.key,
    required this.product,
    required this.currency,
    this.onTap, // 初始化点击回调函数
  });

  @override
  _SubscribeItemState createState() => _SubscribeItemState();
}

class _SubscribeItemState extends State<SubscribeItem> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap, // 绑定点击事件
      child: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            AspectRatio(
              aspectRatio: 351 / 110,
              child: Assets.store.imgSubscribeItemBg.image(
                width: 351.sp,
                height: 110.sp,
                fit: BoxFit.fill,
              ),
            ),
            Container(
              padding: EdgeInsets.only(left: 16.sp, right: 27.sp),
              // color: Colors.orange,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        GradientText(
                          getSubscribeName(widget.product.type ?? 0),
                        ),
                        // SizedBox(height: 4.sp),
                        Row(
                          children: [
                            Text(
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              '${widget.currency}${(widget.product.isFirstBuy ?? true) ? widget.product.firstAmount : widget.product.payAmount}',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 18.sp,
                                textBaseline: TextBaseline.alphabetic,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(width: 6.w),
                            Text(
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              '${AppTrans.subscriptionRenewal()} ${widget.currency}${widget.product.payAmount ?? ""}/${getTimeStr(widget.product.type ?? 0)}',
                              style: TextStyle(
                                color: const Color(0xFF999999),
                                fontWeight: FontWeight.w400,
                                fontSize: 11.sp,
                                textBaseline: TextBaseline.alphabetic,
                              ),
                            ),
                          ],
                        ),
                        // SizedBox(height: 4.sp),
                        Container(
                          padding: EdgeInsets.only(
                            left: 8.sp,
                            top: 1.8.sp,
                            right: 11.sp,
                            bottom: 2.sp,
                          ),
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(159, 117, 0, 1),
                            borderRadius: BorderRadius.circular(42.r),
                          ),
                          child: Text(
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            '${AppTrans.unlockOne()} ${getTimeStr(widget.product.type ?? 0)}',
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        // SizedBox(height: 4.sp),
                        Text(
                          AppTrans.autoRenew(),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 11.sp,
                            fontWeight: FontWeight.w300,
                            color: const Color(0xFF999999),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 16.sp),
                    child: widget.product.duration == 7
                        ? Assets.store.imgSubscribeItemIcon7.image(
                            width: 83.sp,
                            height: 83.sp,
                          )
                        : widget.product.duration == 31
                            ? Assets.store.imgSubscribeItemIcon31.image(
                                width: 83.sp,
                                height: 83.sp,
                              )
                            : Assets.store.imgSubscribeItemIcon365.image(
                                width: 83.sp,
                                height: 83.sp,
                              ),
                  ),
                ],
              ),
            ),
            ((widget.product?.inShowNewDiscount ?? false) &&
                    (widget.product.isFirstBuy ?? true))
                ? Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.sp,
                        vertical: 4.sp,
                      ), // 根据需要调整内边距
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF4500),
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(12.r),
                          bottomLeft: Radius.circular(12.r),
                        ),
                      ),
                      child: Text(
                        AppTrans.firstDeposit(),
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 10.sp,
                          color: Colors.white, // 根据背景颜色调整文字颜色
                        ),
                      ),
                    ),
                  )
                : const SizedBox()
          ],
        ),
      ),
    );
  }

  String getSubscribeName(int type) {
    switch (type) {
      case 5:
        return AppTrans.subscriptionWeekly();
      case 6:
        return AppTrans.monthlySubscription();
      case 7:
        return AppTrans.annualSubscription();
      default:
        return '';
    }
  }

  String getTimeStr(int type) {
    switch (type) {
      case 5:
        return AppTrans.week();
      case 6:
        return AppTrans.month();
      case 7:
        return AppTrans.year();
      default:
        return '';
    }
  }
}

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/player/cover_info.dart';
import 'package:playlet/common/player/player_view_core.dart';
import 'package:playlet/common/player/player_view_core_track_event.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/recommend.dart';
import 'package:playlet/modules/recommend/recommend_controller.dart';

class RecommendItem extends StatefulWidget {
  const RecommendItem({
    super.key,
    required this.item,
    required this.index,
  });

  final int index;
  final RecommendList item;

  @override
  State<RecommendItem> createState() => _RecommendItemState();
}

class _RecommendItemState extends State<RecommendItem> {
  RecommendList get item => widget.item;
  final RecommendController contrller = Get.find<RecommendController>();

  final GlobalKey<PlayerViewCoreState> _playerCoreKey =
      GlobalKey<PlayerViewCoreState>();

  bool isEnd = false;
  bool isInitialized = false;

  late Worker _playIndexWorker;

  @override
  void initState() {
    super.initState();
    _playIndexWorker = ever(contrller.playIndex, (val) {
      if (val == widget.index) {
        onPlay();
      } else {
        onPause();
      }
    });
  }

  @override
  void dispose() {
    _playIndexWorker.dispose();
    super.dispose();
  }

  // 创建埋点事件处理对象
  PlayerViewCoreTrackEvent? _trackEvent;
  PlayerViewCoreTrackEvent _createTrackEvent() {
    if (_trackEvent != null) {
      return _trackEvent!;
    }

    _trackEvent = PlayerViewCoreTrackEvent(
      scene: EventValue.newUserPage,
      shortPlayCode:
          item.shortPlayCode != null ? int.tryParse(item.shortPlayCode!) : null,
      episodeNum: item.episodeNum,
      from: contrller.from,
      isFree: '0',
      reelName: item.shortPlayName,
    );
    return _trackEvent!;
  }

  /// 播放视频
  Future<void> onPlay() async {
    if (contrller.playIndex.value == widget.index) {
      await _playerCoreKey.currentState?.onPlay();
      await onSetMuted();
    }
  }

  /// 暂停视频
  Future<void> onPause() async {
    await _playerCoreKey.currentState?.onPause();
  }

  Future<void> onSetMuted() async {
    await _playerCoreKey.currentState?.onSetMuted(true);
  }

  String getRandomSentence() {
    final List<String> sentences = [
      "97%",
      "92%",
      "86%",
      "83%",
      "79%",
      "71%",
      "69%"
    ];
    return sentences[Random().nextInt(sentences.length)] + AppTrans.choose();
  }

  String getTag() {
    if (item.subscript != null && item.subscript!.isNotEmpty) {
      return item.subscript!;
    }
    return getRandomSentence();
  }

  final itemWidth = (Get.width - 52.sp) / 3;
  final itemHeight = (Get.width - 52.sp) / 3 / 0.75;

  @override
  Widget build(BuildContext context) {
    contrller.useTrackEventReelShow(item);
    return SizedBox(
      width: itemWidth,
      child: GestureDetector(
        onTap: () => contrller.onToDetail(item),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(4.sp),
              child: SizedBox(
                width: itemWidth,
                height: itemHeight,
                child: Stack(
                  alignment: Alignment.topRight,
                  children: [
                    _buildPlayView(),
                    Positioned(
                      top: -1,
                      right: 0,
                      child: Row(
                        children: [
                          Container(
                            height: 20.sp,
                            padding: EdgeInsets.only(
                              left: 9.sp,
                              right: 4.sp,
                            ),
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: Assets.recommend.tagBg.provider(),
                                fit: BoxFit.contain,
                              ),
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              getTag(),
                              style: TextStyle(
                                fontSize: 11.sp,
                                overflow: TextOverflow.ellipsis,
                                fontWeight: FontWeight.w500,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withValues(alpha: 0.33),
                                    offset: const Offset(0, 1),
                                    blurRadius: 2,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 8.sp),
            Text(
              item.shortPlayName ?? '',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                overflow: TextOverflow.ellipsis,
              ),
              maxLines: 2,
            ),
            SizedBox(height: 12.sp),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayView() {
    return Obx(() {
      if (contrller.playIndex.value == widget.index) {
        return SizedBox(
          width: itemWidth,
          child: Stack(
            fit: StackFit.expand,
            children: [
              PlayerViewCore(
                key: _playerCoreKey,
                videoUrl: contrller.getVideoUrlByResolution(
                  item.firstVideoUrl!,
                  "720p",
                ),
                coverInfo: CoverInfo(
                  thumbUrl: item.coverUrl,
                  fit: BoxFit.cover,
                ),
                playerViewType: PlayerViewType.retention,
                onPlayerInitialized: (val) {
                  isInitialized = val;
                  onPlay();
                },
                onTimeChange: (time) {
                  if (time.inSeconds > 3 && !isEnd) {
                    isEnd = true;
                    onPause();
                    contrller.onPlayEnd(widget.index);
                  }
                },
                containerSize: Size(itemWidth, itemHeight),
                aspectRatio: widget.item.aspectRatio ?? itemWidth / itemHeight,
                isWaterMark: false,
                shortPlayCode: item.shortPlayCode != null
                    ? int.tryParse(item.shortPlayCode!)
                    : null,
                trackEvent: _createTrackEvent(),
              ),
              Positioned.fill(
                  child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => contrller.onToDetail(item),
              )),
            ],
          ),
        );
      }
      return CoverWidget(
        width: itemWidth,
        height: itemHeight,
        imageUrl: item.coverUrl ?? '',
        fit: BoxFit.cover,
      );
    });
  }
}

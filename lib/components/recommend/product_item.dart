import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/store/store_product_result.dart';

class ProductItem extends StatefulWidget {
  const ProductItem(
      {super.key,
      this.disabled = false,
      required this.item,
      required this.currency,
      required this.showOriginPrice});

  final bool disabled;
  final bool showOriginPrice;
  final SkuInfoResponses item;
  final RxString currency;

  @override
  State<ProductItem> createState() => _ProductItemState();
}

Color disabledColor = const Color.fromRGBO(153, 153, 153, 1);

class _ProductItemState extends State<ProductItem> {
  bool get disabled => widget.disabled;

  final cardBack = BoxDecoration(
    color: const Color.fromRGBO(25, 25, 25, 1),
    border: Border.all(
      color: const Color.fromRGBO(255, 255, 255, 0.3),
      width: 1,
    ),
    borderRadius: BorderRadius.circular(8.sp),
  );
  final disabledCardBack = BoxDecoration(
    color: const Color.fromRGBO(25, 25, 25, 1),
    border: Border.all(
      color: const Color.fromRGBO(255, 255, 255, 0.3),
      width: 1,
    ),
    borderRadius: BorderRadius.circular(8.sp),
  );
  final coinsTextStyle = TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );
  final disabledCoinsTextStyle = TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
    color: disabledColor,
  );
  final bounsTextStyle = TextStyle(
    color: const Color.fromRGBO(255, 233, 144, 1),
    fontSize: 12.sp,
  );
  final disabledBounsTextStyle = TextStyle(
    color: disabledColor,
    fontSize: 12.sp,
  );
  // 现价
  final priceTextStyle = TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );
  final disabledPriceTextStyle = TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
    color: disabledColor,
  );
  final oldPriceTextStyle = TextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w400,
    decoration: TextDecoration.lineThrough,
    decorationColor: const Color.fromRGBO(117, 117, 117, 1),
    color: const Color.fromRGBO(117, 117, 117, 1),
  );
  final disabledOldPriceTextStyle = TextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w400,
    decoration: TextDecoration.lineThrough,
    color: disabledColor,
  );

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          decoration: disabled ? disabledCardBack : cardBack,
          clipBehavior: Clip.none,
          padding: EdgeInsets.only(
            top: 10.sp,
            left: 12.sp,
            bottom: 10.sp,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Opacity(
                    opacity: disabled ? 0.5 : 1,
                    child: Assets.imgCoinF.image(width: 22.sp, height: 22.sp),
                  ),
                  SizedBox(width: 6.sp),
                  Text(
                    "${widget.item.coins} ${AppTrans.coins()}",
                    style: disabled ? disabledCoinsTextStyle : coinsTextStyle,
                  ),
                ],
              ),
              if (widget.item.productGiveCoins > 0)
                Text(
                  "+${widget.item.productGiveCoins} ${AppTrans.bonus()}",
                  style: disabled ? disabledBounsTextStyle : bounsTextStyle,
                ),
              Obx(
                () => Row(
                  children: [
                    Text(
                      "${widget.currency.value} ${widget.item.recharge}",
                      style: disabled ? disabledPriceTextStyle : priceTextStyle,
                    ),
                    SizedBox(width: 10.sp),
                    if (widget.showOriginPrice && widget.item.originalPrice > 0)
                      Opacity(
                        opacity: disabled ? 0.5 : 1,
                        child: Text(
                          "${widget.currency.value} ${widget.item.originalPrice}",
                          style: oldPriceTextStyle,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (widget.item.subscript.isNotEmpty)
          Positioned(
            height: 22.33.sp,
            top: 0,
            right: -2.7.sp,
            child: Stack(
              children: [
                Container(
                  height: 18.sp,
                  decoration: BoxDecoration(
                    color: disabled
                        ? const Color.fromRGBO(81, 81, 81, 1)
                        : const Color.fromRGBO(255, 69, 0, 1),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(45.r),
                      topRight: Radius.circular(25.r),
                      bottomLeft: Radius.circular(45.r),
                    ),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 6.sp),
                  alignment: Alignment.center,
                  child: Text(
                    "+${widget.item.subscript}",
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: disabled ? disabledColor : Colors.white,
                      fontStyle: FontStyle.italic,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: SizedBox(
                    width: 2.75.sp,
                    height: 4.33.sp,
                    child: CustomPaint(
                      painter: TrianglePainter(
                        color: disabled
                            ? const Color.fromRGBO(81, 81, 81, 1)
                            : const Color.fromRGBO(255, 69, 0, 1),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}

class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, 0); // 顶点
    path.lineTo(size.width, 0); // 右下角
    path.lineTo(0, size.height); // 左下角
    path.close(); // 闭合路径

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

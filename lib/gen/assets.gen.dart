/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsBackIconGen {
  const $AssetsBackIconGen();

  /// File path: assets/back_icon/white_back_icon.png
  AssetGenImage get whiteBackIcon =>
      const AssetGenImage('assets/back_icon/white_back_icon.png');

  /// List of all assets
  List<AssetGenImage> get values => [whiteBackIcon];
}

class $AssetsDetailsGen {
  const $AssetsDetailsGen();

  /// Directory path: assets/details/advance_watch_ad
  $AssetsDetailsAdvanceWatchAdGen get advanceWatchAd =>
      const $AssetsDetailsAdvanceWatchAdGen();

  /// File path: assets/details/bouns.png
  AssetGenImage get bouns => const AssetGenImage('assets/details/bouns.png');

  /// File path: assets/details/coins.png
  AssetGenImage get coins => const AssetGenImage('assets/details/coins.png');

  /// File path: assets/details/limited_time_back.png
  AssetGenImage get limitedTimeBack =>
      const AssetGenImage('assets/details/limited_time_back.png');

  /// File path: assets/details/limited_time_bg_back.png
  AssetGenImage get limitedTimeBgBack =>
      const AssetGenImage('assets/details/limited_time_bg_back.png');

  /// File path: assets/details/limited_time_top.png
  AssetGenImage get limitedTimeTop =>
      const AssetGenImage('assets/details/limited_time_top.png');

  /// File path: assets/details/radio.png
  AssetGenImage get radio => const AssetGenImage('assets/details/radio.png');

  /// File path: assets/details/retention_close.png
  AssetGenImage get retentionClose =>
      const AssetGenImage('assets/details/retention_close.png');

  /// File path: assets/details/retention_play.png
  AssetGenImage get retentionPlay =>
      const AssetGenImage('assets/details/retention_play.png');

  /// File path: assets/details/time_dowm.png
  AssetGenImage get timeDowm =>
      const AssetGenImage('assets/details/time_dowm.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    bouns,
    coins,
    limitedTimeBack,
    limitedTimeBgBack,
    limitedTimeTop,
    radio,
    retentionClose,
    retentionPlay,
    timeDowm,
  ];
}

class $AssetsEmptyGen {
  const $AssetsEmptyGen();

  /// File path: assets/empty/empty_noContent.png
  AssetGenImage get emptyNoContent =>
      const AssetGenImage('assets/empty/empty_noContent.png');

  /// File path: assets/empty/empty_noNetwork.png
  AssetGenImage get emptyNoNetwork =>
      const AssetGenImage('assets/empty/empty_noNetwork.png');

  /// File path: assets/empty/empty_timeout.png
  AssetGenImage get emptyTimeout =>
      const AssetGenImage('assets/empty/empty_timeout.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    emptyNoContent,
    emptyNoNetwork,
    emptyTimeout,
  ];
}

class $AssetsFullscreenGen {
  const $AssetsFullscreenGen();

  /// File path: assets/fullscreen/ic_change_screen.png
  AssetGenImage get icChangeScreen =>
      const AssetGenImage('assets/fullscreen/ic_change_screen.png');

  /// File path: assets/fullscreen/ic_restore_screen.png
  AssetGenImage get icRestoreScreen =>
      const AssetGenImage('assets/fullscreen/ic_restore_screen.png');

  /// List of all assets
  List<AssetGenImage> get values => [icChangeScreen, icRestoreScreen];
}

class $AssetsGoodReviewGen {
  const $AssetsGoodReviewGen();

  /// File path: assets/good_review/goold.png
  AssetGenImage get goold =>
      const AssetGenImage('assets/good_review/goold.png');

  /// File path: assets/good_review/star.png
  AssetGenImage get star => const AssetGenImage('assets/good_review/star.png');

  /// File path: assets/good_review/stat_acctive.png
  AssetGenImage get statAcctive =>
      const AssetGenImage('assets/good_review/stat_acctive.png');

  /// List of all assets
  List<AssetGenImage> get values => [goold, star, statAcctive];
}

class $AssetsHomeGen {
  const $AssetsHomeGen();

  /// File path: assets/home/<USER>
  AssetGenImage get homeBannerPlay =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeMaskGroup =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeMore =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homePlaceholder =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeRecentlyBigBg =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeRecentlyClose =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeRecentlyContinue =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeRecentlyCover =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeRecentlyPlay =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeRecentlySmallBg =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeRecommend =>
      const AssetGenImage('assets/home/<USER>');

  /// File path: assets/home/<USER>
  AssetGenImage get homeTopLogo =>
      const AssetGenImage('assets/home/<USER>');

  /// List of all assets
  List<AssetGenImage> get values => [
    homeBannerPlay,
    homeMaskGroup,
    homeMore,
    homePlaceholder,
    homeRecentlyBigBg,
    homeRecentlyClose,
    homeRecentlyContinue,
    homeRecentlyCover,
    homeRecentlyPlay,
    homeRecentlySmallBg,
    homeRecommend,
    homeTopLogo,
  ];
}

class $AssetsLanguageGen {
  const $AssetsLanguageGen();

  /// File path: assets/language/ic_language_check.png
  AssetGenImage get icLanguageCheck =>
      const AssetGenImage('assets/language/ic_language_check.png');

  /// File path: assets/language/ic_language_uncheck.png
  AssetGenImage get icLanguageUncheck =>
      const AssetGenImage('assets/language/ic_language_uncheck.png');

  /// List of all assets
  List<AssetGenImage> get values => [icLanguageCheck, icLanguageUncheck];
}

class $AssetsLiveActivityGen {
  const $AssetsLiveActivityGen();

  /// File path: assets/live_activity/live_activity_bonus.png
  AssetGenImage get liveActivityBonus =>
      const AssetGenImage('assets/live_activity/live_activity_bonus.png');

  /// File path: assets/live_activity/live_activity_coins.png
  AssetGenImage get liveActivityCoins =>
      const AssetGenImage('assets/live_activity/live_activity_coins.png');

  /// File path: assets/live_activity/live_activity_cover.png
  AssetGenImage get liveActivityCover =>
      const AssetGenImage('assets/live_activity/live_activity_cover.png');

  /// File path: assets/live_activity/live_activity_logo.png
  AssetGenImage get liveActivityLogo =>
      const AssetGenImage('assets/live_activity/live_activity_logo.png');

  /// File path: assets/live_activity/live_activity_play.png
  AssetGenImage get liveActivityPlay =>
      const AssetGenImage('assets/live_activity/live_activity_play.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    liveActivityBonus,
    liveActivityCoins,
    liveActivityCover,
    liveActivityLogo,
    liveActivityPlay,
  ];
}

class $AssetsLoadingGen {
  const $AssetsLoadingGen();

  /// File path: assets/loading/dot_loading.webp
  AssetGenImage get dotLoading =>
      const AssetGenImage('assets/loading/dot_loading.webp');

  /// File path: assets/loading/ff_loading.webp
  AssetGenImage get ffLoading =>
      const AssetGenImage('assets/loading/ff_loading.webp');

  /// File path: assets/loading/icon_loading.webp
  AssetGenImage get iconLoading =>
      const AssetGenImage('assets/loading/icon_loading.webp');

  /// List of all assets
  List<AssetGenImage> get values => [dotLoading, ffLoading, iconLoading];
}

class $AssetsLoginGen {
  const $AssetsLoginGen();

  /// File path: assets/login/apple_black.png
  AssetGenImage get appleBlack =>
      const AssetGenImage('assets/login/apple_black.png');

  /// File path: assets/login/facebook_white_icon.png
  AssetGenImage get facebookWhiteIcon =>
      const AssetGenImage('assets/login/facebook_white_icon.png');

  /// File path: assets/login/fb_icon_blue.png
  AssetGenImage get fbIconBlue =>
      const AssetGenImage('assets/login/fb_icon_blue.png');

  /// File path: assets/login/google_icon.png
  AssetGenImage get googleIcon =>
      const AssetGenImage('assets/login/google_icon.png');

  /// File path: assets/login/icon_bonus.png
  AssetGenImage get iconBonus =>
      const AssetGenImage('assets/login/icon_bonus.png');

  /// File path: assets/login/login_guide_bg.png
  AssetGenImage get loginGuideBg =>
      const AssetGenImage('assets/login/login_guide_bg.png');

  /// File path: assets/login/login_guide_close.png
  AssetGenImage get loginGuideClose =>
      const AssetGenImage('assets/login/login_guide_close.png');

  /// File path: assets/login/login_head_bg.png
  AssetGenImage get loginHeadBg =>
      const AssetGenImage('assets/login/login_head_bg.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    appleBlack,
    facebookWhiteIcon,
    fbIconBlue,
    googleIcon,
    iconBonus,
    loginGuideBg,
    loginGuideClose,
    loginHeadBg,
  ];
}

class $AssetsMylistGen {
  const $AssetsMylistGen();

  /// File path: assets/mylist/arrow_left.png
  AssetGenImage get arrowLeft =>
      const AssetGenImage('assets/mylist/arrow_left.png');

  /// File path: assets/mylist/favorites.png
  AssetGenImage get favorites =>
      const AssetGenImage('assets/mylist/favorites.png');

  /// File path: assets/mylist/history.png
  AssetGenImage get history => const AssetGenImage('assets/mylist/history.png');

  /// File path: assets/mylist/mylist_already_collected.png
  AssetGenImage get mylistAlreadyCollected =>
      const AssetGenImage('assets/mylist/mylist_already_collected.png');

  /// File path: assets/mylist/mylist_already_select.png
  AssetGenImage get mylistAlreadySelect =>
      const AssetGenImage('assets/mylist/mylist_already_select.png');

  /// File path: assets/mylist/mylist_back.png
  AssetGenImage get mylistBack =>
      const AssetGenImage('assets/mylist/mylist_back.png');

  /// File path: assets/mylist/mylist_delete.png
  AssetGenImage get mylistDelete =>
      const AssetGenImage('assets/mylist/mylist_delete.png');

  /// File path: assets/mylist/mylist_delete_disable.png
  AssetGenImage get mylistDeleteDisable =>
      const AssetGenImage('assets/mylist/mylist_delete_disable.png');

  /// File path: assets/mylist/mylist_edit.png
  AssetGenImage get mylistEdit =>
      const AssetGenImage('assets/mylist/mylist_edit.png');

  /// File path: assets/mylist/mylist_not_collected.png
  AssetGenImage get mylistNotCollected =>
      const AssetGenImage('assets/mylist/mylist_not_collected.png');

  /// File path: assets/mylist/mylist_not_select.png
  AssetGenImage get mylistNotSelect =>
      const AssetGenImage('assets/mylist/mylist_not_select.png');

  /// File path: assets/mylist/mylist_select_all.png
  AssetGenImage get mylistSelectAll =>
      const AssetGenImage('assets/mylist/mylist_select_all.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    arrowLeft,
    favorites,
    history,
    mylistAlreadyCollected,
    mylistAlreadySelect,
    mylistBack,
    mylistDelete,
    mylistDeleteDisable,
    mylistEdit,
    mylistNotCollected,
    mylistNotSelect,
    mylistSelectAll,
  ];
}

class $AssetsNotificationGen {
  const $AssetsNotificationGen();

  /// File path: assets/notification/bg_notification_reward.png
  AssetGenImage get bgNotificationReward =>
      const AssetGenImage('assets/notification/bg_notification_reward.png');

  /// File path: assets/notification/ic_notification_close.png
  AssetGenImage get icNotificationClose =>
      const AssetGenImage('assets/notification/ic_notification_close.png');

  /// File path: assets/notification/ic_notification_reward.png
  AssetGenImage get icNotificationReward =>
      const AssetGenImage('assets/notification/ic_notification_reward.png');

  /// File path: assets/notification/ic_notification_switch_default.png
  AssetGenImage get icNotificationSwitchDefault => const AssetGenImage(
    'assets/notification/ic_notification_switch_default.png',
  );

  /// File path: assets/notification/ic_notification_switch_selected.png
  AssetGenImage get icNotificationSwitchSelected => const AssetGenImage(
    'assets/notification/ic_notification_switch_selected.png',
  );

  /// List of all assets
  List<AssetGenImage> get values => [
    bgNotificationReward,
    icNotificationClose,
    icNotificationReward,
    icNotificationSwitchDefault,
    icNotificationSwitchSelected,
  ];
}

class $AssetsProfileGen {
  const $AssetsProfileGen();

  /// File path: assets/profile/avatar.png
  AssetGenImage get avatar => const AssetGenImage('assets/profile/avatar.png');

  /// File path: assets/profile/bonus.png
  AssetGenImage get bonus => const AssetGenImage('assets/profile/bonus.png');

  /// File path: assets/profile/coins.png
  AssetGenImage get coins => const AssetGenImage('assets/profile/coins.png');

  /// File path: assets/profile/copy.png
  AssetGenImage get copy => const AssetGenImage('assets/profile/copy.png');

  /// File path: assets/profile/feedback.png
  AssetGenImage get feedback =>
      const AssetGenImage('assets/profile/feedback.png');

  /// File path: assets/profile/language.png
  AssetGenImage get language =>
      const AssetGenImage('assets/profile/language.png');

  /// File path: assets/profile/rewards.png
  AssetGenImage get rewards =>
      const AssetGenImage('assets/profile/rewards.png');

  /// File path: assets/profile/setting.png
  AssetGenImage get setting =>
      const AssetGenImage('assets/profile/setting.png');

  /// File path: assets/profile/subscription_block.png
  AssetGenImage get subscriptionBlock =>
      const AssetGenImage('assets/profile/subscription_block.png');

  /// File path: assets/profile/vip.png
  AssetGenImage get vip => const AssetGenImage('assets/profile/vip.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    avatar,
    bonus,
    coins,
    copy,
    feedback,
    language,
    rewards,
    setting,
    subscriptionBlock,
    vip,
  ];
}

class $AssetsRecommendGen {
  const $AssetsRecommendGen();

  /// File path: assets/recommend/dis_store_tag_bg.png
  AssetGenImage get disStoreTagBg =>
      const AssetGenImage('assets/recommend/dis_store_tag_bg.png');

  /// File path: assets/recommend/limited_back.png
  AssetGenImage get limitedBack =>
      const AssetGenImage('assets/recommend/limited_back.png');

  /// File path: assets/recommend/tag_bg.png
  AssetGenImage get tagBg => const AssetGenImage('assets/recommend/tag_bg.png');

  /// File path: assets/recommend/time_back.png
  AssetGenImage get timeBack =>
      const AssetGenImage('assets/recommend/time_back.png');

  /// File path: assets/recommend/time_end.png
  AssetGenImage get timeEnd =>
      const AssetGenImage('assets/recommend/time_end.png');

  /// File path: assets/recommend/top_back.png
  AssetGenImage get topBack =>
      const AssetGenImage('assets/recommend/top_back.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    disStoreTagBg,
    limitedBack,
    tagBg,
    timeBack,
    timeEnd,
    topBack,
  ];
}

class $AssetsResourcebitGen {
  const $AssetsResourcebitGen();

  /// File path: assets/resourcebit/banner_placeholder.png
  AssetGenImage get bannerPlaceholder =>
      const AssetGenImage('assets/resourcebit/banner_placeholder.png');

  /// File path: assets/resourcebit/home_bottom_float_close.png
  AssetGenImage get homeBottomFloatClose =>
      const AssetGenImage('assets/resourcebit/home_bottom_float_close.png');

  /// File path: assets/resourcebit/home_popup_close.png
  AssetGenImage get homePopupClose =>
      const AssetGenImage('assets/resourcebit/home_popup_close.png');

  /// File path: assets/resourcebit/skip.png
  AssetGenImage get skip => const AssetGenImage('assets/resourcebit/skip.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    bannerPlaceholder,
    homeBottomFloatClose,
    homePopupClose,
    skip,
  ];
}

class $AssetsRewardsGen {
  const $AssetsRewardsGen();

  /// File path: assets/rewards/icon_back.png
  AssetGenImage get iconBack =>
      const AssetGenImage('assets/rewards/icon_back.png');

  /// File path: assets/rewards/img_checkin_alert_ad.png
  AssetGenImage get imgCheckinAlertAd =>
      const AssetGenImage('assets/rewards/img_checkin_alert_ad.png');

  /// File path: assets/rewards/img_checkin_alert_header.png
  AssetGenImage get imgCheckinAlertHeader =>
      const AssetGenImage('assets/rewards/img_checkin_alert_header.png');

  /// File path: assets/rewards/img_rewards_select.png
  AssetGenImage get imgRewardsSelect =>
      const AssetGenImage('assets/rewards/img_rewards_select.png');

  /// File path: assets/rewards/img_rewards_top.png
  AssetGenImage get imgRewardsTop =>
      const AssetGenImage('assets/rewards/img_rewards_top.png');

  /// File path: assets/rewards/img_rewards_user_tag_bg.png
  AssetGenImage get imgRewardsUserTagBg =>
      const AssetGenImage('assets/rewards/img_rewards_user_tag_bg.png');

  /// File path: assets/rewards/img_task_list_email.png
  AssetGenImage get imgTaskListEmail =>
      const AssetGenImage('assets/rewards/img_task_list_email.png');

  /// File path: assets/rewards/img_task_list_facebook.png
  AssetGenImage get imgTaskListFacebook =>
      const AssetGenImage('assets/rewards/img_task_list_facebook.png');

  /// File path: assets/rewards/img_task_list_notification.png
  AssetGenImage get imgTaskListNotification =>
      const AssetGenImage('assets/rewards/img_task_list_notification.png');

  /// File path: assets/rewards/img_task_list_phone.png
  AssetGenImage get imgTaskListPhone =>
      const AssetGenImage('assets/rewards/img_task_list_phone.png');

  /// File path: assets/rewards/img_task_select.png
  AssetGenImage get imgTaskSelect =>
      const AssetGenImage('assets/rewards/img_task_select.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    iconBack,
    imgCheckinAlertAd,
    imgCheckinAlertHeader,
    imgRewardsSelect,
    imgRewardsTop,
    imgRewardsUserTagBg,
    imgTaskListEmail,
    imgTaskListFacebook,
    imgTaskListNotification,
    imgTaskListPhone,
    imgTaskSelect,
  ];
}

class $AssetsRewardsBindGen {
  const $AssetsRewardsBindGen();

  /// File path: assets/rewards_bind/icon_rewads_delete.png
  AssetGenImage get iconRewadsDelete =>
      const AssetGenImage('assets/rewards_bind/icon_rewads_delete.png');

  /// File path: assets/rewards_bind/icon_rewads_more.png
  AssetGenImage get iconRewadsMore =>
      const AssetGenImage('assets/rewards_bind/icon_rewads_more.png');

  /// File path: assets/rewards_bind/img_bind_email_success.png
  AssetGenImage get imgBindEmailSuccess =>
      const AssetGenImage('assets/rewards_bind/img_bind_email_success.png');

  /// File path: assets/rewards_bind/img_bind_phone_success.png
  AssetGenImage get imgBindPhoneSuccess =>
      const AssetGenImage('assets/rewards_bind/img_bind_phone_success.png');

  /// File path: assets/rewards_bind/img_input_error.png
  AssetGenImage get imgInputError =>
      const AssetGenImage('assets/rewards_bind/img_input_error.png');

  /// File path: assets/rewards_bind/img_rewards_animation.webp
  AssetGenImage get imgRewardsAnimation =>
      const AssetGenImage('assets/rewards_bind/img_rewards_animation.webp');

  /// List of all assets
  List<AssetGenImage> get values => [
    iconRewadsDelete,
    iconRewadsMore,
    imgBindEmailSuccess,
    imgBindPhoneSuccess,
    imgInputError,
    imgRewardsAnimation,
  ];
}

class $AssetsStoreGen {
  const $AssetsStoreGen();

  /// File path: assets/store/icon_agreement_select.png
  AssetGenImage get iconAgreementSelect =>
      const AssetGenImage('assets/store/icon_agreement_select.png');

  /// File path: assets/store/icon_agreement_unselect.png
  AssetGenImage get iconAgreementUnselect =>
      const AssetGenImage('assets/store/icon_agreement_unselect.png');

  /// File path: assets/store/icon_first_item.png
  AssetGenImage get iconFirstItem =>
      const AssetGenImage('assets/store/icon_first_item.png');

  /// File path: assets/store/icon_store_pop_exit.png
  AssetGenImage get iconStorePopExit =>
      const AssetGenImage('assets/store/icon_store_pop_exit.png');

  /// File path: assets/store/img_first_item_bg.png
  AssetGenImage get imgFirstItemBg =>
      const AssetGenImage('assets/store/img_first_item_bg.png');

  /// File path: assets/store/img_product_clock.png
  AssetGenImage get imgProductClock =>
      const AssetGenImage('assets/store/img_product_clock.png');

  /// File path: assets/store/img_store_item_bg.png
  AssetGenImage get imgStoreItemBg =>
      const AssetGenImage('assets/store/img_store_item_bg.png');

  /// File path: assets/store/img_store_tag_bg.png
  AssetGenImage get imgStoreTagBg =>
      const AssetGenImage('assets/store/img_store_tag_bg.png');

  /// File path: assets/store/img_subscribe_item_bg.png
  AssetGenImage get imgSubscribeItemBg =>
      const AssetGenImage('assets/store/img_subscribe_item_bg.png');

  /// File path: assets/store/img_subscribe_item_icon_31.png
  AssetGenImage get imgSubscribeItemIcon31 =>
      const AssetGenImage('assets/store/img_subscribe_item_icon_31.png');

  /// File path: assets/store/img_subscribe_item_icon_365.png
  AssetGenImage get imgSubscribeItemIcon365 =>
      const AssetGenImage('assets/store/img_subscribe_item_icon_365.png');

  /// File path: assets/store/img_subscribe_item_icon_7.png
  AssetGenImage get imgSubscribeItemIcon7 =>
      const AssetGenImage('assets/store/img_subscribe_item_icon_7.png');

  /// File path: assets/store/img_subscribe_item_tag.png
  AssetGenImage get imgSubscribeItemTag =>
      const AssetGenImage('assets/store/img_subscribe_item_tag.png');

  /// File path: assets/store/img_subscription_detail_bg.png
  AssetGenImage get imgSubscriptionDetailBg =>
      const AssetGenImage('assets/store/img_subscription_detail_bg.png');

  /// File path: assets/store/img_subscription_list_bg.png
  AssetGenImage get imgSubscriptionListBg =>
      const AssetGenImage('assets/store/img_subscription_list_bg.png');

  /// File path: assets/store/img_subscription_vip_big.png
  AssetGenImage get imgSubscriptionVipBig =>
      const AssetGenImage('assets/store/img_subscription_vip_big.png');

  /// File path: assets/store/img_time_warning.png
  AssetGenImage get imgTimeWarning =>
      const AssetGenImage('assets/store/img_time_warning.png');

  /// File path: assets/store/img_watch_ad.png
  AssetGenImage get imgWatchAd =>
      const AssetGenImage('assets/store/img_watch_ad.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    iconAgreementSelect,
    iconAgreementUnselect,
    iconFirstItem,
    iconStorePopExit,
    imgFirstItemBg,
    imgProductClock,
    imgStoreItemBg,
    imgStoreTagBg,
    imgSubscribeItemBg,
    imgSubscribeItemIcon31,
    imgSubscribeItemIcon365,
    imgSubscribeItemIcon7,
    imgSubscribeItemTag,
    imgSubscriptionDetailBg,
    imgSubscriptionListBg,
    imgSubscriptionVipBig,
    imgTimeWarning,
    imgWatchAd,
  ];
}

class $AssetsTabbarGen {
  const $AssetsTabbarGen();

  /// File path: assets/tabbar/home.png
  AssetGenImage get home => const AssetGenImage('assets/tabbar/home.png');

  /// File path: assets/tabbar/home_active.png
  AssetGenImage get homeActive =>
      const AssetGenImage('assets/tabbar/home_active.png');

  /// File path: assets/tabbar/my_list.png
  AssetGenImage get myList => const AssetGenImage('assets/tabbar/my_list.png');

  /// File path: assets/tabbar/my_list_active.png
  AssetGenImage get myListActive =>
      const AssetGenImage('assets/tabbar/my_list_active.png');

  /// File path: assets/tabbar/profile.png
  AssetGenImage get profile => const AssetGenImage('assets/tabbar/profile.png');

  /// File path: assets/tabbar/profile_active.png
  AssetGenImage get profileActive =>
      const AssetGenImage('assets/tabbar/profile_active.png');

  /// File path: assets/tabbar/reels.png
  AssetGenImage get reels => const AssetGenImage('assets/tabbar/reels.png');

  /// File path: assets/tabbar/reels_active.png
  AssetGenImage get reelsActive =>
      const AssetGenImage('assets/tabbar/reels_active.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    home,
    homeActive,
    myList,
    myListActive,
    profile,
    profileActive,
    reels,
    reelsActive,
  ];
}

class $AssetsUpgradeGen {
  const $AssetsUpgradeGen();

  /// File path: assets/upgrade/upgrade_icon.png
  AssetGenImage get upgradeIcon =>
      const AssetGenImage('assets/upgrade/upgrade_icon.png');

  /// File path: assets/upgrade/upgrade_top_bg.png
  AssetGenImage get upgradeTopBg =>
      const AssetGenImage('assets/upgrade/upgrade_top_bg.png');

  /// List of all assets
  List<AssetGenImage> get values => [upgradeIcon, upgradeTopBg];
}

class $AssetsVideoGen {
  const $AssetsVideoGen();

  /// File path: assets/video/close.png
  AssetGenImage get close => const AssetGenImage('assets/video/close.png');

  /// File path: assets/video/like.png
  AssetGenImage get like => const AssetGenImage('assets/video/like.png');

  /// File path: assets/video/list.png
  AssetGenImage get list => const AssetGenImage('assets/video/list.png');

  /// File path: assets/video/live_active.png
  AssetGenImage get liveActive =>
      const AssetGenImage('assets/video/live_active.png');

  /// File path: assets/video/lock-episode.png
  AssetGenImage get lockEpisode =>
      const AssetGenImage('assets/video/lock-episode.png');

  /// File path: assets/video/pause.png
  AssetGenImage get pause => const AssetGenImage('assets/video/pause.png');

  /// File path: assets/video/play.png
  AssetGenImage get play => const AssetGenImage('assets/video/play.png');

  /// File path: assets/video/play_episode.png
  AssetGenImage get playEpisode =>
      const AssetGenImage('assets/video/play_episode.png');

  /// File path: assets/video/share.png
  AssetGenImage get share => const AssetGenImage('assets/video/share.png');

  /// File path: assets/video/unlock.png
  AssetGenImage get unlock => const AssetGenImage('assets/video/unlock.png');

  /// File path: assets/video/vip.png
  AssetGenImage get vip => const AssetGenImage('assets/video/vip.png');

  /// File path: assets/video/water_mark.png
  AssetGenImage get waterMark =>
      const AssetGenImage('assets/video/water_mark.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    close,
    like,
    list,
    liveActive,
    lockEpisode,
    pause,
    play,
    playEpisode,
    share,
    unlock,
    vip,
    waterMark,
  ];
}

class $AssetsDetailsAdvanceWatchAdGen {
  const $AssetsDetailsAdvanceWatchAdGen();

  /// File path: assets/details/advance_watch_ad/advance_top_icon.png
  AssetGenImage get advanceTopIcon => const AssetGenImage(
    'assets/details/advance_watch_ad/advance_top_icon.png',
  );

  /// File path: assets/details/advance_watch_ad/advance_unlock_icon.png
  AssetGenImage get advanceUnlockIcon => const AssetGenImage(
    'assets/details/advance_watch_ad/advance_unlock_icon.png',
  );

  /// File path: assets/details/advance_watch_ad/advance_unlocked_icon.png
  AssetGenImage get advanceUnlockedIcon => const AssetGenImage(
    'assets/details/advance_watch_ad/advance_unlocked_icon.png',
  );

  /// File path: assets/details/advance_watch_ad/advance_watch_button_bg.png
  AssetGenImage get advanceWatchButtonBg => const AssetGenImage(
    'assets/details/advance_watch_ad/advance_watch_button_bg.png',
  );

  /// File path: assets/details/advance_watch_ad/advance_watch_close.png
  AssetGenImage get advanceWatchClose => const AssetGenImage(
    'assets/details/advance_watch_ad/advance_watch_close.png',
  );

  /// File path: assets/details/advance_watch_ad/continue_right.png
  AssetGenImage get continueRight =>
      const AssetGenImage('assets/details/advance_watch_ad/continue_right.png');

  /// File path: assets/details/advance_watch_ad/continue_watch_current.png
  AssetGenImage get continueWatchCurrent => const AssetGenImage(
    'assets/details/advance_watch_ad/continue_watch_current.png',
  );

  /// File path: assets/details/advance_watch_ad/continue_watch_next.png
  AssetGenImage get continueWatchNext => const AssetGenImage(
    'assets/details/advance_watch_ad/continue_watch_next.png',
  );

  /// File path: assets/details/advance_watch_ad/mask_bg_continue_top.png
  AssetGenImage get maskBgContinueTop => const AssetGenImage(
    'assets/details/advance_watch_ad/mask_bg_continue_top.png',
  );

  /// File path: assets/details/advance_watch_ad/mask_bg_pop.png
  AssetGenImage get maskBgPop =>
      const AssetGenImage('assets/details/advance_watch_ad/mask_bg_pop.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    advanceTopIcon,
    advanceUnlockIcon,
    advanceUnlockedIcon,
    advanceWatchButtonBg,
    advanceWatchClose,
    continueRight,
    continueWatchCurrent,
    continueWatchNext,
    maskBgContinueTop,
    maskBgPop,
  ];
}

class Assets {
  const Assets._();

  static const AssetGenImage alertGradientBg = AssetGenImage(
    'assets/alert_gradient_bg.png',
  );
  static const AssetGenImage arrowCircleDown = AssetGenImage(
    'assets/arrow_circle_down.png',
  );
  static const AssetGenImage back = AssetGenImage('assets/back.png');
  static const $AssetsBackIconGen backIcon = $AssetsBackIconGen();
  static const $AssetsDetailsGen details = $AssetsDetailsGen();
  static const AssetGenImage dialogGrayClose = AssetGenImage(
    'assets/dialog_gray_close.png',
  );
  static const $AssetsEmptyGen empty = $AssetsEmptyGen();
  static const $AssetsFullscreenGen fullscreen = $AssetsFullscreenGen();
  static const $AssetsGoodReviewGen goodReview = $AssetsGoodReviewGen();
  static const $AssetsHomeGen home = $AssetsHomeGen();
  static const AssetGenImage iconBack = AssetGenImage('assets/icon_back.png');
  static const AssetGenImage imgCoinB = AssetGenImage('assets/img_coin_b.png');
  static const AssetGenImage imgCoinF = AssetGenImage('assets/img_coin_f.png');
  static const AssetGenImage imgFeedAdDefault = AssetGenImage(
    'assets/img_feed_ad_default.png',
  );
  static const AssetGenImage iosLogo = AssetGenImage('assets/ios_logo.png');
  static const $AssetsLanguageGen language = $AssetsLanguageGen();
  static const AssetGenImage launcherIcon = AssetGenImage(
    'assets/launcher_icon.png',
  );
  static const $AssetsLiveActivityGen liveActivity = $AssetsLiveActivityGen();
  static const $AssetsLoadingGen loading = $AssetsLoadingGen();
  static const $AssetsLoginGen login = $AssetsLoginGen();
  static const AssetGenImage logo = AssetGenImage('assets/logo.png');
  static const $AssetsMylistGen mylist = $AssetsMylistGen();
  static const $AssetsNotificationGen notification = $AssetsNotificationGen();
  static const String playerAndroid = 'assets/player_android.lic';
  static const String playerIos = 'assets/player_ios.lic';
  static const $AssetsProfileGen profile = $AssetsProfileGen();
  static const $AssetsRecommendGen recommend = $AssetsRecommendGen();
  static const AssetGenImage remind = AssetGenImage('assets/remind.png');
  static const $AssetsResourcebitGen resourcebit = $AssetsResourcebitGen();
  static const $AssetsRewardsGen rewards = $AssetsRewardsGen();
  static const $AssetsRewardsBindGen rewardsBind = $AssetsRewardsBindGen();
  static const AssetGenImage splashLogo = AssetGenImage(
    'assets/splash_logo.png',
  );
  static const $AssetsStoreGen store = $AssetsStoreGen();
  static const $AssetsTabbarGen tabbar = $AssetsTabbarGen();
  static const AssetGenImage triangleSmall = AssetGenImage(
    'assets/triangle_small.png',
  );
  static const $AssetsUpgradeGen upgrade = $AssetsUpgradeGen();
  static const $AssetsVideoGen video = $AssetsVideoGen();

  /// List of all assets
  static List<dynamic> get values => [
    alertGradientBg,
    arrowCircleDown,
    back,
    dialogGrayClose,
    iconBack,
    imgCoinB,
    imgCoinF,
    imgFeedAdDefault,
    iosLogo,
    launcherIcon,
    logo,
    playerAndroid,
    playerIos,
    remind,
    splashLogo,
    triangleSmall,
  ];
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

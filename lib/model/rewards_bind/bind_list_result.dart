/// provider : 20
/// providerId : ""

class BindListResult {
  List<BindProvider> providers;

  BindListResult({required this.providers});

  factory BindListResult.fromJson(List<dynamic> json) {
    List<BindProvider> providerList = json
        .map((item) => BindProvider(
      provider: item['provider'],
      providerId: item['providerId'],
    ))
        .toList();
    return BindListResult(providers: providerList);
  }
}

class BindProvider {
  int provider;
  String providerId;

  BindProvider({required this.provider, required this.providerId});
}


class CountryCodeInfo {
  String areaCode;
  String countryName;
  String? countryCode;

  CountryCodeInfo({
    required this.areaCode,
    required this.countryName,
    this.countryCode,
  });

  factory CountryCodeInfo.fromJson(Map<String, dynamic> json) {
    return CountryCodeInfo(
      areaCode: json['areaCode'],
      countryName: json['countryName'],
      countryCode: json['countryCode'],
    );
  }
}

List<CountryCodeInfo> parseCountryCodeList(List<dynamic> jsonList) {
  return jsonList.map((item) => CountryCodeInfo.fromJson(item)).toList();
}
    
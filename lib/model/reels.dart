import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:playlet/components/reels/player/reel_player_view.dart';
import 'package:playlet/service/ad/admodel/ad_model.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';

enum ReelType {
  // 短剧
  shorts,
  // 广告
  ad,
  // 资源位
  resourceBit
}

class ReelItem {
  ReelType type;
  final Reel? reel;
  AdModel? adModel;
  ResourceBitModel? resourceBitModel;
  ReelItem({
    this.type = ReelType.shorts,
    this.reel,
    this.adModel,
    this.resourceBitModel,
  });
}

class Reel {
  String videoUrl;
  Duration playPosition = Duration.zero;
  ForYouShortInfo info;
  GlobalKey<ReelPlayerViewState> playerKey = GlobalKey();
  Reel({
    required this.info,
    required this.videoUrl,
  });

  setPlayPosition(Duration position) {
    playPosition = position;
  }
}

class ShortPlayResult {
  int? consecutiveTimes;
  List<ForYouShortInfo>? forYouList;
  bool? hasMore;
  int? lastShortPlayId;
  String? realLanguageCode;

  ShortPlayResult(
      {this.consecutiveTimes,
      this.forYouList,
      this.hasMore,
      this.lastShortPlayId,this.realLanguageCode});

  ShortPlayResult.fromJson(Map<String, dynamic> json) {
    consecutiveTimes = json['consecutiveTimes'];
    if (json['forYouList'] != null) {
      forYouList = <ForYouShortInfo>[];
      json['forYouList'].forEach((v) {
        forYouList!.add(ForYouShortInfo.fromJson(v));
      });
    }
    hasMore = json['hasMore'];
    lastShortPlayId = json['lastShortPlayId'];
    realLanguageCode = json['realLanguageCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['consecutiveTimes'] = consecutiveTimes;
    if (forYouList != null) {
      data['forYouList'] = forYouList!.map((v) => v.toJson()).toList();
    }
    data['hasMore'] = hasMore;
    data['lastShortPlayId'] = lastShortPlayId;
    data['realLanguageCode'] = realLanguageCode;
    return data;
  }
}

class ForYouShortInfo {
  VideoInfoMap? bitrate;
  VideoInfoMap? codec;
  int? collectNum;
  String? coverId;
  int? episodeNum;
  int? finalDramId;
  int? firstDramId;
  int? id;
  int? isCollect;
  int? isRecommendPool;
  bool? needDecrypt;
  int? playNum;
  String? recommendContent;
  int? shortPlayCode;
  int? shortPlayId;
  String? shortPlayName;
  int? shortPlayType;
  String? summary;
  int? totalEpisodes;
  int? videoDuration;
  int? videoType;
  int? updateEpisode;
  VideoInfoMap? videoUrl;
  double? aspectRatio;

  ForYouShortInfo(
      {this.bitrate,
      this.codec,
      this.collectNum,
      this.coverId,
      this.episodeNum,
      this.finalDramId,
      this.firstDramId,
      this.id,
      this.isCollect,
      this.isRecommendPool,
      this.needDecrypt,
      this.playNum,
      this.recommendContent,
      this.shortPlayCode,
      this.shortPlayId,
      this.shortPlayName,
      this.shortPlayType,
      this.summary,
      this.totalEpisodes,
      this.videoDuration,
      this.videoType,
      this.updateEpisode,
      this.videoUrl,
      this.aspectRatio});

  ForYouShortInfo.fromJson(Map<String, dynamic> json) {
    if (json["bitrate"] != null) {
      bitrate = VideoInfoMap.fromJson(jsonDecode(json['bitrate']));
    }
    if (json['codec'] != null) {
      codec = VideoInfoMap.fromJson(jsonDecode(json['codec']));
    }
    collectNum = json['collectNum'];
    coverId = json['coverId'];
    episodeNum = json['episodeNum'];
    finalDramId = json['finalDramId'];
    firstDramId = json['firstDramId'];
    id = json['id'];
    isCollect = json['isCollect'];
    updateEpisode = json['updateEpisode'] ?? 0;
    isRecommendPool = json['isRecommendPool'];
    needDecrypt = json['needDecrypt'];
    playNum = json['playNum'];
    recommendContent = json['recommendContent'];
    shortPlayCode = json['shortPlayCode'];
    shortPlayId = json['shortPlayId'];
    shortPlayName = json['shortPlayName'];
    shortPlayType = json['shortPlayType'];
    summary = json['summary'];
    totalEpisodes = json['totalEpisodes'];
    videoDuration = json['videoDuration'];
    videoType = json['videoType'];
    if (json['videoUrl'] != null) {
      videoUrl = VideoInfoMap.fromJson(jsonDecode(json['videoUrl']));
    }
    aspectRatio = json['aspectRatio'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['bitrate'] = bitrate;
    data['codec'] = codec;
    data['collectNum'] = collectNum;
    data['coverId'] = coverId;
    data['episodeNum'] = episodeNum;
    data['finalDramId'] = finalDramId;
    data['firstDramId'] = firstDramId;
    data['id'] = id;
    data['isCollect'] = isCollect;
    data['isRecommendPool'] = isRecommendPool;
    data['needDecrypt'] = needDecrypt;
    data['playNum'] = playNum;
    data['recommendContent'] = recommendContent;
    data['shortPlayCode'] = shortPlayCode;
    data['shortPlayId'] = shortPlayId;
    data['shortPlayName'] = shortPlayName;
    data['shortPlayType'] = shortPlayType;
    data['summary'] = summary;
    data['totalEpisodes'] = totalEpisodes;
    data['videoDuration'] = videoDuration;
    data['videoType'] = videoType;
    data['videoUrl'] = videoUrl;
    data['aspectRatio'] = aspectRatio;
    return data;
  }
}

class VideoInfoMap {
  String? video720;
  String? video1080;
  String? video480;

  VideoInfoMap({this.video720, this.video1080, this.video480});

  VideoInfoMap.fromJson(Map<String, dynamic> json) {
    video720 = json['video_720'];
    video1080 = json['video_1080'];
    video480 = json['video_480'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['video_720'] = video720;
    data['video_1080'] = video1080;
    data['video_480'] = video480;
    return data;
  }
}

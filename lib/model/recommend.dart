import 'dart:convert';

import 'package:playlet/model/reels.dart';
import 'package:playlet/model/store/store_product_result.dart';

class RecommendData {
  List<RecommendList>? recommendList;
  bool? showOriginPrice;
  int? showSkuAmount;
  List<SkuInfoResponses>? skuInfoList;
  List<SubscribeSkuResponses>? subscribeList;

  RecommendData(
      {this.recommendList,
      this.showOriginPrice,
      this.showSkuAmount,
      this.skuInfoList,
      this.subscribeList});

  RecommendData.fromJson(Map<String, dynamic> json) {
    if (json['recommendList'] != null) {
      recommendList = <RecommendList>[];
      json['recommendList'].forEach((v) {
        recommendList!.add(RecommendList.fromJson(v));
      });
    }
    showOriginPrice = json['showOriginPrice'];
    showSkuAmount = json['showSkuAmount'];
    if (json['skuInfoList'] != null) {
      skuInfoList = [];
      json['skuInfoList'].forEach((v) {
        skuInfoList!.add(SkuInfoResponses.fromJson(v));
      });
    }
    if (json['subscribeList'] != null) {
      subscribeList = <SubscribeSkuResponses>[];
      json['subscribeList'].forEach((v) {
        subscribeList!.add(SubscribeSkuResponses.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (recommendList != null) {
      data['recommendList'] = recommendList!.map((v) => v.toJson()).toList();
    }
    data['showOriginPrice'] = showOriginPrice;
    data['showSkuAmount'] = showSkuAmount;
    if (skuInfoList != null) {
      data['skuInfoList'] = skuInfoList!.map((v) => v.toJson()).toList();
    }
    if (subscribeList != null) {
      data['subscribeList'] = subscribeList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class RecommendList {
  String? coverUrl;
  int? dramaId;
  int? episodeNum;
  VideoInfoMap? firstVideoUrl;
  bool? needDecrypt;
  String? shortPlayCode;
  String? shortPlayId;
  String? shortPlayName;
  String? shortPlaySummary;
  int? showScene;
  String? subscript;
  double? aspectRatio;

  RecommendList(
      {this.coverUrl,
      this.dramaId,
      this.episodeNum,
      this.firstVideoUrl,
      this.needDecrypt,
      this.shortPlayCode,
      this.shortPlayId,
      this.shortPlayName,
      this.shortPlaySummary,
      this.showScene,
      this.subscript,
      this.aspectRatio});

  RecommendList.fromJson(Map<String, dynamic> json) {
    coverUrl = json['coverUrl'];
    dramaId = json['dramaId'];
    episodeNum = json['episodeNum'];
    if (json['firstVideoUrl'] != null) {
      firstVideoUrl = VideoInfoMap.fromJson(jsonDecode(json['firstVideoUrl']));
    }
    needDecrypt = json['needDecrypt'];
    shortPlayCode = json['shortPlayCode'];
    shortPlayId = json['shortPlayId'];
    shortPlayName = json['shortPlayName'];
    shortPlaySummary = json['shortPlaySummary'];
    showScene = json['showScene'];
    subscript = json['subscript'];
    aspectRatio = json['aspectRatio'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['coverUrl'] = coverUrl;
    data['dramaId'] = dramaId;
    data['episodeNum'] = episodeNum;
    data['firstVideoUrl'] = firstVideoUrl;
    data['needDecrypt'] = needDecrypt;
    data['shortPlayCode'] = shortPlayCode;
    data['shortPlayId'] = shortPlayId;
    data['shortPlayName'] = shortPlayName;
    data['shortPlaySummary'] = shortPlaySummary;
    data['showScene'] = showScene;
    data['subscript'] = subscript;
    data['aspectRatio'] = aspectRatio;
    return data;
  }
}

class RecommendTimeData {
  int? startTime;
  int? endTime;

  RecommendTimeData({this.startTime, this.endTime});

  RecommendTimeData.fromJson(Map<String, dynamic> json) {
    startTime = json['startTime'];
    endTime = json['endTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['startTime'] = startTime;
    data['endTime'] = endTime;
    return data;
  }
}

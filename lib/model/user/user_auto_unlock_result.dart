class UserAutoUnlockResult {
  UserAutoUnlockResult({
    this.autoUnlock,
    this.autoUnlockEpisode,
  });
  bool? autoUnlock;
  bool? autoUnlockEpisode;

  UserAutoUnlockResult.fromJson(dynamic json) {
    autoUnlock = json['autoUnlock'];
    autoUnlockEpisode = json['autoUnlockEpisode'];
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['autoUnlock'] = autoUnlock;
    map['autoUnlockEpisode'] = autoUnlockEpisode;
    return map;
  }
}

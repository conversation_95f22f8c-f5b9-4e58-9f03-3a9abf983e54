class StoreProductResult {
  bool isDisplayedTripartite;
  SkuInfoResponses? retainSkuInfoResponses;
  int rewardSkuPositionType;
  List<SkuInfoResponses>? skuInfoResponses;
  int skuPositionType;
  List<SubscribeSkuResponses>? subscribeSkuResponses;

  StoreProductResult({
    this.isDisplayedTripartite = false,
    this.retainSkuInfoResponses,
    this.rewardSkuPositionType = 0,
    this.skuInfoResponses,
    this.skuPositionType = 0,
    this.subscribeSkuResponses,
  });

  StoreProductResult.fromJson(dynamic json)
      : isDisplayedTripartite = json['isDisplayedTripartite']?? false,
        retainSkuInfoResponses = json['retainSkuInfoResponses'] != null
            ? SkuInfoResponses.fromJson(json['retainSkuInfoResponses'])
            : null,
        rewardSkuPositionType = json['rewardSkuPositionType']?? 0,
        skuPositionType = json['skuPositionType']?? 0 {
    if (json['skuInfoResponses'] != null) {
      skuInfoResponses = [];
      json['skuInfoResponses'].forEach((v) {
        skuInfoResponses?.add(SkuInfoResponses.fromJson(v));
      });
    }

    if (json['subscribeSkuResponses'] != null) {
      subscribeSkuResponses = [];
      json['subscribeSkuResponses'].forEach((v) {
        subscribeSkuResponses?.add(SubscribeSkuResponses.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['isDisplayedTripartite'] = isDisplayedTripartite;
    if (retainSkuInfoResponses != null) {
      map['retainSkuInfoResponses'] = retainSkuInfoResponses?.toJson();
    }
    map['rewardSkuPositionType'] = rewardSkuPositionType;
    if (skuInfoResponses != null) {
      map['skuInfoResponses'] = skuInfoResponses?.map((v) => v.toJson()).toList();
    }
    map['skuPositionType'] = skuPositionType;
    if (subscribeSkuResponses != null) {
      map['subscribeSkuResponses'] = subscribeSkuResponses?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class SubscribeSkuResponses {
  int bonus;
  int coins;
  String countryCode;
  int duration;
  String firstAmount;
  bool inSubscription;
  bool isFirstBuy;
  int originalCoins;
  String payAmount;
  int productId;
  String showScene;
  String skuId;
  int skuType;
  int subscriptionId;
  int type;
  // 自定义字段 是否显示右上角首购优惠
  bool inShowNewDiscount;

  SubscribeSkuResponses({
    this.bonus = 0,
    this.coins = 0,
    this.countryCode = '',
    this.duration = 0,
    this.firstAmount = '',
    this.inSubscription = false,
    this.isFirstBuy = false,
    this.originalCoins = 0,
    this.payAmount = '',
    this.productId = 0,
    this.showScene = '',
    this.skuId = '',
    this.skuType = 0,
    this.subscriptionId = 0,
    this.type = 0,
    this.inShowNewDiscount = false,
  });

  SubscribeSkuResponses.fromJson(dynamic json)
      : bonus = json['bonus']?? 0,
        coins = json['coins']?? 0,
        countryCode = json['countryCode']?? '',
        duration = json['duration']?? 0,
        firstAmount = json['firstAmount']?? '',
        inSubscription = json['inSubscription']?? false,
        isFirstBuy = json['isFirstBuy']?? false,
        originalCoins = json['originalCoins']?? 0,
        payAmount = json['payAmount']?? '',
        productId = json['productId']?? 0,
        showScene = json['showScene']?? '',
        skuId = json['skuId']?? '',
        skuType = json['skuType']?? 0,
        subscriptionId = json['subscriptionId']?? 0,
        inShowNewDiscount=(json['firstAmount']?? '').isNotEmpty,
        type = json['type']?? 0;


  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['bonus'] = bonus;
    map['coins'] = coins;
    map['countryCode'] = countryCode;
    map['duration'] = duration;
    map['firstAmount'] = firstAmount;
    map['inSubscription'] = inSubscription;
    map['isFirstBuy'] = isFirstBuy;
    map['originalCoins'] = originalCoins;
    map['payAmount'] = payAmount;
    map['productId'] = productId;
    map['showScene'] = showScene;
    map['skuId'] = skuId;
    map['skuType'] = skuType;
    map['subscriptionId'] = subscriptionId;
    map['type'] = type;
    map['inShowNewDiscount'] = inShowNewDiscount;
    return map;
  }
}

class SkuInfoResponses {
  int skuType;
  String skuProductId;
  int prizeId;
  String skuModelConfigId;
  String subscript;
  dynamic keepGiveCoins;
  dynamic retentionSeconds;
  dynamic buyMaxNum;
  String productName;
  String recharge;
  double originalPrice;
  double activityPrice;
  int coins;
  int productGiveCoins;
  String gpSkuId;
  String iosSkuId;
  String coinsUnit;
  String giveCoinsUnit;
  bool purchasedSku;
  double disRate;
  int expirationTime;
  String currency;

  SkuInfoResponses({
    this.skuType = 0,
    this.skuProductId = '',
    this.prizeId = 0,
    this.skuModelConfigId = '',
    this.subscript = '',
    this.keepGiveCoins,
    this.retentionSeconds,
    this.buyMaxNum,
    this.productName = '',
    this.recharge = '',
    this.originalPrice = 0.0,
    this.activityPrice = 0.0,
    this.coins = 0,
    this.productGiveCoins = 0,
    this.gpSkuId = '',
    this.iosSkuId = '',
    this.coinsUnit = '',
    this.giveCoinsUnit = '',
    this.purchasedSku = false,
    this.disRate = 0.0,
    this.expirationTime = 0,
    this.currency = '',
  });

  SkuInfoResponses.fromJson(dynamic json)
      : skuType = json['skuType']?? 0,
        skuProductId = json['skuProductId']?? '',
        prizeId = json['prizeId']?? 0,
        skuModelConfigId = json['skuModelConfigId']?? '',
        subscript = json['subscript']?? '',
        keepGiveCoins = json['keepGiveCoins'],
        retentionSeconds = json['retentionSeconds'],
        buyMaxNum = json['buyMaxNum'],
        productName = json['productName']?? '',
        recharge = json['recharge']?? '',
        originalPrice = json['originalPrice']?? 0.0,
        activityPrice = json['activityPrice']?? 0.0,
        coins = json['coins']?? 0,
        productGiveCoins = json['productGiveCoins']?? 0,
        gpSkuId = json['gpSkuId']?? '',
        iosSkuId = json['iosSkuId']?? '',
        coinsUnit = json['coinsUnit']?? '',
        giveCoinsUnit = json['giveCoinsUnit']?? '',
        purchasedSku = json['purchasedSku']?? false,
        disRate = json['disRate']?? 0.0,
        expirationTime = json['expirationTime']?? 0,
        currency = json['currency']?? '';

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['skuType'] = skuType;
    map['skuProductId'] = skuProductId;
    map['prizeId'] = prizeId;
    map['skuModelConfigId'] = skuModelConfigId;
    map['subscript'] = subscript;
    map['keepGiveCoins'] = keepGiveCoins;
    map['retentionSeconds'] = retentionSeconds;
    map['buyMaxNum'] = buyMaxNum;
    map['productName'] = productName;
    map['recharge'] = recharge;
    map['originalPrice'] = originalPrice;
    map['activityPrice'] = activityPrice;
    map['coins'] = coins;
    map['productGiveCoins'] = productGiveCoins;
    map['gpSkuId'] = gpSkuId;
    map['iosSkuId'] = iosSkuId;
    map['coinsUnit'] = coinsUnit;
    map['giveCoinsUnit'] = giveCoinsUnit;
    map['purchasedSku'] = purchasedSku;
    map['disRate'] = disRate;
    map['expirationTime'] = expirationTime;
    map['currency'] = currency;
    return map;
  }
}
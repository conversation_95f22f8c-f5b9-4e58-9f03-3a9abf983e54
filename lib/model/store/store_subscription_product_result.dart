class StoreSubscriptionProductResult {
  StoreSubscriptionProductResult({this.productList});

  List<SubProductList>? productList;

  StoreSubscriptionProductResult.fromJson(dynamic json) {
    if (json['productList'] != null) {
      productList = [];
      json['productList'].forEach((v) {
        productList?.add(SubProductList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (productList != null) {
      map['productList'] = productList?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class SubProductList {
  String subscribeName;
  String countryCode;
  int subscriptionId;
  int productId;
  String skuId;
  int skuType;
  String payAmount;
  int originalCoins;
  int coins;
  int bonus;
  int duration;
  int type;
  String rights;
  String rightsDesc;
  String firstAmount;
  String subscript;
  double disRate;
  bool isFirstBuy;
  String showScene;
  int cardStyle;
  bool inSubscription;
  // 自定义字段 是否显示右上角首购优惠
  bool inShowNewDiscount;

  SubProductList({
    this.subscribeName = "",
    this.countryCode = "",
    this.subscriptionId = 0,
    this.productId = 0,
    this.skuId = "",
    this.skuType = 0,
    this.payAmount = "",
    this.originalCoins = 0,
    this.coins = 0,
    this.bonus = 0,
    this.duration = 0,
    this.type = 0,
    this.rights = "",
    this.rightsDesc = "",
    this.firstAmount = "",
    this.subscript = "",
    this.disRate = 0.0,
    this.isFirstBuy = false,
    this.showScene = "",
    this.cardStyle = 0,
    this.inSubscription = false,
    this.inShowNewDiscount = false,
  });

  SubProductList.fromJson(dynamic json)
      : subscribeName = json['subscribeName'] ?? "",
        countryCode = json['countryCode'] ?? "",
        subscriptionId = json['subscriptionId'] ?? 0,
        productId = json['productId'] ?? 0,
        skuId = json['skuId'] ?? "",
        skuType = json['skuType'] ?? 0,
        payAmount = json['payAmount'] ?? "",
        originalCoins = json['originalCoins'] ?? 0,
        coins = json['coins'] ?? 0,
        bonus = json['bonus'] ?? 0,
        duration = json['duration'] ?? 0,
        type = json['type'] ?? 0,
        rights = json['rights'] ?? "",
        rightsDesc = json['rightsDesc'] ?? "",
        firstAmount = json['firstAmount'] ?? "",
        subscript = json['subscript'] ?? "",
        disRate = json['disRate'] ?? 0.0,
        isFirstBuy = json['isFirstBuy'] ?? false,
        showScene = json['showScene'] ?? "",
        cardStyle = json['card_style'] ?? 0,
        inShowNewDiscount=(json['firstAmount']?? '').isNotEmpty,
        inSubscription = json['inSubscription'] ?? false;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['subscribeName'] = subscribeName;
    map['countryCode'] = countryCode;
    map['subscriptionId'] = subscriptionId;
    map['productId'] = productId;
    map['skuId'] = skuId;
    map['skuType'] = skuType;
    map['payAmount'] = payAmount;
    map['originalCoins'] = originalCoins;
    map['coins'] = coins;
    map['bonus'] = bonus;
    map['duration'] = duration;
    map['type'] = type;
    map['rights'] = rights;
    map['rightsDesc'] = rightsDesc;
    map['firstAmount'] = firstAmount;
    map['subscript'] = subscript;
    map['disRate'] = disRate;
    map['isFirstBuy'] = isFirstBuy;
    map['showScene'] = showScene;
    map['card_style'] = cardStyle;
    map['inSubscription'] = inSubscription;
    map['inShowNewDiscount'] = inShowNewDiscount;
    return map;
  }
}

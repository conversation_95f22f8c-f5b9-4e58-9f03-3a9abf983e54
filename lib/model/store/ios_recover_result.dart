/// bonus : 450
/// coins : 1500
/// isRecharged : true
/// userId : 10001989

class IosRecoverResult {
  IosRecoverResult({
    this.bonus,
    this.coins,
    this.isRecharged,
    this.userId,
    this.coinsRecover,
    this.bonusRecover,
  });

  IosRecoverResult.fromJson(dynamic json) {
    bonus = json['bonus']??0;
    coins = json['coins']??0;
    isRecharged = json['isRecharged']??false;
    userId = json['userId']??0;
    coinsRecover = json['coinsRecover']??0;
    bonusRecover = json['bonusRecover']??0;
  }

  int? bonus;
  int? coins;
  bool? isRecharged;
  int? userId;
  int? coinsRecover;
  int? bonusRecover;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['bonus'] = bonus;
    map['coins'] = coins;
    map['isRecharged'] = isRecharged;
    map['userId'] = userId;
    map['coinsRecover'] = coinsRecover;
    map['bonusRecover'] = bonusRecover;
    return map;
  }
}

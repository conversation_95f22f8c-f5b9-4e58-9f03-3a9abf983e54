
class InAppPurchaseRecoveryData {
  final String skuId;
  final String purchaseData;
  final String signature;
  final int? skuType;
  final String receiptData;
  final String skuProductId;
  final String rawData;


  InAppPurchaseRecoveryData({
    required this.skuId,
    required this.purchaseData,
    required this.signature,
    required this.skuType,
    required this.receiptData,
    required this.skuProductId,
    required this.rawData,
  });

  // 将对象转换为 Map，用于存储到 SafeStorage 中
  Map<String, dynamic> toJson() {
    return {
      'skuId': skuId,
      'purchaseData': purchaseData,
      'signature': signature,
      'skuType': skuType,
      'receiptData': receiptData,
      'skuProductId': skuProductId,
      'rawData': rawData,
    };
  }

  // 从 Map 中创建对象
  factory InAppPurchaseRecoveryData.fromJson(Map<String, dynamic> json) {
    return InAppPurchaseRecoveryData(
      skuId: json['skuId'],
      purchaseData: json['purchaseData'],
      signature: json['signature'],
      skuType: json['skuType'],
      receiptData: json['receiptData'],
      skuProductId: json['skuProductId'],
      rawData: json['rawData'],
    );
  }
}
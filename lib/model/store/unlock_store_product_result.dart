import 'package:playlet/model/store/store_product_result.dart';


class UnLockStoreProductResult {
  UnLockStoreProductResult({
    this.isDisplayedTripartite,
    this.retainSkuInfoResponses,
    this.rewardSkuPositionType,
    this.skuInfoResponses,
    this.skuPositionType,
    this.subscribeSkuResponses,
    this.watchAdResultResponse,
  });

  UnLockStoreProductResult.fromJson(dynamic json) {
    isDisplayedTripartite = json['isDisplayedTripartite'];
    retainSkuInfoResponses = json['retainSkuInfoResponses'] != null
        ? SkuInfoResponses.fromJson(json['retainSkuInfoResponses'])
        : null;
    rewardSkuPositionType = json['rewardSkuPositionType'];
    if (json['skuInfoResponses'] != null) {
      skuInfoResponses = [];
      json['skuInfoResponses'].forEach((v) {
        skuInfoResponses?.add(SkuInfoResponses.fromJson(v));
      });
    }
    skuPositionType = json['skuPositionType'];
    if (json['subscribeSkuResponses'] != null) {
      subscribeSkuResponses = [];
      json['subscribeSkuResponses'].forEach((v) {
        subscribeSkuResponses?.add(SubscribeSkuResponses.fromJson(v));
      });
    }
    watchAdResultResponse = json['watchAdResultResponse'] != null
        ? WatchAdResultResponse.fromJson(json['watchAdResultResponse'])
        : null;
  }

  bool? isDisplayedTripartite;
  SkuInfoResponses? retainSkuInfoResponses;
  int? rewardSkuPositionType;
  List<SkuInfoResponses>? skuInfoResponses;
  int? skuPositionType;
  List<SubscribeSkuResponses>? subscribeSkuResponses;
  WatchAdResultResponse? watchAdResultResponse;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['isDisplayedTripartite'] = isDisplayedTripartite;
    if (retainSkuInfoResponses != null) {
      map['retainSkuInfoResponses'] = retainSkuInfoResponses?.toJson();
    }
    map['rewardSkuPositionType'] = rewardSkuPositionType;
    if (skuInfoResponses != null) {
      map['skuInfoResponses'] = skuInfoResponses?.map((v) => v.toJson()).toList();
    }
    map['skuPositionType'] = skuPositionType;
    if (subscribeSkuResponses != null) {
      map['subscribeSkuResponses'] =
          subscribeSkuResponses?.map((v) => v.toJson()).toList();
    }
    if (watchAdResultResponse != null) {
      map['watchAdResultResponse'] = watchAdResultResponse?.toJson();
    }
    return map;
  }
}


class WatchAdResultResponse {
  WatchAdResultResponse({
    this.canWatchAdNum = 0,
    this.totalWatchAdNum = 0,
  });

  WatchAdResultResponse.fromJson(dynamic json) {
    canWatchAdNum = json['canWatchAdNum']?? 0;
    totalWatchAdNum = json['totalWatchAdNum']?? 0;
  }

  int? canWatchAdNum;
  int? totalWatchAdNum;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['canWatchAdNum'] = canWatchAdNum;
    map['totalWatchAdNum'] = totalWatchAdNum;
    return map;
  }
}
/// bonusCurrent : 640
/// coinsCurrent : 600
/// payRecoverAndroidInfoResponses : [{"bonusRecover":600,"coinsRecover":600,"skuId":"tv.flareflow.sku.600"}]
/// userCode : "10001513"

class AndroidRecoverResult {
  AndroidRecoverResult({
      this.bonusCurrent, 
      this.coinsCurrent, 
      this.payRecoverAndroidInfoResponses, 
      this.userCode,});

  AndroidRecoverResult.fromJson(dynamic json) {
    bonusCurrent = json['bonusCurrent'];
    coinsCurrent = json['coinsCurrent'];
    if (json['payRecoverAndroidInfoResponses'] != null) {
      payRecoverAndroidInfoResponses = [];
      json['payRecoverAndroidInfoResponses'].forEach((v) {
        payRecoverAndroidInfoResponses?.add(PayRecoverAndroidInfoResponses.fromJson(v));
      });
    }
    userCode = json['userCode'];
  }
  int? bonusCurrent;
  int? coinsCurrent;
  List<PayRecoverAndroidInfoResponses>? payRecoverAndroidInfoResponses;
  String? userCode;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['bonusCurrent'] = bonusCurrent;
    map['coinsCurrent'] = coinsCurrent;
    if (payRecoverAndroidInfoResponses != null) {
      map['payRecoverAndroidInfoResponses'] = payRecoverAndroidInfoResponses?.map((v) => v.toJson()).toList();
    }
    map['userCode'] = userCode;
    return map;
  }

}

/// bonusRecover : 600
/// coinsRecover : 600
/// skuId : "tv.flareflow.sku.600"

class PayRecoverAndroidInfoResponses {
  PayRecoverAndroidInfoResponses({
      this.bonusRecover, 
      this.coinsRecover, 
      this.skuId,});

  PayRecoverAndroidInfoResponses.fromJson(dynamic json) {
    bonusRecover = json['bonusRecover'];
    coinsRecover = json['coinsRecover'];
    skuId = json['skuId'];
  }
  int? bonusRecover;
  int? coinsRecover;
  String? skuId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['bonusRecover'] = bonusRecover;
    map['coinsRecover'] = coinsRecover;
    map['skuId'] = skuId;
    return map;
  }

}
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';

class BannerMoreModel {
  int? bannerId;
  int? ruleCode;
  List<ShortPlayResponseList>? shortPlayResponseList;
  int? showNum;
  int? sort;
  int? style;
  String? title;
  bool? useMultiTypeContentList;

  BannerMoreModel(
      {this.bannerId,
        this.ruleCode,
        this.shortPlayResponseList,
        this.showNum,
        this.sort,
        this.style,
        this.title,
        this.useMultiTypeContentList});

  BannerMoreModel.fromJson(Map<String, dynamic> json) {
    bannerId = json['bannerId'];
    ruleCode = json['ruleCode'];
    if (json['shortPlayResponseList'] != null) {
      shortPlayResponseList = <ShortPlayResponseList>[];
      json['shortPlayResponseList'].forEach((v) {
        shortPlayResponseList!.add(new ShortPlayResponseList.fromJson(v));
      });
    }
    showNum = json['showNum'];
    sort = json['sort'];
    style = json['style'];
    title = json['title'];
    useMultiTypeContentList = json['useMultiTypeContentList'];
  }

  // Map<String, dynamic> toJson() {
  //   final Map<String, dynamic> data = new Map<String, dynamic>();
  //   data['bannerId'] = this.bannerId;
  //   data['ruleCode'] = this.ruleCode;
  //   if (this.shortPlayResponseList != null) {
  //     data['shortPlayResponseList'] =
  //         this.shortPlayResponseList!.map((v) => v.toJson()).toList();
  //   }
  //   data['showNum'] = this.showNum;
  //   data['sort'] = this.sort;
  //   data['style'] = this.style;
  //   data['title'] = this.title;
  //   data['useMultiTypeContentList'] = this.useMultiTypeContentList;
  //   return data;
  // }
}


import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:playlet/components/details/player/episode_player_view.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/model/store/unlock_store_product_result.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';

class Episode {
  EpisodeVideoData info;
  String videoUrl;
  GlobalKey<EpisodePlayerViewState> playerKey = GlobalKey();
  Duration playPosition = Duration.zero;
  Episode({
    required this.info,
    required this.videoUrl,
  });

  setPlayPosition(Duration position) {
    playPosition = position;
  }
}

class EpisodeTabInfo {
  String label;
  List<EpisodeTab> list;

  EpisodeTabInfo({required this.label, required this.list});
}

class EpisodeTab {
  int index;
  bool isDisable;
  EpisodeData info;
  EpisodeTab({
    required this.index,
    required this.info,
    this.isDisable = false,
  });
}

class DetailsOptions {
  int businessId;
  String scene;
  int playerEpisodeIndex;
  Duration playerEpisodePosition;
  int? videoDuration;
  String from;
  String moduleName;
  String moduleId;
  HomeModuleStyle? moduleStyle;
  String positionId;
  bool? openEpisodePanel;
  bool isSupportLandscapeMode = false;
  String? resourceBitId;

  DetailsOptions({
    required this.businessId,
    required this.scene,
    this.playerEpisodeIndex = 0,
    this.playerEpisodePosition = Duration.zero,
    this.videoDuration,
    this.from = '',
    this.moduleName = '',
    this.moduleId = '',
    this.moduleStyle,
    this.positionId = '',
    this.openEpisodePanel,
    this.isSupportLandscapeMode = false,
    this.resourceBitId,
  });

  @override
  String toString() {
    return "DetailsOptions businessId: $businessId, scene: $scene, playerEpisodeIndex: $playerEpisodeIndex, playerEpisodePosition: $playerEpisodePosition, from: $from, moduleName: $moduleName, moduleId: $moduleId, positionId: $positionId";
  }
}

class ShortPlayDetail {
  int? collectNum;
  int? id;
  List<LabelResponseList>? labelResponseList;
  int? lockBegin;
  String? picUrl;
  String? recommendContent;
  int? shortPlayCode;
  int? shortPlayId;
  int? shortPlayType;
  String? shortPlayName;
  String? summary;
  int? totalEpisodes;
  int? unlockStyle;
  int? unlockType;
  int? unlockTypeAb;
  int? updateEpisode;
  int? isCollect;

  ShortPlayDetail(
      {this.collectNum,
      this.id,
      this.labelResponseList,
      this.lockBegin,
      this.picUrl,
      this.isCollect,
      this.recommendContent,
      this.shortPlayCode,
      this.shortPlayType,
      this.shortPlayName,
      this.summary,
      this.totalEpisodes,
      this.unlockStyle,
      this.unlockType,
      this.unlockTypeAb,
      this.updateEpisode});

  ShortPlayDetail.fromJson(Map<String, dynamic> json) {
    collectNum = json['collectNum'];
    id = json['id'];
    if (json['labelResponseList'] != null) {
      labelResponseList = <LabelResponseList>[];
      json['labelResponseList'].forEach((v) {
        labelResponseList!.add(LabelResponseList.fromJson(v));
      });
    }
    lockBegin = json['lockBegin'];
    isCollect = json['isCollect'];
    picUrl = json['picUrl'];
    recommendContent = json['recommendContent'];
    shortPlayCode = json['shortPlayCode'];
    shortPlayType = json['shortPlayType'];
    shortPlayName = json['shortPlayName'];
    summary = json['summary'];
    totalEpisodes = json['totalEpisodes'];
    unlockStyle = json['unlockStyle'];
    unlockType = json['unlockType'];
    unlockTypeAb = json['unlockTypeAb'];
    updateEpisode = json['updateEpisode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['collectNum'] = collectNum;
    data['id'] = id;
    if (labelResponseList != null) {
      data['labelResponseList'] =
          labelResponseList!.map((v) => v.toJson()).toList();
    }
    data['lockBegin'] = lockBegin;
    data['isCollect'] = isCollect;
    data['picUrl'] = picUrl;
    data['recommendContent'] = recommendContent;
    data['shortPlayCode'] = shortPlayCode;
    data['shortPlayType'] = shortPlayType;
    data['shortPlayName'] = shortPlayName;
    data['summary'] = summary;
    data['totalEpisodes'] = totalEpisodes;
    data['unlockStyle'] = unlockStyle;
    data['unlockType'] = unlockType;
    data['unlockTypeAb'] = unlockTypeAb;
    data['updateEpisode'] = updateEpisode;
    return data;
  }
}

class LabelResponseList {
  int? id;
  String? labelName;

  LabelResponseList({this.id, this.labelName});

  LabelResponseList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    labelName = json['labelName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['labelName'] = labelName;
    return data;
  }
}

class EpisodeData {
  int? alreadyLock;
  int? episodeNum;
  int? id;
  int? lock;
  int? lockBegin;
  int? price;
  int? shortPlayCode;
  int? shortPlayId;
  int? unlockType;

  EpisodeData(  
      {this.alreadyLock,
      this.episodeNum,
      this.id,
      this.lock,
      this.lockBegin,
      this.price,
      this.shortPlayCode,
      this.shortPlayId,
      this.unlockType});

  EpisodeData.fromJson(Map<String, dynamic> json) {
    alreadyLock = json['alreadyLock'];
    episodeNum = json['episodeNum'];
    id = json['id'];
    lock = json['lock'];
    lockBegin = json['lockBegin'];
    price = json['price'];
    shortPlayCode = json['shortPlayCode'];
    shortPlayId = json['shortPlayId'];
    unlockType = json['unlockType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['alreadyLock'] = alreadyLock;
    data['episodeNum'] = episodeNum;
    data['id'] = id;
    data['lock'] = lock;
    data['lockBegin'] = lockBegin;
    data['price'] = price;
    data['shortPlayCode'] = shortPlayCode;
    data['shortPlayId'] = shortPlayId;
    data['unlockType'] = unlockType;
    return data;
  }
}

class EpisodeVideoData {
  int? alreadyLock;
  double? aspectRatio;
  VideoInfoMap? codec;
  int? collectNum;
  String? coverId;
  String? encryptedVideoUrl;
  int? episodeNum;
  String? frameExtractionCover;
  int? id;
  int? isCollect;
  int? lock;
  bool? needDecrypt;
  int? price;
  String? recommendContent;
  int? shortPlayId;
  String? shortPlayName;
  String? summary;
  int? totalEpisodes;
  int? unlockStyle;
  int? unlockType;
  int? unlockTypeAb;
  int? videoDuration;
  VideoInfoMap? videoUrl;

  EpisodeVideoData(
      {this.alreadyLock,
      this.aspectRatio,
      this.codec,
      this.collectNum,
      this.coverId,
      this.encryptedVideoUrl,
      this.episodeNum,
      this.frameExtractionCover,
      this.id,
      this.isCollect,
      this.lock,
      this.needDecrypt,
      this.price,
      this.recommendContent,
      this.shortPlayId,
      this.shortPlayName,
      this.summary,
      this.totalEpisodes,
      this.unlockStyle,
      this.unlockType,
      this.unlockTypeAb,
      this.videoDuration,
      this.videoUrl});

  EpisodeVideoData.fromJson(Map<String, dynamic> json) {
    alreadyLock = json['alreadyLock'];
    aspectRatio = json['aspectRatio'];
    if (json['codec'] != null) {
      codec = VideoInfoMap.fromJson(jsonDecode(json['codec']));
    }
    collectNum = json['collectNum'];
    coverId = json['coverId'];
    encryptedVideoUrl = json['encryptedVideoUrl'];
    episodeNum = json['episodeNum'];
    frameExtractionCover = json['frameExtractionCover'];
    id = json['id'];
    isCollect = json['isCollect'];
    lock = json['lock'];
    needDecrypt = json['needDecrypt'];
    price = json['price'];
    recommendContent = json['recommendContent'];
    shortPlayId = json['shortPlayId'];
    shortPlayName = json['shortPlayName'];
    summary = json['summary'];
    totalEpisodes = json['totalEpisodes'];
    unlockStyle = json['unlockStyle'];
    unlockType = json['unlockType'];
    unlockTypeAb = json['unlockTypeAb'];
    videoDuration = json['videoDuration'];
    if (json['videoUrl'] != null) {
      videoUrl = VideoInfoMap.fromJson(jsonDecode(json['videoUrl']));
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['alreadyLock'] = alreadyLock;
    data['aspectRatio'] = aspectRatio;
    data['codec'] = codec;
    data['collectNum'] = collectNum;
    data['coverId'] = coverId;
    data['encryptedVideoUrl'] = encryptedVideoUrl;
    data['episodeNum'] = episodeNum;
    data['frameExtractionCover'] = frameExtractionCover;
    data['id'] = id;
    data['isCollect'] = isCollect;
    data['lock'] = lock;
    data['needDecrypt'] = needDecrypt;
    data['price'] = price;
    data['recommendContent'] = recommendContent;
    data['shortPlayId'] = shortPlayId;
    data['shortPlayName'] = shortPlayName;
    data['summary'] = summary;
    data['totalEpisodes'] = totalEpisodes;
    data['unlockStyle'] = unlockStyle;
    data['unlockType'] = unlockType;
    data['unlockTypeAb'] = unlockTypeAb;
    data['videoDuration'] = videoDuration;
    if (videoUrl != null) {
      data['videoUrl'] = videoUrl!.toJson();
    }
    return data;
  }
}

class UnlockResult {
  int? coins;
  int? useBonus;
  int? bonus;
  int? useCoins;
  List<EpisodeVideoData>? unlockDramas;
  List<Costs>? costs;
  bool? autoUnlock;
  bool? autoUnlockEpisode;
  int? totalWatchAdNum;

  UnlockResult({
    this.coins,
    this.useBonus,
    this.bonus,
    this.useCoins,
    this.unlockDramas,
    this.costs,
    this.autoUnlock,
    this.autoUnlockEpisode,
    this.totalWatchAdNum,
  });

  UnlockResult.fromJson(Map<String, dynamic> json) {
    coins = json['coins'];
    useBonus = json['useBonus'];
    bonus = json['bonus'];
    useCoins = json['useCoins'];
    if (json['unlockDramas'] != null) {
      unlockDramas = <EpisodeVideoData>[];
      json['unlockDramas'].forEach((v) {
        if (v != null) {
          unlockDramas!.add(EpisodeVideoData.fromJson(v));
        }
      });
    }
    if (json['costs'] != null) {
      costs = <Costs>[];
      json['costs'].forEach((v) {
        costs!.add(Costs.fromJson(v));
      });
    }
    autoUnlock = json['autoUnlock'];
    autoUnlockEpisode = json['autoUnlockEpisode'];
    totalWatchAdNum = json['totalWatchAdNum'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['coins'] = coins;
    data['useBonus'] = useBonus;
    data['bonus'] = bonus;
    data['useCoins'] = useCoins;
    if (unlockDramas != null) {
      data['unlockDramas'] = unlockDramas!.map((v) => v.toJson()).toList();
    }
    if (costs != null) {
      data['costs'] = costs!.map((v) => v.toJson()).toList();
    }
    data['autoUnlock'] = autoUnlock;
    data['autoUnlockEpisode'] = autoUnlockEpisode;
    data['totalWatchAdNum'] = totalWatchAdNum;
    return data;
  }
}

class Costs {
  int? dramaId;
  int? useBonus;
  int? useCoins;
  int? bonus;
  int? coins;

  Costs({this.dramaId, this.useBonus, this.useCoins, this.bonus, this.coins});

  Costs.fromJson(Map<String, dynamic> json) {
    dramaId = json['dramaId'];
    useBonus = json['useBonus'];
    useCoins = json['useCoins'];
    bonus = json['bonus'];
    coins = json['coins'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['dramaId'] = dramaId;
    data['useBonus'] = useBonus;
    data['useCoins'] = useCoins;
    data['bonus'] = bonus;
    data['coins'] = coins;
    return data;
  }
}

class UnlockAdCountData {
  UnlockAdCountData({
    this.canWatchAdNum,
    this.totalWatchAdNum,
  });

  int? canWatchAdNum;
  int? totalWatchAdNum;

  UnlockAdCountData.fromJson(Map<String, dynamic> json) {
    canWatchAdNum = json['canWatchAdNum'];
    totalWatchAdNum = json['totalWatchAdNum'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['canWatchAdNum'] = canWatchAdNum;
    data['totalWatchAdNum'] = totalWatchAdNum;
    return data;
  }
}

class UnlockWatchAdInfoResponse {
    //广告看完返回的下一集的信息
  EpisodeVideoData? nextDrama; 
  int? canWatchAdNum;		
  int? totalWatchAdNum;		
  bool? autoUnlock;		
  bool? autoUnlockEpisode;	
  List<EpisodeVideoData>? unlockDramas;

  UnlockWatchAdInfoResponse({
    this.autoUnlock,
    this.autoUnlockEpisode,
    this.canWatchAdNum,
    this.nextDrama,
    this.totalWatchAdNum,
    this.unlockDramas,
  });

  factory UnlockWatchAdInfoResponse.fromJson(Map<String, dynamic> json) => UnlockWatchAdInfoResponse(
    autoUnlock: json['autoUnlock'],
    autoUnlockEpisode: json['autoUnlockEpisode'],
    canWatchAdNum: json['canWatchAdNum'],
    nextDrama: (json['unlockDramas'] as List?)?.isNotEmpty == true 
      ? EpisodeVideoData.fromJson((json['unlockDramas'] as List).first) 
      : null,
    totalWatchAdNum: json['totalWatchAdNum'],
    unlockDramas: (json['unlockDramas'] as List).map((e) => EpisodeVideoData.fromJson(e)).toList(),
  );

  Map<String, dynamic> toJson() => {
    'autoUnlock': autoUnlock,
    'autoUnlockEpisode': autoUnlockEpisode,
    'canWatchAdNum': canWatchAdNum,
    'nextDrama': nextDrama?.toJson(),
    'totalWatchAdNum': totalWatchAdNum,
    'unlockDramas': unlockDramas?.map((e) => e.toJson()).toList(),
  };
}

class CollectionRequestDataModel {
  CollectionRequestDataModel({
    this.pageSize,
    this.lastTime,
    this.colletType,
    this.collectSource,
  });

  /// 每页显示数量
  int? pageSize;

  /// 每页最后一条数据的时间戳
  int? lastTime;

  /// 1 收藏 2点赞
  int? colletType;

  /// 1 短剧 2剧集 3花絮 4高能
  List<int>? collectSource;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['pageSize'] = pageSize;
    data['lastTime'] = lastTime;
    data['colletType'] = colletType;
    data['collectSource'] = collectSource;
    return data;
  }
}

class CollectionDataModel {
  CollectionDataModel({
    this.collectTime,
    this.coverId,
    this.id,
    this.isWholeBuy,
    this.shortPlayCode,
    this.shortPlayId,
    this.shortPlayName,
    this.summary,
    this.totalEpisodes,
    this.classNum,
    this.episodeNum,
    this.isReservation,
  });

  /// 收藏时间
  int? collectTime;

  /// 封面url
  String? coverId;

  /// 主键id
  int? id;

  /// 是否已全集购买
  bool? isWholeBuy;

  /// 短剧Code
  int? shortPlayCode;

  /// 短剧id
  int? shortPlayId;

  /// 短剧名称
  String? shortPlayName;

  /// 简介
  String? summary;

  /// 总集数
  int? totalEpisodes;

  /// 分类号
  String? classNum;

  /// 第几集
  int? episodeNum;

  /// 收藏是否来源于预约 0 or null:否; 1:是
  int? isReservation;

  ///短剧内容分级信息
  // shortPlayContentRatingResponse

  CollectionDataModel.fromJson(Map<String, dynamic> json) {
    collectTime = json['collectTime'];
    coverId = json['coverId'];
    id = json['id'];
    isWholeBuy = json['isWholeBuy'];
    shortPlayCode = json['shortPlayCode'];
    shortPlayId = json['shortPlayId'];
    shortPlayName = json['shortPlayName'];
    summary = json['summary'];
    totalEpisodes = json['totalEpisodes'];
    classNum = json['classNum'];
    episodeNum = json['episodeNum'];
    isReservation = json['isReservation'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['collectTime'] = collectTime;
    data['coverId'] = coverId;
    data['id'] = id;
    data['isWholeBuy'] = isWholeBuy;
    data['shortPlayCode'] = shortPlayCode;
    data['shortPlayId'] = shortPlayId;
    data['shortPlayName'] = shortPlayName;
    data['summary'] = summary;
    data['totalEpisodes'] = totalEpisodes;
    data['classNum'] = classNum;
    data['episodeNum'] = episodeNum;
    data['isReservation'] = isReservation;
    return data;
  }

  CollectionDataModel copyWith({
    int? collectTime,
    String? coverId,
    int? id,
    bool? isWholeBuy,
    int? shortPlayCode,
    int? shortPlayId,
    String? shortPlayName,
    String? summary,
    int? totalEpisodes,
    String? classNum,
    int? episodeNum,
    int? isReservation,
  }) {
    return CollectionDataModel(
      collectTime: collectTime ?? this.collectTime,
      coverId: coverId ?? this.coverId,
      id: id ?? this.id,
      isWholeBuy: isWholeBuy ?? this.isWholeBuy,
      shortPlayCode: shortPlayCode ?? this.shortPlayCode,
      shortPlayId: shortPlayId ?? this.shortPlayId,
      shortPlayName: shortPlayName ?? this.shortPlayName,
      summary: summary ?? this.summary,
      totalEpisodes: totalEpisodes ?? this.totalEpisodes,
      classNum: classNum ?? this.classNum,
      episodeNum: episodeNum ?? this.episodeNum,
      isReservation: isReservation ?? this.isReservation,
    );
  }

  @override
  int get hashCode {
    final List<Object?> values = <Object?>[
      collectTime,
      coverId,
      id,
      isWholeBuy,
      shortPlayCode,
      shortPlayId,
      shortPlayName,
      summary,
      totalEpisodes,
      classNum,
      episodeNum,
      isReservation,
    ];
    return Object.hashAll(values);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    if (other.runtimeType != runtimeType) {
      return false;
    }
    return other is CollectionDataModel &&
        other.collectTime == collectTime &&
        other.coverId == coverId &&
        other.id == id &&
        other.isWholeBuy == isWholeBuy &&
        other.shortPlayCode == shortPlayCode &&
        other.shortPlayId == shortPlayId &&
        other.shortPlayName == shortPlayName &&
        other.summary == summary &&
        other.totalEpisodes == totalEpisodes &&
        other.classNum == classNum &&
        other.episodeNum == episodeNum &&
        other.isReservation == isReservation;
  }
}

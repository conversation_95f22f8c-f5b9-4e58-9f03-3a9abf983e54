class HistoryRequestDataModel {
  HistoryRequestDataModel({
    this.pageSize,
    this.lastTime,
  });

  /// 每页显示数量
  int? pageSize;

  /// 每页最后一条数据的时间戳
  int? lastTime;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['pageSize'] = pageSize;
    data['lastTime'] = lastTime;
    return data;
  }
}

class HistoryDataModel {
  HistoryDataModel({
    this.shortPlayCode,
    this.id,
    this.shortPlayName,
    this.watchTime,
    this.episodeNum,
    this.coverId,
    this.lastWatchTime,
    this.totalEpisodes,
    this.videoDuration,
    this.shortPlayId,
    this.dramaId,
    this.collectStatus,
    this.labelResponseList,
  });

  /// 短剧Code
  int? shortPlayCode;

  ///短剧内容分级信息
  // shortPlayContentRatingResponse

  /// 主键id
  int? id;

  /// 短剧名称
  String? shortPlayName;

  /// 观看的时间数(比如观看到第几秒)
  int? watchTime;

  /// 第几集
  int? episodeNum;

  /// 封面url
  String? coverId;

  /// 最后一次观看时间
  int? lastWatchTime;

  /// 总集数
  int? totalEpisodes;

  /// 视频时长,单位s
  int? videoDuration;

  /// 短剧id
  int? shortPlayId;

  /// 剧集ID
  int? dramaId;

  /// 收藏状态 0 未收藏 1 收藏
  int? collectStatus;

  /// 标签 key:labelResponseList
  List<HistoryLabelData>? labelResponseList;

  /// 类目
  /// classList
  HistoryDataModel.fromJson(Map<String, dynamic> json) {
    shortPlayCode = json['shortPlayCode'];
    id = json['id'];
    shortPlayName = json['shortPlayName'];
    watchTime = json['watchTime'];
    episodeNum = json['episodeNum'];
    coverId = json['coverId'];
    lastWatchTime = json['lastWatchTime'];
    totalEpisodes = json['totalEpisodes'];
    videoDuration = json['videoDuration'];
    shortPlayId = json['shortPlayId'];
    dramaId = json['dramaId'];
    collectStatus = json['collectStatus'];
    labelResponseList = json['labelResponseList'] != null &&
            json['labelResponseList'] is List<dynamic>
        ? (json['labelResponseList']! as List<dynamic>)
            .map((e) => HistoryLabelData.fromJson(e))
            .toList()
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['shortPlayCode'] = shortPlayCode;
    data['id'] = id;
    data['shortPlayName'] = shortPlayName;
    data['watchTime'] = watchTime;
    data['episodeNum'] = episodeNum;
    data['coverId'] = coverId;
    data['lastWatchTime'] = lastWatchTime;
    data['totalEpisodes'] = totalEpisodes;
    data['videoDuration'] = videoDuration;
    data['shortPlayId'] = shortPlayId;
    data['collectStatus'] = collectStatus;
    data['labelResponseList'] =
        labelResponseList?.map((e) => e.toJson()).toList();
    return data;
  }

  HistoryDataModel copyWith({
    int? shortPlayCode,
    int? id,
    String? shortPlayName,
    int? watchTime,
    int? episodeNum,
    String? coverId,
    int? lastWatchTime,
    int? totalEpisodes,
    int? videoDuration,
    int? shortPlayId,
    int? dramaId,
    int? collectStatus,
    List<HistoryLabelData>? labelResponseList,
  }) {
    return HistoryDataModel(
      shortPlayCode: shortPlayCode ?? this.shortPlayCode,
      id: id ?? this.id,
      shortPlayName: shortPlayName ?? this.shortPlayName,
      watchTime: watchTime ?? this.watchTime,
      episodeNum: episodeNum ?? this.episodeNum,
      coverId: coverId ?? this.coverId,
      lastWatchTime: lastWatchTime ?? this.lastWatchTime,
      totalEpisodes: totalEpisodes ?? this.totalEpisodes,
      videoDuration: videoDuration ?? this.videoDuration,
      shortPlayId: shortPlayId ?? this.shortPlayId,
      dramaId: dramaId ?? this.dramaId,
      collectStatus: collectStatus ?? this.collectStatus,
      labelResponseList: labelResponseList ?? this.labelResponseList,
    );
  }

  @override
  int get hashCode {
    final List<Object?> values = <Object?>[
      shortPlayCode,
      id,
      shortPlayName,
      watchTime,
      episodeNum,
      coverId,
      lastWatchTime,
      totalEpisodes,
      videoDuration,
      shortPlayId,
      dramaId,
      collectStatus,
      labelResponseList,
    ];
    return Object.hashAll(values);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    if (other.runtimeType != runtimeType) {
      return false;
    }
    return other is HistoryDataModel &&
        other.shortPlayCode == shortPlayCode &&
        other.id == id &&
        other.shortPlayName == shortPlayName &&
        other.watchTime == watchTime &&
        other.episodeNum == episodeNum &&
        other.coverId == coverId &&
        other.lastWatchTime == lastWatchTime &&
        other.totalEpisodes == totalEpisodes &&
        other.videoDuration == videoDuration &&
        other.shortPlayId == shortPlayId &&
        other.dramaId == dramaId &&
        other.labelResponseList == labelResponseList &&
        other.collectStatus == collectStatus;
  }
}

class HistoryLabelData {
  HistoryLabelData({
    this.id,
    this.labelName,
  });

  /// 主键id
  int? id;

  /// 标签名字
  String? labelName;

  HistoryLabelData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    labelName = json['labelName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['labelName'] = labelName;
    return data;
  }
}

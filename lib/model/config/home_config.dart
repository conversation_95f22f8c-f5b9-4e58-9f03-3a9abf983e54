class HomeConfigResult {
  String? facebookLoginType;
  String? ggLoginBonus;
  bool? haveFacebookAccount;
  String? metaLoginBonus;
  String? notificationsBonus;
  String? userAccountMergeBonus;

  HomeConfigResult(
      {this.facebookLoginType,
      this.ggLoginBonus,
      this.haveFacebookAccount,
      this.metaLoginBonus,
      this.notificationsBonus,
      this.userAccountMergeBonus});

  HomeConfigResult.fromJson(Map<String, dynamic> json) {
    facebookLoginType = json['facebookLoginType'];
    ggLoginBonus = json['ggLoginBonus'];
    haveFacebookAccount = json['haveFacebookAccount'];
    metaLoginBonus = json['metaLoginBonus'];
    notificationsBonus = json['notificationsBonus'];
    userAccountMergeBonus = json['userAccountMergeBonus'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['facebookLoginType'] = facebookLoginType;
    data['ggLoginBonus'] = ggLoginBonus;
    data['haveFacebookAccount'] = haveFacebookAccount;
    data['metaLoginBonus'] = metaLoginBonus;
    data['notificationsBonus'] = notificationsBonus;
    data['userAccountMergeBonus'] = userAccountMergeBonus;
    return data;
  }
}
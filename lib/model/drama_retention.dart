import 'dart:convert';

import 'reels.dart';

class DramaRetentionItem {
  String? bitrate;
  String? codec;
  int? collectNum;
  String? coverId;
  int? episodeNum;
  int? id;
  int? isCollect;
  bool? needDecrypt;
  int? playNum;
  String? recommendContent;
  int? shortPlayCode;
  int? shortPlayId;
  String? shortPlayName;
  int? shortPlayType;
  String? summary;
  int? totalEpisodes;
  int? videoDuration;
  int? videoType;
  VideoInfoMap? videoUrl;
  double? aspectRatio;
  int? watchTime;

  DramaRetentionItem(
      {this.bitrate,
      this.codec,
      this.collectNum,
      this.coverId,
      this.episodeNum,
      this.id,
      this.isCollect,
      this.needDecrypt,
      this.playNum,
      this.recommendContent,
      this.shortPlayCode,
      this.shortPlayId,
      this.shortPlayName,
      this.shortPlayType,
      this.summary,
      this.totalEpisodes,
      this.videoDuration,
      this.videoType,
      this.videoUrl,
      this.aspectRatio,
      this.watchTime});

  DramaRetentionItem.fromJson(Map<String, dynamic> json) {
    bitrate = json['bitrate'];
    codec = json['codec'];
    collectNum = json['collectNum'];
    coverId = json['coverId'];
    episodeNum = json['episodeNum'];
    id = json['id'];
    isCollect = json['isCollect'];
    needDecrypt = json['needDecrypt'];
    playNum = json['playNum'];
    recommendContent = json['recommendContent'];
    shortPlayCode = json['shortPlayCode'];
    shortPlayId = json['shortPlayId'];
    shortPlayName = json['shortPlayName'];
    shortPlayType = json['shortPlayType'];
    summary = json['summary'];
    totalEpisodes = json['totalEpisodes'];
    videoDuration = json['videoDuration'];
    videoType = json['videoType'];
    aspectRatio = json['aspectRatio'];
    watchTime = json['watchTime'];
    if (json['videoUrl'] != null) {
      videoUrl = VideoInfoMap.fromJson(jsonDecode(json['videoUrl']));
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['bitrate'] = bitrate;
    data['codec'] = codec;
    data['collectNum'] = collectNum;
    data['coverId'] = coverId;
    data['episodeNum'] = episodeNum;
    data['id'] = id;
    data['isCollect'] = isCollect;
    data['needDecrypt'] = needDecrypt;
    data['playNum'] = playNum;
    data['recommendContent'] = recommendContent;
    data['shortPlayCode'] = shortPlayCode;
    data['shortPlayId'] = shortPlayId;
    data['shortPlayName'] = shortPlayName;
    data['shortPlayType'] = shortPlayType;
    data['summary'] = summary;
    data['totalEpisodes'] = totalEpisodes;
    data['videoDuration'] = videoDuration;
    data['videoType'] = videoType;
    data['aspectRatio'] = aspectRatio;
    data['watchTime'] = watchTime;
    if (videoUrl != null) {
      data['videoUrl'] = videoUrl?.toJson();
    }
    return data;
  }
}

class Result {
  int code = 0;
  String? msg;
  dynamic data;
  TokenModel? token;

  Result({required this.code, this.msg, this.data, this.token});

  Result.fromJson(Map<String, dynamic> json) {
    code = int.parse(json['code']);
    msg = json['msg'];
    data = json['data'];
    token = json['token'] != null ? TokenModel.fromJson(json['token']) : null;
  }

  bool get isSuccess => code == 0;

  @override
  String toString() {
    return 'Result{code: $code, message: $msg, data: $data}';
  }
}

class TokenModel {
  String? accessToken;
  int? tokenExpsDays;
  String? refreshToken;
  int? refreshTokenExpsDays;

  TokenModel(
      {this.accessToken,
      this.tokenExpsDays,
      this.refreshToken,
      this.refreshTokenExpsDays});

  TokenModel.fromJson(Map<String, dynamic> json) {
    accessToken = json['accessToken'];
    tokenExpsDays = json['tokenExpsDays'];
    refreshToken = json['refreshToken'];
    refreshTokenExpsDays = json['refreshTokenExpsDays'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['accessToken'] = accessToken;
    data['tokenExpsDays'] = tokenExpsDays;
    data['refreshToken'] = refreshToken;
    data['refreshTokenExpsDays'] = refreshTokenExpsDays;
    return data;
  }
}

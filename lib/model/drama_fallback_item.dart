import 'dart:convert';

/// 兜底剧数据模型
/// 用于解析 getPopularShortPlay 接口返回的数据
class DramaFallbackItem {
  /// 短剧ID
  final int? id;
  
  /// 是否需要解密
  final bool? needDecrypt;
  
  /// 封面图片URL
  final String? picUrl;
  
  /// 短剧编码
  final int? shortPlayCode;
  
  /// 短剧名称
  final String? shortPlayName;

  DramaFallbackItem({
    this.id,
    this.needDecrypt,
    this.picUrl,
    this.shortPlayCode,
    this.shortPlayName,
  });

  /// 从JSON创建对象
  factory DramaFallbackItem.fromJson(Map<String, dynamic> json) {
    return DramaFallbackItem(
      id: json['id'] is String ? int.tryParse(json['id']) : json['id'],
      needDecrypt: json['needDecrypt'],
      picUrl: json['picUrl'],
      shortPlayCode: json['shortPlayCode'] is String 
          ? int.tryParse(json['shortPlayCode']) 
          : json['shortPlayCode'],
      shortPlayName: json['shortPlayName'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'needDecrypt': needDecrypt,
      'picUrl': picUrl,
      'shortPlayCode': shortPlayCode,
      'shortPlayName': shortPlayName,
    };
  }

  @override
  String toString() {
    return 'DramaFallbackItem{id: $id, needDecrypt: $needDecrypt, picUrl: $picUrl, shortPlayCode: $shortPlayCode, shortPlayName: $shortPlayName}';
  }
}

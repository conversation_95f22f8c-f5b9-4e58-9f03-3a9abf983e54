/// clickDoubleAdToday : false
/// nextSignTime : 0
/// signRecords : [{"bonus":5,"day":"1","isSign":false},{"bonus":15,"day":"2","isSign":false},{"bonus":20,"day":"3","isSign":false},{"bonus":35,"day":"4","isSign":false},{"bonus":50,"day":"5","isSign":false},{"bonus":75,"day":"6","isSign":false},{"bonus":90,"day":"7","isSign":false}]

class CheckInListResult {
  CheckInListResult({
    this.clickDoubleAdToday,
    this.nextSignTime,
    this.todayEndSignTime,
    this.signRecords,});

  CheckInListResult.fromJson(dynamic json) {
    clickDoubleAdToday = json['clickDoubleAdToday'];
    nextSignTime = json['nextSignTime'];
    todayEndSignTime = json['todayEndSignTime'];
    if (json['signRecords'] != null) {
      signRecords = [];
      json['signRecords'].forEach((v) {
        signRecords?.add(SignRecords.fromJson(v));
      });
    }
  }
  bool? clickDoubleAdToday;
  int? nextSignTime;
  int? todayEndSignTime;
  List<SignRecords>? signRecords;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['clickDoubleAdToday'] = clickDoubleAdToday;
    map['nextSignTime'] = nextSignTime;
    map['todayEndSignTime'] = todayEndSignTime;
    if (signRecords != null) {
      map['signRecords'] = signRecords?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// bonus : 5
/// day : "1"
/// isSign : true
/// isToday : false

class SignRecords {
  SignRecords({
    this.bonus,
    this.day,
    this.isSign,
    this.isToday,});

  SignRecords.fromJson(dynamic json) {
    bonus = json['bonus'];
    day = json['day'];
    isSign = json['isSign'];
    isToday = json['isToday'];
  }
  int? bonus;
  String? day;
  bool? isSign;
  bool? isToday;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['bonus'] = bonus;
    map['day'] = day;
    map['isSign'] = isSign;
    map['isToday'] = isToday;
    return map;
  }

}
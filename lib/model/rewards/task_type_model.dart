class TaskType {
  static const int fbBinding = 1;
  static const int emailBinding = 2;
  static const int phoneBinding = 3;
  static const int getRewardNotification = 4;
}

class TaskStatus {
  // 表示任务不可用的状态
  static const int noUnLock = 0;
  // 表示任务处于待完成的状态，即任务已经可以开始执行，但尚未完成
  static const int start = 1;
  // 表示任务已经完成，但奖励还未被领取，处于等待用户领取奖励的阶段
  static const int receive = 2;
  // 表示任务奖励已经被成功领取，整个任务流程结束
  static const int complete = 3;
}

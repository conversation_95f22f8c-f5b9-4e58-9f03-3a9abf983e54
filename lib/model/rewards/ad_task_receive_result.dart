/// bounsNum : 25
/// watchAdEndBonus : 0

class AdTaskReceiveResult {
  AdTaskReceiveResult({
      this.bounsNum, 
      this.watchAdEndBonus,});

  AdTaskReceiveResult.fromJson(dynamic json) {
    bounsNum = json['bounsNum'];
    watchAdEndBonus = json['watchAdEndBonus'];
  }
  int? bounsNum;
  int? watchAdEndBonus;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['bounsNum'] = bounsNum;
    map['watchAdEndBonus'] = watchAdEndBonus;
    return map;
  }

}
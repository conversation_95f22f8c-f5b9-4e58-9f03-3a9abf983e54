/// nextSignTime : 1800
/// bonus : 100

class CheckInResult {
  CheckInResult({
    this.nextSignTime,
    this.bonus,
  });

  CheckInResult.fromJson(dynamic json) {
    nextSignTime = json['nextSignTime'];
    bonus = json['bonus'];
  }
  int? nextSignTime;
  int? bonus;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['nextSignTime'] = nextSignTime;
    map['bonus'] = bonus;
    return map;
  }
}

// 签到看广告返回的结果
class SignWatchADResultModel {
  int? bonus;
  SignWatchADResultModel({required this.bonus});

  SignWatchADResultModel.fromJson(dynamic json) {
    bonus = json['bonus'];
  }

}
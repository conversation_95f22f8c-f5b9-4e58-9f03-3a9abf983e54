/// allCompleted : false
/// balanceBonus : 150
/// balanceCoins : 0
/// taskBonus : 100

class UserTaskReceiveResult {
  UserTaskReceiveResult({
      this.allCompleted, 
      this.balanceBonus, 
      this.balanceCoins, 
      this.taskBonus,});

  UserTaskReceiveResult.fromJson(dynamic json) {
    allCompleted = json['allCompleted'];
    balanceBonus = json['balanceBonus'];
    balanceCoins = json['balanceCoins'];
    taskBonus = json['taskBonus'];
  }
  bool? allCompleted;
  int? balanceBonus;
  int? balanceCoins;
  int? taskBonus;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['allCompleted'] = allCompleted;
    map['balanceBonus'] = balanceBonus;
    map['balanceCoins'] = balanceCoins;
    map['taskBonus'] = taskBonus;
    return map;
  }

}
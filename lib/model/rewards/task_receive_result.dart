/// isAllCompleted : false
/// balanceCoins : 1200
/// balanceBonus : 50
/// taskBonus : 30

class TaskReceiveResult {
  TaskReceiveResult({
      this.isAllCompleted, 
      this.balanceCoins, 
      this.balanceBonus, 
      this.taskBonus,});

  TaskReceiveResult.fromJson(dynamic json) {
    isAllCompleted = json['isAllCompleted'];
    balanceCoins = json['balanceCoins'];
    balanceBonus = json['balanceBonus'];
    taskBonus = json['taskBonus'];
  }
  bool? isAllCompleted;
  int? balanceCoins;
  int? balanceBonus;
  int? taskBonus;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['isAllCompleted'] = isAllCompleted;
    map['balanceCoins'] = balanceCoins;
    map['balanceBonus'] = balanceBonus;
    map['taskBonus'] = taskBonus;
    return map;
  }

}
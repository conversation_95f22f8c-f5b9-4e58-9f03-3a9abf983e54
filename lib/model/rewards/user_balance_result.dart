/// userId : 123456
/// coins : 500
/// bonus : 200
/// balance : 700

class UserBalanceResult {
  UserBalanceResult({
    this.userId = 0,
    this.coins = 0,
    this.bonus = 0,
    this.balance = 0,
  });

  UserBalanceResult.fromJson(dynamic json) {
    userId = json['userId'];
    coins = json['coins'];
    bonus = json['bonus'];
    balance = json['balance'];
  }

  late int userId;
  late int coins;
  late int bonus;
  late int balance;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['userId'] = userId;
    map['coins'] = coins;
    map['bonus'] = bonus;
    map['balance'] = balance;
    return map;
  }
}

/// adBonusResponses : [{"bonus":5,"id":1,"watchNextTime":100,"watched":false},{"bonus":6,"id":2,"watchNextTime":100,"watched":false},{"bonus":10,"id":3,"watchNextTime":100,"watched":false},{"bonus":5,"id":4,"watchNextTime":100,"watched":false},{"bonus":10,"id":5,"watchNextTime":100,"watched":false},{"bonus":20,"id":6,"watchNextTime":100,"watched":false}]
/// watchAdEndBonus : 25

class TaskAdListResult {
  TaskAdListResult({
      this.adBonusResponses, 
      this.watchAdEndBonus,});

  TaskAdListResult.fromJson(dynamic json) {
    if (json['adBonusResponses'] != null) {
      adBonusResponses = [];
      json['adBonusResponses'].forEach((v) {
        adBonusResponses?.add(AdBonusResponses.fromJson(v));
      });
    }
    watchAdEndBonus = json['watchAdEndBonus'];
  }
  List<AdBonusResponses>? adBonusResponses;
  int? watchAdEndBonus;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (adBonusResponses != null) {
      map['adBonusResponses'] = adBonusResponses?.map((v) => v.toJson()).toList();
    }
    map['watchAdEndBonus'] = watchAdEndBonus;
    return map;
  }

}

/// bonus : 5
/// id : 1
/// watchNextTime : 100
/// watched : false
/// 添加自定义属性 taskId。1。未开始 2 待领取 3 已领取

class AdBonusResponses {
  AdBonusResponses({
      this.bonus, 
      this.id, 
      this.watchNextTime, 
      this.watched,

  });

  AdBonusResponses.fromJson(dynamic json) {
    bonus = json['bonus'];
    id = json['id'];
    watchNextTime = json['watchNextTime'];
    watched = json['watched'];
  }
  int? bonus;
  int? id;
  int? watchNextTime;
  bool? watched;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['bonus'] = bonus;
    map['id'] = id;
    map['watchNextTime'] = watchNextTime;
    map['watched'] = watched;
    return map;
  }

}
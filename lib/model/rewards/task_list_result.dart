/// taskModuleResponseList : [{"id":1,"moduleTaskType":1,"remark":"新手任务相关说明","modelName":"新手任务模块","appTaskReponseList":[{"id":101,"taskType":1,"taskBonus":50,"taskName":"使用 Facebook 登录获取奖励","icon":"https://example.com/icon1.png","taskTimes":1,"completeTaskTimes":0,"button":"立即登录","remark":"通过 Facebook 登录可获得奖励","receiveRewardsNum":0,"isShow":1},{"id":102,"taskType":2,"taskBonus":30,"taskName":"填写邮箱获取奖励","icon":"https://example.com/icon2.png","taskTimes":1,"completeTaskTimes":0,"button":"填写邮箱","remark":"正确填写邮箱地址可获得奖励","receiveRewardsNum":0,"isShow":1}]},{"id":2,"moduleTaskType":2,"remark":"每日任务相关说明","modelName":"每日任务模块","appTaskReponseList":[{"id":201,"taskType":3,"taskBonus":20,"taskName":"填写手机号码","icon":"https://example.com/icon3.png","taskTimes":1,"completeTaskTimes":0,"button":"填写手机","remark":"填写有效的手机号码可获得奖励","receiveRewardsNum":0,"isShow":1}]}]

class TaskListResult {
  TaskListResult({
      this.taskModuleResponseList,});

  TaskListResult.fromJson(dynamic json) {
    if (json['taskModuleResponseList'] != null) {
      taskModuleResponseList = [];
      json['taskModuleResponseList'].forEach((v) {
        taskModuleResponseList?.add(TaskModuleResponseList.fromJson(v));
      });
    }
  }
  List<TaskModuleResponseList>? taskModuleResponseList;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (taskModuleResponseList != null) {
      map['taskModuleResponseList'] = taskModuleResponseList?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// id : 1
/// moduleTaskType : 1
/// remark : "新手任务相关说明"
/// modelName : "新手任务模块"
/// appTaskReponseList : [{"id":101,"taskType":1,"taskBonus":50,"taskName":"使用 Facebook 登录获取奖励","icon":"https://example.com/icon1.png","taskTimes":1,"completeTaskTimes":0,"button":"立即登录","remark":"通过 Facebook 登录可获得奖励","receiveRewardsNum":0,"isShow":1},{"id":102,"taskType":2,"taskBonus":30,"taskName":"填写邮箱获取奖励","icon":"https://example.com/icon2.png","taskTimes":1,"completeTaskTimes":0,"button":"填写邮箱","remark":"正确填写邮箱地址可获得奖励","receiveRewardsNum":0,"isShow":1}]

class TaskModuleResponseList {
  TaskModuleResponseList({
      this.id, 
      this.moduleTaskType, 
      this.remark, 
      this.modelName, 
      this.appTaskReponseList,});

  TaskModuleResponseList.fromJson(dynamic json) {
    id = json['id'];
    moduleTaskType = json['moduleTaskType'];
    remark = json['remark'];
    modelName = json['modelName'];
    if (json['appTaskReponseList'] != null) {
      appTaskReponseList = [];
      json['appTaskReponseList'].forEach((v) {
        appTaskReponseList?.add(AppTaskReponseList.fromJson(v));
      });
    }
  }
  int? id;
  int? moduleTaskType;
  String? remark;
  String? modelName;
  List<AppTaskReponseList>? appTaskReponseList;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['moduleTaskType'] = moduleTaskType;
    map['remark'] = remark;
    map['modelName'] = modelName;
    if (appTaskReponseList != null) {
      map['appTaskReponseList'] = appTaskReponseList?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// id : 101
/// taskType : 1
/// taskBonus : 50
/// taskName : "使用 Facebook 登录获取奖励"
/// icon : "https://example.com/icon1.png"
/// taskTimes : 1
/// completeTaskTimes : 0
/// button : "立即登录"
/// remark : "通过 Facebook 登录可获得奖励"
/// receiveRewardsNum : 0
/// isShow : 1

class AppTaskReponseList {
  AppTaskReponseList({
      this.id, 
      this.taskType, 
      this.taskBonus, 
      this.taskName, 
      this.icon, 
      this.taskTimes, 
      this.completeTaskTimes, 
      this.button, 
      this.remark, 
      this.receiveRewardsNum, 
      this.status,});

  AppTaskReponseList.fromJson(dynamic json) {
    id = json['id'];
    taskType = json['taskType'];
    taskBonus = json['taskBonus'];
    taskName = json['taskName'];
    icon = json['icon'];
    taskTimes = json['taskTimes'];
    completeTaskTimes = json['completeTaskTimes'];
    button = json['button'];
    remark = json['remark'];
    receiveRewardsNum = json['receiveRewardsNum'];
    status = json['status'];
  }
  int? id;
  int? taskType;
  int? taskBonus;
  String? taskName;
  String? icon;
  int? taskTimes;
  int? completeTaskTimes;
  String? button;
  String? remark;
  int? receiveRewardsNum;
  int? status;


  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['taskType'] = taskType;
    map['taskBonus'] = taskBonus;
    map['taskName'] = taskName;
    map['icon'] = icon;
    map['taskTimes'] = taskTimes;
    map['completeTaskTimes'] = completeTaskTimes;
    map['button'] = button;
    map['remark'] = remark;
    map['receiveRewardsNum'] = receiveRewardsNum;
    map['status'] = status;
    return map;
  }

}
class AppUpgradeResult  {
  int? update;//是否更新和类别, 0-不更新，1-提示更新，2-强制更新
  String? updateRemark;//更新提示内容
  int? tipPeriod;//提醒周期(天)
  String? storeLink;//商店链接
  String? minVersion;//最小版本, 小于此版本的客户端要更新

  AppUpgradeResult(
      {this.update,
      this.updateRemark,
      this.tipPeriod,
      this.storeLink,
      this.minVersion});

  AppUpgradeResult.fromJson(Map<String, dynamic> json) {
    update = json['update'];
    updateRemark = json['updateRemark'];
    tipPeriod = json['tipPeriod'];
    storeLink = json['storeLink'];
    minVersion = json['minVersion'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['update'] = update;
    data['updateRemark'] = updateRemark;
    data['tipPeriod'] = tipPeriod;
    data['storeLink'] = storeLink;
    data['minVersion'] = minVersion;
    return data;
  }
}

class AppUpgradeType {
  /// 0:不更新(不弹框)
  static const int manual = 0;

  ///1:建议升级
  static const int suggest = 1;

  ///2: 强制升级
  static const int force = 2;
}
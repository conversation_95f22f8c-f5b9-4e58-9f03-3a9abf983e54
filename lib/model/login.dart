class InitLoginResult {
  String? token;
  String? notification;
  UserResponse? userResponse;
  int? giveBonus;

  InitLoginResult(
      {this.token, this.notification, this.userResponse, this.giveBonus});

  InitLoginResult.fromJson(Map<String, dynamic> json) {
    token = json['token'];
    notification = json['notification'];
    giveBonus = json['giveBonus'];
    userResponse = json['userResponse'] != null
        ? UserResponse.fromJson(json['userResponse'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['token'] = token;
    data['notification'] = notification;
    data['giveBonus'] = giveBonus;
    if (userResponse != null) {
      data['userResponse'] = userResponse!.toJson();
    }
    return data;
  }
}

class UserResponse {
  /// 用户编号
  String? userId;

  /// 昵称
  String? nickName;

  /// 头像
  String? headPic;

  /// 角色;1.游客2正式用户
  int? role;

  /// 第三方1谷歌2faceBook
  String? tripartiteCode;

  /// firebase下的授权类型 10代表google  20代表facebook登录 80代表apple登录
  int? firebaseSource;

  /// 金币
  int? coins;

  /// bonus
  int? bonus;

  /// 是否审核人员
  bool? isUserSpecial;

  /// 国家code
  String? countryCode;

  /// 是否充值过  true：充值过；false未充值
  bool? isRecharged;

  /// 自动解锁是否开启
  bool? autoUnlock;

  /// 自动解锁是否开启
  bool? autoUnlockEpisode;

  /// 该用户是否可以进行合并
  bool? canMerge;

  /// 是否订阅
  bool? isSubscription;

  /// 是否订阅黑名单
  bool? isSubscriptionBlack;

  /// 订阅类型：1.周卡，2.月卡，3.季卡，4.年卡
  int? subscriptionType;

  ///level=0是对照组，只能领取当天，level=1是实验组，可以领取之前所有未领取的天数的
  int? receiveType;

  /// 订阅结束时间
  int? subscriptionEndTime;

  /// 是否需要android客户端确认订阅
  bool? subscriptionConfirm;

  /// 是否要显示订阅入口
  bool? hasSubscription;

  /// userCodeUUID
  String? userCodeUUID;

  /// 订阅用户编号
  String? subscriptionUserCode;

  ///订阅用户三方账号类型
  ///提供者 10代表谷歌 20代表facebook 30代表twitter 40代表github 50代表email 60代表手机号 70代表whatsapp 80代表apple 90代表匿名登录
  int? subscriptionProvider;

  /// 订阅用户昵称
  String? subscriptionNickName;

  /// 订阅下发的bonus
  int? giveBonus;

  /// facebook登录方式：1-sdk；2-服务端api
  String? facebookLoginType;

  /// 是否拥有Facebook账号
  bool? haveFacebookAccount;

  /// campaign解锁类型（1 金币，2 广告，3 金币+广告）
  int? campaignUnlockType;

  /// 是否允许充值
  bool? allowRecharge;

  UserResponse(
      {this.userId,
      this.nickName,
      this.headPic,
      this.role,
      this.tripartiteCode,
      this.firebaseSource,
      this.coins,
      this.bonus,
      this.isUserSpecial,
      this.countryCode,
      this.isRecharged,
      this.autoUnlock,
      this.autoUnlockEpisode,
      this.canMerge,
      this.isSubscription,
      this.isSubscriptionBlack,
      this.subscriptionType,
      this.receiveType,
      this.subscriptionEndTime,
      this.subscriptionConfirm,
      this.hasSubscription,
      this.userCodeUUID,
      this.subscriptionUserCode,
      this.subscriptionProvider,
      this.subscriptionNickName,
      this.giveBonus,
      this.facebookLoginType,
      this.haveFacebookAccount,
      this.campaignUnlockType,
      this.allowRecharge});

  UserResponse.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    nickName = json['nickName'];
    headPic = json['headPic'];
    role = json['role'];
    tripartiteCode = json['tripartiteCode'];
    firebaseSource = json['firebaseSource'];
    coins = json['coins'];
    bonus = json['bonus'];
    isUserSpecial = json['isUserSpecial'];
    countryCode = json['countryCode'];
    isRecharged = json['isRecharged'];
    autoUnlock = json['autoUnlock'];
    autoUnlockEpisode = json['autoUnlockEpisode'];
    canMerge = json['canMerge'];
    isSubscription = json['isSubscription'];
    isSubscriptionBlack = json['isSubscriptionBlack'];
    subscriptionType = json['subscriptionType'];
    receiveType = json['receiveType'];
    subscriptionEndTime = json['subscriptionEndTime'];
    subscriptionConfirm = json['subscriptionConfirm'];
    hasSubscription = json['hasSubscription'];
    userCodeUUID = json['userCodeUUID'];
    subscriptionUserCode = json['subscriptionUserCode'];
    subscriptionProvider = json['subscriptionProvider'];
    subscriptionNickName = json['subscriptionNickName'];
    giveBonus = json['giveBonus'];
    facebookLoginType = json['facebookLoginType'];
    haveFacebookAccount = json['haveFacebookAccount'];
    campaignUnlockType = json['campaignUnlockType'];
    allowRecharge = json['allowRecharge'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userId'] = userId;
    data['nickName'] = nickName;
    data['headPic'] = headPic;
    data['role'] = role;
    data['tripartiteCode'] = tripartiteCode;
    data['firebaseSource'] = firebaseSource;
    data['coins'] = coins;
    data['bonus'] = bonus;
    data['isUserSpecial'] = isUserSpecial;
    data['countryCode'] = countryCode;
    data['isRecharged'] = isRecharged;
    data['autoUnlock'] = autoUnlock;
    data['autoUnlockEpisode'] = autoUnlockEpisode;
    data['canMerge'] = canMerge;
    data['isSubscription'] = isSubscription;
    data['isSubscriptionBlack'] = isSubscriptionBlack;
    data['subscriptionType'] = subscriptionType;
    data['receiveType'] = receiveType;
    data['subscriptionEndTime'] = subscriptionEndTime;
    data['subscriptionConfirm'] = subscriptionConfirm;
    data['hasSubscription'] = hasSubscription;
    data['userCodeUUID'] = userCodeUUID;
    data['subscriptionUserCode'] = subscriptionUserCode;
    data['subscriptionProvider'] = subscriptionProvider;
    data['subscriptionNickName'] = subscriptionNickName;
    data['giveBonus'] = giveBonus;
    data['facebookLoginType'] = facebookLoginType;
    data['haveFacebookAccount'] = haveFacebookAccount;
    data['campaignUnlockType'] = campaignUnlockType;
    data['allowRecharge'] = allowRecharge;
    return data;
  }
}

import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:get_storage/get_storage.dart';
import '../common/log/ff_log.dart';

/// SafeStorage 是对 GetStorage 的安全封装
/// 提供了错误处理和日志记录功能
/// 支持多个 GetStorage 实例，以 container 为 key 管理实例
class SafeStorage {
  static final Map<String, SafeStorage> _instances = {};
  static final Map<String, bool> _isInitializedForContainer = {};
  late GetStorage _storage;
  final String _container;
  
  /// 日志标签常量
  static const String _tag = 'SafeStorage';

  /// 安全创建或获取 SafeStorage 实例，创建之前会先确保调用init([String container = 'GetStorage'])方法
  /// [container] 存储容器名称，默认为 'GetStorage'
  /// [path] 存储路径
  /// [initialData] 初始数据
  static Future<SafeStorage?> safeInstance([
    String container = 'GetStorage',
    String? path,
    Map<String, dynamic>? initialData
  ]) async {
    bool success = await init(container);
    if (!success) {
      FFLog.error('SafeStorage 实例创建失败: $container', tag: _tag);
      return null;
    }
    return SafeStorage(container, path, initialData);
  }

  /// 创建或获取 SafeStorage 实例
  /// [container] 存储容器名称，默认为 'GetStorage'
  /// [path] 存储路径
  /// [initialData] 初始数据
  factory SafeStorage([
    String container = 'GetStorage',
    String? path,
    Map<String, dynamic>? initialData
  ]) {
    if (_instances.containsKey(container)) {
      return _instances[container]!;
    } else {
      final instance = SafeStorage._internal(container, path, initialData);
      _instances[container] = instance;
      return instance;
    }
  }

  SafeStorage._internal(this._container, [String? path, Map<String, dynamic>? initialData]) {
    try {
      _storage = GetStorage(_container, path, initialData);
      FFLog.info('SafeStorage 实例创建成功: $_container', tag: _tag);
    } catch (e) {
      FFLog.error('SafeStorage 实例创建失败: $_container, 错误: $e', tag: _tag);
    }
  }

  /// 初始化存储
  /// [container] 存储容器名称，默认为 'GetStorage'
  static Future<bool> init([String container = 'GetStorage']) async {
    bool isInitialized = _isInitializedForContainer[container] ?? false;
    if (isInitialized) {
      return true;
    }
    try {
      FFLog.info('初始化 SafeStorage: $container', tag: _tag);
      bool isInitialized = await GetStorage.init(container);
      _isInitializedForContainer[container] = isInitialized;
      return isInitialized;
    } catch (e) {
      FFLog.error('初始化 SafeStorage 失败: $container, 错误: $e', tag: _tag);
      return false;
    }
  }

  /// 读取指定键的值
  T? read<T>(String key) {
    try {
      final value = _storage.read<T>(key);
      return value;
    } catch (e) {
      FFLog.error('读取数据失败: $key, 错误: $e', tag: _tag);
      return null;
    }
  }

  /// 获取所有键
  T? getKeys<T>() {
    try {
      final keys = _storage.getKeys();
      return keys;
    } catch (e) {
      FFLog.error('获取所有键失败, 错误: $e', tag: _tag);
      return null;
    }
  }

  /// 获取所有值
  T? getValues<T>() {
    try {
      final values = _storage.getValues();
      return values;
    } catch (e) {
      FFLog.error('获取所有值失败, 错误: $e', tag: _tag);
      return null;
    }
  }

  /// 检查是否存在指定键的数据
  bool hasData(String key) {
    try {
      final result = _storage.hasData(key);
      return result;
    } catch (e) {
      FFLog.error('检查数据是否存在失败: $key, 错误: $e', tag: _tag);
      return false;
    }
  }

  /// 获取变更
  Map<String, dynamic> get changes {
    try {
      return _storage.changes;
    } catch (e) {
      FFLog.error('获取变更失败, 错误: $e', tag: _tag);
      return {};
    }
  }

  /// 监听容器变化
  VoidCallback? listen(VoidCallback callback) {
    try {
      return _storage.listen(callback);
    } catch (e) {
      FFLog.error('添加监听器失败, 错误: $e', tag: _tag);
      return null;
    }
  }

  /// 监听特定键的变化
  VoidCallback? listenKey(String key, ValueSetter callback) {
    try {
      return _storage.listenKey(key, callback);
    } catch (e) {
      FFLog.error('添加键监听器失败: $key, 错误: $e', tag: _tag);
      return null;
    }
  }

  /// 写入数据
  Future<void> write(String key, dynamic value) async {
    try {
      await _storage.write(key, value);
    } catch (e) {
      FFLog.error('写入数据失败: $key = $value, 错误: $e', tag: _tag);
    }
  }

  /// 写入内存数据（不保存到磁盘）
  void writeInMemory(String key, dynamic value) {
    try {
      _storage.writeInMemory(key, value);
    } catch (e) {
      FFLog.error('写入内存数据失败: $key = $value, 错误: $e', tag: _tag);
    }
  }

  /// 仅当数据为空时写入
  Future<void> writeIfNull(String key, dynamic value) async {
    try {
      await _storage.writeIfNull(key, value);
    } catch (e) {
      FFLog.error('尝试写入空数据失败: $key = $value, 错误: $e', tag: _tag);
    }
  }

  /// 移除指定键的数据
  Future<void> remove(String key) async {
    try {
      await _storage.remove(key);
    } catch (e) {
      FFLog.error('移除数据失败: $key, 错误: $e', tag: _tag);
    }
  }

  /// 清除所有数据
  Future<void> erase() async {
    try {
      await _storage.erase();
    } catch (e) {
      FFLog.error('清除所有数据失败, 错误: $e', tag: _tag);
    }
  }

  /// 保存数据到磁盘
  Future<void> save() async {
    try {
      await _storage.save();
    } catch (e) {
      FFLog.error('保存数据到磁盘失败, 错误: $e', tag: _tag);
    }
  }
}
extension DateTimeExtension on DateTime {
  /// 判断两个日期是否为同一天
  bool isSameDay(DateTime date) {
    return year == date.year && 
           month == date.month && 
           day == date.day;
  }

  /// 将秒数格式化为时间字符串
  static String convertSecondsToString(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}";
    } else {
      return "${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}";
    }
  }
}
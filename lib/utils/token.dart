import 'package:get_storage/get_storage.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/safe_storage.dart';

class Token {
  static final SafeStorage _storage = SafeStorage();
  static const String token = "token";

  static String getToken() {
    return _storage.read(token)??"";
  }

  static Future<void> setToken(String token) async {
    await _storage.write(Token.token, token);
  }

  static Future<void> removeToken() async {
    await _storage.remove(Token.token);
    await _storage.remove(UserService.userInfoKey);
  }
}

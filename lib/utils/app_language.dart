import 'dart:ui';

import 'package:get/get.dart';
import '../i18n/translation_service.dart';
// import 'package:get_storage/get_storage.dart';

/// App语言管理工具类
/// 提供简单的语言管理API，作为TranslationService的门面(Facade)
class AppLanguage {
  /// 获取当前语言对应的Locale
  static String getCurrentLanguage() {
    final translationService = Get.find<TranslationService>();
    return translationService.getCurrentLanguage();
  }

  /// 统一语言 均按照zh-Hant/zh-Hans传值
  static String getCurrentFormatLanguage(){
    var language = getCurrentLanguage();
    if(language == "zh_hans"){
      return "zh-Hans";
    }else if(language == "zh_hant"){
      return "zh-Hant";
    }
    return language;
  }

    static Locale getCurrentLocale() {
    final translationService = Get.find<TranslationService>();
    return translationService.getCurrentLocale();
  }

  /// 设置应用语言
  /// @param lang 语言代码，如'en', 'zh', 'zh-CN', 'zh-TW'等
  static Future<void> setLanguage(String lang) async {
    final translationService = Get.find<TranslationService>();
    translationService.setLanguage(lang);
  }

  /// 使用系统语言设置
  static Future<void> useSystemLanguage() async {
    final translationService = Get.find<TranslationService>();
    translationService.useSystemLanguage();
  }
}

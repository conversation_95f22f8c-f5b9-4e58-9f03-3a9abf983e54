import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/safe_storage.dart';

typedef FFSetting<T> = _FFUserDefaultProperty<T>;
typedef FFAssociationSetting<T> = _FFAssociationUserProperty<T>;

class FFUserDefault {
  //触发条件完成状态
  static FFAssociationSetting<bool> isAdDisplayTriggered = FFAssociationSetting<bool>('isAdDisplayTriggered', false);

  static FFAssociationSetting<int> exitDetailPageCount = FFAssociationSetting<int>('exitDetailPageCount', 0);
  static FFAssociationSetting<int> watchAdRewardCount = FFAssociationSetting<int>('watchAdRewardCount', 0);

}

class _FFUserDefaultProperty<T> {
  final String key;
  final T defaultValue;
  
  const _FFUserDefaultProperty(this.key, this.defaultValue);
  
  T get value => SafeStorage().read<T>(key) ?? defaultValue;
  
  set value(T newValue) => SafeStorage().write(key, newValue);
}

class _FFAssociationUserProperty<T> {
  UserService userService = Get.find<UserService>();
  final String key;
  final T defaultValue;
  
   _FFAssociationUserProperty(this.key, this.defaultValue);
  
  T get value => SafeStorage().read<T>('${userService.userInfo.value?.userId ?? 0}_$key') ?? defaultValue;
  
  set value(T newValue) => SafeStorage().write('${userService.userInfo.value?.userId ?? 0}_$key', newValue);
}

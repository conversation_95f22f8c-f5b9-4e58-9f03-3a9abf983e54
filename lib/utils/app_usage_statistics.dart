import 'dart:math';

import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/app_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/app_analysis.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';
import 'package:uuid/uuid.dart';

class AppUsageStatistics {
  static final SafeStorage _storage = SafeStorage();
  static int _onForegroundTime = -1;
  static const String _todayHasOpen = "todayHasOpen";
  static const String _active = "active";
  static const String _lastReportActiveUserTime = "lastReportActiveUserTime";

  static bool isTodayHasOpen(String date) {
    return _storage.read<bool>(_todayHasOpen + date) ?? false;
  }

  static void savaTodayHasOpen(String date, bool todayHasOpen) async {
    await _storage.write(_todayHasOpen + date, todayHasOpen);
  }

  static bool isActive(String date) {
    return _storage.read<bool>(_active + date) ?? false;
  }

  static void saveActive(String date, bool active) async {
    await _storage.write(_active + date, active);
  }

  static bool _hasValidUserId() {
    return Get.find<UserService>().userInfo.value?.userId != null;
  }

  static void onForeground() {
    if (!_hasValidUserId()) {
      return;
    }
    if (_onForegroundTime == -1) {
      _onForegroundTime = AppDeviceService.instance.getCurrentCalibratedTime();
      final date = DateFormat('yyyy-MM-dd').format(DateTime.now());
      if (!isTodayHasOpen(date)) {
        savaTodayHasOpen(date, true);
        useTrackEvent(EventName.interactive,
            extra: {EventKey.from: AppAnalysisStore.getAppLaunchFrom()},
            priority: TrackEventPriority.high);
      }
    }
    trackActiveUser();
  }

  static void onBackground() {
    if (_onForegroundTime == -1 || !_hasValidUserId()) {
      return;
    }
    final onBackgroundTime = AppDeviceService.instance.getCurrentCalibratedTime();
    final duration = (onBackgroundTime - _onForegroundTime) ~/ 1000;
    if (duration <= 0) {
      _onForegroundTime = -1;
      return;
    }

    useTrackEvent(EventName.app_usage_duration, extra: {
      EventKey.duration: duration.toString(),
      EventKey.session_id:
          "${AppDeviceService.instance.getCurrentCalibratedTime()}_${Random().nextInt(1000000)}",
      EventKey.start_time: _onForegroundTime.toString(),
      EventKey.end_time: onBackgroundTime.toString(),
    }, priority: TrackEventPriority.high);
    _onForegroundTime = -1;
    trackActiveUser();
    AppAnalysisStore.saveAppLaunchFrom(EventValue.icon.toString());
  }

  static void trackActive() {
    if (!_hasValidUserId()) {
      return;
    }
    final date = DateFormat('yyyy-MM-dd').format(DateTime.now());
    if (!isActive(date)) {
      saveActive(date, true);
      useTrackEvent(EventName.active);
    }
  }

  static void trackActiveUser() {
    if (!_hasValidUserId()) {
      return;
    }
    final curTime = DateTime.now().millisecondsSinceEpoch;
    final lastReportTime = _storage.read<int>(_lastReportActiveUserTime) ?? 0;
    if (lastReportTime == 0 || curTime - lastReportTime > 60 * 60 * 1000) {
      _storage.write(_lastReportActiveUserTime, curTime);
      useTrackEvent(EventName.active_user,
          extra: {EventKey.from: AppAnalysisStore.getAppLaunchFrom()});
    }
  }
}

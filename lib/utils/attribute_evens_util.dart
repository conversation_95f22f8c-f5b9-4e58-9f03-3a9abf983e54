import 'package:deeplink_dev/attribution_sdk_core.dart';
import 'package:deeplink_dev/in_app_event_key.dart';
import 'package:deeplink_dev/in_app_event_type.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/payment/payment_service.dart';
import 'package:get/get.dart';

/// 属性事件工具类 - 封装所有事件类型
class AttributeEventsUtil {
  static const tag = "AttributeEventsUtil";
  static bool isSdkInitialized = false;

  // 通用函数，用于从参数中获取字符串值
  static String? _getStringParam(Map<String, dynamic> params, String key) {
    String? result =  params.containsKey(key) ? params[key]?.toString() : null;
    if (result == 'null') {
      return null;
    }
    return result;
  }

  static Future<void> eventInterceptor(String eventName,
      {Map<String, String>? extra}) async {
    // 将extra转换为Map<String, dynamic>以便处理
    final Map<String, dynamic> extras = {};
    if (extra != null) {
      extras.addAll(extra);
    }

    // 在入口处统一获取常用参数，使用通用函数处理
    String? orderNo = _getStringParam(extras, TrackEvent.order_no);
    String? skuId = _getStringParam(extras, TrackEvent.sku);
    String? shortsId = _getStringParam(extras, TrackEvent.reel_id);
    String? reelId = _getStringParam(extras, EventKey.reelId);
    String? productId = _getStringParam(extras, TrackEvent.product_id);
    String? productName = _getStringParam(extras, EventKey.reelName);

    if (eventName == TrackEvent.order_create ||
        eventName == TrackEvent.pay_success) {
      String? currencyCode =  Get.find<PaymentService>().currencyCode.value;
      // 打印所有参数值，便于调试
      FFLog.info(
          "Event parameters - eventName: $eventName, orderNo: $orderNo, skuId: $skuId, shortsId: $shortsId, reelId: $reelId, productId: $productId, productName: $productName",
          tag: tag);
      // 如果没有SKU，记录日志，但不返回
      if (skuId == null || skuId.isEmpty) {
        FFLog.info("No SKU provided, will continue with other parameters",
            tag: tag);
      }

      // 确定事件类型
      String eventType;
      if (eventName == TrackEvent.order_create) {
        eventType = InAppEventType.addToCart;
      } else {
        // 如果skuId不为空且包含".vip."，则为订阅事件
        if (skuId != null && skuId.isNotEmpty && skuId.contains(".sub.")) {
          eventType = InAppEventType.subscribe;
        } else {
          eventType = InAppEventType.purchase;
        }
      }

      // 确定订阅天数
      String? subscribeDay;
      if (skuId != null && skuId.contains(".sub.") == true) {
        if (skuId.contains("week")) {
          subscribeDay = "7";
        } else if (skuId.contains("month")) {
          subscribeDay = "30";
        } else if (skuId.contains("year")) {
          subscribeDay = "365";
        }
      }

      // 解析金额
      double amount = 0;
      try {
        final amountStr = extras[TrackEvent.amount];
        if (amountStr != null && amountStr.isNotEmpty) {
          amount = double.tryParse(amountStr) ?? 0;
        }
      } catch (e) {
        FFLog.error("Failed to parse amount: ${extras[TrackEvent.amount]}",
            tag: tag);
      }

      // 创建内容项
      Map<String, dynamic> contentItem = {};

      // 根据规则添加productId
      String? finalProductId;


      if (reelId != null && reelId.isNotEmpty) {
        FFLog.info("Using reelId as finalProductId", tag: tag);
        finalProductId = reelId;
      } else if (productId != null && productId.isNotEmpty) {
        FFLog.info("Using productId as finalProductId", tag: tag);
        // 将productId转换为字符串
        finalProductId = productId;
      } else {
        FFLog.info("Both reelId and productId are null or empty", tag: tag);
      }

      FFLog.info("finalProductId: $finalProductId", tag: tag);

      if (finalProductId != null && finalProductId.isNotEmpty) {
        contentItem[InAppEventKey.productId] = finalProductId;
      } else if (skuId != null && skuId.isNotEmpty) {
        contentItem[InAppEventKey.productId] = skuId;
      }

      // 如果skuId不为空，则添加skuId
      if (skuId != null && skuId.isNotEmpty) {
        contentItem[InAppEventKey.skuId] = skuId;
      }

      // 添加其他必要字段
      contentItem[InAppEventKey.quantity] = "1";
      contentItem[InAppEventKey.value] = amount.toString();

      // 如果产品名称不为空，则添加
      if (productName?.isNotEmpty == true) {
        contentItem[InAppEventKey.productName] = productName;
      }

      // 使用_generateProductInfo方法生成符合SDK要求的参数格式
      Map<String, dynamic> params = _generateProductInfo([contentItem],
          currency: currencyCode,
          totalValue: amount.toString(),
          orderNo: orderNo,
          subscribeDay: subscribeDay,
          purchaseDate: (eventType == InAppEventType.purchase ||
                  eventType == InAppEventType.subscribe)
              ?  (DateTime.now().millisecondsSinceEpoch ~/ 1000).toInt().toString()
              : null);

      // 如果是添加到购物车事件，还需要发送结账事件
      if (eventType == InAppEventType.addToCart) {
        // 在程序尾部统一调用logInAppEvent方法
        await _sendEvents([eventType, InAppEventType.initiateCheckOut], params);
      } else {
        // 在程序尾部统一调用logInAppEvent方法
        await _sendEvents([eventType], params);
      }
    } else if (eventName == TrackEvent.reelPlay) {
      
      // 打印所有参数值，便于调试
      FFLog.info(
          "Event parameters - eventName: $eventName, orderNo: $orderNo, skuId: $skuId, shortsId: $shortsId, reelId: $reelId, productId: $productId, productName: $productName",
          tag: tag);
      // 创建内容项
      Map<String, dynamic> contentItem = {};

      // 根据优先级获取产品ID：shortsId > reelId > productId
      String? finalProductId;

      if (shortsId != null && shortsId.isNotEmpty) {
        FFLog.info("reelPlay Using shortsId as finalProductId", tag: tag);
        finalProductId = shortsId;
      } else if (reelId != null && reelId.isNotEmpty) {
        FFLog.info("reelPlay Using reelId as finalProductId", tag: tag);
        finalProductId = reelId;
      } else if (productId != null && productId.isNotEmpty) {
        FFLog.info("reelPlay Using productId as finalProductId", tag: tag);
        // 将productId转换为字符串
        finalProductId = productId;
      } else {
        FFLog.info("reelPlay All IDs are null or empty", tag: tag);
      }

      FFLog.info("reelPlay finalProductId: $finalProductId", tag: tag);

      if (finalProductId != null && finalProductId.isNotEmpty) {
        contentItem[InAppEventKey.productId] = finalProductId;
      }

      // 添加其他必要字段
      contentItem[InAppEventKey.quantity] = "1";
      contentItem[InAppEventKey.value] = "0";

      if (productName?.isNotEmpty == true) {
        contentItem[InAppEventKey.productName] = productName;
      }

      Map<String, dynamic> params =
          _generateProductInfo([contentItem], currency: null, totalValue: null);

      await _sendEvents([InAppEventType.viewContent], params);
    }
    // else if (eventName == TrackEvent.EVENT_CLICK_COLLECTION) { // 使用TrackEvent而不是EventName
    //   if (extras?[TrackEvent.KEY_TYPE] == TrackEvent.VALUE_DO_COLLECTION) { // 使用TrackEvent代替EventValue
    //     // 从事件中获取ID，如果没有则返回
    //     // 假设event是当前上下文中的一个对象
    //     final event = {'eventId': 'event_${DateTime.now().millisecondsSinceEpoch}', 'eventTime': DateTime.now().millisecondsSinceEpoch};
    //     String? eventId = event['eventId'] as String?;
    //     if (eventId == null) return;

    //     String? shortsId = extras?[TrackEvent.reel_id] ?? "";
    //     String productName = extras?[TrackEvent.KEY_CUSTOM_SHORTS_NAME] ?? "";

    //     // 创建订单信息
    //     Map<String, dynamic> orderInfo = {
    //       'currency': "",
    //       'value': 0,
    //       'contents': [
    //         {
    //           'productId': shortsId,
    //           'productName': productName,
    //           'quantity': 1,
    //           'value': 0
    //         }
    //       ]
    //     };

    //     Map<String, dynamic> params = {};
    //     params['EVENT_ID'] = eventId;
    //     params['EVENT_TIME'] = event['eventTime'];
    //     params['ORDER_INFO'] = orderInfo;

    //     logInAppEvent(InAppEventType.addToWishList, extraParams: params);
    //   }
    // }
  }

  static Future<void> logInAppEvent(String eventType,
      {dynamic extraParams}) async {
    FFLog.info("logInAppEvent: $eventType, extraParams: $extraParams",
        tag: tag);
    try {
      AttributionSdkCore.instance.logEvent(eventType, extraParams);
    } catch (e) {
      FFLog.error("Failed to log event: $eventType, error: $e", tag: tag);
    }
  }

  static Map<String, dynamic> _generateProductInfo(
      List<Map<dynamic, dynamic>> products,
      {String? orderNo,
      String? currency,
      String? totalValue,
      String? searchContent,
      String? subscribeDay,
      String? purchaseDate}) {
    // 创建一个基本的Map
    Map<String, dynamic> result = {};

    // 只添加非空的参数
    if (orderNo != null && orderNo.isNotEmpty) {
      result[InAppEventKey.orderNo] = orderNo;
    }
    if (currency != null && currency.isNotEmpty) {
      // currency 要大写
      result[InAppEventKey.currency] = currency.toUpperCase();
    }
    if (totalValue != null && totalValue.isNotEmpty) {
      result[InAppEventKey.value] = totalValue;
    }
    if (searchContent != null && searchContent.isNotEmpty) {
      result[InAppEventKey.searchContent] = searchContent;
    }
    if (subscribeDay != null && subscribeDay.isNotEmpty) {
      result[InAppEventKey.subscribeDay] = subscribeDay;
    }
    if (purchaseDate != null && purchaseDate.isNotEmpty) {
      result[InAppEventKey.purchaseDate] = purchaseDate;
    }

    result[InAppEventKey.products] = products;

    return result;
  }

  static Future<void> _sendEvents(
      List<String> eventTypes, Map<String, dynamic> params) async {
    for (var eventType in eventTypes) {
      await logInAppEvent(eventType, extraParams: params);
    }
  }
}

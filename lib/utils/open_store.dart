import 'dart:developer';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

/// 打开商店详情页
class OpenStore {
  OpenStore._();

  static final OpenStore _instance = OpenStore._();

  /// Returns an instance using the default [OpenStore].
  static OpenStore get instance => _instance;

  static const _playMarketUrl =
      'https://play.google.com/store/apps/details?id=';
  static const _appStoreUrlIOS = 'https://apps.apple.com/app/id';

  final _platformNotSupportedException = Exception('Platform not supported');

  /// Main method of this package
  /// Allows to open your app's page in store by platform
  ///
  /// Enabled for Android & iOS
  /// [PlatformException] will throw if you try using this package in other OS
  ///
  /// [CantLaunchPageException] will throw if you don't specify
  /// app id on Platform that you useing right now
  Future<void> open({
    String? androidAppBundleId,
    String? appStoreId,
  }) async {
    assert(
      appStoreId != null || androidAppBundleId != null,
      "You must pass one of this parameters",
    );

    try {
      await _open(
        appStoreId,
        androidAppBundleId,
      );
    } on Exception catch (e, st) {
      log([e, st].toString());
      rethrow;
    }
  }

  Future<void> _open(String? appStoreId, String? androidAppBundleId) async {
    if (Platform.isIOS) {
      await _openIOS(appStoreId);
      return;
    }
    if (Platform.isAndroid) {
      await _openAndroid(androidAppBundleId);
      return;
    }

    throw _platformNotSupportedException;
  }

  Future _openAndroid(String? androidAppBundleId) async {
    if (androidAppBundleId != null) {
      await openUrl('$_playMarketUrl$androidAppBundleId');
      return;
    }
    throw CantLaunchPageException("androidAppBundleId is not passed");
  }

  Future _openIOS(String? appStoreId) async {
    if (appStoreId != null) {
      await openUrl('$_appStoreUrlIOS$appStoreId');
      return;
    }
    throw CantLaunchPageException("appStoreId is not passed");
  }

  Future<void> openUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
      return;
    }
    throw CantLaunchPageException('Could not launch $url');
  }
}

class OpenStoreException implements Exception {
  String cause;
  OpenStoreException(this.cause);
}

class CantLaunchPageException extends OpenStoreException {
  CantLaunchPageException(super.cause);
}

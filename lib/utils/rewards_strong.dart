import 'package:playlet/utils/safe_storage.dart';



class RewardsAdStrong {
  static final SafeStorage _storage = SafeStorage();

  static int getAdTaskStatus(String taskID) {
    return _storage.read(taskID) ?? 1;
  }

  static Future<void> setAdTaskStatus(String taskID, int status) async {
    await _storage.write(taskID, status);
  }

  static Future<void> removeAdTaskStatus(
    String taskID,
  ) async {
    await _storage.remove(taskID);
  }
}

class RewardsBindStrong {
  static final SafeStorage _storage = SafeStorage();
  static const String storageKey = "verification_code_time";

  static void startCountdown() {
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    int endTime = currentTime + 60 * 1000;
    _storage.write(storageKey, endTime);
  }

  static int getLeftTime() {
    int? endTime = _storage.read(storageKey);
    if (endTime == null) {
      return 0;
    }
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    int leftTime = endTime - currentTime;
    if (leftTime < 0) {
      return 0;
    }
    return (leftTime / 1000).toInt();
  }

  // 关闭倒计时
  static void closeCountdown() {
    _storage.remove(storageKey);
  }
}

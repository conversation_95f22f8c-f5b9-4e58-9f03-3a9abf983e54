import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:playlet/api/user.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/rewards/event/rewards_event.dart';
import 'package:playlet/service/attribute_service.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';

//登录类型(1:游客,2:苹果登录,3:Facebook登录,4:谷歌登录,5:邮箱注册(针对H5))
class LoginType {
  /// 游客
  static const int tourists = 1;

  /// Facebook登录
  static const int facebook = 3;

  /// 谷歌登录
  static const int google = 4;

  /// Apple登录
  static const int apple = 2;
}

class Auth {
  static SafeStorage storage = SafeStorage();
  static String loginType = "login_type";
  static String loginKey = "login_key";

  // 用户活跃上报时间
  static String userReportActiveTime = "user_reportActiveTime";

  static String? authToken = "";
  static String? idToken = "";
  static String _scene = EventValue.login_page;
  static String _loginType = EventValue.facebook;
  static const int loginFail = 0;
  static const int loginSuccess = 1;
  static const int loginCancel = 2;
  static int loginStatus = loginFail; //传入值[0：失败(登录但失败了)，1：成功，2：取消登录]
  static String? loginFailReason = "";

  // 1.0版本存在的存储key  start
  static String olLoginType = "login_type";
  static String oldLoginKey = "login_key";
  static String oldUserInfo = "user_info";
  static String oldLoginUser = "login_user";
  static String oldTokenExpirationTime = "token_expiration_time";
  static String oldRefreshTokenExpirationTime = "refresh_token_expiration_time";
  static String oldLanguage = "language";
  static String oldHomeLoginDialog = "home_login_dialog";
  static String oldTimeLimitShopDialog = "time_limit_shop_dialog";
  static String oldProfileLoginDialog = "profile_login_dialog";
  static String oldCoinsList = "coins_list";
  static String oldReserveList = "reserveList";

  // 1.0版本存在的存储key  end
  static Future<UserCredential?> _googleUserCredential() async {
    try {
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();

      // 检查用户是否取消登录
      if (googleUser == null) {
        FFLog.info("用户取消了Google登录");
        loginStatus = loginCancel;
        return null;
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // 检查认证信息是否有效
      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        FFLog.error("Google登录失败: 无效的认证信息");
        loginFailReason = "Google登录失败: 无效的认证信息";
        return null;
      }

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      authToken = googleAuth.accessToken;
      idToken = googleAuth.idToken;
      return await FirebaseAuth.instance.signInWithCredential(credential);
    } on FirebaseAuthException catch (e) {
      FFLog.error("Firebase认证失败: ${e.message}");
      loginFailReason = "Firebase认证失败: ${e.message}";
      return null;
    } catch (e) {
      FFLog.error("Google登录异常: $e");
      loginFailReason = "Google登录异常: $e";
      //Get.snackbar("登录失败", "Google登录异常: $e");
      return null;
    }
  }

// ... existing code ...

  static Future<UserCredential?> _facebookUserCredential() async {
    try {
      final LoginResult loginResult = await FacebookAuth.instance
          .login(loginTracking: LoginTracking.enabled);

      // 检查登录状态
      if (loginResult.status == LoginStatus.cancelled) {
        FFLog.info("用户取消了Facebook登录");
        loginStatus = loginCancel;
        return null;
      } else if (loginResult.status != LoginStatus.success) {
        FFLog.error("Facebook登录失败: ${loginResult.status}");
        loginFailReason = "Facebook登录失败: ${loginResult.status}";
        return null;
      }

      // 检查accessToken是否为空
      if (loginResult.accessToken == null) {
        FFLog.error("Facebook登录成功但accessToken为空");
        loginFailReason = "Facebook登录成功但accessToken为空";
        return null;
      }

      final credential =
          FacebookAuthProvider.credential(loginResult.accessToken!.tokenString);
      authToken = loginResult.accessToken!.tokenString;
      return await FirebaseAuth.instance.signInWithCredential(credential);
    } catch (e) {
      FFLog.error("Facebook登录异常: $e");
      loginFailReason = "Facebook登录异常: $e";
      //Get.snackbar("登录失败", "Facebook登录异常: $e");
      return null;
    }
  }

// ... existing code ...

  static Future<UserCredential?> _appleUserCredential() async {
    try {
      final appleProvider = AppleAuthProvider();
      final authResult =
          await FirebaseAuth.instance.signInWithProvider(appleProvider);

      // 检查用户是否取消登录
      if (authResult.user == null) {
        FFLog.info("用户取消了Apple登录");
        loginStatus = loginCancel;
        return null;
      }

      // 检查认证是否成功
      if (authResult.credential == null) {
        FFLog.error("Apple登录失败: 无效的认证凭证");
        loginFailReason = "Apple登录失败: 无效的认证凭证";
        return null;
      }

      authToken = authResult.credential?.accessToken;
      idToken = await authResult.user?.getIdToken();
      return authResult;
    } on FirebaseAuthException catch (e) {
      FFLog.error("Firebase Apple登录失败: ${e.message}");
      loginFailReason = "Firebase Apple登录失败: ${e.message}";
      return null;
    } catch (e) {
      FFLog.error("Apple登录异常: $e");
      loginFailReason = "Apple登录异常: $e";
      //Get.snackbar("登录失败", "Apple登录异常: $e");
      return null;
    }
  }

  static void _trackLoginResultEvent(
    String? reason, {
    required String? trackScene,
  }) {
    if (reason != null && reason.isNotEmpty) {
      useTrackEvent(EventName.account_login_result, extra: {
        EventKey.scene: trackScene ?? '',
        EventKey.type: _loginType,
        EventKey.is_success: loginStatus.toString(),
        EventKey.reason: reason
      });
    } else {
      useTrackEvent(EventName.account_login_result, extra: {
        EventKey.scene: _scene,
        EventKey.type: _loginType,
        EventKey.is_success: loginStatus.toString(),
      });
    }
  }

  /// 统一处理登录流程，并返回是否登录成功
  static Future<bool> signInWithProvider(
    int loginType, {
    String? fbRewardsTrackFrom,
  }) async {
    try {
      loginStatus = loginFail;
      loginFailReason = "";
      UserCredential? userCredential;
      int firebaseSource = 10;
      if (loginType == LoginType.google) {
        userCredential = await _googleUserCredential();
        firebaseSource = 10;
      } else if (loginType == LoginType.facebook) {
        userCredential = await _facebookUserCredential();
        firebaseSource = 20;
      } else if (loginType == LoginType.apple) {
        userCredential = await _appleUserCredential();
        firebaseSource = 80;
      }
      if (loginType != LoginType.tourists && userCredential == null) {
        _trackLoginResultEvent(loginFailReason, trackScene: _scene);
        return false;
      }
      idToken = await userCredential?.user?.getIdToken();
      String userCode = Get.find<UserService>().userInfo.value?.userId ?? "";
      final result = await ApiUser.tripartiteLogin(
          firebaseSource, authToken, idToken, userCode);
      if (result == null) {
        loginStatus = loginFail;
        _trackLoginResultEvent("服务端异常", trackScene: _scene);
        return false;
      }

      /// 展示facebook登录获取奖励币提示
      if (loginType == LoginType.facebook) {
        if (result.giveBonus != null && result.giveBonus! > 0) {
          RewardsEvent.submitTaskFinish(
            from: fbRewardsTrackFrom ?? '',
            type: EventValue.newbieTask,
            taskName: EventValue.fbLogin,
            bonus: result.giveBonus!.toString(),
          );
          Get.toast(
              'FB ${AppTrans.login()} +${result.giveBonus!} ${AppTrans.bonus()}');

          // 刷新可获得金币总数
          Get.find<UserService>().fetchRewardsTotalBonus();
        }
      }

      // 登录成功后，调用更新用户信息方法
      await updateUserInfoAndReport(loginType);

      loginStatus = loginSuccess;
      _trackLoginResultEvent("", trackScene: _scene);
      return true;
    } catch (e) {
      FFLog.info(e, tag: "tripartiteLogin");
      return false;
    }
  }

  /// 同步第三方登录的用户信息并上报
  static Future<void> updateUserInfoAndReport(int loginType) async {
    try {
      FFLog.info("开始同步用户信息", tag: "updateUserInfo");

      // 使用AttributeService进行用户信息同步和上报
      final attributeService = Get.find<AttributeService>();
      await attributeService.updateUserInfoAndReport();

      FFLog.info("用户信息同步完成", tag: "updateUserInfo");
    } catch (e) {
      FFLog.error("同步用户信息失败: $e", tag: "updateUserInfo");
    }
  }

  static Future<bool> signInWithGoogle(String scene) async {
    _scene = scene;
    _loginType = EventValue.google;
    return await signInWithProvider(LoginType.google);
  }

  static Future<bool> signInWithFacebook(String scene,
      {String? fbRewardsTrackFrom}) async {
    _scene = scene;
    _loginType = EventValue.facebook;
    return await signInWithProvider(LoginType.facebook,
        fbRewardsTrackFrom: fbRewardsTrackFrom);
  }

  static Future<bool> signInWithApple(String scene) async {
    _scene = scene;
    _loginType = EventValue.apple;
    return await signInWithProvider(LoginType.apple);
  }

  static Future<bool> signInTourists() async {
    return await Auth.signInWithProvider(LoginType.tourists);
  }

  static Future<void> signOut() async {
    await FirebaseAuth.instance.signOut();
  }

  static refreshToken() async {
    // final UserService userService = Get.find<UserService>();
    // final AppService appService = Get.find<AppService>();
    // final user = userService.getUser();
    // final token = appService.getToken();
    // if (user == null || token == null) return;
    // final tokenExpirationTime = appService.tokenExpirationTime;
    // int currentTimeInSeconds = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    // int tokenEndTime = tokenExpirationTime - currentTimeInSeconds;
    // if (tokenEndTime > 60 * 60 * 24 * 7) return;
    // int refreshTokenEndTime =
    //     appService.refreshTokenExpirationTime - currentTimeInSeconds;
    // if (refreshTokenEndTime > 10) {
    //   await ApiUser.refreshToken(token.refreshToken!);
    // } else {
    //   final loginKey = storage.read<String>(Auth.loginKey);
    //   final loginType = storage.read<int>(Auth.loginType);
    //   if (loginKey == null || loginType == null) return;
    //   UserLoginOption? option = await UserLoginOption.toLoginParams(
    //     loginKey,
    //     loginType,
    //   );
    //   final result = await ApiUser.fastLogin(option);
    //   if (result != null) {
    //     await userService.loginWithUser(result);
    //   }
    // }
  }

  static setAuth(int loginType, String? loginKey) {
    storage.write(Auth.loginType, loginType);
    storage.write(Auth.loginKey, loginKey);
  }

  static Future<void> clearAuth() async {
    await storage.remove(Auth.loginType);
    await storage.remove(Auth.loginKey);
  }

  // 获取1.0版本的用户Id
  static Future<int?> getOldUserId() async {
    try {
      final String? userStr = storage.read(oldLoginUser);
      if (userStr != null) {
        Map<String, dynamic> userMap = jsonDecode(userStr);
        var userId = userMap['accInfo']?["userId"];
        FFLog.info("getOldUserId userId:$userId", tag: "auth");
        return userId;
      }
    } catch (e) {
      FFLog.error("getOldUserId error:$e", tag: "auth");
    }
    return null;
  }

  // 移除1.0版本的旧数据
  static removeOldUserStore() {
    // 这三个与1.1版本的配置一样，不用删除
    // storage.remove(olLoginType);
    // storage.remove(oldLoginKey);
    // storage.remove(oldProfileLoginDialog);
    storage.remove(oldUserInfo);
    storage.remove(oldLoginUser);
    storage.remove(oldTokenExpirationTime);
    storage.remove(oldRefreshTokenExpirationTime);
    storage.remove(oldLanguage);
    storage.remove(oldHomeLoginDialog);
    storage.remove(oldTimeLimitShopDialog);
    storage.remove(oldCoinsList);
    storage.remove(oldReserveList);
  }

  /// 记录用户活跃上报时间
  static setUserReportActiveTime() {
    storage.write(
        Auth.userReportActiveTime, DateTime.now().millisecondsSinceEpoch);
  }

  /// 获取用户活跃上报时间
  static Future<int> getUserReportActiveTime() async {
    return await storage.read(Auth.userReportActiveTime) ?? 0;
  }
}

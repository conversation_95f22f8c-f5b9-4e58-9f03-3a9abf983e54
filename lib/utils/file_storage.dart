import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:path_provider/path_provider.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/widget/cover_widget.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class FileStorage {
  static const _tag = 'FileStorage';

  static Future<String?> getApplicationSupportDirectoryPath() async {
    try {
      Directory directory;
      if (Platform.isIOS) {
        directory = await getLibraryDirectory();
      } else {
        directory = await getApplicationSupportDirectory();
      }
      return directory.path;
    } catch (e) {
      FFLog.error('获取应用支持目录失败: $e', tag: _tag);
      return null;
    }
  }

  static Future<String?> read(String path) async {
    try {
      final file = File(path);
      if (!await file.exists()) {
        return null;
      }
      final jsonString = await file.readAsString();
      if (jsonString.isEmpty) {
        return null;
      }
      return jsonString;
    } catch (e) {
      FFLog.error('读取文件失败: $e', tag: _tag);
      return null;
    }
  }

  static Future<bool> write(String json, String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
      }
      await file.create(recursive: true);
      await file.writeAsString(json);
      return true;
    } catch (e) {
      FFLog.error('写入文件失败: $e', tag: _tag);
      return false;
    }
  }

  /// 加载图片，返回本地缓存图片地址
  static Future<String?> loadImageUrl(String? imageUrl) async {
    if (imageUrl == null || imageUrl.isEmpty) {
      return null;
    }
    try {
      String? path = await getImageCachePath(imageUrl);
      if (path != null) {
        return path;
      }
      FFLog.info('加载图片：$imageUrl');
      String key = getImageCacheKey(imageUrl);
      FileInfo? fileInfo = await CachedNetworkImageProvider.defaultCacheManager.downloadFile(imageUrl, key: key);
      if (fileInfo.statusCode == 200) {
        return fileInfo.file.path;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 获取本地缓存图片路径，不会发起加载
  static Future<String?> getImageCachePath(String? imageUrl) async {
    if (imageUrl == null || imageUrl.isEmpty) {
      return null;
    }
    try {
      String key = getImageCacheKey(imageUrl);
      FileInfo? fileInfo = await CachedNetworkImageProvider.defaultCacheManager.getFileFromCache(key);
      return fileInfo?.file.path;
    } catch (e) {
      return null;
    }
  }

  /// 图片移除auth_key参数，保持同一张图片的url不受auth_key影响导致缓存key不同
  static String removeAuthKeyFromImageUrl(String url) {
    final uri = Uri.parse(url);
    final queryParameters = Map<String, dynamic>.from(uri.queryParameters);
    queryParameters.remove('auth_key');
    final newUri = uri.replace(queryParameters: queryParameters);
    return newUri.toString();
  }

  /// 生成缓存图片key
  static String getImageCacheKey(String url) {
    String newUrl = removeAuthKeyFromImageUrl(url);
    return '${newUrl}_2'; // 加上后缀_2是为了避免命中旧版本压缩后的缓存图片
  }
}
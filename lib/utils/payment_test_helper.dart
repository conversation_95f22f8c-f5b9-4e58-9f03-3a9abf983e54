import '../common/log/ff_log.dart';
import 'payment_event_tracker.dart';
import 'payment_debug_helper.dart';

/// 支付测试助手，用于验证修复效果
class PaymentTestHelper {
  static const String tag = "PaymentTestHelper";
  
  /// 模拟iOS恢复购买场景
  static void simulateIOSRestoreScenario() {
    FFLog.info("$tag simulateIOSRestoreScenario -> Starting iOS restore scenario simulation");
    
    // 清除之前的数据
    PaymentEventTracker.clearAll();
    
    // 模拟正常购买流程
    FFLog.info("$tag simulateIOSRestoreScenario -> Simulating normal purchase flow");
    PaymentEventTracker.recordEvent("normal_order_1", "order_create", additionalInfo: "normal_purchase");
    PaymentEventTracker.recordEvent("normal_order_1", "pay_success", additionalInfo: "normal_purchase");
    
    PaymentEventTracker.recordEvent("normal_order_2", "order_create", additionalInfo: "normal_purchase");
    PaymentEventTracker.recordEvent("normal_order_2", "order_create_cancel", additionalInfo: "normal_purchase");
    
    PaymentEventTracker.recordEvent("normal_order_3", "order_create", additionalInfo: "normal_purchase");
    PaymentEventTracker.recordEvent("normal_order_3", "order_create_fail", additionalInfo: "normal_purchase");
    
    // 模拟iOS恢复购买触发的额外事件（修复前的问题）
    FFLog.info("$tag simulateIOSRestoreScenario -> Simulating iOS restore triggering extra events (before fix)");
    PaymentEventTracker.recordEvent("restore_order_1", "pay_success", additionalInfo: "ios_restore_before_fix");
    PaymentEventTracker.recordEvent("restore_order_2", "pay_success", additionalInfo: "ios_restore_before_fix");
    PaymentEventTracker.recordEvent("restore_order_3", "order_create_cancel", additionalInfo: "ios_restore_before_fix");
    
    // 打印统计结果
    FFLog.info("$tag simulateIOSRestoreScenario -> Statistics after simulating iOS restore issue:");
    PaymentEventTracker.printStatistics();
    
    final stats = PaymentEventTracker.getStatistics();
    if (stats['hasLogicError'] == true) {
      FFLog.error("$tag simulateIOSRestoreScenario -> ✅ Successfully reproduced the iOS restore issue!");
    } else {
      FFLog.info("$tag simulateIOSRestoreScenario -> ❌ Failed to reproduce the issue");
    }
  }
  
  /// 模拟修复后的正常场景
  static void simulateFixedScenario() {
    FFLog.info("$tag simulateFixedScenario -> Starting fixed scenario simulation");
    
    // 清除之前的数据
    PaymentEventTracker.clearAll();
    
    // 模拟正常购买流程
    FFLog.info("$tag simulateFixedScenario -> Simulating normal purchase flow");
    PaymentEventTracker.recordEvent("fixed_order_1", "order_create", additionalInfo: "normal_purchase");
    PaymentEventTracker.recordEvent("fixed_order_1", "pay_success", additionalInfo: "normal_purchase");
    
    PaymentEventTracker.recordEvent("fixed_order_2", "order_create", additionalInfo: "normal_purchase");
    PaymentEventTracker.recordEvent("fixed_order_2", "order_create_cancel", additionalInfo: "normal_purchase");
    
    PaymentEventTracker.recordEvent("fixed_order_3", "order_create", additionalInfo: "normal_purchase");
    PaymentEventTracker.recordEvent("fixed_order_3", "order_create_fail", additionalInfo: "normal_purchase");
    
    // 模拟iOS恢复购买（修复后不会触发额外事件）
    FFLog.info("$tag simulateFixedScenario -> Simulating iOS restore (after fix - no extra events)");
    // 注意：修复后，恢复购买不会触发这些事件
    
    // 打印统计结果
    FFLog.info("$tag simulateFixedScenario -> Statistics after fix:");
    PaymentEventTracker.printStatistics();
    
    final stats = PaymentEventTracker.getStatistics();
    if (stats['hasLogicError'] == false) {
      FFLog.info("$tag simulateFixedScenario -> ✅ Fix is working correctly!");
    } else {
      FFLog.error("$tag simulateFixedScenario -> ❌ Fix is not working properly");
    }
  }
  
  /// 对比修复前后的效果
  static void compareBeforeAndAfterFix() {
    FFLog.info("$tag compareBeforeAndAfterFix -> Comparing before and after fix");
    
    FFLog.info("$tag compareBeforeAndAfterFix -> === BEFORE FIX ===");
    simulateIOSRestoreScenario();
    final beforeStats = PaymentEventTracker.getStatistics();
    
    FFLog.info("$tag compareBeforeAndAfterFix -> === AFTER FIX ===");
    simulateFixedScenario();
    final afterStats = PaymentEventTracker.getStatistics();
    
    FFLog.info("$tag compareBeforeAndAfterFix -> === COMPARISON RESULTS ===");
    FFLog.info("$tag compareBeforeAndAfterFix -> Before Fix - Has Logic Error: ${beforeStats['hasLogicError']}");
    FFLog.info("$tag compareBeforeAndAfterFix -> After Fix - Has Logic Error: ${afterStats['hasLogicError']}");
    FFLog.info("$tag compareBeforeAndAfterFix -> Before Fix - Event Counts: ${beforeStats['eventCounts']}");
    FFLog.info("$tag compareBeforeAndAfterFix -> After Fix - Event Counts: ${afterStats['eventCounts']}");
    
    if (beforeStats['hasLogicError'] == true && afterStats['hasLogicError'] == false) {
      FFLog.info("$tag compareBeforeAndAfterFix -> 🎉 FIX SUCCESSFUL! Logic error resolved.");
    } else {
      FFLog.error("$tag compareBeforeAndAfterFix -> ❌ Fix may not be working as expected");
    }
  }
  
  /// 验证实际数据的逻辑
  static void validateRealData(Map<String, int> realEventCounts) {
    FFLog.info("$tag validateRealData -> Validating real production data");
    
    final orderCreate = realEventCounts['order_create'] ?? 0;
    final paySuccess = realEventCounts['pay_success'] ?? 0;
    final orderCancel = realEventCounts['order_create_cancel'] ?? 0;
    final orderFail = realEventCounts['order_create_fail'] ?? 0;
    
    final totalResultEvents = paySuccess + orderCancel + orderFail;
    
    FFLog.info("$tag validateRealData -> Real Data Analysis:");
    FFLog.info("$tag validateRealData -> order_create: $orderCreate");
    FFLog.info("$tag validateRealData -> pay_success: $paySuccess");
    FFLog.info("$tag validateRealData -> order_create_cancel: $orderCancel");
    FFLog.info("$tag validateRealData -> order_create_fail: $orderFail");
    FFLog.info("$tag validateRealData -> Total result events: $totalResultEvents");
    FFLog.info("$tag validateRealData -> Difference: ${totalResultEvents - orderCreate}");
    
    if (totalResultEvents > orderCreate) {
      FFLog.error("$tag validateRealData -> ❌ LOGIC ERROR DETECTED in real data!");
      FFLog.error("$tag validateRealData -> Result events ($totalResultEvents) > order_create ($orderCreate)");
      
      if (paySuccess > orderCreate) {
        FFLog.error("$tag validateRealData -> Issue: pay_success ($paySuccess) > order_create ($orderCreate)");
        FFLog.error("$tag validateRealData -> Likely cause: iOS restore purchases triggering pay_success without order_create");
      }
      
      if (orderCancel > orderCreate) {
        FFLog.error("$tag validateRealData -> Issue: order_create_cancel ($orderCancel) > order_create ($orderCreate)");
        FFLog.error("$tag validateRealData -> Likely cause: iOS restore purchases triggering cancel events");
      }
      
      if (orderFail > orderCreate) {
        FFLog.error("$tag validateRealData -> Issue: order_create_fail ($orderFail) > order_create ($orderCreate)");
        FFLog.error("$tag validateRealData -> Likely cause: iOS restore purchases triggering fail events");
      }
    } else {
      FFLog.info("$tag validateRealData -> ✅ Real data looks healthy!");
    }
  }
  
  /// 生成修复建议
  static String generateFixRecommendations(Map<String, int> realEventCounts) {
    final orderCreate = realEventCounts['order_create'] ?? 0;
    final paySuccess = realEventCounts['pay_success'] ?? 0;
    final orderCancel = realEventCounts['order_create_cancel'] ?? 0;
    final orderFail = realEventCounts['order_create_fail'] ?? 0;
    
    final totalResultEvents = paySuccess + orderCancel + orderFail;
    
    final recommendations = StringBuffer();
    recommendations.writeln("=== Payment Event Fix Recommendations ===");
    
    if (totalResultEvents > orderCreate) {
      recommendations.writeln("🚨 CRITICAL ISSUE DETECTED:");
      recommendations.writeln("Result events ($totalResultEvents) exceed order_create events ($orderCreate)");
      recommendations.writeln("");
      recommendations.writeln("📋 RECOMMENDED ACTIONS:");
      recommendations.writeln("1. Apply the iOS restore purchase fix immediately");
      recommendations.writeln("2. Add event filtering for restore purchases");
      recommendations.writeln("3. Implement event tracking and monitoring");
      recommendations.writeln("4. Review and validate all payment event triggers");
      recommendations.writeln("");
      
      if (paySuccess > orderCreate) {
        recommendations.writeln("🔍 SPECIFIC ISSUE: pay_success events exceed order_create");
        recommendations.writeln("   - Filter out pay_success events during restore purchases");
        recommendations.writeln("   - Check for duplicate pay_success triggers");
      }
      
      if (orderCancel > orderCreate) {
        recommendations.writeln("🔍 SPECIFIC ISSUE: order_create_cancel events exceed order_create");
        recommendations.writeln("   - Filter out cancel events during restore purchases");
        recommendations.writeln("   - Review cancel event trigger conditions");
      }
      
      if (orderFail > orderCreate) {
        recommendations.writeln("🔍 SPECIFIC ISSUE: order_create_fail events exceed order_create");
        recommendations.writeln("   - Filter out fail events during restore purchases");
        recommendations.writeln("   - Review error handling during restore");
      }
    } else {
      recommendations.writeln("✅ Event logic appears healthy");
      recommendations.writeln("📋 MAINTENANCE RECOMMENDATIONS:");
      recommendations.writeln("1. Continue monitoring with PaymentEventTracker");
      recommendations.writeln("2. Regular health checks with PaymentDebugHelper");
      recommendations.writeln("3. Maintain event filtering for restore purchases");
    }
    
    recommendations.writeln("=== End Recommendations ===");
    
    final reportString = recommendations.toString();
    FFLog.info("$tag generateFixRecommendations -> Generated recommendations:\n$reportString");
    return reportString;
  }
}

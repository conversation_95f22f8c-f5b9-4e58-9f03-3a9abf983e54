import 'package:get_storage/get_storage.dart';
import 'package:playlet/utils/safe_storage.dart';

class NotificationStore {
  static final SafeStorage _storage = SafeStorage();
  static const String showCustomNotificationCount = "showCustomNotificationCount";
  static const String lastShowCustomNotificationDate = "lastShowCustomNotificationDate";

  static int? getShowCustomNotificationCount() {
    return _storage.read(showCustomNotificationCount);
  }

  static Future<void> setShowCustomNotificationCount(int count) async {
    await _storage.write(showCustomNotificationCount, count);
  }

  // 获取上次显示的日期
  static String? getLastShowCustomNotificationDate() {
    return _storage.read(lastShowCustomNotificationDate);
  }

  // 保存当前显示日期
  static Future<void> saveCurrentShowCustomNotificationDate() async {
    String today = DateTime.now().toString().split(' ')[0]; // 格式：yyyy-MM-dd
    await _storage.write(lastShowCustomNotificationDate, today);
  }

  // 判断今天是否已经显示过
  static bool isShowedCustomNotificationToday() {
    String? lastDate = getLastShowCustomNotificationDate();
    if (lastDate == null) return false;
    
    String today = DateTime.now().toString().split(' ')[0];
    return lastDate == today;
  }
}

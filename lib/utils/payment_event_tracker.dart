import 'dart:collection';
import '../common/log/ff_log.dart';

/// 支付事件追踪器，用于防止重复埋点和调试支付流程
class PaymentEventTracker {
  static const String tag = "PaymentEventTracker";
  
  // 存储已触发的事件，key为订单号或skuId，value为事件类型列表
  static final Map<String, Set<String>> _triggeredEvents = {};
  
  // 存储事件触发的时间戳
  static final Map<String, Map<String, int>> _eventTimestamps = {};
  
  // 最大保存的事件记录数量
  static const int maxRecords = 1000;
  
  /// 记录事件触发
  /// [orderId] 订单ID或skuId
  /// [eventType] 事件类型 (order_create, pay_success, order_create_cancel, order_create_fail)
  /// [additionalInfo] 额外信息用于调试
  static void recordEvent(String orderId, String eventType, {String? additionalInfo}) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    // 初始化记录
    _triggeredEvents.putIfAbsent(orderId, () => <String>{});
    _eventTimestamps.putIfAbsent(orderId, () => <String, int>{});
    
    // 检查是否重复触发
    if (_triggeredEvents[orderId]!.contains(eventType)) {
      final lastTimestamp = _eventTimestamps[orderId]![eventType] ?? 0;
      final timeDiff = timestamp - lastTimestamp;
      
      FFLog.error("$tag recordEvent -> DUPLICATE EVENT DETECTED! orderId=$orderId, eventType=$eventType, timeDiff=${timeDiff}ms, additionalInfo=$additionalInfo");
      
      // 如果是短时间内的重复事件（小于5秒），可能是bug
      if (timeDiff < 5000) {
        FFLog.error("$tag recordEvent -> POTENTIAL BUG: Duplicate event within 5 seconds!");
      }
    }
    
    // 记录事件
    _triggeredEvents[orderId]!.add(eventType);
    _eventTimestamps[orderId]![eventType] = timestamp;
    
    FFLog.info("$tag recordEvent -> Event recorded: orderId=$orderId, eventType=$eventType, additionalInfo=$additionalInfo");
    
    // 清理旧记录
    _cleanupOldRecords();
  }
  
  /// 检查事件序列是否合理
  /// [orderId] 订单ID
  static void validateEventSequence(String orderId) {
    final events = _triggeredEvents[orderId];
    if (events == null || events.isEmpty) {
      return;
    }
    
    final hasOrderCreate = events.contains('order_create');
    final hasPaySuccess = events.contains('pay_success');
    final hasOrderCancel = events.contains('order_create_cancel');
    final hasOrderFail = events.contains('order_create_fail');
    
    // 检查逻辑异常
    if ((hasPaySuccess || hasOrderCancel || hasOrderFail) && !hasOrderCreate) {
      FFLog.error("$tag validateEventSequence -> LOGIC ERROR: orderId=$orderId has result events but no order_create event! events=$events");
    }
    
    if (hasPaySuccess && (hasOrderCancel || hasOrderFail)) {
      FFLog.error("$tag validateEventSequence -> LOGIC ERROR: orderId=$orderId has both success and failure events! events=$events");
    }
    
    FFLog.info("$tag validateEventSequence -> Event sequence for orderId=$orderId: $events");
  }
  
  /// 获取订单的所有事件
  static Set<String>? getOrderEvents(String orderId) {
    return _triggeredEvents[orderId];
  }
  
  /// 获取统计信息
  static Map<String, dynamic> getStatistics() {
    final stats = <String, int>{
      'order_create': 0,
      'pay_success': 0,
      'order_create_cancel': 0,
      'order_create_fail': 0,
    };
    
    for (final events in _triggeredEvents.values) {
      for (final event in events) {
        stats[event] = (stats[event] ?? 0) + 1;
      }
    }
    
    final totalOrders = _triggeredEvents.length;
    final orderCreateCount = stats['order_create'] ?? 0;
    final resultEventsCount = (stats['pay_success'] ?? 0) + 
                             (stats['order_create_cancel'] ?? 0) + 
                             (stats['order_create_fail'] ?? 0);
    
    return {
      'totalOrders': totalOrders,
      'eventCounts': stats,
      'orderCreateCount': orderCreateCount,
      'resultEventsCount': resultEventsCount,
      'hasLogicError': resultEventsCount > orderCreateCount,
    };
  }
  
  /// 打印统计信息
  static void printStatistics() {
    final stats = getStatistics();
    FFLog.info("$tag printStatistics -> Payment Event Statistics:");
    FFLog.info("$tag printStatistics -> Total Orders: ${stats['totalOrders']}");
    FFLog.info("$tag printStatistics -> Event Counts: ${stats['eventCounts']}");
    FFLog.info("$tag printStatistics -> Order Create: ${stats['orderCreateCount']}");
    FFLog.info("$tag printStatistics -> Result Events: ${stats['resultEventsCount']}");
    FFLog.info("$tag printStatistics -> Has Logic Error: ${stats['hasLogicError']}");
    
    if (stats['hasLogicError'] == true) {
      FFLog.error("$tag printStatistics -> CRITICAL: Result events count exceeds order create count!");
    }
  }
  
  /// 清理旧记录
  static void _cleanupOldRecords() {
    if (_triggeredEvents.length > maxRecords) {
      final keysToRemove = _triggeredEvents.keys.take(_triggeredEvents.length - maxRecords).toList();
      for (final key in keysToRemove) {
        _triggeredEvents.remove(key);
        _eventTimestamps.remove(key);
      }
      FFLog.info("$tag _cleanupOldRecords -> Cleaned up ${keysToRemove.length} old records");
    }
  }
  
  /// 清除所有记录
  static void clearAll() {
    _triggeredEvents.clear();
    _eventTimestamps.clear();
    FFLog.info("$tag clearAll -> All event records cleared");
  }
}

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:playlet/utils/safe_storage.dart';

import '../service/user_service.dart';

/// 一天展示一次
class LimitedTime {
  ///修改为拼接用户id
  // static String getStorageKey() = "limited_time_show";
  //
  // static String getStorageLimitKey() = "limited_time_end";

  static DateFormat format = DateFormat('yyyy-dd-MM');

  static SafeStorage storage = SafeStorage();

  // 初次显示持久化截止时间多少秒
  static bool setFirstPopup(int time, String skuId, String skuProductId) {
    String? date = storage.read(getLimitedStartKey());
    if (date != null && date == format.format(DateTime.now())) {
      Get.log('LimitedTime setFirstPopup 当天时间已经设置过了');
      return false;
    }
    // 获取当前时间戳，毫秒
    int newTime = DateTime.now().millisecondsSinceEpoch;
    // 截止时间时间戳
    int endTime = newTime + time * 1000;
    storage.write(getLimitedStartKey(), format.format(DateTime.now()));
    storage.write(getLimitedEndKey(), endTime);
    storage.write(getStorageSkuIdKey(), skuId);
    storage.write(getStorageSkuProductIdKey(), skuProductId);
    Get.log('LimitedTime setFirstPopup: $endTime');
    return true;
  }

  ///查询弹窗，商品页面中是否展示
  ///本地没有存储的时间戳则返回true
  ///没有到截止时间则返回true
  ///否则返回false
  static bool getPopupShow() {
    int? endTime = storage.read(getLimitedEndKey());
    String? date = storage.read(getLimitedStartKey());
    if (date != null) {
      if (date != format.format(DateTime.now())) {
        storage.remove(getLimitedEndKey());
        storage.remove(getLimitedStartKey());
        Get.log('LimitedTime getPopupShow 当天不存在: $endTime');
        return false;
      }
    }
    if (endTime == null) {
      Get.log('LimitedTime getPopupShow 时间不存在: $endTime');
      return false;
    }
    int newTime = DateTime.now().millisecondsSinceEpoch;
    if (newTime < endTime) {
      Get.log('LimitedTime getPopupShow 今天并且倒计时未过期: $endTime');
      return true;
    } else {
      Get.log('LimitedTime getPopupShow 今天并且倒计时已过期: $endTime');
      return false;
    }
  }

  static bool getLimitedTimePopupShow() {
    int? endTime = storage.read(getLimitedEndKey());
    String? date = storage.read(getLimitedStartKey());
    if (endTime == null || date == null) {
      return true;
    }
    if (date != format.format(DateTime.now())) {
      return true;
    }
    return false;
  }

  //关闭当天展示
  static closeThatDayShow() async {
    await storage.write(getLimitedEndKey(), 0);
  }

  ///获取剩余时间
  ///返回剩余时间，单位秒
  static int getLeftTime() {
    int? endTime = storage.read(getLimitedEndKey());
    if (endTime == null) {
      return 0;
    }
    int newTime = DateTime.now().millisecondsSinceEpoch;
    int leftTime = endTime - newTime;
    if (leftTime < 0) {
      return 0;
    }
    Get.log('LimitedTime getLeftTime 返回倒计时时间: ${(leftTime / 1000).toInt()}');
    return (leftTime / 1000).toInt();
  }

  static int? getEndTime() {
    return storage.read(getLimitedEndKey());
  }

  /// 获取sku id
  static String? getSkuId() {
    return storage.read(getStorageSkuIdKey());
  }

  /// 获取sku product id
  static String? getSkuProductId() {
    return storage.read(getStorageSkuProductIdKey());
  }

  //开始时间key  拼接用户id
  static String getLimitedStartKey() {
    String userId = Get.find<UserService>().userInfo.value?.userId ?? "";
    return "limited_time_show_$userId";
  }

  //结束时间key  拼接用户id
  static String getLimitedEndKey() {
    String userId = Get.find<UserService>().userInfo.value?.userId ?? "";
    return "limited_time_end_$userId";
  }

  /// skuid  拼接用户id
  static String getStorageSkuIdKey() {
    String userId = Get.find<UserService>().userInfo.value?.userId ?? "";
    return "limited_sku_id_$userId";
  }

  /// sku product id  拼接用户id
  static String getStorageSkuProductIdKey() {
    String userId = Get.find<UserService>().userInfo.value?.userId ?? "";
    return "limited_sku_product_id_$userId";
  }
}

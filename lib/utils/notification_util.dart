import 'dart:convert';
import 'dart:io';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/notification/android_notification_manager.dart';
import 'package:playlet/utils/events.dart';

import 'app_analysis.dart';

class NotificationUtil {
  static final NotificationUtil instance = NotificationUtil._internal();

  factory NotificationUtil() => instance;

  NotificationUtil._internal();

  final FlutterLocalNotificationsPlugin notificationsPlugin = FlutterLocalNotificationsPlugin();
  final androidChanelId = "flareflow";
  final androidChanelName = "flareflow android";
  final androidChanelDescription = "flareflow for notifications.";
  static bool _isForeground = false;

  init() async {
// 合并配置
    InitializationSettings settings = const InitializationSettings(
      // Android 初始化配置
      android: AndroidInitializationSettings('@drawable/ic_notification'),
      // iOS 初始化配置
      iOS: DarwinInitializationSettings(
        requestSoundPermission: false,
        requestBadgePermission: false,
        requestAlertPermission: false,
      ),
    );

    // 为Android创建通知渠道
    if (Platform.isAndroid) {
      AndroidNotificationChannel channel = AndroidNotificationChannel(
        androidChanelId,
        androidChanelName,
        description: androidChanelDescription,
        importance: Importance.high,
      );

      await notificationsPlugin.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()?.createNotificationChannel(channel);
    }

    // 检查应用是否通过通知启动
    await _checkAppLaunchFromNotification();

    // 初始化插件
    await notificationsPlugin.initialize(
      settings,
      onDidReceiveNotificationResponse: (NotificationResponse notificationResponse) async {
        try {
          final String? payload = notificationResponse.payload;
          if (payload != null) {
            FFLog.info('通知点击事件，payload: $payload', tag: "NotificationUtil");
            var message = jsonDecode(payload);
            // 通过发送事件 EventBus
            eventBus.fire(BackgroundNotificationClickEvent(data: message));
            if (!_isForeground) {
              // 前后台切换时保存
              _saveAppLaunchFrom(message);
            }
          }
        } catch (e) {
          FFLog.error('notificationsPlugin，通知栏点击时，数据转换错误: $e', tag: "NotificationUtil");
        }
      },
    );
  }

  /// 保存启动来源
  void _saveAppLaunchFrom(Map<String, dynamic> data){
    var from = EventValue.push.toString();
    var notificationType = data["data"]?["notificationType"] ?? "";
    if (notificationType == AndroidNotificationManager.notificationPermanent) {
      from = EventValue.permanent;
    } else if (notificationType == AndroidNotificationManager.notificationFsi) {
      from = EventValue.fsi;
    }
    AppAnalysisStore.saveAppLaunchFrom(from);
  }

  // 检查应用是否通过通知启动
  Future<void> _checkAppLaunchFromNotification() async {
    final NotificationAppLaunchDetails? launchDetails = await notificationsPlugin.getNotificationAppLaunchDetails();
    if (launchDetails != null && launchDetails.didNotificationLaunchApp) {
      FFLog.info('应用通过通知启动，payload: ${launchDetails.notificationResponse?.payload}', tag: "NotificationUtil");
      if (launchDetails.notificationResponse != null) {
        try {
          final String? payload = launchDetails.notificationResponse?.payload;
          if (payload != null) {
            var message = jsonDecode(payload);
            // 冷启动时，保存启动来源
            _saveAppLaunchFrom(message);
            // 通过发送事件 EventBus
            eventBus.fire(BackgroundNotificationClickEvent(data: message));
          }
        } catch (e) {
          FFLog.error('notificationsPlugin，应用通过通知启动时，数据转换错误: $e', tag: "NotificationUtil");
        }
      }
    }
  }

  // 发送本地通知
  sendNotification(int id, String? title, String? body, String? payload) {
    notificationsPlugin.show(
      id,
      title,
      body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          androidChanelName,
          androidChanelDescription,
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: payload,
    );
  }

  // 校验全屏通知
  checkFullScreenIntentPermission() {
    notificationsPlugin.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()?.requestFullScreenIntentPermission();
  }

  static void onForeground() {
    _isForeground = true;
  }

  static void onBackground() {
    _isForeground = false;
  }
}

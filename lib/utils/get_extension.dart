import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:playlet/common/color/color_hex.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/loading/ffloading.dart';
import 'package:playlet/components/widget/login_dialog_widget.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/utils/track_event.dart';
import 'package:toastification/toastification.dart';

extension GetExtension on GetInterface {
  /// 关闭loading
  dismiss() async {
    await SmartDialog.dismiss(status: SmartStatus.loading);
  }

  /// 显示loading
  loading() async {
    await SmartDialog.showLoading(
        builder: (context) => const FFLoadingWidget());
  }

  loadingDot() async {
    await SmartDialog.showLoading(
        maskColor: Colors.transparent,
        builder: (context) => FFLoadingWidget(
              icon: Assets.loading.dotLoading.path,
            ));
  }

  /// 显示登录弹窗
  Future<void> loginDialog({
    required String from,
    required String fbRewardsTrackFrom,
  }) async {
    useTrackEvent(EventName.login_windows_show, extra: {EventKey.from: from});
    await SmartDialog.show(
      alignment: Alignment.bottomCenter,
      builder: (context) => SafeArea(
        child: LoginDialogWidget(
          trackScene: from,
          fbRewardsTrackFrom: fbRewardsTrackFrom,
        ),
      ),
    );
  }

  /// 显示toast
  /// 新增返回值,不用的话,不需要处理
 ToastificationItem toast(String message, {EdgeInsetsGeometry? margin, EdgeInsetsGeometry? padding, Alignment? alignment, Duration? duration, Color? background}) {
    return toastification.show(
      style: ToastificationStyle.simple,
      title: Text(
        message,
        maxLines: 3,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 14.sp,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
      alignment: alignment ?? Alignment.bottomCenter,
      context: Get.context,
      borderSide: BorderSide.none,
      dragToClose: false,
      autoCloseDuration: duration?? const Duration(milliseconds: 1800),
      margin:
          margin ?? EdgeInsets.only(bottom: 40.sp, left: 42.sp, right: 42.sp),
      padding: padding ?? EdgeInsets.all(20.sp),
      borderRadius: BorderRadius.circular(10.r),
      backgroundColor: background ?? const Color.fromRGBO(5, 5, 5, 0.5),
    );
  }

  /// 关闭toast提示
  toastDismiss({ToastificationItem? item, bool animation = false}) {
    if (item != null) {
      toastification.dismiss(item, showRemoveAnimation: animation);
    } else {
      toastification.dismissAll();
    }
  }

  /// 确保控制器已被初始化
  ///
  /// 如果控制器未注册，则使用 [Get.put] 注册它并设置为永久保留
  ///
  /// 参数:
  /// - [T]: 控制器类型
  /// - [create]: 创建控制器的函数
  /// - [tag]: 可选的标签，用于区分同一类型的多个控制器
  /// - [pageName]: 页面名称，用于日志记录
  ///
  /// 返回:
  /// - 控制器实例
  ensureController<T>(
    T Function() create, {
    String pageName = 'unknown',
  }) {
    try {
      if (!isRegistered<T>()) {
        FFLog.error('${T.toString()} 未注册 ,pageName=$pageName，正在注册...',
            tag: pageName);
        return put<T>(create(), permanent: true);
      }
      return find<T>();
    } catch (e) {
      FFLog.error('${T.toString()} 注册失败 ,pageName=$pageName: $e ',
          tag: pageName);
    }
  }
}

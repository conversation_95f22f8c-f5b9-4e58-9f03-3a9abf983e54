import 'package:get_storage/get_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/utils/safe_storage.dart';
import 'package:playlet/utils/track_event.dart';

class AppAnalysisStore {
  static final SafeStorage _storage = SafeStorage();
  static const String appLaunchFrom = "appLaunchFrom";
  static const String lastAppVersion = "lastAppVersion";
  static const String lastAppUpdateType = "lastAppUpdateType";

  // 获取启动app来源
  static String getAppLaunchFrom() {
    return _storage.read<String>(appLaunchFrom) ?? EventValue.icon.toString();
  }

  // 保存启动app来源
  static Future<void> saveAppLaunchFrom(String from) async {
    await _storage.write(appLaunchFrom, from);
  }

  // 获取上个版本号
  static String getLastAppVersion() {
    return _storage.read<String>(lastAppVersion) ?? "";
  }

  // 保存上个版本号
  static Future<void> saveLastAppVersion(String version) async {
    await _storage.write(lastAppVersion, version);
  }

  // 获取上个版本更新类型
  static String getLastAppUpdateType() {
    return _storage.read<String>(lastAppUpdateType) ?? "";
  }

  // 保存上个版本更新类型
  static Future<void> saveLastAppUpdateType(String type) async {
    await _storage.write(lastAppUpdateType, type);
  }

  static Future<void> doSomethingIfUpdateFromOldVersion() async {
    final oldAppVersion = getLastAppVersion();
    if (oldAppVersion.isEmpty) return;
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    if (oldAppVersion == packageInfo.version) return;
    useTrackEvent(EventName.update_success,
        extra: {EventKey.type: getLastAppUpdateType()});
    saveLastAppVersion(packageInfo.version);
  }
}

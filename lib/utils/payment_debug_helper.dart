import 'dart:async';
import '../common/log/ff_log.dart';
import 'payment_event_tracker.dart';

/// 支付调试助手，用于监控和分析支付流程
class PaymentDebugHelper {
  static const String tag = "PaymentDebugHelper";
  
  static Timer? _periodicTimer;
  static bool _isMonitoring = false;
  
  /// 开始监控支付事件
  /// [intervalSeconds] 监控间隔（秒）
  static void startMonitoring({int intervalSeconds = 30}) {
    if (_isMonitoring) {
      FFLog.info("$tag startMonitoring -> Already monitoring");
      return;
    }
    
    _isMonitoring = true;
    FFLog.info("$tag startMonitoring -> Starting payment event monitoring, interval=${intervalSeconds}s");
    
    _periodicTimer = Timer.periodic(Duration(seconds: intervalSeconds), (timer) {
      _checkPaymentEventHealth();
    });
  }
  
  /// 停止监控
  static void stopMonitoring() {
    if (!_isMonitoring) {
      return;
    }
    
    _isMonitoring = false;
    _periodicTimer?.cancel();
    _periodicTimer = null;
    FFLog.info("$tag stopMonitoring -> Payment event monitoring stopped");
  }
  
  /// 检查支付事件健康状态
  static void _checkPaymentEventHealth() {
    final stats = PaymentEventTracker.getStatistics();
    final orderCreateCount = stats['orderCreateCount'] as int;
    final resultEventsCount = stats['resultEventsCount'] as int;
    final hasLogicError = stats['hasLogicError'] as bool;
    
    FFLog.info("$tag _checkPaymentEventHealth -> Health check: orderCreate=$orderCreateCount, resultEvents=$resultEventsCount, hasError=$hasLogicError");
    
    if (hasLogicError) {
      FFLog.error("$tag _checkPaymentEventHealth -> CRITICAL: Payment event logic error detected!");
      _analyzeLogicError(stats);
    }
    
    // 打印详细统计
    PaymentEventTracker.printStatistics();
  }
  
  /// 分析逻辑错误
  static void _analyzeLogicError(Map<String, dynamic> stats) {
    final eventCounts = stats['eventCounts'] as Map<String, int>;
    final orderCreateCount = eventCounts['order_create'] ?? 0;
    final paySuccessCount = eventCounts['pay_success'] ?? 0;
    final orderCancelCount = eventCounts['order_create_cancel'] ?? 0;
    final orderFailCount = eventCounts['order_create_fail'] ?? 0;
    
    FFLog.error("$tag _analyzeLogicError -> Event breakdown:");
    FFLog.error("$tag _analyzeLogicError -> order_create: $orderCreateCount");
    FFLog.error("$tag _analyzeLogicError -> pay_success: $paySuccessCount");
    FFLog.error("$tag _analyzeLogicError -> order_create_cancel: $orderCancelCount");
    FFLog.error("$tag _analyzeLogicError -> order_create_fail: $orderFailCount");
    
    final totalResultEvents = paySuccessCount + orderCancelCount + orderFailCount;
    final difference = totalResultEvents - orderCreateCount;
    
    FFLog.error("$tag _analyzeLogicError -> Total result events: $totalResultEvents");
    FFLog.error("$tag _analyzeLogicError -> Difference: $difference");
    
    if (paySuccessCount > orderCreateCount) {
      FFLog.error("$tag _analyzeLogicError -> ISSUE: pay_success ($paySuccessCount) > order_create ($orderCreateCount)");
      FFLog.error("$tag _analyzeLogicError -> Possible causes: 1) Restore purchases triggering pay_success without order_create 2) Duplicate pay_success events");
    }
    
    if (orderCancelCount > orderCreateCount) {
      FFLog.error("$tag _analyzeLogicError -> ISSUE: order_create_cancel ($orderCancelCount) > order_create ($orderCreateCount)");
      FFLog.error("$tag _analyzeLogicError -> Possible causes: 1) Cancel events from previous sessions 2) Duplicate cancel events");
    }
    
    if (orderFailCount > orderCreateCount) {
      FFLog.error("$tag _analyzeLogicError -> ISSUE: order_create_fail ($orderFailCount) > order_create ($orderCreateCount)");
      FFLog.error("$tag _analyzeLogicError -> Possible causes: 1) Fail events from previous sessions 2) Duplicate fail events");
    }
  }
  
  /// 模拟支付流程测试
  static void simulatePaymentFlow() {
    FFLog.info("$tag simulatePaymentFlow -> Starting payment flow simulation");
    
    // 模拟正常流程
    PaymentEventTracker.recordEvent("test_order_1", "order_create", additionalInfo: "simulation_normal");
    PaymentEventTracker.recordEvent("test_order_1", "pay_success", additionalInfo: "simulation_normal");
    
    // 模拟取消流程
    PaymentEventTracker.recordEvent("test_order_2", "order_create", additionalInfo: "simulation_cancel");
    PaymentEventTracker.recordEvent("test_order_2", "order_create_cancel", additionalInfo: "simulation_cancel");
    
    // 模拟失败流程
    PaymentEventTracker.recordEvent("test_order_3", "order_create", additionalInfo: "simulation_fail");
    PaymentEventTracker.recordEvent("test_order_3", "order_create_fail", additionalInfo: "simulation_fail");
    
    // 模拟异常流程（没有order_create的pay_success）
    PaymentEventTracker.recordEvent("test_order_4", "pay_success", additionalInfo: "simulation_anomaly");
    
    FFLog.info("$tag simulatePaymentFlow -> Simulation completed");
    PaymentEventTracker.printStatistics();
  }
  
  /// 生成支付事件报告
  static String generateReport() {
    final stats = PaymentEventTracker.getStatistics();
    final eventCounts = stats['eventCounts'] as Map<String, int>;
    
    final report = StringBuffer();
    report.writeln("=== Payment Event Report ===");
    report.writeln("Generated at: ${DateTime.now()}");
    report.writeln("Total Orders: ${stats['totalOrders']}");
    report.writeln("Order Create Count: ${stats['orderCreateCount']}");
    report.writeln("Result Events Count: ${stats['resultEventsCount']}");
    report.writeln("Has Logic Error: ${stats['hasLogicError']}");
    report.writeln("");
    report.writeln("Event Breakdown:");
    eventCounts.forEach((event, count) {
      report.writeln("  $event: $count");
    });
    
    if (stats['hasLogicError'] == true) {
      report.writeln("");
      report.writeln("⚠️ CRITICAL ISSUES DETECTED:");
      final orderCreateCount = eventCounts['order_create'] ?? 0;
      final paySuccessCount = eventCounts['pay_success'] ?? 0;
      final orderCancelCount = eventCounts['order_create_cancel'] ?? 0;
      final orderFailCount = eventCounts['order_create_fail'] ?? 0;
      
      if (paySuccessCount > orderCreateCount) {
        report.writeln("  - pay_success events exceed order_create events");
      }
      if (orderCancelCount > orderCreateCount) {
        report.writeln("  - order_create_cancel events exceed order_create events");
      }
      if (orderFailCount > orderCreateCount) {
        report.writeln("  - order_create_fail events exceed order_create events");
      }
    }
    
    report.writeln("=== End Report ===");
    
    final reportString = report.toString();
    FFLog.info("$tag generateReport -> Report generated:\n$reportString");
    return reportString;
  }
  
  /// 重置所有统计数据
  static void resetStatistics() {
    PaymentEventTracker.clearAll();
    FFLog.info("$tag resetStatistics -> All payment event statistics reset");
  }
  
  /// 检查是否正在监控
  static bool get isMonitoring => _isMonitoring;
}

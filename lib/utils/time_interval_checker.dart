import 'package:playlet/utils/safe_storage.dart';

/// 时间持久化与间隔判断类
/// 用于存储时间点并判断是否超过指定的时间间隔
class TimeIntervalChecker {
  final SafeStorage storage = SafeStorage();

  /// 存储键前缀，用于在 SafeStorage 中区分不同的时间记录
  final String keyPrefix;

  /// 构造函数
  /// [keyPrefix] 存储键前缀，用于区分不同的时间记录
  TimeIntervalChecker({this.keyPrefix = 'time_interval_'});

  /// 保存当前时间
  /// [key] 时间记录的唯一标识
  Future<void> saveCurrentTime(String key) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    await storage.write('$keyPrefix$key', now);
  }

  /// 获取保存的时间
  /// [key] 时间记录的唯一标识
  /// 返回保存的时间戳，如果不存在则返回 null
  Future<int?> getSavedTime(String key) async {
    return storage.read('$keyPrefix$key');
  }

  /// 清除保存的时间
  /// [key] 时间记录的唯一标识
  Future<void> clearSavedTime(String key) async {
    await storage.remove('$keyPrefix$key');
  }

  /// 判断是否超过指定的时间间隔
  /// [key] 时间记录的唯一标识
  /// [intervalInSeconds] 时间间隔（秒）
  /// 返回是否超过指定的时间间隔，如果没有保存过时间则返回 true
  Future<bool> isIntervalExceeded(String key, int intervalInSeconds) async {
    final savedTime = await getSavedTime(key);

    // 如果没有保存过时间，则视为已超过间隔
    if (savedTime == null) {
      return true;
    }

    final now = DateTime.now().millisecondsSinceEpoch;
    final elapsedMilliseconds = now - savedTime;
    final elapsedSeconds = elapsedMilliseconds ~/ 1000;

    return elapsedSeconds >= intervalInSeconds;
  }

  /// 判断是否超过指定的时间间隔，如果超过则更新时间
  /// [key] 时间记录的唯一标识
  /// [intervalInSeconds] 时间间隔（秒）
  /// 返回是否超过指定的时间间隔
  Future<bool> checkAndUpdateInterval(String key, int intervalInSeconds) async {
    final isExceeded = await isIntervalExceeded(key, intervalInSeconds);

    if (isExceeded) {
      await saveCurrentTime(key);
    }

    return isExceeded;
  }
}

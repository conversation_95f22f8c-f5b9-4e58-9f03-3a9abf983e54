import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/firebase_options.dart';
import 'package:playlet/service/app_device_service.dart';

class FirebaseUtil {
  static const String _tag = "FirebaseUtil";
  static bool _isInitialized = false;
  static String? _lastSetUserId;

  static Future<void> _initFirebaseCrashlytics() async {
    if (_isInitialized) return;

    try {
      // 1. 确保 Firebase 已初始化
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // 2. 配置 Crashlytics
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);

      // 注意：错误处理设置已移至 runAppWithCrashlytics 方法中

      _isInitialized = true;
      FFLog.info('Firebase初始化成功', tag: _tag);
    } catch (e) {
      FFLog.error('Firebase初始化失败: $e', tag: _tag);
      rethrow;
    }
  }

  /// 判断错误是否应该被标记为致命错误
  ///
  /// [error] 错误对象
  /// [stack] 堆栈信息
  ///
  /// 返回 true 表示应该被标记为致命错误，false 表示不应该
  static bool _shouldMarkAsFatal(dynamic error, StackTrace stack) {
    // 处理空值情况
    if (error == null) return false;

    // 获取错误信息
    final errorMessage =
        error.toString().toLowerCase().replaceAll(RegExp(r'\s+'), '');
    final Type errorType = error.runtimeType;

    // 错误类型检查
    if (errorMessage.contains('outofmemory') ||
        errorMessage.contains('heapmemoryexceeded')) {
      FFLog.error('检测到致命错误($errorType): ${error.toString()}', tag: _tag);
      return true;
    }

    // 堆栈分析
    final stackStr = stack.toString().toLowerCase();
    if (stackStr.contains('package:flutter/') &&
        stackStr.contains('assertion failed')) {
      FFLog.error('检测到致命断言错误: ${error.toString()}', tag: _tag);
      return true;
    }

    // 默认情况
    FFLog.info('非致命错误($errorType): ${error.toString()}', tag: _tag);
    return false;
  }

  /// 记录错误到Crashlytics
  ///
  /// [error] 错误对象
  /// [stack] 堆栈信息
  /// [errorSource] 错误来源
  /// [details] Flutter错误详情，如果有的话
  static void recordError(dynamic error, StackTrace stack, String errorSource,
      {FlutterErrorDetails? details}) {
    // 判断是否为致命错误
    final bool isFatal = _shouldMarkAsFatal(error, stack);

    // 如果是Flutter错误，始终记录到控制台
    if (details != null) {
      FlutterError.dumpErrorToConsole(details);
    }

    // 打印日志
    final logFunc = isFatal ? FFLog.error : FFLog.warning;
    logFunc('recordError -> $errorSource${isFatal ? "致命" : "非致命"}错误: $error',
        tag: _tag);

    if (!_isInitialized) {
      FFLog.warning('Firebase未初始化，无法记录$errorSource错误: $error', tag: _tag);
      return;
    }

    // 记录错误
    if (details != null) {
      isFatal
          ? FirebaseCrashlytics.instance.recordFlutterFatalError(details)
          : FirebaseCrashlytics.instance.recordFlutterError(details);
    } else {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: isFatal);
    }
  }

  /// 设置崩溃处理逻辑
  ///
  /// 配置 Flutter 错误处理和平台错误处理
  static Future<void> setupCrashlyticsHandler() async {
    await _initFirebaseCrashlytics();

    // 设置Flutter错误处理
    FlutterError.onError = (FlutterErrorDetails details) {
      recordError(
          details.exception, details.stack ?? StackTrace.current, "Flutter",
          details: details);
    };

    // 设置平台错误处理
    PlatformDispatcher.instance.onError = (error, stack) {
      recordError(error, stack, "平台");
      return true;
    };
  }

  static Future<void> setUserId(String inUserId) async {
    try {
      final deviceModel = await AppDeviceService.instance.getDeviceModel();
      final userIdentifier = '$inUserId-$deviceModel';

      if (!_isInitialized) {
        FFLog.debug('Firebase未初始化，无法设置userId:$userIdentifier', tag: _tag);
        return;
      }

      // 如果是相同的userId，跳过设置
      if (_lastSetUserId == userIdentifier) {
        FFLog.info('userId: $userIdentifier 已经设置过，跳过', tag: _tag);
        return;
      }
      // 设置 Crashlytics 用户ID
      await FirebaseCrashlytics.instance.setUserIdentifier(userIdentifier);

      // 设置 Analytics 用户ID
      await FirebaseAnalytics.instance.setUserId(id: userIdentifier);

      _lastSetUserId = userIdentifier;
      FFLog.info('设置userId成功: $userIdentifier', tag: _tag);
    } catch (e) {
      FFLog.error('设置userId失败: $e', tag: _tag);
    }
  }
}

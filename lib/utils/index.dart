import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/model/webview.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/i18n/trans.dart';

class Utils {
  static String getAssetsUrl(String url, String mediaDomain) {
    if (url.contains("http")) {
      return url;
    } else {
      if (url.startsWith("/")) {
        return "$mediaDomain$url";
      }
      return "$mediaDomain/$url";
    }
  }

  static String getVideoUrl(String url, String? cdn) {
    if (url.contains("http")) {
      return url;
    } else {
      if (url.startsWith("/")) {
        return "$cdn$url";
      }
      return "$cdn/$url";
    }
  }

  static openUserAgreement() {
    AppNavigator.startWebViewPage(
      WebviewModel(
        title: AppTrans.userAgreement(),
        url: Config.userAgreement,
      ),
    );
  }

  static openPrivacyPolicy() {
    AppNavigator.startWebViewPage(
      WebviewModel(
        title: AppTrans.privacyPolicy(),
        url: Config.privacyPolicy,
      ),
    );
  }

  static openRechargeAgreement() {
    AppNavigator.startWebViewPage(
      WebviewModel(
        title: AppTrans.rechargeAgreement(),
        url: Config.rechargeAgreement,
      ),
    );
  }

  static String formatNumberK(int? number) {
    if (number == null) {
      return '0';
    }
    if (number >= 1000) {
      double result = number / 1000;
      return '${result.toStringAsFixed(1)}K'; // 保留一位小数
    } else {
      return number.toString();
    }
  }

  static String getImageRatioScale(String url,
      {double? width, double? height, double scale = 1.5}) {
    double newWidth = 0;
    double newHeight = 0;
    if (width != null && height != null) {
      newWidth = width * scale;
      newHeight = height * scale;
    } else {
      Size size = getDefaultRatioScale();
      newWidth = size.width.toDouble();
      newHeight = size.height.toDouble();
    }
    if (url.contains("?")) {
      return "$url&x-oss-process=1/m_fill,w_${newWidth.toInt()},h_${newHeight.toInt()}";
    }
    return "$url?x-oss-process=1/m_fill,w_${newWidth.toInt()},h_${newHeight.toInt()}";
  }

  static getDefaultRatioScale() {
    final width = Get.width * 0.7;
    return Size(width, width * 16 / 9);
  }
}

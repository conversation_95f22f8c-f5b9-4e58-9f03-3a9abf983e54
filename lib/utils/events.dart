import 'package:event_bus/event_bus.dart';
import 'package:playlet/model/login.dart';
import 'package:playlet/model/reels.dart';
import 'package:playlet/modules/details/details_controller.dart';

/*

## eventBus使用示例：
1.定义event数据类 InitLoginEventData 如下：
2.发出event
  eventBus.fire(InitLoginEventData(userInfo: userInfo));
3.定义event订阅
  late final StreamSubscription eventSubscription;
4.初始化并监听event订阅
  activeSubscription = eventBus.on<InitLoginEventData>().listen((event) {
    ///todo: Do Something
  });

*/

EventBus eventBus = EventBus();

class InitLoginEventData {
  InitLoginEventData({required this.userInfo});
  final UserResponse userInfo;
}

// // 触发选中通知弹框
// class SelectNotificationEventData {
//   SelectNotificationEventData({required this.type});
//   // 1 免费聚集上新，2 充值优化活动提醒，3 每日签到提醒
//   final int type;
// }

// 后台触发的通知栏点击
class BackgroundNotificationClickEvent {
  BackgroundNotificationClickEvent({required this.data});
  final Map<String, dynamic> data;
}

class PushNotificationToReelEventData {
  PushNotificationToReelEventData({
    required this.shortPlayId,
    required this.dramaId,
  });
  final String shortPlayId;
  final String dramaId;
}

class PushNotificationToReelShortEventData {
  PushNotificationToReelShortEventData({
    required this.shortPlayId,
    required this.dramaId,
    this.reelType = ReelType.shorts,
    this.callback,
  });
  final String shortPlayId;
  final String dramaId;
  ReelType reelType;
  final Function(bool)? callback;
}

class ShortWatchHistoryEvent {
  ShortWatchHistoryEvent({required this.shortPlayId, required this.episodeNum});
  final int? shortPlayId;
  final int? episodeNum;
}

class PlayerStatusChangedEvent {
  final DetailsController detailsController;
  final bool isPlaying;

  PlayerStatusChangedEvent(
      {required this.detailsController, required this.isPlaying});
}

class DetailsPageOpenedEvent {
  final DetailsController detailsController;

  DetailsPageOpenedEvent({required this.detailsController});
}

class DetailsPageClosedEvent {
  final DetailsController detailsController;

  DetailsPageClosedEvent({required this.detailsController});
}

/// 刷新历史观看剧列表
class RefreshWatchHistoryListEvent {
  RefreshWatchHistoryListEvent();
}

extension MapExtension on Map {
  /// 安全获取map中的值
  T? safeGet<T>(String key) {
    final value = this[key];
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    if (T == String) {
      try {
        return value.toString() as T;
      } catch (e) {
        return null;
      }
    }
    if (T == int || T == double || T == bool) {
      String? stringValue = safeGet<String>(key);
      if (stringValue == null) {
        return null;
      }
      try {
        if (T == int) {
          return int.parse(stringValue) as T;
        }
        if (T == double) {
          return double.parse(stringValue) as T;
        }
        if (T == bool) {
          return bool.parse(stringValue) as T;
        }
      } catch (e) {
        return null;
      }
    }
    return null;
  }
}
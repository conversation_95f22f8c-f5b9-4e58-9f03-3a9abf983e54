import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class EmailUtils {
  EmailUtils._();

  static bool checkEmail(String? email) {
    return email != null && email.isNotEmpty && GetUtils.isEmail(email) == true;
  }

  static bool checkBody(String? body) {
    return body != null && body.isNotEmpty;
  }

  static Future<bool?> sendEmail({
    required String to,
    required String subject,
    required String body,
  }) async {
    final Uri uri = Uri(
      scheme: 'mailto',
      path: to,
      query: _encodeQueryParameters(<String, String>{
        'subject': subject,
        'body': body,
      }),
    );
    bool canLaunch = await canLaunchUrl(uri);
    if (canLaunch == true) {
      bool result = await launchUrl(uri);
      if (result == true) {
        return true;
      } else {
        return false;
      }
    } else {
      _openGoogleSignUp();
      return null;
    }
  }

  static String? _encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  static Future<void> _openGoogleSignUp() async {
    // Google邮箱注册页面URL
    const url = 'https://accounts.google.com/signup';
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      // 使用url_launcher打开URL
      await launchUrl(uri);
    } else {
      throw 'Could not launch $url';
    }
  }
}

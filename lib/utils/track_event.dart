import 'dart:async';

import 'package:dlink_analytics/analytics_core.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/service/analytics_service.dart';
import 'package:playlet/service/attribute_service.dart';
import 'package:playlet/utils/attribute_evens_util.dart';

enum TrackEventPriority {
  /// 根据dlink策略上报
  normal(0),
  /// 立即上报
  high(1);
  
  final int value;
  const TrackEventPriority(this.value);

  static TrackEventPriority? fromInt(int? value) {
    if (value == null) {
      return null;
    }
    return TrackEventPriority.values.firstWhereOrNull(
      (type) => type.value == value,
    );
  }
}

/// 埋点事件
/// [event] 事件名称
/// [extra] 事件参数
/// [priority] 事件优先级，TrackEventPriority.high立即上报
void useTrackEvent(String event, {Map<String, String>? extra, TrackEventPriority priority = TrackEventPriority.normal}) {
  FFLog.info('event: $event, extra: $extra', tag: 'TrackEvent');
  AnalyticsCore.instance.log(event, eventParams: extra, priority: priority.value);

  // 使用unawaited来避免阻塞主线程
  unawaited(_sendToAttributionSdk(event, extra));
}

/// 将事件发送到归因SDK的异步方法
Future<void> _sendToAttributionSdk(
    String event, Map<String, String>? extra) async {
  try {
    // 然后进行事件拦截处理
    await AttributeEventsUtil.eventInterceptor(event, extra: extra);
  } catch (e) {
    FFLog.error('Failed to send event to attribution SDK: $e',
        tag: 'TrackEvent');
  }
}

void flushDataOnPageChange() {
  FFLog.info("flushDataOnPageChange");
  // 立即上报埋点
  Get.find<AnalyticsService>().immediatelyReport();
  // 触发归因信息上报
  Get.find<AttributeService>().checkAndReportAttribution();
}
import 'package:intl/intl.dart' as intl;
import 'package:playlet/service/app_device_service.dart';

extension DateExtension on DateTime {
  bool isToday() {
    final dateFormatter = intl.DateFormat(
      'yyyy-MM-dd',
      AppDeviceService.instance.getSystemLanguage(),
    );
    String formatDate = dateFormatter.format(this);
    String formatNow = dateFormatter.format(DateTime.now());
    return formatNow == formatDate;
  }
}

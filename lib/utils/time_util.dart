import 'package:playlet/common/log/ff_log.dart';

class TimeUtil {
  /// 计算当前时间距离指定时间的秒数
  /// [days] 默认为0，表示当前，1表示明天
  /// [targetHour] 默认为6，表示目标小时
  /// [targetMinute] 默认为0，表示目标分钟
  /// [targetSecond] 默认为0，表示目标秒数
  static int getSecondsUntilForTime({int days = 0, int targetHour = -1, int targetMinute = -1, int targetSecond = -1}) {
    final now = DateTime.now();

    // 创建今天目标时间点
    final todayTarget = DateTime(
      now.year,
      now.month,
      now.day + days,
      targetHour >= 0 ? targetHour : now.hour,
      targetMinute >= 0 ? targetMinute : now.minute,
      targetSecond >= 0 ? targetSecond : now.second,
    );
    return todayTarget.difference(now).inSeconds;
  }

  /// 判断给定的毫秒时间戳是否与当前时间在同一天
  /// [milliseconds] 毫秒时间戳
  /// 返回true表示在同一天，false表示不在同一天
  static bool isSameDay(int milliseconds) {
    // 获取当前时间
    final now = DateTime.now();
    // 将毫秒时间戳转换为DateTime对象
    final targetDate = DateTime.fromMillisecondsSinceEpoch(milliseconds);

    // 判断年、月、日是否相同
    return now.year == targetDate.year && now.month == targetDate.month && now.day == targetDate.day;
  }

  /// 当前晚上22点以后或早上6点之前
  static bool isBetween22PmTo6Am() {
    final now = DateTime.now();
    final startTarget = DateTime(now.year, now.month, now.day, 22, 0, 0);
    final endTarget = DateTime(now.year, now.month, now.day, 6, 0, 0);
    return now.isAfter(startTarget) && now.isBefore(endTarget);
  }

  /// 是否超过特定时间
  static bool isOffsetTimeBefore(int? milliseconds, int offsetTime) {
    if (milliseconds == null) {
      return true;
    }
    // 获取当前时间
    final now = DateTime.now();
    // 将毫秒时间戳转换为DateTime对象
    final targetDate = DateTime.fromMillisecondsSinceEpoch(milliseconds);
    var seconds = now.difference(targetDate).inSeconds;
    FFLog.printToAndroidLogcat("PushManager", "seconds:$seconds,offsetTime:$offsetTime", "debug");
    return seconds >= offsetTime;
  }
}

import 'dart:convert';
import 'dart:io';

import 'package:in_app_purchase/in_app_purchase.dart'; // 假设这是引入三方库的方式
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:playlet/utils/safe_storage.dart';

import '../common/log/ff_log.dart';
import '../model/store/in_app_purchase_recovery_data.dart';
import '../service/payment/payment_model.dart';

class ProductStorage {
  static final SafeStorage _storage = SafeStorage();

  static const String reStorageKey = 'reStorageKey';

  static Future<void> saveUserPayment(
      Payment? payment, PurchaseDetails purchase) async {
    if (payment != null) {


      try {
        // android 需要的补单参数
        var skuId = payment.skuId;
        var skuType = payment.skuType;
        var skuProductId = payment.skuProductId;
        var purchaseData = "";
        var signature = "";
        // ios 需要的补单参数
        var receiptData = "";
        if (Platform.isAndroid) {
          var googleDetail = purchase as GooglePlayPurchaseDetails;
          purchaseData = googleDetail.billingClientPurchase.originalJson;
          signature = googleDetail.billingClientPurchase.signature;
        } else if (Platform.isIOS) {
          var appleDetail = purchase as AppStorePurchaseDetails;
          receiptData = appleDetail.verificationData.serverVerificationData;
        }

        var inAppPurchaseRecoveryData = InAppPurchaseRecoveryData(
            skuId: skuId,
            purchaseData: purchaseData,
            signature: signature,
            skuType: skuType,
            receiptData: receiptData,
            skuProductId: skuProductId,
            rawData: purchase.toJson()
        );


        FFLog.info("保存支付补单数据。 : ${inAppPurchaseRecoveryData.toJson()}");
        await savePayment(skuId, inAppPurchaseRecoveryData);
      } catch (e) {
        FFLog.error("保存支付补单数据错误 : $e");
      }
    } else {
      FFLog.error("保存支付补单数据 保存用户信息 支付参数为空");
    }
  }

  // 根据 reStorageKey 获取存储的字符串列表数据
  static List<String>? getRestorePaymentList() {
    // 读取存储的数据
    var storedData = _storage.read<List<dynamic>?>(reStorageKey);
    if (storedData == null) {
      return null;
    }
    // 将 List<dynamic> 转换为 List<String>
    return storedData.cast<String>();
  }

  // 根据 reStorageKey 设置存储的单个 string 数据，并返回存储后的列表
  static Future<List<String>> saveSinglePayment(String skuId) async {
    List<String> existingList = getRestorePaymentList() ?? <String>[];
    existingList.add(skuId);
    await _storage.write(reStorageKey, existingList);
    return existingList;
  }

  // 根据 skuId 获取存储的 Map 数据
  static InAppPurchaseRecoveryData? getPaymentBySkuId(String skuId) {
    // 读取存储的数据
    FFLog.info("读取存储的数据");
    final json = _storage.read<Map<String, dynamic>>(skuId);
    if (json != null) {
      return InAppPurchaseRecoveryData.fromJson(json);
    }
    return null;
  }

  // 根据 skuId 设置存储的 Map 数据
  static Future<void> savePayment(
      String skuId, InAppPurchaseRecoveryData payment) async {
    await saveSinglePayment(skuId);
    // 将 Map 数据存储到 SafeStorage 中
    await _storage.write(skuId, payment.toJson());
  }

  // 根据 skuId 移除存储的数据
  static Future<void> removePayment(String skuId) async {
    // 从 SafeStorage 中移除指定 skuId 的数据
    deletePayment(skuId);
    await _storage.remove(skuId);
  }

  // 根据 skuId 从存储的字符串数组数据中删除指定项
  static Future<void> deletePayment(String skuId) async {
    var existingList = getRestorePaymentList() ?? <String>[];

    if (existingList.contains(skuId)) {
      existingList.remove(skuId);
    }
    await _storage.write(reStorageKey, existingList);
  }
}






// 扩展 PurchaseDetails 类，添加 toJson 方法
extension PurchaseDetailsJsonExtension on PurchaseDetails {
  String toJson() {
    final map = {
      'purchaseID': purchaseID,
      'productID': productID,
      'verificationData': _verificationDataToJson(verificationData),
      'transactionDate': transactionDate,
      'status': status.index,
      'error': _iapErrorToJson(error),
      'pendingCompletePurchase': pendingCompletePurchase,
    };
    return jsonEncode(map);
  }

  // 辅助方法：将 PurchaseVerificationData 转为 JSON 格式
  Map<String, dynamic> _verificationDataToJson(PurchaseVerificationData verificationData) {
    return {
      'localVerificationData': verificationData.localVerificationData,
      'serverVerificationData': verificationData.serverVerificationData,
      'source': verificationData.source,
    };
  }

  // 辅助方法：将 IAPError 转为 JSON 格式
  Map<String, dynamic> _iapErrorToJson(IAPError? error) {
    if (error == null) {
      return {};
    }
    return {
      'source': error.source,
      'code': error.code,
      'message': error.message,
      'details': error.details,
    };
  }
}


// 扩展 String 类，添加 fromJson 方法来将 JSON 转为 PurchaseDetails 对象
extension StringToPurchaseDetails on String {
  PurchaseDetails toPurchaseDetails() {
    final Map<String, dynamic> jsonData = jsonDecode(this);
    return PurchaseDetails(
      purchaseID: jsonData['purchaseID'],
      productID: jsonData['productID'],
      verificationData: _jsonToVerificationData(jsonData['verificationData']),
      transactionDate: jsonData['transactionDate'],
      status: PurchaseStatus.values[jsonData['status']],
    )
      ..error = _jsonToIAPError(jsonData['error'])
      ..pendingCompletePurchase = jsonData['pendingCompletePurchase'];
  }

  // 辅助方法：将 JSON 格式的 verificationData 转为 PurchaseVerificationData 对象
  PurchaseVerificationData _jsonToVerificationData(Map<String, dynamic> jsonData) {
    return PurchaseVerificationData(
      localVerificationData: jsonData['localVerificationData'],
      serverVerificationData: jsonData['serverVerificationData'],
      source: jsonData['source'],
    );
  }

  // 辅助方法：将 JSON 格式的 error 转为 IAPError 对象
  IAPError? _jsonToIAPError(Map<String, dynamic>? jsonData) {
    if (jsonData == null || jsonData.isEmpty) {
      return null;
    }
    return IAPError(
      source: jsonData['source'],
      code: jsonData['code'],
      message: jsonData['message'],
      details: jsonData['details'],
    );
  }


}

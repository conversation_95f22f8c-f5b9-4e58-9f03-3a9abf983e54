import 'dart:async';
import 'dart:io';

import 'package:get/get.dart';
import 'package:playlet/model/details.dart';
import 'package:playlet/model/main.dart';
import 'package:playlet/model/webview.dart';
import 'package:playlet/modules/home/<USER>/tab_home_data.dart';
import 'package:playlet/modules/login/login_controller.dart';
import 'package:playlet/modules/resource_bit/webview/resource_bit_webview_context.dart';
import 'package:playlet/modules/rewards/event/rewards_event.dart';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/service/good_review_service.dart';

import '../common/event/track_event.dart';
import '../model/rewards_bind/bind_options.dart';

class AppNavigator {
  AppNavigator._();

  /// 是否可以马上跳转
  /// 如果是false，会先存到_tasks里，当变为true时再按顺序执行跳转
  static bool _canNavigate = false;
  static bool get canNavigate => _canNavigate;
  static set canNavigate(bool value) {
    _canNavigate = value;
    if (value) {
      _executeTasks();
    }
  }

  static final List<Future<void> Function()> _tasks = [];

  static void exitApp() {
    exit(0);
  }

  /// 跳转至Main
  static Future<void> startMainPage({MainOptions? options}) async {
    await Get.offAllNamed(Routes.mainPage,arguments: options);
  }

  /// 跳转至详情页面
  static Future<void> startDetailsPage(DetailsOptions options,
      {bool off = false}) async {
    await _addTask(() async {
      final GoodReviewService goodReviewService = Get.find<GoodReviewService>();
      goodReviewService.onStartDetail();
      await _toNamed(Routes.detailsPage, arguments: options, off: off);
      goodReviewService.onEndDetail();
    });
  }

  /// 跳转至WebView页面
  static Future<void> startWebViewPage(WebviewModel options,
      {bool off = false}) async {
    await _addTask(() async {
      await _toNamed(
        Routes.webViewPage,
        arguments: options,
        off: off,
      );
    });
  }

  /// 跳转至登录页面
  static Future<void> startLoginPage({LoginPageParams? params}) async {
    await _addTask(() async {
      await Get.toNamed(Routes.loginPage, arguments: params);
    });
  }

  static Future<void> startSettingPage() async {
    await _addTask(() async {
      await Get.toNamed(Routes.settingPage);
    });
  }

  static Future<void> startFeedbackPage() async {
    await _addTask(() async {
      await Get.toNamed(Routes.feedbackPage);
    });
  }

  /// 跳转至More页面
  static Future<void> startMorePage(
      BannerResponseList bannerResponseList) async {
    await _addTask(() async {
      await Get.toNamed(Routes.morePage, arguments: bannerResponseList);
    });
  }

  static Future<void> startSplashPage() async {
    await _addTask(() async {
      await Get.offAllNamed(Routes.splashPage);
    });
  }

  static Future<void> startLanguagePage() async {
    await _addTask(() async {
      await Get.toNamed(Routes.languagePage);
    });
  }

  /// 跳转到商店页面
  static Future<void> startStorePage({bool off = false}) async {
    await _addTask(() async {
      await _toNamed(Routes.storePage, off: off);
    });
  }

  /// 跳转到订阅页面
  static Future<void> startSubscriptionPage(
      {String from = TrackEvent.other, bool off = false}) async {
    await _addTask(() async {
      await _toNamed(
        Routes.subscriptionPage,
        arguments: from,
        off: off,
      );
    });
  }

  /// 跳转到任务页面
  ///[from]  埋点来源
  static Future<void> startRewardsPage({required String from}) async {
    await _addTask(() async {
      RewardsEvent.submitRewardClick(scene: from);
      await Get.toNamed(Routes.rewardsPage, arguments: from);
    });
  }

  /// 跳转到任务页面 并关闭绑定页面
  static Future<void> offNamedUntilRewardsPage({required String from}) async {
    await _addTask(() async {
      await Get.offNamedUntil(Routes.rewardsPage, (route) {
        // 保留不是这三个页面的所有路由
        return ![
          Routes.rewardsPage,
          Routes.rewardsBindPage,
          Routes.rewardsVerificationPage,
        ].contains(route.settings.name);
      }, arguments: from);
    });
  }

  /// 跳转到新人推荐页面
  static Future<void> startRecommendPage(
      {required String from, bool off = false}) async {
    await _addTask(() async {
      await _toNamed(Routes.recommendPage, arguments: from, off: off);
    });
  }

  /// 跳转到调试页面
  static Future<void> startDebugPage() async {
    await _addTask(() async {
      await Get.toNamed(Routes.debugPage);
    });
  }

  /// 跳转到绑定页面
  static Future<void> startRewardsBindPage(BindOptionsModel options) async {
    await _addTask(() async {
      await Get.toNamed(Routes.rewardsBindPage, arguments: options);
    });
  }

  /// 跳转到绑定验证页面
  static Future<void> startRewardsVerificationPage(BindOptionsModel options) async {
    await _addTask(() async {
    await  Get.toNamed(Routes.rewardsVerificationPage, arguments: options);
    });
  }

  /// 跳转到用户账号信息页面
  static Future<void> startAccountInfoPage() async {
    await _addTask(() async {
     await Get.toNamed(Routes.accountInfoPage);
    });
  }

  /// 跳转至资源位WebView页面
  static Future<void> startResourceBitWebviewPage(ResourceBitWebViewContext context) async {
    await _addTask(() async {
      await Get.toNamed(Routes.resourceBitWebviewPage, arguments: context);
    });
  }

  static Future<void> back() async {
    await _addTask(() async {
      Get.back();
    });
  }

  /// 跳转至指定页面
  static Future<T?>? _toNamed<T>(
    String page, {
    dynamic arguments,
    int? id,
    Map<String, String>? parameters,
    bool off = false,
  }) async {
    if (off) {
      return await Get.offAndToNamed(
        page,
        arguments: arguments,
        id: id,
        parameters: parameters,
      );
    } else {
      return await Get.toNamed(
        page,
        arguments: arguments,
        id: id,
        parameters: parameters,
      );
    }
  }

  /// 添加任务
  static Future<void> _addTask(Future<void> Function() task) async {
    if (canNavigate) {
      // 如果已经可以跳转了，直接执行
      await task();
      return;
    }
    // 如果不能跳转，先存到_tasks里
    _tasks.add(task);
  }

  /// 执行_tasks里的任务
  static Future<void> _executeTasks() async {
    if (_tasks.isEmpty) {
      return;
    }
    for (var task in _tasks) {
      await task();
    }
    _tasks.clear();
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/utils/events.dart';

enum RouteObserverCustomType {
  push,
  pop,
}

class RouteObserverCustomEvent {
  String from;
  String to;
  RouteObserverCustomType type = RouteObserverCustomType.push;
  RouteObserverCustomEvent({required this.from, required this.to,required this.type});
}

class RouteObserverCustom extends GetObserver {
  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);
    eventBus.fire(RouteObserverCustomEvent(
      from: previousRoute?.settings.name ?? '',
      to: route.settings.name ?? '',
      type: RouteObserverCustomType.push,
    ));
    Get.log(
      'RouteObserverCustom: form:${previousRoute?.settings.name} to:${route.settings.name}',
    );
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);
    eventBus.fire(RouteObserverCustomEvent(
      from: route.settings.name ?? '',
      to: previousRoute?.settings.name ?? '',
      type: RouteObserverCustomType.pop,
    ));
    Get.log(
      'RouteObserverCustom: form:${route.settings.name} to:${previousRoute?.settings.name}',
    );
  }
}

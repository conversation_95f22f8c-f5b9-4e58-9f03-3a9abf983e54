import 'package:get/get.dart';
import 'package:playlet/modules/account_info/account_info_binding.dart';
import 'package:playlet/modules/account_info/account_info_page.dart';
import 'package:playlet/modules/debug/debug_binding.dart';
import 'package:playlet/modules/debug/debug_page.dart';
import 'package:playlet/modules/feedback/feedback_binding.dart';
import 'package:playlet/modules/feedback/feedback_page.dart';
import 'package:playlet/modules/language/language_binding.dart';
import 'package:playlet/modules/language/language_page.dart';
import 'package:playlet/modules/login/login_binding.dart';
import 'package:playlet/modules/login/login_page.dart';
import 'package:playlet/modules/main/main_binding.dart';
import 'package:playlet/modules/main/main_page.dart';
import 'package:playlet/modules/more/more_binding.dart';
import 'package:playlet/modules/more/more_page.dart';
import 'package:playlet/modules/recommend/recommend_binding.dart';
import 'package:playlet/modules/recommend/recommend_page.dart';
import 'package:playlet/modules/resource_bit/webview/resource_bit_webview_binding.dart';
import 'package:playlet/modules/resource_bit/webview/resource_bit_webview_page.dart';
import 'package:playlet/modules/rewards/rewards_binding.dart';
import 'package:playlet/modules/rewards_bind/rewards_bind_binding.dart';
import 'package:playlet/modules/rewards_bind/rewards_bind_page.dart';
import 'package:playlet/modules/rewards_verification/rewards_verification_binding.dart';
import 'package:playlet/modules/rewards_verification/rewards_verification_page.dart';
import 'package:playlet/modules/setting/setting_binding.dart';
import 'package:playlet/modules/setting/setting_page.dart';
import 'package:playlet/modules/store/store_binding.dart';
import 'package:playlet/modules/store/store_page.dart';
import 'package:playlet/modules/webview/webview_binding.dart';
import 'package:playlet/modules/webview/webview_page.dart';

import '../modules/details/details_binding.dart';
import '../modules/details/details_page.dart';
import '../modules/rewards/rewards_page.dart';
import '../modules/subscription/subscription_binding.dart';
import '../modules/subscription/subscription_page.dart';
import 'pages.dart';

abstract class AppPages {
  static final pages = [
    GetPage(
      name: Routes.mainPage,
      page: () => const MainPage(),
      binding: MainBinding(),
      transition: Transition.native,
    ),
    GetPage(
      name: Routes.detailsPage,
      page: () => const DetailsPage(),
      binding: DetailsBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.webViewPage,
      binding: WebviewBinding(),
      page: () => const WebviewPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.settingPage,
      binding: SettingBinding(),
      page: () => const SettingPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.feedbackPage,
      binding: FeedbackBinding(),
      page: () => const FeedbackPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.loginPage,
      binding: LoginBinding(),
      page: () => const LoginPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.morePage,
      binding: MoreBinding(),
      page: () => const MorePage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.storePage,
      binding: StoreBinding(),
      page: () => StorePage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.subscriptionPage,
      binding: SubscriptionBinding(),
      page: () => SubscriptionPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.rewardsPage,
      binding: RewardsBinding(),
      page: () => const RewardsPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.languagePage,
      binding: LanguageBinding(),
      page: () => LanguagePage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.recommendPage,
      binding: RecommendBinding(),
      page: () => const RecommendPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.languagePage,
      binding: LanguageBinding(),
      page: () => LanguagePage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.debugPage,
      binding: DebugBinding(),
      page: () => const DebugPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.rewardsBindPage,
      binding: RewardsBindBinding(),
      page: () =>  RewardsBindPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.rewardsVerificationPage,
      binding: RewardsVerificationBinding(),
      page: () =>  RewardsVerificationPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.accountInfoPage,
      binding: AccountInfoBinding(),
      page: () =>  AccountInfoPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.resourceBitWebviewPage,
      binding: ResourceBitWebviewBinding(),
      page: () => const ResourceBitWebviewPage(),
      transition: Transition.rightToLeft,
    ),
  ];
}

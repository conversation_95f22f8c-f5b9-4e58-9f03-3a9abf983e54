import 'dart:async';
import 'dart:io';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:leak_detector/leak_detector.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/i18n/translation_service.dart';
import 'package:playlet/modules/splash/splash_binding.dart';
import 'package:playlet/modules/splash/splash_page.dart';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/routers/route_observer_custom.dart';
import 'package:playlet/routers/routers.dart';
import 'package:playlet/service/ad/consent_manager.dart';
import 'package:playlet/service/analytics_service.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/app_service.dart';
import 'package:playlet/service/index.dart';
import 'package:playlet/service/ios_app_delegate_service.dart';
import 'package:playlet/service/other_task/get_watch_ad_task_service.dart';
import 'package:playlet/theme/theme.dart';
import 'package:playlet/utils/app_language.dart';
import 'package:playlet/utils/firebase_util.dart';
import 'package:toastification/toastification.dart';

Future<void> main() async {
  // 使用 runZonedGuarded 运行应用，并设置错误处理
  runZonedGuarded(() async {
    if (!Config.isProduction) {
      //maxRetainingPath:引用链的最大长度，设置越短性能越高，但是很有可能获取不到完整的泄漏路径 默认是 300
      LeakDetector().init(maxRetainingPath: 300);
    }

    //闪屏页相关
    WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

    // 设置崩溃处理逻辑
    await FirebaseUtil.setupCrashlyticsHandler();

    FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

    // 设置全局默认为竖屏
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    /// 统一设置弹窗蒙板背景颜色
    SmartDialog.config
      ..attach = SmartConfigAttach(maskColor: Colors.black.withValues(alpha: 0.7))
      ..custom = SmartConfigCustom(maskColor: Colors.black.withValues(alpha: 0.7));

    if (Platform.isIOS) {
      await _waitForNetworkPermissionForIOS();

      widgetsBinding.addPostFrameCallback((_) async {
        final status =
            await AppTrackingTransparency.requestTrackingAuthorization();
        if (kDebugMode) {
          FFLog.debug("requestTrackingAuthorization status: $status");
        }
      });
    }

    await initServices();
    AnalyticsService analyticsService = Get.find<AnalyticsService>();
    await analyticsService.setSystemInfoToAnalytics();
    analyticsService.setUidToAnalytics();
    analyticsService.listenUserInfo();
    AppService appService = Get.find<AppService>();
    appService.initLifecycleObserver();
    GetWatchAdTaskService().getRewardAdTask;
    unawaited(AppDeviceService.instance.refreshServerTime());

    final lang = AppLanguage.getCurrentLocale();

    IOSAppDelegateService appDelegateService =
        Get.find<IOSAppDelegateService>();
    await appDelegateService.launchForIOS();

    // 运行应用
    runApp(MyApp(initialLocale: lang));

    // 在应用启动后立即移除原生闪屏
    await Future.delayed(const Duration(milliseconds: 500));
    FlutterNativeSplash.remove();
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.light,
      systemNavigationBarDividerColor: Colors.transparent,
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.light,
    ));
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }, (error, stack) {
    FirebaseUtil.recordError(error, stack, "runZonedGuarded");
  });
}

// 等待网络权限和连接建立
Future<void> _waitForNetworkPermissionForIOS() async {
  if (!Platform.isIOS) {
    return;
  }
  // 初始检查网络状态
  var connectivity = Connectivity();
  var connectivityResult = await connectivity.checkConnectivity();

  // 如果初始状态不是"无网络"，则不需要等待
  if (!connectivityResult.contains(ConnectivityResult.none)) {
    return;
  }

  // 创建一个Completer来等待网络连接
  Completer<void> networkCompleter = Completer<void>();

  // 监听网络状态变化
  StreamSubscription<List<ConnectivityResult>>? subscription;
  subscription = Connectivity()
      .onConnectivityChanged
      .listen((List<ConnectivityResult> result) {
    // 当网络状态变为非none时，完成等待
    if (!result.contains(ConnectivityResult.none) &&
        !networkCompleter.isCompleted) {
      networkCompleter.complete();
      subscription?.cancel();
      subscription = null;
    }
  });

  // 等待网络连接或超时
  return networkCompleter.future;
}

class MyApp extends StatefulWidget {
  const MyApp({super.key, required this.initialLocale});
  final Locale initialLocale;

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final _consentManager = ConsentManager();
  bool _isMobileAdsInitializeCalled = false;
  bool _isPrivacyOptionsRequired = false;
  StreamSubscription? _leakSubscription;

  @override
  void initState() {
    super.initState();

    _consentManager.gatherConsent((consentGatheringError) {
      if (consentGatheringError != null) {
        // Consent not obtained in current session.
        debugPrint("隐私同意收集失败: ${consentGatheringError.message}");
      }

      // Check if a privacy options entry point is required.
      _getIsPrivacyOptionsRequired();

      // Attempt to initialize the Mobile Ads SDK.
      _initializeMobileAdsSDK();
    });

    // This sample attempts to load ads using consent obtained in the previous session.
    _initializeMobileAdsSDK();

    if (!Config.isProduction) {
      _listenLeak();
    }
  }

  void _listenLeak() {
    if (Config.isProduction) {
      return;
    }
    _leakSubscription = LeakDetector().onLeakedStream.listen((LeakedInfo info) {
      //print to console
      for (var node in info.retainingPath) {
        FFLog.info("leaked info: $node", tag: "LeakDetector");
      }
      //show preview page
      // if (Get.context != null && mounted) {
      // showLeakedInfoPage(Get.context!, info);
      // }
    });
  }

  @override
  void dispose() {
    _leakSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Get.find<TranslationService>();

    return Obx(() {
      // 使用TranslationService中的当前语言获取Locale
      final currentLocale = translationService.getCurrentLocale();
      final isRtl =
          translationService.isRtl(translationService.getCurrentLanguage());

      return Directionality(
        textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
        child: ScreenUtilInit(
          designSize: const Size(393, 852),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return ToastificationWrapper(
              child: GetMaterialApp(
                title: Config.appName,
                debugShowCheckedModeBanner: false,
                theme: AppTheme.theme,
                darkTheme: AppTheme.theme,
                navigatorObservers: [
                  if (!Config.isProduction)
                    LeakNavigatorObserver(
                      //返回false则不会校验这个页面.
                      shouldCheck: (route) {
                        return route.settings.name != null &&
                            route.settings.name != '/';
                      },
                    ),
                  RouteObserverCustom(),
                  FlutterSmartDialog.observer,
                ],
                // 使用响应式获取的currentLocale，而不是固定的appLanguage
                locale: currentLocale,
                fallbackLocale: const Locale('en', 'US'),
                translations: translationService,
                localizationsDelegates: const [
                  GlobalCupertinoLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                ],
                initialRoute: Routes.splashPage,
                defaultTransition: Transition.noTransition,
                getPages: AppPages.pages,
                initialBinding: SplashBinding(),
                home: const PopScope(child: SplashPage()),
                builder: FlutterSmartDialog.init(
                    // 在此实现自定义loading，和toast
                    ),
              ),
            );
          },
        ),
      );
    });
  }

  void _getIsPrivacyOptionsRequired() async {
    if (await _consentManager.isPrivacyOptionsRequired()) {
      setState(() {
        _isPrivacyOptionsRequired = true;
      });
    }
  }

  void _initializeMobileAdsSDK() async {
    if (_isMobileAdsInitializeCalled) {
      return;
    }

    if (await _consentManager.canRequestAds()) {
      _isMobileAdsInitializeCalled = true;

      // Initialize the Mobile Ads SDK.
      MobileAds.instance.initialize().then((initializationStatus) {
        initializationStatus.adapterStatuses.forEach((key, value) {
          debugPrint('Adapter status for $key: ${value.description}');
        });
      });
      // Load an ad.
    }
  }
}

import 'package:get/get.dart';

/// 翻译工具类，提供基础翻译功能
class TransUtils {
  /// 无参数翻译
  static String tr(String key) => key.tr;

  /// 带参数翻译，泛型实现
  /// 使用泛型<T>可以支持传入任意类型的参数(字符串、数字、日期等)
  /// 内部会自动将传入的参数转换为字符串，使翻译调用更灵活
  static String trParams<T>(String key, Map<String, T> params) {
    String text = key.tr;

    // 直接在字符串中替换占位符，确保与翻译文件中的格式匹配
    // 新格式使用 {0}, {1}, {2} 等作为占位符
    params.forEach((k, v) {
      text = text.replaceAll(k, v.toString());
    });

    return text;
  }

  /// 获取翻译列表（用于处理多条目翻译）
  /// 自动探测最大可用的索引号，不需要手动指定数量
  static List<String> trList(String keyPrefix) {
    List<String> result = [];
    int index = 1;

    // 持续尝试获取翻译，直到找不到对应的键为止
    while (true) {
      String key = '$keyPrefix$index';
      String value = key.tr;

      // 当翻译结果与键名相同时，说明没有找到翻译，退出循环
      if (value == key) {
        break;
      }

      result.add(value);
      index++;
    }

    return result;
  }
}

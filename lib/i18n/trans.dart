import 'trans_utils.dart';

/// 统一翻译类，使用静态方法
class AppTrans {

  // #首頁-推荐
  static String discoverFragmentPlayNow() => TransUtils.tr('discoverFragmentPlayNow');

  // #reels
  static String all() => TransUtils.tr('all');
  static String allEpisodes<T>(T s) => TransUtils.trParams('allEpisodes', {'{0}': s});
  static String ep() => TransUtils.tr('ep');
  static String list() => TransUtils.tr('list');
  static String playNow() => TransUtils.tr('playNow');
  static String vip() => TransUtils.tr('vip');

  // #剧集选择
  static String allEp<T>(T s) => TransUtils.trParams('allEp', {'{0}': s});

  // #剧集未更新集数解锁样式
  static String updateEP<T>(T s) => TransUtils.trParams('updateEP', {'{0}': s});

  // #sku挽留弹窗
  static String countdown() => TransUtils.tr('countdown');

  // #账号升级
  static String appVersionUpgradeTitle() => TransUtils.tr('appVersionUpgradeTitle');
  static String later() => TransUtils.tr('later');
  static String update() => TransUtils.tr('update');

  // #登录页面
  static String privacyPolicy() => TransUtils.tr('privacyPolicy');
  static String signInAnd() => TransUtils.tr('signInAnd');
  static String signInApple() => TransUtils.tr('signInApple');
  static String signInFacebook() => TransUtils.tr('signInFacebook');
  static String signInGoogle() => TransUtils.tr('signInGoogle');
  static String signInReward<T>(T s) => TransUtils.trParams('signInReward', {'{0}': s});
  static String signInTips() => TransUtils.tr('signInTips');
  static String signInWelcome<T>(T s) => TransUtils.trParams('signInWelcome', {'{0}': s});
  static String userAgreement() => TransUtils.tr('userAgreement');

  // #profile
  static String annual() => TransUtils.tr('annual');
  static String bonus() => TransUtils.tr('bonus');
  static String expirationDate() => TransUtils.tr('expirationDate');
  static String feedback() => TransUtils.tr('feedback');
  static String go() => TransUtils.tr('go');
  static String guest() => TransUtils.tr('guest');
  static String joinVipDetailDesc() => TransUtils.tr('joinVipDetailDesc');
  static String joinVipTitleDesc() => TransUtils.tr('joinVipTitleDesc');
  static String language() => TransUtils.tr('language');
  static String login() => TransUtils.tr('login');
  static String monthly() => TransUtils.tr('monthly');
  static String myWallet() => TransUtils.tr('myWallet');
  static String rewards() => TransUtils.tr('rewards');
  static String settings() => TransUtils.tr('settings');
  static String subscribed() => TransUtils.tr('subscribed');
  static String topUp() => TransUtils.tr('topUp');
  static String weekly() => TransUtils.tr('weekly');

  // #订阅
  static String month() => TransUtils.tr('month');
  static String week() => TransUtils.tr('week');
  static String year() => TransUtils.tr('year');

  // #设置
  static String automaticEpisodeUnlock() => TransUtils.tr('automaticEpisodeUnlock');
  static String deleteAccount() => TransUtils.tr('deleteAccount');
  static String logOut() => TransUtils.tr('logOut');
  static String setting() => TransUtils.tr('setting');

  // #退出账号 确认弹窗
  static String cancel() => TransUtils.tr('cancel');
  static String confirm() => TransUtils.tr('confirm');
  static String logOutAlertDesc() => TransUtils.tr('logOutAlertDesc');
  static String logOutAlertTitle() => TransUtils.tr('logOutAlertTitle');

  // #删除账号 确认弹窗
  static String deleteAccountAlertDesc() => TransUtils.tr('deleteAccountAlertDesc');
  static String deleteAccountAlertTitle() => TransUtils.tr('deleteAccountAlertTitle');

  // #任务栏签到弹窗
  static String congratulations() => TransUtils.tr('congratulations');
  static String getDoubleRewards() => TransUtils.tr('getDoubleRewards');
  static String nowLook() => TransUtils.tr('nowLook');
  static String watchAnAd() => TransUtils.tr('watchAnAd');

  // #feedback
  static String email() => TransUtils.tr('email');
  static String feedbackPlaceholder() => TransUtils.tr('feedbackPlaceholder');
  static String submit() => TransUtils.tr('submit');

  // #My List
  static String favorites() => TransUtils.tr('favorites');
  static String history() => TransUtils.tr('history');
  static String myList() => TransUtils.tr('myList');

  // #store
  static String coins() => TransUtils.tr('coins');
  static String store() => TransUtils.tr('store');
  static String storeSubscription() => TransUtils.tr('storeSubscription');
  static String storeSubscriptionInfo() => TransUtils.tr('storeSubscriptionInfo');

  // #订阅详情页
  static String subscriptionAgreement() => TransUtils.tr('subscriptionAgreement');
  static String subscriptionInfo<T>(T s) => TransUtils.trParams('subscriptionInfo', {'{0}': s});

  // #Subscription
  static String autoRenew() => TransUtils.tr('autoRenew');
  static String firstDeposit() => TransUtils.tr('firstDeposit');
  static String noThanks() => TransUtils.tr('noThanks');
  static String restore() => TransUtils.tr('restore');
  static String subscription() => TransUtils.tr('subscription');
  static String subscriptionCurrent() => TransUtils.tr('subscriptionCurrent');
  static String subscriptionExpired() => TransUtils.tr('subscriptionExpired');
  static String subscriptionExpiredDetail() => TransUtils.tr('subscriptionExpiredDetail');
  static String subscriptionFeature() => TransUtils.tr('subscriptionFeature');
  static String subscriptionMaturity() => TransUtils.tr('subscriptionMaturity');
  static String subscriptionOk() => TransUtils.tr('subscriptionOk');
  static String subscriptionRenewal() => TransUtils.tr('subscriptionRenewal');
  static String subscriptionWeekly() => TransUtils.tr('subscriptionWeekly');
  static String subscriptionYour() => TransUtils.tr('subscriptionYour');
  static String totalMembership() => TransUtils.tr('totalMembership');
  static String unlockOne() => TransUtils.tr('unlockOne');

  // #下拉充值页面
  static String annualSubscription() => TransUtils.tr('annualSubscription');
  static String balance() => TransUtils.tr('balance');
  static String monthlySubscription() => TransUtils.tr('monthlySubscription');
  static String rechargeAgreement() => TransUtils.tr('rechargeAgreement');
  static String rechargeMeansAgree() => TransUtils.tr('rechargeMeansAgree');
  static String thisEpisode() => TransUtils.tr('thisEpisode');
  static String watchAds() => TransUtils.tr('watchAds');

  // #Rewards
  static String checkIn() => TransUtils.tr('checkIn');
  static String dailyCheckIn() => TransUtils.tr('dailyCheckIn');
  static String dailyTasks() => TransUtils.tr('dailyTasks');
  static String day() => TransUtils.tr('day');
  static String myBonus() => TransUtils.tr('myBonus');
  static String nextCheckIn() => TransUtils.tr('nextCheckIn');
  static String rewardsAd() => TransUtils.tr('rewardsAd');
  static String rewardsAdBonus() => TransUtils.tr('rewardsAdBonus');
  static String rewardsEnable() => TransUtils.tr('rewardsEnable');
  static String rewardsWatch() => TransUtils.tr('rewardsWatch');
  static String today() => TransUtils.tr('today');

  // #无内容
  static String emptyNoContent() => TransUtils.tr('emptyNoContent');

  // #无网络
  static String emptyNoNetwork() => TransUtils.tr('emptyNoNetwork');

  // #加载超时
  static String emptyRefresh() => TransUtils.tr('emptyRefresh');
  static String emptyTimeout() => TransUtils.tr('emptyTimeout');

  // #首页
  static String videoEpisodeAll<T>(T s) => TransUtils.trParams('videoEpisodeAll', {'{0}': s});
  static String videoEpisodeUpdateProgress<T1, T2>(T1 s1, T2 s2) => TransUtils.trParams('videoEpisodeUpdateProgress', {'{0}': s1, '{1}': s2});

  // #弹窗_加载中
  static String adLoading() => TransUtils.tr('adLoading');

  // #需求文档->广告变现
  static String adWaitingTry() => TransUtils.tr('adWaitingTry');

  // #自定义弹框
  static String clearAll() => TransUtils.tr('clearAll');
  static String clearedRestored() => TransUtils.tr('clearedRestored');
  static String noMoreContents() => TransUtils.tr('noMoreContents');
  static String noNetwork() => TransUtils.tr('noNetwork');
  static String notificationCustomReceived<T>(T s) => TransUtils.trParams('notificationCustomReceived', {'{0}': s});
  static String notificationCustomTitle<T>(T s) => TransUtils.trParams('notificationCustomTitle', {'{0}': s});
  static String notificationReceive() => TransUtils.tr('notificationReceive');
  static String timeout() => TransUtils.tr('timeout');

  // #unlock_one
  static String notificationCustomContent() => TransUtils.tr('notificationCustomContent');

  // #选择弹框
  static String notificationSelectContentA() => TransUtils.tr('notificationSelectContentA');
  static String notificationSelectContentB() => TransUtils.tr('notificationSelectContentB');
  static String notificationSelectContentC() => TransUtils.tr('notificationSelectContentC');
  static String notificationSelectContentD() => TransUtils.tr('notificationSelectContentD');
  static String notificationSelectTitle() => TransUtils.tr('notificationSelectTitle');
  static String notificationTurnOn() => TransUtils.tr('notificationTurnOn');

  // #下拉刷新
  static String refreshing() => TransUtils.tr('refreshing');

  // #加载更多
  static String noMore() => TransUtils.tr('noMore');

  // #splash
  static String failed() => TransUtils.tr('failed');
  static String lastUpdated() => TransUtils.tr('lastUpdated');
  static String loadingMore() => TransUtils.tr('loadingMore');
  static String pullRefresh() => TransUtils.tr('pullRefresh');
  static String pullToLoad() => TransUtils.tr('pullToLoad');
  static String refreshingMore() => TransUtils.tr('refreshingMore');
  static String releaseReady() => TransUtils.tr('releaseReady');
  static String succeeded() => TransUtils.tr('succeeded');

  // #底部导航栏
  static String discover() => TransUtils.tr('discover');
  static String profile() => TransUtils.tr('profile');
  static String reels() => TransUtils.tr('reels');

  // #语言
  static String languageSetting() => TransUtils.tr('languageSetting');
  static String save() => TransUtils.tr('save');

  // #订阅详情页-已订阅
  static String reStoreNoOrder() => TransUtils.tr('reStoreNoOrder');
  static String reStoreSuccess() => TransUtils.tr('reStoreSuccess');

  // #logout页面-版本号
  static String version() => TransUtils.tr('version');

  // #我的页面
  static String user() => TransUtils.tr('user');

  // #付费卡点弹窗
  static String price() => TransUtils.tr('price');

  // #登陆引导奖励
  static String signInToGet() => TransUtils.tr('signInToGet');
  static String welomeToFlareFlow() => TransUtils.tr('welomeToFlareFlow');

  // #meta登录引导
  static String protectProperityRemindContent() => TransUtils.tr('protectProperityRemindContent');

  // #我的-登录fb引导
  static String bonusForFirstLogin() => TransUtils.tr('bonusForFirstLogin');

  // #后台配置营销热区->首页开屏资源位
  static String skip() => TransUtils.tr('skip');

  // #横屏支持
  static String fullscreen() => TransUtils.tr('fullscreen');
  static String restoreScreen() => TransUtils.tr('restoreScreen');

  // #输入手机号-未输入
  static String checkEnteredContent() => TransUtils.tr('checkEnteredContent');
  static String enterPhone() => TransUtils.tr('enterPhone');
  static String enterPhoneNumber() => TransUtils.tr('enterPhoneNumber');
  static String mobileLogin() => TransUtils.tr('mobileLogin');

  // #输入邮箱-未输入
  static String emailBinding() => TransUtils.tr('emailBinding');
  static String enterEmailAccount() => TransUtils.tr('enterEmailAccount');

  // #输入手机号
  static String enterEmailAddress() => TransUtils.tr('enterEmailAddress');

  // #输入手机号-填写验证码-重新发送
  static String notReceiveTryAgain() => TransUtils.tr('notReceiveTryAgain');
  static String resendAfterCountDown<T>(T s) => TransUtils.trParams('resendAfterCountDown', {'{0}': s});

  // #输入手机号-填写验证码
  static String verificationCodeSentToPhone() => TransUtils.tr('verificationCodeSentToPhone');

  // #输入邮箱-填写验证码
  static String verificationCodeSentToEmail() => TransUtils.tr('verificationCodeSentToEmail');

  // #手机号绑定成功
  static String goToReceive() => TransUtils.tr('goToReceive');
  static String phoneBindingSuccess() => TransUtils.tr('phoneBindingSuccess');

  // #邮箱绑定成功
  static String emailBindingSuccess() => TransUtils.tr('emailBindingSuccess');

  // #设置-账户信息入口
  static String accountInfo() => TransUtils.tr('accountInfo');

  // #账户信息页
  static String bindNow() => TransUtils.tr('bindNow');
  static String facebook() => TransUtils.tr('facebook');
  static String phone() => TransUtils.tr('phone');
  static String unbound() => TransUtils.tr('unbound');

  // #连续广告弹窗
  static String advanceWatchCancel() => TransUtils.tr('advance_watch_cancel');
  static String advanceWatchUnlockCurrent<T>(T s) => TransUtils.trParams('advance_watch_unlock_current', {'{0}': s});

  // #连续广告解锁挽留弹窗
  static String advanceContinueCongratulations() => TransUtils.tr('advance_continue_congratulations');
  static String advanceContinueNextUnlock<T>(T s) => TransUtils.trParams('advance_continue_next_unlock', {'{0}': s});
  static String advanceContinueOnemore() => TransUtils.tr('advance_continue_onemore');
  static String advanceContinueTip() => TransUtils.tr('advance_continue_tip');
  static String advanceContinueUnlocked<T>(T s) => TransUtils.trParams('advance_continue_unlocked', {'{0}': s});

  // #安卓/通知栏
  static String pushBeingBroadcasted() => TransUtils.tr('push_being_broadcasted');
  static String pushClose() => TransUtils.tr('push_close');
  static String pushLocalShortName() => TransUtils.tr('push_local_short_name');
  static String pushLocalShortSummary() => TransUtils.tr('push_local_short_summary');
  static String pushWatchNow() => TransUtils.tr('push_watch_now');

  // #好评弹窗
  static String goodReviewLabel() => TransUtils.tr('good_review_label');
  static String goodReviewTig() => TransUtils.tr('good_review_tig');

  // #新人任务
  static String recommendMore() => TransUtils.tr('recommend_more');
  static String recommendNewcomers() => TransUtils.tr('recommend_newcomers');
  static String recommendTitle() => TransUtils.tr('recommend_title');
  static String recommendTitleLabel() => TransUtils.tr('recommend_title_label');

  // #剧集挽留
  static String dramaRetentionFavorite() => TransUtils.tr('drama_retention_favorite');
  static String dramaRetentionPlayNow() => TransUtils.tr('drama_retention_play_now');
  static String dramaRetentionRecommendedForYou() => TransUtils.tr('drama_retention_recommended_for_you');

  // #rewards右侧显示总计可获得的bonus数量角标
  static String getBonus<T>(T s) => TransUtils.trParams('getBonus', {'{0}': s});

  // #fsi权限引导弹框
  static String fsiAlertAllow() => TransUtils.tr('fsi_alert_allow');
  static String fsiAlertContent() => TransUtils.tr('fsi_alert_content');

  // #剧集挽留收藏提示
  static String dramaRetentionFavoriteTig() => TransUtils.tr('drama_retention_favorite_tig');

  // #最近播放
  static String recentlyAllEp<T>(T s) => TransUtils.trParams('recently_all_ep', {'{0}': s});
  static String recentlyContinue() => TransUtils.tr('recently_continue');
  static String recentlyEp<T>(T s) => TransUtils.trParams('recently_ep', {'{0}': s});

  // #实时活动->样式1
  static String liveActivitySigninButtonTitle() => TransUtils.tr('live_activity_signin_button_title');
  static String liveActivitySigninRemain<T>(T s) => TransUtils.trParams('live_activity_signin_remain', {'{0}': s});
  static String liveActivitySigninTitle() => TransUtils.tr('live_activity_signin_title');

  // #实时活动->样式2
  static String liveActivityDefaultButtonTitle() => TransUtils.tr('live_activity_default_button_title');
  static String liveActivityDefaultTitle() => TransUtils.tr('live_activity_default_title');

  // #实时活动->样式3
  static String liveActivityWatchButtonTitle() => TransUtils.tr('live_activity_watch_button_title');
  static String liveActivityWatchStartTime<T>(T s) => TransUtils.trParams('live_activity_watch_start_time', {'{0}': s});
  static String liveActivityWatchTitle() => TransUtils.tr('live_activity_watch_title');

  // #实时活动->样式4
  static String liveActivityRecommendButtonTitle() => TransUtils.tr('live_activity_recommend_button_title');
  static String liveActivityRecommendCollect<T>(T s) => TransUtils.trParams('live_activity_recommend_collect', {'{0}': s});

  // #输入手机号-已输入
  static String getVerificationCode() => TransUtils.tr('getVerificationCode');

  // #新人推荐页
  static String choose() => TransUtils.tr('choose');

  // #其他
  static String comingsoonNextDrama<T>(T s) => TransUtils.trParams('comingsoonNextDrama', {'{0}': s});
  static String languageDe() => TransUtils.tr('languageDe');
  static String languageEn() => TransUtils.tr('languageEn');
  static String languageEs() => TransUtils.tr('languageEs');
  static String languageFr() => TransUtils.tr('languageFr');
  static String languageId() => TransUtils.tr('languageId');
  static String languageIt() => TransUtils.tr('languageIt');
  static String languageJa() => TransUtils.tr('languageJa');
  static String languageKo() => TransUtils.tr('languageKo');
  static String languagePt() => TransUtils.tr('languagePt');
  static String languageTh() => TransUtils.tr('languageTh');
  static String languagezhHans() => TransUtils.tr('languagezhHans');
  static String noProductsAvailable() => TransUtils.tr('noProductsAvailable');
  static String noProductsSubscription() => TransUtils.tr('noProductsSubscription');
  static String switchedToSpeedTip<T>(T s) => TransUtils.trParams('switchedToSpeedTip', {'{0}': s});
  static String unLockVideoTip<T>(T s) => TransUtils.trParams('unLockVideoTip', {'{0}': s});
  static String unableUnlockEpisodes() => TransUtils.tr('unableUnlockEpisodes');
  static String unableUnlockPublished() => TransUtils.tr('unableUnlockPublished');
  static String unlock() => TransUtils.tr('unlock');
  static String unlockMoreEpisodes() => TransUtils.tr('unlockMoreEpisodes');
  static String unlockNow() => TransUtils.tr('unlockNow');
  static String watchAdsToUnlock() => TransUtils.tr('watchAdsToUnlock');
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/utils/safe_storage.dart';
import '../common/log/ff_log.dart';
import 'translations/en.dart';
import 'translations/zh_hans.dart';
import 'translations/zh_hant.dart';
import 'translations/ja.dart';
import 'translations/ko.dart';
import 'translations/ar.dart';
import 'translations/de.dart';
import 'translations/es.dart';
import 'translations/fr.dart';
import 'translations/id.dart';
import 'translations/it.dart';
import 'translations/pt.dart';
import 'translations/th.dart';

/// 翻译服务类，负责管理多语言
class TranslationService extends Translations {
  static const String languageKey = 'language_key';

  // 语言代码常量 - 简化代码
  static const String langEn = 'en';
  static const String langZhHans = 'zh_hans';
  static const String langZhHant = 'zh_hant';
  static const String langJa = 'ja';
  static const String langKo = 'ko';
  static const String langAr = 'ar';
  static const String langDe = 'de';
  static const String langEs = 'es';
  static const String langFr = 'fr';
  static const String langId = 'id';
  static const String langIt = 'it';
  static const String langPt = 'pt';
  static const String langTh = 'th';

  // 默认语言
  static const String defaultLanguageCode = langEn;

  // 支持的语言代码
  static final List<String> supportedLanguages = [
    langEn,
    langJa,
    langEs,
    langPt,
    langKo,
    langId,
    langDe,
    langFr,
    langIt,
    langZhHans,
    langTh
  ];

  // 语言信息映射 (整合语言映射表和语言名称)
  static Map<String, LanguageInfo> get languageInfoMap => {
    langEn: LanguageInfo(
      name: AppTrans.languageEn(),
      translations: en,
    ),
    langZhHans: LanguageInfo(
      name: AppTrans.languagezhHans(),
      translations: zhHans,
    ),
    langZhHant: LanguageInfo(
      name: '繁體中文',
      translations: zhHant,
    ),
    langJa: LanguageInfo(
      name: AppTrans.languageJa(),
      translations: ja,
    ),
    langKo: LanguageInfo(
      name: AppTrans.languageKo(),
      translations: ko,
    ),
    langAr: LanguageInfo(
      name: 'العربية',
      translations: ar,
    ),
    langDe: LanguageInfo(
      name: AppTrans.languageDe(),
      translations: de,
    ),
    langEs: LanguageInfo(
      name: AppTrans.languageEs(),
      translations: es,
    ),
    langFr: LanguageInfo(
      name: AppTrans.languageFr(),
      translations: fr,
    ),
    langId: LanguageInfo(
      name: AppTrans.languageId(),
      translations: id,
    ),
    langIt: LanguageInfo(
      name: AppTrans.languageIt(),
      translations: it,
    ),
    langPt: LanguageInfo(
      name: AppTrans.languagePt(),
      translations: pt,
    ),
    langTh: LanguageInfo(
      name: AppTrans.languageTh(),
      translations: th,
    ),
  };

  // 语言代码到Locale的映射
  static final Map<String, Locale> _localeMap = {
    langEn: const Locale('en', 'US'),
    langZhHans: const Locale('zh', 'CN'),
    langZhHant: const Locale('zh', 'TW'),
    langJa: const Locale('ja', 'JP'),
    langKo: const Locale('ko', 'KR'),
    langAr: const Locale('ar', 'SA'),
    langDe: const Locale('de', 'DE'),
    langEs: const Locale('es', 'ES'),
    langFr: const Locale('fr', 'FR'),
    langId: const Locale('id', 'ID'),
    langIt: const Locale('it', 'IT'),
    langPt: const Locale('pt', 'BR'),
    langTh: const Locale('th', 'TH'),
  };

  // RTL语言列表
  static final Set<String> rtlLanguages = {langAr};

  // 检查是否为RTL语言
  bool isRtl(String langCode) => rtlLanguages.contains(langCode);

  // 当前语言
  final RxString currentLanguage = defaultLanguageCode.obs;

  @override
  Map<String, Map<String, String>> get keys => {
        for (var entry in languageInfoMap.entries)
          entry.key: entry.value.translations
      };

  // 初始化服务
  Future<TranslationService> init() async {
    FFLog.info('Initializing translation service...',
        tag: 'TranslationService');

    // 从存储中获取语言设置
    final storage = SafeStorage();
    String? langCode = storage.read(languageKey);
    FFLog.info('Retrieved stored language: $langCode',
        tag: 'TranslationService');

    // 如果有存储的语言设置，使用该设置；否则使用设备语言或默认语言
    if (langCode != null && languageInfoMap.containsKey(langCode)) {
      FFLog.info('Using stored language: $langCode', tag: 'TranslationService');
      changeLocale(langCode);
    } else {
      // 使用系统语言
      FFLog.info('No valid stored language found, using system language',
          tag: 'TranslationService');
      useSystemLanguage();
    }

    return this;
  }

  // 更改语言
  void changeLocale(String langCode) {
    FFLog.info('Attempting to change locale to: $langCode',
        tag: 'TranslationService');

    if (!languageInfoMap.containsKey(langCode)) {
      FFLog.warning(
          'Language code not supported: $langCode, falling back to default: $defaultLanguageCode',
          tag: 'TranslationService');
      langCode = defaultLanguageCode;
    }

    final storage = SafeStorage();
    final String previousLang = currentLanguage.value;
    final Locale newLocale = _localeMap[langCode]!;

    // 存储语言设置
    storage.write(languageKey, langCode);
    FFLog.info('Language setting saved to storage: $langCode',
        tag: 'TranslationService');

    // 更新当前语言
    currentLanguage.value = langCode;

    // 更新应用语言
    Get.updateLocale(newLocale);

    FFLog.info(
        'Language changed from: $previousLang to: $langCode (${languageInfoMap[langCode]?.name})',
        tag: 'TranslationService');
    FFLog.info(
        'Locale updated to: ${newLocale.languageCode}_${newLocale.countryCode}',
        tag: 'TranslationService');
    Get.log('Language changed to: $langCode');
  }

  // 使用系统语言
  void useSystemLanguage() {
    FFLog.info('Attempting to use system language', tag: 'TranslationService');
    final deviceLocale = Get.deviceLocale;

    if (deviceLocale != null) {
      FFLog.info(
          'Device locale detected: ${deviceLocale.languageCode}_${deviceLocale.countryCode}',
          tag: 'TranslationService');
      final deviceLangCode = _getLanguageCode(deviceLocale);
      FFLog.info('Mapped device language code: $deviceLangCode',
          tag: 'TranslationService');

      // 检查设备语言是否在支持的语言列表中
      if (supportedLanguages.contains(deviceLangCode)) {
        FFLog.info('Device language is supported, using: $deviceLangCode',
            tag: 'TranslationService');
        changeLocale(deviceLangCode);
      } else {
        // 如果设备语言不在支持列表中，强制使用英文
        FFLog.info(
            'Device language $deviceLangCode is not in supported list, forcing English',
            tag: 'TranslationService');
        changeLocale(defaultLanguageCode);
      }
    } else {
      // 使用默认语言
      FFLog.warning(
          'Unable to detect device locale, using default: $defaultLanguageCode',
          tag: 'TranslationService');
      changeLocale(defaultLanguageCode);
    }
  }

  // 设置语言 - 智能处理各种输入格式
  void setLanguage(String lang) {
    FFLog.info('Setting language with input: $lang', tag: 'TranslationService');
    final langCode = getLanguageCodeFromInput(lang, null);
    FFLog.info('Resolved language code: $langCode', tag: 'TranslationService');
    changeLocale(langCode);
  }

  // 获取当前语言
  String getCurrentLanguage() => currentLanguage.value;

  // 获取当前Locale
  Locale getCurrentLocale() =>
      _localeMap[currentLanguage.value] ?? const Locale('en', 'US');

  // 获取支持的语言列表
  List<String> getSupportedLanguages() => supportedLanguages;

  // 获取语言名称
  String getLanguageName(String langCode) =>
      languageInfoMap[langCode]?.name ?? langCode;

  // 智能解析各种格式的语言输入，转换为标准语言代码
  String getLanguageCodeFromInput(String languageCode, String? countryCode) {
    FFLog.debug(
        'Resolving language code from input: $languageCode, countryCode: $countryCode',
        tag: 'TranslationService');

    // 处理已经是完整语言代码格式的情况
    if (languageInfoMap.containsKey(languageCode)) {
      FFLog.debug('Input is already a valid language code: $languageCode',
          tag: 'TranslationService');
      return languageCode;
    }

    // 特殊处理中文，需要区分简繁体
    if (languageCode == 'zh') {
      // 根据国家/地区代码判断使用简体还是繁体
      if (countryCode == 'TW' || countryCode == 'HK' || countryCode == 'MO') {
        FFLog.debug(
            'Chinese with TW/HK/MO country code, using Traditional Chinese',
            tag: 'TranslationService');
        return langZhHant; // 繁体中文
      } else if (countryCode == null) {
        // 没有指定国家代码，检查设备语言
        final deviceLocale = Get.deviceLocale;
        if (deviceLocale != null && deviceLocale.countryCode != null) {
          final country = deviceLocale.countryCode!;
          FFLog.debug(
              'No country code specified, checking device country: $country',
              tag: 'TranslationService');
          if (country == 'TW' || country == 'HK' || country == 'MO') {
            FFLog.debug('Device country indicates Traditional Chinese',
                tag: 'TranslationService');
            return langZhHant;
          }
        }
      }
      FFLog.debug('Using Simplified Chinese as default for Chinese language',
          tag: 'TranslationService');
      return langZhHans; // 默认使用简体中文
    }

    // 对于其他标准语言代码（en, ja, ko等），如果在支持列表中直接返回
    if (supportedLanguages.contains(languageCode)) {
      FFLog.debug('Input is a supported standard language code: $languageCode',
          tag: 'TranslationService');
      return languageCode;
    }

    // 处理旧式标准代码格式（带国家代码的情况）
    if (languageCode.contains('_') || languageCode.contains('-')) {
      // 提取语言部分
      final baseLang = languageCode.split(RegExp(r'[_-]'))[0].toLowerCase();
      FFLog.debug('Extracted base language from code with country: $baseLang',
          tag: 'TranslationService');

      // 对中文特殊处理
      if (baseLang == 'zh') {
        if (languageCode.contains('TW') ||
            languageCode.contains('HK') ||
            languageCode.contains('MO') ||
            languageCode.contains('Hant')) {
          FFLog.debug(
              'Chinese code indicates Traditional Chinese: $languageCode',
              tag: 'TranslationService');
          return langZhHant;
        } else {
          FFLog.debug(
              'Chinese code indicates Simplified Chinese: $languageCode',
              tag: 'TranslationService');
          return langZhHans;
        }
      }

      // 对其他语言，如果基础语言代码在支持列表中，直接返回
      if (supportedLanguages.contains(baseLang)) {
        FFLog.debug('Base language is supported: $baseLang',
            tag: 'TranslationService');
        return baseLang;
      }
    }

    // 如果无法匹配，返回默认语言
    FFLog.debug(
        'No matching language found, using default: $defaultLanguageCode',
        tag: 'TranslationService');
    return defaultLanguageCode;
  }

  // 获取语言代码 (从Locale)
  String _getLanguageCode(Locale? locale) {
    if (locale == null) {
      FFLog.debug('Locale is null, using default language',
          tag: 'TranslationService');
      return defaultLanguageCode;
    }
    FFLog.debug(
        'Getting language code from locale: ${locale.languageCode}_${locale.countryCode}',
        tag: 'TranslationService');
    return getLanguageCodeFromInput(locale.languageCode, locale.countryCode);
  }
}

/// 语言信息类，用于整合语言相关的所有信息
class LanguageInfo {
  final String name;
  final Map<String, String> translations;

  const LanguageInfo({
    required this.name,
    required this.translations,
  });
}

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  // TODO 正式包需配置
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCqxB8rF01635yqNZJoYhC8fMmbt7IYCY8',
    appId: '1:402592875397:android:ce992d0b4133b9cbecbaec',
    messagingSenderId: '402592875397',
    projectId: 'fzfbtest',
  );

  // TODO 正式包需配置
  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBm9xfZwiIilQVGpB3TZMzZAEuQbz7_A2k',
    appId: '1:402592875397:ios:0265d00afa72a75decbaec',
    messagingSenderId: '402592875397',
    projectId: 'fzfbtest',
  );
}

/// 密钥管理类
import 'dart:math';
import 'package:get_storage/get_storage.dart';
import 'package:playlet/utils/safe_storage.dart';

class SecretRepo {
  // =============== 单例模式实现 ===============
  /// 单例实例
  static final SecretRepo _instance = SecretRepo._();
  
  /// 私有构造函数
  SecretRepo._();
  
  // =============== 常量定义 ===============
  /// RC4密钥的存储键名
  static const String _rc4KeyName = "secret_repoapi_rc4_key";
  
  // =============== 私有变量 ===============
  /// RC4密钥的内存缓存
  static String? _cachedRC4Key;
  
  // =============== 公共方法 ===============
  /// 获取RSA公钥
  static String getPublicKey() {
    // 使用分段拼接和混淆的方式生成公钥
    const List<String> parts = [
      'MIGf', 'MA0G', 'CSqG', 'SIb3',
      'DQEB', 'AQUA', 'A4GN', 'ADCB',
      'iQKB', 'gQCj', 'R7Z+', 'HTtK',
      '1iGx', 'ZHcA', 'Dpyr', '9P+J',
      'Vwko', 'YmTq', '3GBf', '4b3b',
      '385m', 'iNAg', '0zs0', 'cYCh',
      'sGUv', 'E6m7', 'OCmx', 'zYy/',
      'tlM4', 'nThW', 'oSC1', 'H04o',
      'CMmB', 'D38w', 'wpRu', '5OYu',
      'eOI9', '6pFX', '6cVJ', 'lmrm',
      'AuaQ', 'jgPm', 'Vi8U', '/zqr',
      'EDma', 'ZOCU', 'yy6z', 'y9+R',
      '51Xm', '574K', 'oBka', 'fZGF',
      '+wID', 'AQAB'
    ];

    // 使用动态计算的方式组合公钥
    final StringBuffer key = StringBuffer();
    key.write('-----BEGIN PUBLIC KEY-----\n');

    // 使用复杂的组合逻辑
    String base64Content = '';
    for (int i = 0; i < parts.length; i++) {
      // 每4个部分组合一次，增加复杂度
      if (i % 4 == 0 && i > 0) {
        base64Content += '\n';
      }
      base64Content += parts[i];
    }

    key.write(base64Content);
    key.write('\n-----END PUBLIC KEY-----');

    return key.toString();
  }

  /// 生成RC4密钥
  static String generateRC4Key() {
    // 如果内存缓存中已有值，直接返回
    if (_cachedRC4Key != null) {
      return _cachedRC4Key!;
    }
    
    final SafeStorage storage = SafeStorage();
    
    // 检查是否已存在RC4密钥
    if (storage.hasData(_rc4KeyName)) {
      _cachedRC4Key = storage.read(_rc4KeyName);
      return _cachedRC4Key!;
    }
    
    // 生成新的RC4密钥并保存
    final String newKey = _generateRandomString(32);
    storage.write(_rc4KeyName, newKey);
    _cachedRC4Key = newKey;
    return newKey;
  }
  
  // =============== 私有方法 ===============
  /// 生成指定长度的随机字符串
  static String _generateRandomString(int length) {
    const String characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    final Random random = Random();
    final StringBuffer buffer = StringBuffer();
    
    for (int i = 0; i < length; i++) {
      final int index = random.nextInt(characters.length);
      buffer.write(characters[index]);
    }
    
    return buffer.toString();
  }
}

import 'package:playlet/common/http/enum.dart';

class Result {
  int status = HttpResultStatus.success;
  String? message;
  dynamic data;

  Result({required this.status, this.message, this.data});

  Result.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'];
  }

  bool get isSuccess => status == HttpResultStatus.success;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['data'] = data;
    return data;
  }

  @override
  String toString() {
    return 'Result{status: $status, message: $message, data: $data}';
  }
}

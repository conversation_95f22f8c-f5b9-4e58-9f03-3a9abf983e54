// 首页：discover
// 追剧记录：collections
// 最近观看：recently
// Feed流：shorts
// 播放页：immersion
// 充值：topup
// 签到：check_in
// 更多列表：more
// 福利中心：rewards
String? getFrom(String? url) {
  final homeUrls = ["/homeData/encrypt/getTabHomeData"];
  if (homeUrls.contains(url)) {
    return 'discover';
  }
  final collectionsUrls = [
    '/collect/collectList',
  ];
  if (collectionsUrls.contains(url)) {
    return 'collections';
  }
  final recentlyUrls = [
    '/watchHistory/getWatchHistoryList',
    '/watchHistory/delWatchHistory',
  ];
  if (recentlyUrls.contains(url)) {
    return 'recently';
  }
  final shortsUrls = [
    '/forYou/encrypt/getForYouListPageNewV2',
  ];
  if (shortsUrls.contains(url)) {
    return 'shorts';
  }
  final immersionUrls = [
    '/shortPlay/shortPlayDetail',
    '/dramaInfo/dramaList',
    '/dramaInfo/dramaDetailBatch',
    '/shortPlay/unlockByCoin',
    '/shortPlay/unlockByWatchAd',
    '/shortPlay/watchAdUnlockInfo',
    '/sku/getCoinsStoreListAndAdInfoBySkuModel',
  ];
  if (immersionUrls.contains(url)) {
    return 'immersion';
  }
  final topupUrls = [
    '/sku/getCoinsStoreListBySkuModel',
    '/subscription/getProductListV3',
    '/subscription',
  ];
  if (topupUrls.contains(url)) {
    return 'topup';
  }
  final moreUrls = [
    '/homeData/encrypt/getBannerMore',
  ];
  if (moreUrls.contains(url)) {
    return 'more';
  }
  final rewardsUrls = [
    '/sig/sign',
    '/sig/signRecord',
    '/ad/getAdBonus',
    '/ad/signWatchAd',
    '/ad/watchAdUnLockComplete',
    '/appTask/getAppTaskList',
    '/appTask/receiveRewards',
  ];
  if (rewardsUrls.contains(url)) {
    return 'rewards';
  }
  return null;
}

import 'dart:io';

import 'package:playlet/config/index.dart';
import 'package:playlet/service/app_device_service.dart';
import 'package:playlet/service/attribute_service.dart';
import 'package:playlet/utils/app_language.dart';
import 'package:playlet/utils/token.dart';
import 'package:uuid/uuid.dart';

class HttpHeader {
  static Future<Map<String, dynamic>> getHeaders({bool isIp = true}) async {
    Map<String, dynamic> headers = {};
    headers["Content-Type"] = "application/json";
    headers["Accept"] = "application/json";
    headers["AppVersion"] = await AppDeviceService.instance.getVersion();
    if (Platform.isAndroid) {
      headers["isE"] = await AppDeviceService.instance.isRealDevice();
      headers["isD"] = await AppDeviceService.instance.isDeveloperMode();
      headers['gaid'] = await AppDeviceService.instance.getAdvertisingId();
      if(AppDeviceService.instance.mcc!=""){
        headers['mcc'] = AppDeviceService.instance.mcc;
      }
    }

    if (Platform.isIOS) {
      headers["isS"] = await AppDeviceService.instance.isRealDevice();
      headers['idfa'] = await AppDeviceService.instance.getAdvertisingId();
    }

    headers['TraceId'] = const Uuid().v4();
    headers["clientPlatform"] = Platform.isAndroid ? "android" : "iOS";
    headers['system_country_code'] =
        AppDeviceService.instance.getFormatSystemCountryCode();
    // 环境信息
    headers['environment'] = Config.isProduction ? 'prod' : 'dev';    // const bool.fromEnvironment('dart.vm.product') ? 'prod' : 'dev';
    headers['afVersion'] = '1';
    headers["buildValue"] = await AppDeviceService.instance.getVersion();
    headers["locale"] = AppDeviceService.instance.getFormatSystemLanguage();
    headers["timeZone"] = AppDeviceService.instance.currentTimeZone ?? '';
    headers["model"] = await AppDeviceService.instance.getDeviceModel();
    headers["isR"] = await AppDeviceService.instance.isRootedDevice();
    final deviceId = await AppDeviceService.instance.getFingerprint();
    if (deviceId != null) {
      headers["deviceId"] = deviceId;
      headers["vefCode"] = _generateMockVefCode(deviceId);
    }

    headers["language"] = AppLanguage.getCurrentFormatLanguage();
    if (await AppDeviceService.instance.isVpnActive()) {
      headers["hasProxy"] = true;
    }

    // 直接从AttributeService获取加密后的IP地址，有数据就添加到请求头，没有就不添加
    if (isIp) {
      final encryptedIp = AttributeService.getEncryptedIpForHeader();
      if (encryptedIp != null && encryptedIp.isNotEmpty) {
        headers["ci"] = encryptedIp;
      }
    }

    final authToken = Token.getToken();
    if (authToken.isNotEmpty) {
      headers["Authorization"] = authToken;
    }

    headers.forEach((key, value) {
      value = value.toString();
      headers[key] = value.replaceAll(RegExp(r'[^\x20-\x7E]'), '');
    });
    return headers;
  }

  static String _generateMockVefCode(String deviceId) {
    // 模拟设备验证码生成
    return deviceId.hashCode.toRadixString(16);
  }
}

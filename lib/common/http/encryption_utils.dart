import 'dart:convert';
import 'dart:typed_data';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:pointycastle/asymmetric/api.dart';

/// 加解密工具类
class EncryptionUtils {
  static const String _tag = 'EncryptionUtils';

  static void _log(String message) {
    // todo 改成logger库
    print('[$_tag] $message');
  }

  static void _logError(String method, dynamic error,
      [StackTrace? stackTrace]) {
    _log('Error in $method: $error');
    if (stackTrace != null) {
      _log('StackTrace: $stackTrace');
    }
  }

  /// 使用RSA加密数据
  static String encryptWithRSA(String data, String publicKeyString) {
    if (data.isEmpty) return '';
    if (!publicKeyString.contains('BEGIN PUBLIC KEY')) return '';

    try {
      final publicKey = encrypt.RSAKeyParser().parse(publicKeyString);
      final encrypter = encrypt.Encrypter(
        encrypt.RSA(
          publicKey: publicKey as RSAPublicKey,
          encoding: encrypt.RSAEncoding.PKCS1,
        ),
      );
      return encrypter.encrypt(data).base64;
    } catch (e) {
      _log('RSA加密失败: $e');
      return '';
    }
  }

  /// RC4加密
  static String encryptWithRC4(String data, String key) {
    if (data.isEmpty || key.isEmpty) return '';

    try {
      final inputBytes = utf8.encode(data);
      final encryptedBytes = _rc4Base(Uint8List.fromList(inputBytes), key);
      final asString = String.fromCharCodes(encryptedBytes);
      return _toHexString(asString);
    } catch (e) {
      _log('RC4加密失败: $e');
      return '';
    }
  }

  /// RC4解密
  static String decryptWithRC4(String data, String key) {
    if (data.isEmpty || key.isEmpty) return '';
    if (!RegExp(r'^[0-9A-Fa-f]+$').hasMatch(data)) return '';

    try {
      final encryptedBytes = _hexStringToBytes(data);
      final decryptedBytes = _rc4Base(encryptedBytes, key);
      return utf8.decode(decryptedBytes);
    } catch (e) {
      _log('RC4解密失败: $e');
      return '';
    }
  }

  /// 解密响应数据并转换为Map
  static Map<String, dynamic> decryptResponseData(
    dynamic encryptedData,
    String key,
  ) {
    if (key.isEmpty) return {'error': '解密密钥为空'};
    if (encryptedData == null) return {'error': '输入数据为空'};
    if (encryptedData is! String) {
      return encryptedData is Map<String, dynamic>
          ? encryptedData
          : {'error': '数据类型错误，无法解密'};
    }

    try {
      final decrypted = decryptWithRC4(encryptedData, key);
      if (decrypted.isEmpty) return {'error': 'RC4解密失败'};
      return jsonDecode(decrypted);
    } catch (e) {
      _log('响应数据解密失败: $e');
      return {'error': '解密失败: $e'};
    }
  }

  /// 处理请求体加密
  static String transformRequestBody({
    required String encodedPath,
    required Map<String, dynamic>? body,
    required String apiRc4Key,
  }) {
    if (encodedPath.isEmpty) return "{}";
    if (apiRc4Key.isEmpty) return "{}";

    try {
      final requestMap = body ?? {};
      final jsonString = jsonEncode(requestMap);

      final encrypted = encryptWithRC4(jsonString, apiRc4Key);
      return encrypted.isEmpty ? "{}" : encrypted;
    } catch (e) {
      _log('请求体处理失败: $e');
      return "{}";
    }
  }

  // 私有工具方法

  /// RC4基础算法
  static Uint8List _rc4Base(Uint8List input, String key) {
    if (input.isEmpty || key.isEmpty) return Uint8List(0);

    int x = 0;
    int y = 0;
    final state = _initKey(key);
    final result = Uint8List(input.length);

    for (int i = 0; i < input.length; i++) {
      x = (x + 1) & 0xff;
      y = ((state[x] & 0xff) + y) & 0xff;

      // 交换state[x]和state[y]
      final temp = state[x];
      state[x] = state[y];
      state[y] = temp;

      final xorIndex = ((state[x] & 0xff) + (state[y] & 0xff)) & 0xff;
      result[i] = input[i] ^ state[xorIndex];
    }
    return result;
  }

  /// 初始化RC4密钥
  static Uint8List _initKey(String key) {
    if (key.isEmpty) return Uint8List(256);

    final keyBytes = utf8.encode(key);
    final state = Uint8List(256);

    // 初始化状态数组
    for (int i = 0; i < 256; i++) {
      state[i] = i;
    }

    int index1 = 0;
    int index2 = 0;

    // KSA (Key Scheduling Algorithm)
    for (int i = 0; i < 256; i++) {
      index2 = ((keyBytes[index1] & 0xff) + (state[i] & 0xff) + index2) & 0xff;

      // 交换state[i]和state[index2]
      final temp = state[i];
      state[i] = state[index2];
      state[index2] = temp;

      index1 = (index1 + 1) % keyBytes.length;
    }
    return state;
  }

  /// 16进制字符串转字节数组
  static Uint8List _hexStringToBytes(String hexString) {
    if (hexString.isEmpty || hexString.length % 2 != 0) return Uint8List(0);
    if (!RegExp(r'^[0-9A-Fa-f]+$').hasMatch(hexString)) return Uint8List(0);

    final result = Uint8List(hexString.length ~/ 2);
    for (int i = 0; i < hexString.length; i += 2) {
      result[i ~/ 2] = int.parse(hexString.substring(i, i + 2), radix: 16);
    }
    return result;
  }

  /// 按照Java的实现将字符串转换为16进制字符串
  static String _toHexString(String s) {
    if (s.isEmpty) return '';

    final sb = StringBuffer();
    for (int i = 0; i < s.length; i++) {
      final ch = s.codeUnitAt(i);
      final s4 = (ch & 0xFF).toRadixString(16);
      sb.write(s4.length == 1 ? '0$s4' : s4);
    }
    return sb.toString();
  }
}

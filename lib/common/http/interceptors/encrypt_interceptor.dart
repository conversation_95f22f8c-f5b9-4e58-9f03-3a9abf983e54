import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:playlet/common/http/encryption_utils.dart';
import 'package:playlet/common/http/http_header.dart';
import 'package:playlet/common/http/no_encryption.dart';
import 'package:playlet/common/http/secret_repo.dart';
import 'package:playlet/common/log/ff_log.dart';

class EncryptInterceptor extends InterceptorsWrapper {
  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // 判断是否需要加密
    final isEncrypt = !noEncryptionList.contains(options.uri.path);
    Map<String, dynamic> headersMap = await HttpHeader.getHeaders(
      isIp: isEncrypt,
    );
    options.headers.addAll(headersMap);
    if (isEncrypt) {
      options.data = encryptRequestData(options.data, options.uri.path);
    } else {
      options.data = jsonEncode(options.data);
    }
    FFLog.debug(generateCurlCommand(
      options.uri.toString(),
      options.headers,
      options.data,
    ));
    return handler.next(options); //继续处理请求
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final Map<String, dynamic> responseData = jsonDecode(response.data);
    response.data = decryptResponseData(responseData);
    return handler.next(response); //继续处理响应
  }

  /// 加密请求数据
  String encryptRequestData(Map<String, dynamic>? requestData, String path) {
    // 获取本地生成的随机密钥
    final String apiRc4Key = SecretRepo.generateRC4Key();
    final requestBody = EncryptionUtils.transformRequestBody(
      encodedPath: path,
      body: requestData,
      apiRc4Key: apiRc4Key,
    );
    return requestBody;
  }

  /// 解密响应数据
  Map<String, dynamic> decryptResponseData(Map<String, dynamic> responseData) {
    if (responseData.containsKey('result') &&
        responseData['result'] is String) {
      // 获取本地生成的随机密钥
      final String apiRc4Key = SecretRepo.generateRC4Key();
      final decryptedData = EncryptionUtils.decryptResponseData(
        responseData['result'],
        apiRc4Key,
      );
      return decryptedData;
    }
    return responseData;
  }

  /// 生成curl命令字符串
  String generateCurlCommand(
      String url, Map<String, dynamic> headers, String? body) {
    final StringBuffer curl = StringBuffer('curl -X POST "$url"');

    // 添加请求头
    headers.forEach((key, value) {
      curl.write(' -H "$key: $value"');
    });

    // 添加请求体
    if (body != null) {
      curl.write(' -d \'$body\'');
    }

    return curl.toString();
  }
}

import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_name.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/http/interceptors/encrypt_interceptor.dart';
import 'package:playlet/common/http/interceptors/logger_interceptor.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/modules/debug/proxy/debug_proxy_controller.dart';
import 'package:playlet/routers/app_navigator.dart';
import 'package:playlet/utils/auth.dart';
import 'package:playlet/utils/token.dart';
import 'package:playlet/utils/track_event.dart';

import 'get_from.dart';
import 'result.dart';

class HttpService {

  static const String _logTag = "HTTP";
  // 单例模式
  static final HttpService _instance = HttpService._internal();

  factory HttpService() => _instance;

  HttpService._internal();

  /// http服务是否已初始化
  bool isInitialized = false;

  late Dio _dio;
  Dio get dio => _dio;
  DateTime? _lastTokenExpiryTime;

  // 初始化 Dio 实例
  void init({int connectTimeout = 500000, int receiveTimeout = 300000}) {
    _dio = Dio(
      BaseOptions(
        connectTimeout: Duration(milliseconds: connectTimeout),
        receiveTimeout: Duration(milliseconds: receiveTimeout),
        responseType: ResponseType.plain,
        validateStatus: (status) => true,
      ),
    );
 
    // 添加拦截器
    _dio.interceptors.add(EncryptInterceptor());
    _dio.interceptors.add(dioLoggerInterceptor);

    if (!Config.isProduction) {
      DebugProxyController.loadProxyConfigFromCache();
      
      // 测试环境下支持抓包
      final adapter = _dio.httpClientAdapter as IOHttpClientAdapter;
      adapter.createHttpClient = () {
        final client = HttpClient();
        client.findProxy = (url) {
          final ip = Config.proxyIp;
          final port = Config.proxyPort;
          if (ip != null && ip.isNotEmpty && 
              port != null && port.isNotEmpty) {
            return 'PROXY $ip:$port';
          } else {
            return 'DIRECT';
          }
        };
        // 接受无效和不受信任的证书
        client.badCertificateCallback =
            (X509Certificate cert, String host, int port) => true;
        return client;
      };
    }

    isInitialized = true;
  }

  // GET 请求
  Future<Result> get<T>(String path,
      {Map<String, dynamic>? params, Options? options, bool? printRequestData}) async {
    try {
      final response = await _dio.get(getRequestUrl(path),
          queryParameters: params, options: options);
      return _handleResponse(response);
    } on DioException catch (e) {
      return _handleError(e, printRequestData: printRequestData);
    }
  }

  // POST 请求
  Future<Result> post(String path,
      {Map<String, dynamic>? data, Options? options, bool? printRequestData}) async {
    try {
      final response =
          await _dio.post(getRequestUrl(path), data: data, options: options);
      return _handleResponse(response);
    } on DioException catch (e) {
      return _handleError(e, printRequestData: printRequestData);
    }
  }

  // PUT 请求
  Future<Result> put(String path,
      {Map<String, dynamic>? data, Options? options, bool? printRequestData}) async {
    try {
      final response =
          await _dio.put(getRequestUrl(path), data: data, options: options);
      return _handleResponse(response);
    } on DioException catch (e) {
      return _handleError(e, printRequestData: printRequestData);
    }
  }

  // 处理响应
  Future<Result> _handleResponse(Response response) async {
    // 记录URL和响应内容
    final data = response.data;
    dynamic json;
    if (data is String) {
      json = data;
    } else {
      try {
        json = jsonEncode(data);
      } catch (e) {
        json = data;
      }
    }
    FFLog.info(
        "Response URL: ${response.requestOptions.uri}, DATA: $json", tag: _logTag);
    try {
      final result = Result.fromJson(response.data);

      if (!result.isSuccess) {
        // 处理token过期情况
        if (result.status == 20000 ||
            result.status == 20001 ||
            result.status == 20002 ||
            result.status == 20003) {
          // 30秒内不重复执行
          if (_lastTokenExpiryTime == null ||
              DateTime.now().difference(_lastTokenExpiryTime!).inSeconds >= 30) {
            _lastTokenExpiryTime = DateTime.now();
            if (result.status == 20000) {
               useTrackEvent(EventName.token_status, extra: {EventKey.status: EventValue.illegal});
            } else if (result.status == 20003) {
               useTrackEvent(EventName.token_status, extra: {EventKey.status: EventValue.expired});
            }
           
            // 退出登录，清除本地缓存
            // 添加超时处理
            try {
              await Auth.signOut().timeout(const Duration(seconds: 5));
            } catch (e) {
              FFLog.error("firebase登出超时: $e", tag: _logTag);
              // 即使超时也继续执行
            } finally {
              await Token.removeToken();
              AppNavigator.startSplashPage();
            }
          }
        }
        return Result(
          status: result.status,
          message: result.message,
        );
      }
      return result;
    } catch (e) {
      return Result(
        status: 500,
        message: e.toString(),
      );
    }
  }

  String getRequestUrl(String path) {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }
    return '${Config.baseUrl}$path';
  }

  // 错误处理
  Result _handleError(DioException error, {bool? printRequestData = true}) {
    FFLog.error("Error: $error", tag: _logTag);
    // 增加更详细的错误日志
    FFLog.error("Request URL: ${error.requestOptions.uri}", tag: _logTag);
    FFLog.error("Request Method: ${error.requestOptions.method}", tag: _logTag);
    FFLog.error("Request Headers: ${error.requestOptions.headers}", tag: _logTag);
    if (printRequestData == null || printRequestData == true) {
      FFLog.error("Request Data: ${error.requestOptions.data}", tag: _logTag);
    }
    FFLog.error("Status Code: ${error.response?.statusCode}", tag: _logTag);
    FFLog.error("Response Data: ${error.response?.data}", tag: _logTag);
    final path = error.requestOptions.uri.toString();
    FFLog.error("网络请求超时URL: $path");
    final url = path.replaceAll(Config.baseUrl, "");
    final from = getFrom(url);
    if (from != null) {
      useTrackEvent(TrackEvent.unable_connect_server, extra: {
        'from': from,
      });
    }
    return Result(
      status: error.response?.statusCode ?? 500,
      message: error.message,
    );
  }
}

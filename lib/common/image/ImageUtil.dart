import 'dart:io';

import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:playlet/common/log/ff_log.dart';

/// 图片相关处理
class ImageUtil {
  // 单独用于图片的网络加载
  static final _dio = Dio();

  /// 获取图片的字节码
  static Future<List<int>?> fetchImageBytes(String imageUrl) async {
    try {
      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );
      if (response.statusCode == 200) {
        return response.data;
      } else {
        return null;
      }
    } catch (e) {
      FFLog.error('fetchImageBytes: $e', tag: "ImageUtil");
      return null;
    }
  }

  /// 获取的图片的本地路径，会下载到cache目录下
  /// cache目录下已经存在则直接返回
  static Future<String?> fetchImageForPath(String imageUrl) async {
    try {
      // 先判断本地缓存
      final Directory directory = await getApplicationCacheDirectory();
      final String filePath = '${directory.path}/${imageUrl.hashCode}.jpg';
      final File file = File(filePath);
      bool isExists = await file.exists();
      if (isExists) {
        return filePath;
      }
      // 从网络上下载
      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );
      if (response.statusCode == 200) {
        await file.writeAsBytes(response.data);
        return filePath;
      } else {
        return null;
      }
    } catch (e) {
      FFLog.error('fetchImageForPath: $e', tag: "ImageUtil");
      return null;
    }
  }
}

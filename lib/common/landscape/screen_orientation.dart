import 'package:flutter/services.dart';

class ScreenOrientation {
  // com.xxxxxx.app/screen_orientation  这个channel名称必须对应上
  static const MethodChannel _channel =
  MethodChannel('com.flareflow.ios/screen_orientation');

  static Future<void> setLandscape() async {
    await _channel.invokeMethod('setLandscape');
  }

  static Future<void> setPortrait() async {
    await _channel.invokeMethod('setPortrait');
  }
}
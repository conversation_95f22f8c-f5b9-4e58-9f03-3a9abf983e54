import 'dart:async';
import 'dart:ui' as ui;
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:palette_generator/palette_generator.dart';
import 'package:playlet/common/image/ImageUtil.dart';
import 'package:playlet/common/log/ff_log.dart';

class ColoDominantUtil {

  /// 获取图片的主色
  static Future<Color?> fetchDominantColor(String imageUrl) async {
    try {
      var imageData = await ImageUtil.fetchImageBytes(imageUrl);
      if (imageData != null) {
        final Uint8List bytes = Uint8List.fromList(imageData);
        // 使用 Flutter 的 decodeImageFromList 解码图片
        final ui.Image image = await decodeImageFromList(bytes);
        // 创建正确格式的图片数据
        final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.rawRgba);
        if (byteData != null) {
          // 根据封面通常的展示，取左上角的颜色来计算主色
          PaletteGenerator paletteGenerator = await PaletteGenerator.fromByteData(EncodedImage(byteData, width: image.width, height: image.height),
              region: Rect.fromCenter(center: Offset(image.width / 8.0, image.height / 16.0), width: image.width / 16.0, height: image.height / 8.0), maximumColorCount: 5);
          Color? color = paletteGenerator.dominantColor?.color;
          return color;
        }
        return null;
      } else {
        return null;
      }
    } catch (e) {
      FFLog.error('获取图片主色异常: $e', tag: "ColoDominantUtil");
      return null;
    }
  }
}

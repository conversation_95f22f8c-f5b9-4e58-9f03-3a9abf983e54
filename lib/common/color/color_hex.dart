import 'dart:ui';

class ColorHex {
  
  static Color fromHex(String hexColor, {double opacity = 1.0}) {
    int intColor = _parseHexColor(hexColor, opacity: opacity);
    return Color(intColor);
  }

  static int _parseHexColor(String hexColor, {double opacity = 1.0}) {
    // 移除可能存在的 # 符号
    hexColor = hexColor.replaceAll("#", "");

    // 如果十六进制颜色值长度为 6 位，在前面补全 FF 表示完全不透明
    if (hexColor.length == 6) {
      hexColor = "FF$hexColor";
    }

    // 将十六进制字符串转换为整数
    int intColor = int.parse(hexColor, radix: 16);

    // 根据传入的透明度调整颜色的 alpha 值
    int alpha = (opacity * 255).round().clamp(0, 255);
    intColor = intColor & 0x00FFFFFF | (alpha << 24);

    return intColor;
  }
}








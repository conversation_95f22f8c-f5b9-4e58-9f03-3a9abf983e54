import 'dart:async';

import 'package:get/get.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/components/main/bottom_nav_widget.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/gen/assets.gen.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/modules/main/main_controller.dart';
import 'package:playlet/modules/mylist/mylist_controller.dart';
import 'package:playlet/routers/pages.dart';
import 'package:playlet/routers/route_observer_custom.dart';
import 'package:playlet/service/analytics_service.dart';
import 'package:playlet/service/dialog_opportunity_service.dart';
import 'package:playlet/service/resource_bit/model/resource_bit_model.dart';
import 'package:playlet/service/resource_bit/resource_bit_manager.dart';
import 'package:playlet/service/resource_bit/services/base_service.dart';
import 'package:playlet/service/resource_bit/track_event/resource_bit_track_event.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/events.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/track_event.dart';

import '../../service/payment/payment_service.dart';

class BottomNavController extends GetxController {
  RxInt pageIndex = 0.obs;

  RxBool isBottomNavVisible = true.obs;

  RxString currentRoute = "".obs;

  StreamSubscription? routeSubscription;

  StreamSubscription? pushNotificationSubscription;

  // tab数据源
  final List<TabItemProtocol> tabbarItems = [
    TabbarItem(
      title: AppTrans.discover(),
      icon: Assets.tabbar.home.path,
      activeIcon: Assets.tabbar.homeActive.path,
    ),
    TabbarItem(
      title: AppTrans.reels(),
      icon: Assets.tabbar.reels.path,
      activeIcon: Assets.tabbar.reelsActive.path,
    ),
    TabbarItem(
      title: AppTrans.myList(),
      icon: Assets.tabbar.myList.path,
      activeIcon: Assets.tabbar.myListActive.path,
    ),
    TabbarItem(
      title: AppTrans.profile(),
      icon: Assets.tabbar.profile.path,
      activeIcon: Assets.tabbar.profileActive.path,
    )
  ];
  // 显示的item数据源，包括tabItem和资源位item等
  RxList<TabItemProtocol> displayItems = <TabItemProtocol>[].obs;
  StreamSubscription<List<ResourceBitModel>?>? _resourceBitStreamSubscription;

  void _refreshDisplayItems() {
    // 构造显示数据源
    displayItems.clear();
    displayItems.addAll(tabbarItems);
  }

  Future<void> _checkAndInsertResourceBitBottomTab() async {
    try {
      final service = _getResourceBitService();
      if (service == null) {
        return;
      }
      ResourceBitModel? model = await service.getResourceModel();
      if (model == null) {
        return;
      }
      String? path = await service.loadResource(model);
      if (path == null || path.isEmpty) {
        return;
      }

      await service.onShown(model);
      ResourceBitTrackEvent.reelShow(model);

      _refreshDisplayItems();
      _insertResourceBitTabImageItem(model, path);
    } catch (e) {
      FFLog.debug('加载底部tab资源失败: $e');
      return;
    }
  }

  void _insertResourceBitTabImageItem(ResourceBitModel model, String path) {
    // 资源位item
    final tabResourceBitImageItem = TabResourceBitImageItem(
      imagePath: path,
      onTap: () {
        model.onResourceBitClick(ResourceBitScene.bottomTab);
      },
    );
    if (displayItems.length > 1) {
      // 插入到中间
      displayItems.insert(
          (displayItems.length / 2).toInt(), tabResourceBitImageItem);
    } else {
      displayItems.add(tabResourceBitImageItem);
    }
  }

  ResourceBitBaseService? _getResourceBitService() {
    final service =
        ResourceBitManager.getInstance().getService(ResourceBitScene.bottomTab);
    return service;
  }

  void _addResourceBitChangedListener() {
    final service = _getResourceBitService();
    if (service == null) {
      return;
    }
    _removeResourceBitChangedListener();
    _resourceBitStreamSubscription = service.modelsStream.listen((models) {
      unawaited(_checkAndInsertResourceBitBottomTab());
    });
  }

  void _removeResourceBitChangedListener() {
    _resourceBitStreamSubscription?.cancel();
    _resourceBitStreamSubscription = null;
  }

  @override
  void onInit() {
    pageIndex.value = 0;
    currentRoute.value = Routes.mainPage;
    routeSubscription = eventBus.on<RouteObserverCustomEvent>().listen((event) {
      currentRoute.value = event.to;
      // if (event.from.contains('details') && pageIndex.value == 2) {
      //   Future.delayed(const Duration(seconds: 1), () {
      //     _refreshMyListData(false);
      //   });
      // }
      if (event.from != '' &&
          event.to == Routes.mainPage &&
          pageIndex.value == 0) {
        _handleOnHomePageShowed();
      }
    });
    pushNotificationSubscription =
        eventBus.on<PushNotificationToReelEventData>().listen(
      (event) {
        if (currentRoute.value != Routes.mainPage) {
          Get.until((route) => Get.currentRoute == Routes.mainPage);
        }
        pageIndex.value = 1;
        update(["page_view"]);
        Future.delayed(const Duration(milliseconds: 200), () {
          eventBus.fire(
            PushNotificationToReelShortEventData(
              shortPlayId: event.shortPlayId,
              dramaId: event.dramaId,
            ),
          );
        });
      },
    );
    _addResourceBitChangedListener();
    _refreshDisplayItems();
    unawaited(_checkAndInsertResourceBitBottomTab());
    super.onInit();
  }

  @override
  void onClose() {
    _removeResourceBitChangedListener();
    routeSubscription?.cancel();
    pushNotificationSubscription?.cancel();
    super.onClose();
  }

  Future<void> _refreshMyListData(bool collectionEnable) async {
    final myListController = Get.find<MylistController>();
    if (collectionEnable) {
      myListController.refreshCollectionData();
    } else {
      myListController.refreshData();
    }
  }

  /// 切换页面
  void changePage(int index) {
    pageIndex.value = index;
    update(["bottom_nav", "page_view"]);
    if (index == 0) {
      _handleOnHomePageShowed();
    }
    if (index == 2) {
      _refreshMyListData(true);
    }

    if (index == 3) {
      // 添加补单方法
      if (Config.isProduction == true) {
        Get.find<PaymentService>().startRecover(from: EventValue.profile);
      }
      final UserService userService = Get.find<UserService>();
      userService.fetchUserInfo();
      userService.fetchRewardsTotalBonus();
      final DialogOpportunityService dialogOpportunityService =
          Get.find<DialogOpportunityService>();
      if (dialogOpportunityService.initProfileLoginDialog()) {
        Get.loginDialog(
          from: EventValue.porfile_windows,
          fbRewardsTrackFrom: EventValue.profileLoginButton,
        );
      }
    }

    _useEventHandle(index);
    // 切换tab立即上报
    flushDataOnPageChange();
  }

  void _handleOnHomePageShowed() {
    Get.find<MainController>().onHomePageShowed();
  }

  /// 切换底部导航栏的显示与隐藏
  void toggleBottomNav() {
    isBottomNavVisible.value = !isBottomNavVisible.value;
    update(["bottom_nav"]);
  }

  ///埋点上报处理
  void _useEventHandle(int index) {
    switch (index) {
      case 0:
        {}
        break;
      case 1:
        {}
        break;
      case 2:
        {
          ///功能模块：我的页面
          ///事件名称：追剧页面展示（mylist_show）
          ///打点时机：追剧页面展示
          ///参数：无
          useTrackEvent(TrackEvent.mylistShow);
        }
        break;
      case 3:
        {
          ///功能模块：我的页面
          ///事件名称：我的页面展示（me_show）
          ///打点时机：我的页面展示时
          ///参数：无
          useTrackEvent(TrackEvent.meShow);
        }
        break;
      default:
    }
  }
}

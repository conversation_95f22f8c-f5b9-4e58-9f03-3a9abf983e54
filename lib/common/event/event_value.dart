class EventValue {
  static const String shorts = 'shorts';
  static const String immersion = 'immersion';
  //升级弹窗
  static const mandatory_updates = "mandatory_updates";
  static const recommended_updates = "recommended_updates";
  static const update = "update";
  static const cancel = "cancel";

  //登录
  static const login_page = "login_page";
  static const porfile_windows = "porfile_windows";
  static const task_facebook_login = "task_facebook_login";
  static const facebook = "facebook";
  static const apple = "apple";
  static const google = "google";
  static const String assetProtection = 'asset_protection';
  static const String loginGift = 'login_gift';

  //用户数据
  static const push = "push";
  static const icon = "icon";
  static const deeplink = "deeplink";

  /// 来源值
  // 通知栏
  static const String fromPush = 'Push';
  // 常驻通知栏
  static const String permanent = 'permanent';
  // FSI通知
  static const String fsi = 'fsi';
  // 实时活动
  static const String realTimeActivity = 'real_time_activity';
  // 投放链接
  static const String fromDeeplink = 'deeplink';
  // 首页
  static const String fromDiscover = 'discover';
  // 追剧记录
  static const String fromCollections = 'collections';
  // 最近观看
  static const String fromRecently = 'recently';
  // feed流
  static const String fromShorts = 'shorts';
  // 常驻通知栏
  static const String fromAlert = 'alert';
  // 查看更多
  static const String fromDiscoverMore = 'discover_more';

  //归因相关
  static const String netError = 'net_error';
  // 归因剧来源
  static const String campaign = 'campaign';
  // 兜底剧来源
  static const String defaultValue = 'default';


  // 剧集解锁-广告解锁前向来源
  static const String adUnlockFrom = 'ad_unlock';
  static const String continuousAdPopFrom = 'continuous_ad_pop';
  static const String continuousAdRetentionPopFrom = 'continuous_ad_retention_pop';

// 剧集解锁-沉浸页的待解锁弹窗
  static const String adsCoins = 'ads_coins';
// 剧集解锁-付费挽留弹窗
  static const String payRetain = 'pay_retain';
// 剧集解锁-付费挽留商品
  static const String payRetainCommodity = 'pay_retain_commodity';
// 剧集解锁-订阅
  static const String coinSubscribe = 'coin_subscribe';
// 订阅详情页
  static const String subscribe = 'subscribe';
// 充值页-订阅
  static const String rechargeSubscribe = 'recharge_subscribe';
// 订阅到期弹窗
  static const String subscribeExpirePopup = 'subscribe_expire_popup';
// 商店充值页
  static const String recharge = 'recharge';

// 菜单选集入口
  static const String shortMMenu = 'short_menu';
// 底部解锁按钮
  static const String unlockedEpButton = 'unlocked_ep_button';
// 自动拉起
  static const String auto = 'auto';
// 其他
  static const String other = 'other';
// 充值页
  static const String topUp = 'topup';
// 用户手动触发
  static const String manual = 'manual';

// 竖屏状态
  static const String vertical = 'vertical';
// 横屏状态
  static const String horizontal = 'horizontal';

///任务中心
/// Facebook 账号登录任务
static const String fbLogin = "fb_login";
/// 填写邮箱任务
static const String provideEmail = "provide_email";
/// 填写手机号任务
static const String providePhone = "provide_phone";
/// 观看激励广告任务
static const String watchRewardAds = "watch_reward_ads";
/// 自定义弹窗 - 通知权限任务
static const String customize = "customize";
///新手任务
static const String newbieTask = "newbie_task";
///每日任务
static const String dailyTask = "daily_task";
/// 通知权限任务
static const String notification = "notification";

///支付相关 新增
///待解锁页
static const String pageUnlocked = "page_unlocked";

//token 过期
static const String expired = "expired";
//token 非法
static const String illegal = "illegal";

  // 首页-横幅banner
  static const String discoverBanner = 'discover_banner';

  /// 首页登录引导
static const String discoverLogin = "discover_login";

/// 我的页面-登录按钮
static const String profileLoginButton = "profile_login_button";

/// 福利中心-FB
static const String rewardFbTask = "reward_fb_task";

  /// 支付补单场景
  static const String profile = "profile";
  static const String launch = "launch";
  static const String inapp = "inapp";

  /// 最近观看
  static const String recentlyWatchedLong = "recently_watched_long";
  static const String recentlyWatchedSquare = "recently_watched_square";

  /// 资源位
  // 资源位跳转
  static const String fromResourceBit = 'resource_bit';
  // 资源位场景
  static const String resourceBitScene = 'resource_bit';

  /// 新人推荐
  static const String newUserPage = "new_user_page";
  /// 退出剧集挽留
  static const String immersionBack = "immersion_back";
  static const String immersionBackPop = "immersion_back_pop";


}

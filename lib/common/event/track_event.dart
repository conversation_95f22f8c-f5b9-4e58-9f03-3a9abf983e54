class TrackEvent {
// event name or key
  static const push_id = "push_id";
  static const push_name = "push_name";
  static const push_open = "push_open";
  static const push_send = "push_send";
  static const title = "title";
  static const content = "content";
  static const reel_id = "reel_id";
  static const scene = "scene";
  static const module_name = "module_name";
  static const module_id = "module_id";
  static const from = "from";
  static const is_free = "is_free";
  static const tab_name = "tab_name";
  static const tab_id = "tab_id";
  static const system_not_permission_show = "system_not_permission_show";
  static const not_permission_success = "not_permission_success";

  static const system = "system";
  static const discover = "discover";
  static const discover_show = "discover_show";
  static const discover_show_success = "discover_show_success";
  static const icon = "icon";

  static const reel_request = "reel_request";
  static const status = "status";
  static const episode = "episode";
  static const reel_show = "reel_show";
  static const shorts = "shorts";
  static const action = "action";
  static const other = "other";
  static const recharge_show_end = "recharge_show_end";
  static const recharge_show = "recharge_show";
  static const order_create = "order_create";
  static const amount = "amount";
  static const product_id = "product_id";
  static const sku = "sku";
  static const template_id = "template_id";
  static const platform = "platform";
  static const payment_method = "payment_method";
  static const lock_begin = "lock_begin";
  static const order_create_fail = "order_create_fail";
  static const link_tag = "link_tag";
  static const type = "type";
  static const order_create_cancel = "order_create_cancel";
  static const pay_success = "pay_success";
  static const is_first = "is_first";
  static const is_main = "is_main";
  static const order_no = "order_no";
  static const restore_click = "restore_click";
  static const subscribe_show = "subscribe_show";
  static const subscribe_thing_show = "subscribe_thing_show";
  static const reel_cut = "reel_cut";
  static String restore_result = "restore_result";
  static String result = "result";
  static String genre = "genre";

  static const String mylistShow = 'mylist_show';
  static const String meShow = 'me_show';

  // 视频性能相关
  static const String initialLoadingTime = 'initial_loading_time';
  static const String reelPlayBuffering = 'reel_play_buffering';
  static const String reelPlayFail = 'reel_play_fail';
  static const String playbackProgressTrack = 'playback_progress_track';
  static const String playTimeReal = 'play_time_real';
  static const String reelPlay = 'reel_play';
  static const String video_speed_adjust = 'video_speed_adjust';

  static const video_drag_start = "video_drag_start";
  static const seconds = "seconds";
  static const enter_reel_play = "enter_reel_play";
  static const module_type = "module_type";
  static const collections = "collections";
  static const recently = "recently";

  // 空视图无法连接网络
  static const unable_connect_server = "unable_connect_server";

  // 商品展示
  static const order_show = "order_show";
  static const sort = "sort";
  static const coins = "coins";
  static const bonus = "bonus";

  // 广告相关
  static const ad_request = "ad_request";
  static const ad_fill = "ad_fill";
  static const ad_real_impressions = "ad_real_impressions";
  static const ad_impressions = "ad_impressions";
  static const ad_impressions_duration = "ad_impressions_duration";
  static const ad_revenue = "ad_revenue";
  static const ad_clicks = "ad_clicks";
  static const ad_placement_show = "ad_placement_show";
  static const watch_ad_click = "watch_ad_click";

  /// 福利中心相关埋点
  /// 福利中心页面展示
  static const String rewardShow = "reward_show";
  /// 完成每日签到
  static const String checkInClick = "check_in_click";
  /// 翻倍弹窗展示
  static const String checkInDoubleRewardShow = "check_in_double_reward_show";
  /// 翻倍弹窗点击观看
  static const String checkInDoubleRewardClick = "check_in_double_reward_click";
  /// 做任务
  static const String taskClick = "task_click";
  /// 任务完成
  static const String taskFinish = "task_finish";
  /// 首页右上角礼包点击
  static const String homeRewardClick = "home_reward_click";
  /// 福利中心入口点击
  static const String rewardClick = "reward_click";

  /// 我的页面 - 奖励
  static const profileReward = "profile_reward";
  /// 点击推送
  static const push = "push";
  /// 邮箱/手机绑定成功弹窗
  static const binding = "binding";
  /// 签到第几天
  static const day = "day";
  /// 任务名称
  static const taskName = "taskName";
  /// 通知权限任务
  static const String notification = "notification";

  ///首页登录礼包弹窗展示
  static const String loginGiftShow = 'login_gift_show';
  ///首页登录礼包弹窗点击
  static const String loginGiftClick = 'login_gift_click';
  ///资产保护提醒弹窗展示
  static const String assetProtectionShow = 'asset_protection_show';
  ///资产保护提醒弹窗点击
  static const String assetProtectionClick = 'asset_protection_click';

  ///货币代码
  static const String currency = "currency";

  // 商品点击埋点
  static const orderClick = "order_click";

  /// 新人推荐
  /// 新人推荐入口按钮曝光
  static const suspensionButtonClick = "suspension_button_click";
  /// 新人推荐入口按钮点击
  static const suspensionButtonShow = "suspension_button_show";
  /// 新人推荐页展示
  static const newUserDramaShow = "new_user_drama_show";
  /// 新人推荐页点击页面元素时上报
  static const newUserDramaClick = "new_user_drama_click";
  
  /// coins/bonus消耗
  static const episodeUnlockSuccess = "episode_unlock_success";
  /// 广告解锁剧集
  static const episodeUnlockAdSuccess = "episode_unlock_ad_success";
  /// 退出剧集挽留弹窗展示
  static const logoutWindowShow = "logout_window_show";
  /// 退出剧集挽留弹窗点击
  static const logoutWindowClick = "logout_window_click";

  /// 连续广告弹窗展示
  static const String continuousAPop = "continuous_ad_pop";
  /// 连续广告挽留弹窗展示
  static const String continuousAdRetentioPop = "continuous_ad_retention_pop";
  /// ===== 实时活动相关 BEGIN =====
  
  /// 实时活动发送
  /// 实时活动触发时上报，无论用户是否真的有看到，只要在通知栏或者锁屏上有显示就上报
  static const realTimeActivitySend = "real_time_activity_send";

  /// 实时活动显示
  /// 实时活动在用户看到时上报
  static const realTimeActivityShow = "real_time_activity_show";

  /// 实时活动点击
  /// 实时活动点击时上报
  static const realTimeActivityClick = "real_time_activity_click";

  /// ===== 实时活动相关 END =====
  
  /// ===== 资源位相关 Begin =====
  
  // 活动入口展示-汇总
  static const bfActEntryShowAll = "bf_act_entry_show_all";
  // 活动入口点击-汇总
  static const bfActEntryClickAll = "bf_act_entry_click_all";
  // 开屏资源位展示
  static const bfAppOpenShow = "bf_appopen_show";
  // 开屏资源位点击
  static const bfAppOpenClick = "bf_appopen_click";
  // 首页弹窗展示
  static const bfDiscoverWindowShow = "bf_discover_window_show";
  // 首页弹窗点击
  static const bfDiscoverWindowClick = "bf_discover_window_click";
  // 底部悬浮图入口展示
  static const bfIconTabShow = "bf_icon_tab_show";
  // 底部悬浮图入口点击
  static const bfIconTabClick = "bf_icon_tab_click";
  // 底部tab曝光
  static const activityTabShow = "activity_tab_show";
  // 底部tab点击
  static const activityTabClick = "activity_tab_click";
  // 追剧页面横幅曝光
  static const mylistBannerShow = "mylist_banner_show";
  // 追剧页面横幅点击
  static const mylistBannerClick = "mylist_banner_click";
  // 我的页面横幅曝光
  static const profileBannerShow = "profile_banner_show";
  // 我的页面横幅点击
  static const profileBannerClick = "profile_banner_click";
  // 宣传图轮播曝光
  static const discoverCarouselShow = "discover_carousel_show";
  // 宣传图轮播点击
  static const discoverCarouselClick = "discover_carousel_click";
  // Feed流首位资源位曝光
  static const feedOnePositionShow = "feed_one_position_show";
  // Feed流首位资源位点击
  static const feedOnePositionClick = "feed_one_position_click";

  /// ===== 资源位相关 End =====
}

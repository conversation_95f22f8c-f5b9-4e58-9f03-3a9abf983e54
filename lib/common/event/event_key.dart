class EventKey {
  static const scene = "scene";
  static const from = "from";

  static const type = "type";
  static const action = "action";
  static const is_success = "is_success";
  static const reason = "reason";
  static const is_auto = "is_auto";//传入值[0：否，1：是]
  static const duration = "duration";
  static const session_id = "session_id";
  static const start_time = "start_time";
  static const end_time = "end_time";

  static const String reelId = 'reel_id';
  static const String reelName = 'reel_name';
  static const String time = 'time';
  static const String errorCode = 'error_code';
  static const String episodeId = 'episode_id';

  static const String watchProgressPercent = 'watch_progress_percent';
  static const String episode = 'episode';
  static const String isFree = 'is_free';
  static const String moduleName = 'module_name';
  static const String moduleId = 'module_id';
  static const String positionId = 'position_id';
  static const String logic = 'logic';
  static const String speedLevel = 'speed_level';
  static const String lockBegin = 'lock_begin';
  static const String seconds = 'seconds';
  static const String firstDrama = 'first_drama';


  // 广告解锁剧集
  static const String	isEpEnd = "is_ep_end";	//传入值[0：否，1：是]。最后一集判定：A剧共100集，解锁到100集，第100集则是最后一集；若A剧更新到10集，最后一集仍为100集
  static const String lock_begin = "lock_begin";

  //广告相关
  static const ad_unit_name = "ad_unit_name";
  static const ad_platform = "ad_platform";
  static const ad_unit_id = "ad_unit_id";
  static const ad_mediation_name = "ad_mediation_name";
  static const ad_placement = "ad_placement";
  static const ecpm = "ecpm";
  static const impressions_duration = "impressions_duration";
  static const ad_active = "ad_active";

  //归因相关
  static const String status = 'status';
  static const String errorMessage = 'error_message';

  ///支付相关
  static const String resourceBitId = "resource_bit_id";
  static const String playDirection = "play_direction";
  static const String currency = "currency";

  /// ===== 实时活动相关 BEGIN =====
  
  static const String sort = "sort";

  /// ===== 实时活动相关 END =====
  /// 
  /// ===== 资源位相关 Begin =====
  
  static const String skipType = "skip_type";

  /// ===== 资源位相关 End =====
}

import 'package:logger/logger.dart';

class FFLogPrinter extends LogPrinter {
  @override
  List<String> log(LogEvent event) {
    final level = event.level.name.toUpperCase();
    final time = event.time.toString();
    final stackTrace = StackTrace.current.toString().split('\n')[4].split(' ').last;
    final message = event.message.toString();  // 确保消息被完整转换为字符串
    
    final logMessage = '[$level] [$time] $stackTrace $message';
    return [logMessage];
  }
}
import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

import 'file/ff_log_file_manager.dart';

class FFLogOutput extends LogOutput {

  late final FFLogFileManager _logFileManager = FFLogFileManager();
  late final _isIOS = Platform.isIOS;
  
  @override
  void output(OutputEvent event) async {
    for (var line in event.lines) {
      if (kDebugMode) {
        log(getLog(line, event.level));
      }
      if (event.level != Level.trace && event.level != Level.debug) {
        _logFileManager.writeLog(line);
      }
    }
  }

  String getLog(String log, Level level) {
    final emoji = '${_getEmoji(level)} ';
    final logMessage = '$emoji$log';
    if (_isIOS) {
      return logMessage;
    }
    final colorCode = _defaultLevelColors[level];
    return '$colorCode$logMessage\x1B[0m';
  }

  @override
  Future<void> destroy() async {
    
  }

  final Map<Level, AnsiColor> _defaultLevelColors = {
    Level.trace: AnsiColor.fg(AnsiColor.grey(0.5)),
    Level.debug: const AnsiColor.none(),
    Level.info: const AnsiColor.fg(12),
    Level.warning: const AnsiColor.fg(208),
    Level.error: const AnsiColor.fg(196),
    Level.fatal: const AnsiColor.fg(199),
  };

  String _getEmoji(Level level) {
    switch (level) {
      case Level.trace:
        return '🔍';
      case Level.debug:
        return '🐛';
      case Level.info:
        return 'ℹ️';
      case Level.warning:
        return '⚠️';
      case Level.error:
        return '❌';
      case Level.fatal:
        return '💀';
      default:
        return '';
    }
  }
}
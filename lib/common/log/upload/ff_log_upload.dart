import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:isolate';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:playlet/common/http/http.dart';
import 'package:playlet/config/index.dart';
import '../ff_log_constant.dart';
import '../file/ff_log_file_writer.dart';

/// 日志上传类，负责收集日志文件并上传到服务器
class FFLogUploader {
  static const String _tempLogDirName = 'temp_logs';
  static const int _batchSize = 300 * 1024; // 每批300K数据
  static const int _maxRetries = 2;  // 最大重试次数
  
  // 单例实例
  static final FFLogUploader _instance = FFLogUploader._internal();
  
  // 工厂构造函数
  factory FFLogUploader() {
    return _instance;
  }
  
  // 私有构造函数
  FFLogUploader._internal();
  
  // 上传状态标志
  bool _isUploading = false;
  
  // 当前接收端口
  ReceivePort? _receivePort;

  /// 上传日志到服务器
  /// 
  /// 返回上传是否成功
  Future<bool> uploadLogs() async {
    // 如果已经在上传中，直接返回
    if (_isUploading) {
      if (ffEnablePrint) {
        print('[FFLogUploader] 日志上传已在进行中，忽略此次请求');
      }
      return false;
    }
    
    _isUploading = true;
    
    final rootToken = RootIsolateToken.instance;
    if (rootToken == null) {
      if (ffEnablePrint) {
        print('[FFLogUploader] 无法获取RootIsolateToken，上传失败');
      }
      _isUploading = false;
      return false;
    }
    
    _receivePort = ReceivePort();
    
    try {
      await Isolate.spawn(
        _processLogsInIsolate,
        _IsolateParams(_receivePort!.sendPort, rootToken),
      );
    } catch (e) {
      if (ffEnablePrint) {
        print('[FFLogUploader] 创建Isolate失败: $e');
      }
      _cleanupResources();
      return false;
    }

    final completer = Completer<bool>();

    _receivePort?.listen((message) {
      if (message is SendPort) {
        // 收到子Isolate的发送端口，建立双向通信
        message.send(_MainIsolateCommand.start);
        return;
      }
      
      if (message is _LogBatch) {
        // 收到日志批次，进行上传
        _uploadLogBatch(message).then((success) {
          final responsePort = message.responsePort;
          if (success) {
            responsePort.send(_IsolateResponse.batchUploaded);
          } else {
            responsePort.send(_IsolateResponse.uploadFailed);
            if (!completer.isCompleted) {
              completer.complete(false);
              _cleanupResources();
            }
          }
        });
        return;
      }
      
      if (message is _IsolateResponse) {
        // 收到子Isolate的响应
        switch (message) {
          case _IsolateResponse.completed:
            if (!completer.isCompleted) {
              if (ffEnablePrint) {
                print('[FFLogUploader] 全部上传完成');
              }
              completer.complete(true);
              _cleanupResources();
            }
            break;
          case _IsolateResponse.failed:
            if (!completer.isCompleted) {
              completer.complete(false);
              _cleanupResources();
            }
            break;
          default:
            break;
        }
        return;
      }
    });

    return completer.future;
  }
  
  // 清理资源
  void _cleanupResources() {
    
    if (_receivePort != null) {
      _receivePort?.close();
      _receivePort = null;
    }
    
    _isUploading = false;
  }

  /// 在主Isolate中上传日志批次
  static Future<bool> _uploadLogBatch(_LogBatch batch) async {
    if (ffEnablePrint) {
      print('[FFLogUploader] 上传批次 ${batch.index} 大小: ${batch.content.length}');
    }
    
    // 添加重试次数计数器
    int retryCount = 0;
    
    while (true) {
      try {
        final batchData = jsonEncode({
          'content': batch.content,
        });

        final response = await HttpService().post(Config.logServerUrl, data: {
          'content': batchData, 
          'localTimeLong': DateTime.now().millisecondsSinceEpoch
        }, printRequestData: false);
        
        if (response.isSuccess) {
          if (ffEnablePrint) {
            print('[FFLogUploader] 上传批次 ${batch.index} 完成');
          }
          return true;
        }
        
        retryCount = await _retryUploadBatch(batch, batch.index, retryCount, response.message);
        if (retryCount <= _maxRetries) {
          continue;
        }
        return false;
      } catch (e) {
        retryCount = await _retryUploadBatch(batch, batch.index, retryCount, e);
        if (retryCount <= _maxRetries) {
          continue;
        }
        return false;
      }
    }
  }

  static Future<int> _retryUploadBatch(
    _LogBatch batch, 
    int index, 
    int retryCount, 
    dynamic message) async {
    
    if (retryCount < _maxRetries) {
      retryCount++;
      if (ffEnablePrint) {
        print('[FFLogUploader] 上传批次 ${batch.index} 失败: $message，正在进行第 $retryCount 次重试');
      }
      // 添加延迟，避免立即重试
      await Future.delayed(Duration(milliseconds: 500 * retryCount));
      return retryCount;
    }
    
    if (ffEnablePrint) {
      print('[FFLogUploader] 上传批次 ${batch.index} 失败: $message，重试 $retryCount 次后仍然失败');
    }
    return ++retryCount;
  }

  /// 在 isolate 中处理日志文件
  static Future<void> _processLogsInIsolate(_IsolateParams params) async {
    final rootSendPort = params.sendPort;
    final rootToken = params.rootToken;
    final receivePort = ReceivePort();
    
    // 发送此Isolate的SendPort到主Isolate
    rootSendPort.send(receivePort.sendPort);
    
    // 使用状态变量替代Completer
    bool isProcessing = false;
    
    // 处理日志的函数，在收到start命令后调用
    Future<void> processLogs() async {
      BackgroundIsolateBinaryMessenger.ensureInitialized(rootToken);

      // 获取日志文件夹路径
      Directory? logDir;
      try {
        logDir = await FFLogFileWriter.getLogDirectory();
      } catch (e) {
        if (ffEnablePrint) {
          print('[FFLogUploader] 获取日志目录失败: $e');
        }
        rootSendPort.send(_IsolateResponse.failed);
        receivePort.close();
        return;
      }

      Directory? appDir;
      try {
        if (Platform.isAndroid) {
          appDir = await getApplicationSupportDirectory();
        } else {
          appDir = await getApplicationCacheDirectory();
        }
      } catch (e) {
        if (ffEnablePrint) {
          print('[FFLogUploader] 获取应用目录失败: $e');
        }
        rootSendPort.send(_IsolateResponse.failed);
        receivePort.close();
        return;
      }
      
      final tempDir = Directory(path.join(appDir.path, _tempLogDirName));
      
      // 清理临时文件夹
      try {
        await _cleanupTempFiles(tempDir);
      } catch (e) {
        if (ffEnablePrint) {
          print('[FFLogUploader] 清理临时文件夹失败: $e');
        }
        rootSendPort.send(_IsolateResponse.failed);
        receivePort.close();
        return;
      }

      if (!await logDir.exists()) {
        if (ffEnablePrint) {
          print('[FFLogUploader] 日志目录不存在');
        }
        rootSendPort.send(_IsolateResponse.failed);
        receivePort.close();
        return;
      }

      try {
        // 创建临时目录
        await tempDir.create(recursive: true);

        // 复制日志文件到临时目录
        final logFiles = await _copyLogFilesToTemp(logDir, tempDir);
        
        if (logFiles.isEmpty) {
          if (ffEnablePrint) {
            print('[FFLogUploader] 没有找到日志文件');
          }
          await _cleanupTempFiles(tempDir);
          rootSendPort.send(_IsolateResponse.completed);
          receivePort.close();
          return;
        }
        
        // 使用回调方式处理日志文件
        await _processLogFilesWithCallback(
          logFiles,
          rootSendPort,
        );
        
        // 清理临时文件
        await _cleanupTempFiles(tempDir);
        
        // 完成
        rootSendPort.send(_IsolateResponse.completed);
        receivePort.close();
        // 子isolate自行终止
        Isolate.current.kill(priority: Isolate.beforeNextEvent);
      } catch (e) {
        if (ffEnablePrint) {
          print('[FFLogUploader] 处理日志文件时出错: $e');
        }
        try {
          await _cleanupTempFiles(tempDir);
        } catch (_) {}
        rootSendPort.send(_IsolateResponse.failed);
        receivePort.close();
        // 子isolate自行终止
        Isolate.current.kill(priority: Isolate.beforeNextEvent);
      }
    }
    
    // 监听消息
    receivePort.listen((message) {
      if (message is _MainIsolateCommand) {
        if (message == _MainIsolateCommand.start && !isProcessing) {
          isProcessing = true;
          processLogs(); // 收到start命令后开始处理
        } else if (message == _MainIsolateCommand.stop) {
          // 收到停止命令，终止自身
          receivePort.close();
          Isolate.current.kill(priority: Isolate.beforeNextEvent);
        }
      } else if (message is _IsolateResponse) {
        if (message == _IsolateResponse.uploadFailed) {
          // 上传失败，终止处理
          rootSendPort.send(_IsolateResponse.failed);
          receivePort.close();
          // 子isolate自行终止
          Isolate.current.kill(priority: Isolate.beforeNextEvent);
        }
      }
    });
  }

  /// 使用回调方式处理日志文件，不使用Completer等待
  static Future<void> _processLogFilesWithCallback(
    List<File> files,
    SendPort sendPort,
  ) async {
    int currentFileIndex = 0;
    int currentLineIndex = 0;
    
    if (currentFileIndex >= files.length) {
      if (ffEnablePrint) {
        print('[FFLogUploader] 没有需要处理的日志');
      }
      return;
    }
    
    // 读取并上传一个批次
    int batchCount = 0;
    bool continueProcessing = true;
    
    while (continueProcessing && currentFileIndex < files.length) {
      final file = files[currentFileIndex];
      
      List<String> lines;
      try {
        lines = await file.readAsLines();
      } catch (e) {
        if (ffEnablePrint) {
          print('[FFLogUploader] 读取文件失败: $e');
        }
        return;
      }
      
      if (currentLineIndex >= lines.length) {
        // 当前文件已读完，移到下一个文件
        currentFileIndex++;
        currentLineIndex = 0;
        continue;
      }
      
      // 准备一个批次
      StringBuffer currentBatch = StringBuffer();
      int currentBatchSize = 0;
      
      // 读取数据直到达到批次大小
      while (currentFileIndex < files.length) {
        if (lines.isEmpty || currentLineIndex >= lines.length) {
          // 当前文件已读完，移到下一个文件
          currentFileIndex++;
          currentLineIndex = 0;
          
          if (currentFileIndex < files.length) {
            try {
              lines = await files[currentFileIndex].readAsLines();
            } catch (e) {
              if (ffEnablePrint) {
                print('[FFLogUploader] 读取文件失败: $e');
              }
              break;
            }
          }
          continue;
        }
        
        // 读取当前行
        final line = lines[currentLineIndex];
        final lineSize = line.length + 1; // 加1计入换行符的长度
        
        // 如果添加这一行会超过批次大小，则停止添加
        if (currentBatchSize + lineSize > _batchSize && currentBatchSize > 0) {
          break;
        }
        
        // 添加行到当前批次，并添加换行符
        currentBatch.writeln(line); // 使用writeln替代write，自动添加换行符
        currentBatchSize += lineSize;
        currentLineIndex++;
      }
      
      // 如果有数据要上传
      if (currentBatchSize > 0) {
        batchCount++;
        final batchContent = currentBatch.toString();
        
        // 上传批次
        final success = await _uploadBatchAndWaitResult(batchContent, batchCount, sendPort);
        if (!success) {
          return; // 上传失败，终止处理
        }
      } else {
        // 没有数据可上传，结束处理
        continueProcessing = false;
      }
    }
  }
  
  /// 上传批次并等待结果
  static Future<bool> _uploadBatchAndWaitResult(
    String content, 
    int index, 
    SendPort sendPort
  ) async {
    final responsePort = ReceivePort();
    
    sendPort.send(_LogBatch(
      content: content,
      index: index,
      responsePort: responsePort.sendPort,
    ));
    
    final uploadResult = await responsePort.first;
    responsePort.close();
    
    return uploadResult == _IsolateResponse.batchUploaded;
  }

  /// 复制日志文件到临时目录
  static Future<List<File>> _copyLogFilesToTemp(
    Directory logDir, 
    Directory tempDir
  ) async {
    if (ffEnablePrint) {
      print('[FFLogUploader] 原文件路径: ${logDir.path}');
      print('[FFLogUploader] 临时文件路径: ${tempDir.path}');
    }
    final List<File> tempFiles = [];
    
    try {
      await for (final entity in logDir.list()) {
        if (entity is File && path.extension(entity.path) == '.log') {
          final fileName = path.basename(entity.path);
          final tempFile = File(path.join(tempDir.path, fileName));
          try {
            await entity.copy(tempFile.path);
            tempFiles.add(tempFile);
          } catch (e) {
            if (ffEnablePrint) {
              print('[FFLogUploader] 复制文件失败: $e');
            }
            // 继续处理其他文件
          }
        }
      }
    } catch (e) {
      if (ffEnablePrint) {
        print('[FFLogUploader] 列出日志文件失败: $e');
      }
      return [];
    }
    
    // 按文件名排序，确保按时间顺序读取
    return FFLogFileWriter.sortLogFiles(tempFiles);
  }

  /// 清理临时文件
  static Future<void> _cleanupTempFiles(Directory tempDir) async {
    try {
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    } catch (e) {
      if (ffEnablePrint) {
        print('[FFLogUploader] 清理临时文件失败: $e');
      }
      // 继续执行，不要因为清理失败而中断整个流程
    }
  }
}

/// Isolate通信参数
class _IsolateParams {
  final SendPort sendPort;
  final RootIsolateToken rootToken;
  
  _IsolateParams(this.sendPort, this.rootToken);
}

/// 日志批次
class _LogBatch {
  final String content;
  final int index;
  final SendPort responsePort;
  
  _LogBatch({
    required this.content,
    required this.index,
    required this.responsePort,
  });
}

/// 主Isolate命令
enum _MainIsolateCommand {
  start,
  stop,
}

/// Isolate响应
enum _IsolateResponse {
  batchUploaded,
  uploadFailed,
  completed,
  failed,
}
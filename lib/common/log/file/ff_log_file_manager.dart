import 'package:flutter/foundation.dart';

import '../ff_log_constant.dart';
import 'ff_log_file_isolate.dart';
import 'ff_log_file_task_queue.dart';

class FFLogFileManager {
  late final FFLogFileTaskQueue _logFileTaskQueue = FFLogFileTaskQueue(onLogTaskProcess: _onLogTaskProcess);
  late final FFLogFileIsolate _logFileIsolate = FFLogFileIsolate();
  
  void writeLog(String log) {
    _logFileTaskQueue.addTask(log);
  }

  void close() {
    _logFileTaskQueue.close();
  }

  void _onLogTaskProcess(String log) async {
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileManager _onLogTaskProcess]: $log');
    }
    bool success = await _logFileIsolate.connect();
    if (!success) {
      return;
    }
    await _logFileIsolate.sendLog(log, _onSendLogComplete);
  }

  void _onSendLogComplete(bool success) {
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileManager _onSendLogComplete] $success');
    }
    _logFileTaskQueue.popTask();
  }
}
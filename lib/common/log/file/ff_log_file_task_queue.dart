import 'dart:async';

import 'package:flutter/foundation.dart';

import '../ff_log_constant.dart';

class FFLogFileTaskQueue {
  void Function(String) onLogTaskProcess;

  late final _queues = <String>[];

  FFLogFileTaskQueue({required this.onLogTaskProcess});
  
  void addTask(String log) {
    _queues.add('$log\n');
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileTaskQueue addTask]: $log');
    }
    if (_queues.length == 1) {
      popTask();
    }
  }

  void popTask() {
    if (_queues.isEmpty) {
      return;
    }
    final log = _queues.removeAt(0);
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileTaskQueue popTask]: $log');
    }
    onLogTaskProcess.call(log);
  }

  void close() {
    _queues.clear();
  }
}
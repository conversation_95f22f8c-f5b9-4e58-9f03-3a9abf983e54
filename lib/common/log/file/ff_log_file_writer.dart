import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:intl/intl.dart';

import '../ff_log_constant.dart';

class FFLogFileWriter {
  RootIsolateToken? rootToken;

  static const int _maxFileSize = 1 * 1024 * 1024; // 单个文件最多1MB
  static const int _maxFileCount = 2;             // 最多保留2个文件
  static const String _logFilePrefix = 'log_';     // 日志文件前缀
  
  Directory? _logDir;
  static final DateFormat _dateFormat = DateFormat('yyyy-MM-dd_HH-mm-ss');

  static Future<Directory> getLogDirectory() async {
    Directory appDir;
    if (Platform.isAndroid) {
      appDir = await getApplicationSupportDirectory();
    } else {
      appDir = await getApplicationCacheDirectory();
    }
    final logDir = Directory(path.join(appDir.path, 'appLogs'));
    return logDir;
  }

  static List<File> sortLogFiles(List<File> logFiles) {
    // 按时间从旧到新排序日志文件
    logFiles.sort((a, b) {
      final aName = path.basename(a.path);
      final bName = path.basename(b.path);
      final aTime = aName.substring(_logFilePrefix.length, aName.lastIndexOf('.'));
      final bTime = bName.substring(_logFilePrefix.length, bName.lastIndexOf('.'));
      final aDateTime = _dateFormat.parse(aTime);
      final bDateTime = _dateFormat.parse(bTime);
      return aDateTime.compareTo(bDateTime);
    });
    return logFiles;
  }

  // 写入日志
  Future<bool> writeLog(String log) async {
    // 获取最新的日志文件
    final currentFilePath = await _getCurrentLogFilePath();
    if (currentFilePath == null) {
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileWriter writeLog] getCurrentLogFilePath failed');
      }
      return false;
    }
    
    // 写入前检查文件大小
    final file = File(currentFilePath);
    if (await file.exists()) {
      final fileSize = await file.length();
      if (fileSize >= _maxFileSize) {
        // 如果文件已经超过大小限制，创建新文件
        final newFilePath = _getLogFilePath();
        if (newFilePath == null) {
          if (ffEnablePrint) {
            print('[LogFile] [FFLogFileWriter writeLog] getLogFilePath failed');
          }
          return false;
        }
        
        // 删除最旧的日志文件
        await _cleanupOldLogs();
        
        // 使用新文件路径
        return await _writeToFile(newFilePath, log);
      }
    }
    
    // 写入日志
    return await _writeToFile(currentFilePath, log);
  }
  
  // 实际写入文件的方法
  Future<bool> _writeToFile(String filePath, String log) async {
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileWriter _writeToFile] begin write log to $filePath');
    }
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        await file.create(recursive: true);
      }
      // 在日志末尾添加换行符
      await file.writeAsString(log, mode: FileMode.append, flush: true);
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileWriter _writeToFile] write log success');
      }
      return true;
    } catch (e) {
      // 错误处理
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileWriter _writeToFile] write log failed: $e');
      }
      return false;
    }
  }

  // 清理旧日志文件
  Future<void> _cleanupOldLogs() async {
    final logFiles = await _getLogFiles();
    if (logFiles == null) {
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileWriter _cleanupOldLogs] getLogFiles failed');
      }
      return;
    }
    
    // 删除多余的旧文件
    while (logFiles.length >= _maxFileCount) {
      await logFiles.first.delete();
      logFiles.removeAt(0);
    }
  }
  
  // 获取当前的日志文件路径
  Future<String?> _getCurrentLogFilePath() async {
    final logFiles = await _getLogFiles();
    if (logFiles == null) {
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileWriter _getCurrentLogFilePath] getLogFiles failed');
      }
      return null;
    }
    
    // 检查最新的日志文件
    if (logFiles.isNotEmpty) {
      final latestFile = logFiles.last;
      final fileSize = await latestFile.length();
      // 只有当文件大小超过限制时才创建新文件
      if (fileSize < _maxFileSize) {
        return latestFile.path;
      }
    }

    // 清理旧日志文件
    await _cleanupOldLogs();

    // 创建新文件路径
    return _getLogFilePath();
  }

    // 获取日志目录
  Future<Directory?> _getLogDirectory() async {
    if (_logDir != null) {
      return _logDir;
    }
    
    try {
      // 确保在后台隔离中初始化 BackgroundIsolateBinaryMessenger
      final token = rootToken;
      if (token != null) {
        BackgroundIsolateBinaryMessenger.ensureInitialized(token);
      }
      
      final logDir = await FFLogFileWriter.getLogDirectory();
      
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }
      _logDir = logDir;
      return _logDir;
    } catch (e) {
      if (ffEnablePrint) {
        final String message = '[LogFile] [FFLogFileWriter _getLogDirectory] Failed to create log directory: $e';
        print(message);
        throw StateError(message);
      }
      return null;
    }
  }

  // 获取新的日志文件路径
  String? _getLogFilePath() {
    if (_logDir == null) {
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileWriter _getLogFilePath] Log directory is not initialized');
      }
      return null;
    }
    final timestamp = _dateFormat.format(DateTime.now());
    return path.join(_logDir?.path ?? '', '$_logFilePrefix$timestamp.log');
  }

  Future<List<File>?> _getLogFiles() async {
    final logDir = await _getLogDirectory();
    if (logDir == null) {
      return null;
    }
    final files = await logDir.list().toList();
    final logFiles = files.whereType<File>()
        .where((f) => path.basename(f.path).startsWith(_logFilePrefix))
        .toList();
    
    return sortLogFiles(logFiles);
  }
}
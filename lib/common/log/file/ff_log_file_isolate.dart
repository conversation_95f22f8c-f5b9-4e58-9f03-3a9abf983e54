import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../ff_log_constant.dart';
import 'ff_log_file_writer.dart';

class FFLogFileIsolate {
  // Isolate状态
  IsolateState _isolateState = IsolateState.notInitialized;
  Isolate? _isolate;
  // 用于与Isolate通信的端口
  SendPort? _sendPort;
  void Function(bool success)? _onComplete;
  // 添加一个队列存储待发送的日志
  final List<_FFLogPendingTask> _pendingLogs = [];

  Future<bool> connect() async {
    // 确保Isolate已初始化，如果断开则重新初始化
    if (_isolateState != IsolateState.notInitialized && _isolateState != IsolateState.disconnected) {
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileIsolate connect] connect abort with isolateState: $_isolateState');
      }
      return true;
    }
    close();

    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileIsolate connect] connecting');
    }
    _isolateState = IsolateState.initializing;
    
    final receivePort = ReceivePort();
    try {
      _isolate = await Isolate.spawn(_logIsolateEntry, receivePort.sendPort);
      _isolateState = IsolateState.connected;
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileIsolate connect] connected');
      }

      receivePort.listen((message) async {
        if (message is SendPort) {
          // 收到 SendPort
          _sendPort = message;
          if (ffEnablePrint) {
            print('[LogFile] [FFLogFileIsolate listen] receive sendPort');
          }
          _sendPort?.send(RootIsolateToken.instance);
          
          // 处理待发送的日志
          await _processPendingLogs();
          return;
        }
        if (message is LogIsolateMessage) {
          if (message.type == LogMessageType.logWrittenSuccess) {
            // 日志写入完成，处理下一条
            if (ffEnablePrint) {
              print('[LogFile] [FFLogFileIsolate listen] receive written success');
            }
            _onCompleteCallback(true);
            return;
          }
          if (message.type == LogMessageType.logWrittenFailed) {
            // 日志写入失败，处理下一条
            if (ffEnablePrint) {
              print('[LogFile] [FFLogFileIsolate listen] receive written failed');
            }
            _onCompleteCallback(false);
            return;
          }
        }
      }, onDone: () {
        // 当ReceivePort关闭时，重连
        if (ffEnablePrint) {
          print('[LogFile] [FFLogFileIsolate onDone] Log isolate connection closed');
        }
        _handleError();
      }, onError: (e) {
        // 当ReceivePort发生错误时，重连
        if (ffEnablePrint) {
          print('[LogFile] [FFLogFileIsolate onError] Error in log isolate connection: $e');
        }
        _handleError();
      });
      return true;
    } catch (e) {
      receivePort.close(); // 添加这行
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileIsolate connectError] Error spawning log isolate: $e');
      }
      _handleError();
      return false;
    }
  }

  // 添加处理待发送日志的方法
  Future<void> _processPendingLogs() async {
    if (_pendingLogs.isEmpty || _sendPort == null) {
      return;
    }
    
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileIsolate _processPendingLogs] processing ${_pendingLogs.length} pending logs');
    }
    
    // 处理所有待发送的日志
    final tasks = List.of(_pendingLogs);
    for (final task in tasks) {
      _pendingLogs.remove(task);
      await _sendLog(task);
    }
  }

  // 发送日志到 Isolate 处理
  Future<void> sendLog(String log, void Function(bool success) onComplete) async {
    _onComplete = onComplete;
    if (_isolateState == IsolateState.notInitialized || _isolateState == IsolateState.disconnected) {
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileIsolate sendLog] send log to isolate failed with isolateState: $_isolateState');
      }
      _onCompleteCallback(false);
      return;
    }

    _pendingLogs.add(_FFLogPendingTask(log, onComplete));

    // 如果_sendPort尚未准备好，不处理
    if (_sendPort == null) {
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileIsolate sendLog] _sendPort not ready');
      }
      return;
    }
    await _processPendingLogs();
  }

  Future<void> _sendLog(_FFLogPendingTask task) async {
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileIsolate sendLog] begin send log to isolate');
    }

    // 发送日志到 Isolate 处理，添加错误处理
    try {
      _sendPort?.send(LogIsolateMessage(LogMessageType.writeLog, task.log));
    } catch (e) {
      // 发送失败，可能是 Isolate 已断开
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileIsolate sendLog] Failed to send log to isolate: $e');
      }
      _handleError();
      _onCompleteCallback(false);
    }
  }

  // 关闭
  void close() {
    // 清空待发送队列
    _pendingLogs.clear();
    
    // 向Isolate发送关闭消息
    _sendPort?.send(LogIsolateMessage(LogMessageType.close));
    _sendPort = null;
    _isolateState = IsolateState.disconnected;
    _onComplete = null;
    _isolate?.kill(priority: Isolate.immediate);
    _isolate = null;

    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileIsolate close]');
    }
  }

  void _onCompleteCallback(bool success) {
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileIsolate _onCompleteCallback] result is $success');
    }
    _onComplete?.call(success);
    _onComplete = null;
  }

  void _handleError() {
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileIsolate _handleError] handle error');
    }
    close();
  }

  // Isolate 处理日志写入
  static void _logIsolateEntry(SendPort mainSendPort) async {
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileIsolate _logIsolateEntry] begin isolate entry');
    }

    final receivePort = ReceivePort();
    if (ffEnablePrint) {
      print('[LogFile] [FFLogFileIsolate _logIsolateEntry] send receivePort to main isolate');
    }

    mainSendPort.send(receivePort.sendPort);
    
    // 标记是否收到关闭信号
    bool shouldClose = false;
    // 标记是否正在处理日志
    bool isProcessingLog = false;
    // 创建日志任务队列
    final List<String> logQueue = [];

    // 创建单个 FFLogFileWriter 实例
    final logFileWriter = FFLogFileWriter();
    
    // 封装关闭 Isolate 的逻辑
    void closeIsolate() {
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileIsolate closeIsolate] close isolate');
      }
      receivePort.close();
      Isolate.current.kill(priority: Isolate.immediate);
    }
    
    // 处理日志队列的方法
    Future<void> processLogQueue() async {
      if (logQueue.isEmpty || isProcessingLog || logFileWriter.rootToken == null) {
        return;
      }
      
      isProcessingLog = true;
      final log = logQueue.removeAt(0);
      
      final success = await logFileWriter.writeLog(log);
      if (success) {
        if (ffEnablePrint) {
          print('[LogFile] [FFLogFileIsolate isolate processLogQueue] log written success');
        }
        mainSendPort.send(LogIsolateMessage(LogMessageType.logWrittenSuccess));
      } else {
        if (ffEnablePrint) {
          print('[LogFile] [FFLogFileIsolate isolate processLogQueue] log written failed');
        }
        mainSendPort.send(LogIsolateMessage(LogMessageType.logWrittenFailed));
      }
      
      isProcessingLog = false;
      
      // 处理完一条后，继续处理队列中的下一条
      if (logQueue.isNotEmpty) {
        processLogQueue();
      } else if (shouldClose) {
        // 队列为空且收到关闭信号，可以安全关闭
        closeIsolate();
      }
    }
    
    receivePort.listen((message) async {
      if (message is RootIsolateToken) {
        if (ffEnablePrint) {
          print('[LogFile] [FFLogFileIsolate isolate listen] receive token');
        }
        // 收到 RootIsolateToken
        logFileWriter.rootToken = message;
        // 收到token后，开始处理队列中的日志
        processLogQueue();
        return;
      }
      if (message is LogIsolateMessage) {
        if (message.type == LogMessageType.writeLog) {
          // 处理写入日志
          final log = message.log;
          if (log == null) {
            if (ffEnablePrint) {
              print('[LogFile] [FFLogFileIsolate isolate listen] receive write log message with null log');
            }
            mainSendPort.send(LogIsolateMessage(LogMessageType.logWrittenFailed));
            return;
          }
          
          // 将日志加入队列
          logQueue.add(log);
          if (ffEnablePrint) {
            print('[LogFile] [FFLogFileIsolate isolate listen] added log to queue, queue size: ${logQueue.length}');
          }
          
          // 如果rootToken已存在，立即处理队列
          if (logFileWriter.rootToken != null) {
            processLogQueue();
          } else {
            if (ffEnablePrint) {
              print('[LogFile] [FFLogFileIsolate isolate listen] waiting for rootToken to process log');
            }
          }
          return;
        }
        if (message.type == LogMessageType.close) {
          // 收到关闭信号，但不立即关闭
          if (ffEnablePrint) {
            print('[LogFile] [FFLogFileIsolate isolate listen] receive close message');
          }
          shouldClose = true;
          // 如果没有正在处理的日志且队列为空，则立即关闭
          if (!isProcessingLog && logQueue.isEmpty) {
            closeIsolate();
          } else {
            if (ffEnablePrint) {
              print('[LogFile] [FFLogFileIsolate isolate listen] receive close message but tasks pending');
            }
          }
          return;
        }
        return;
      }
      // 忽略未知消息
      if (ffEnablePrint) {
        print('[LogFile] [FFLogFileIsolate isolate listen] receive unknown message');
      }
    });
  }
}

// 添加 Isolate 状态枚举
enum IsolateState {
  notInitialized,  // 未初始化
  initializing,    // 初始化中
  connected,       // 已连接
  disconnected,    // 已断开连接
}

// 日志消息类型枚举
enum LogMessageType {
  token,       // token
  writeLog,   // 写入日志
  logWrittenSuccess, // 日志写入成功
  logWrittenFailed,   // 日志写入失败
  close,      // 关闭
}

// 日志消息封装类
class LogIsolateMessage {
  final LogMessageType type;
  final String? log;
  
  LogIsolateMessage(this.type, [this.log]);
}

class _FFLogPendingTask {
  final String log;
  final void Function(bool success) onComplete;
  
  _FFLogPendingTask(this.log, this.onComplete);
}
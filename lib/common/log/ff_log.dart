import 'dart:io';

import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';
import 'ff_log_printer.dart';
import 'ff_log_output.dart';

class FFLog {
  /// 默认的日志标签
  static const String _defaultTag = 'Default';

  /// 打印追踪级别日志（最详细，替代Verbose，仅Debug模式下输出）：Trace
  /// [message] 日志信息，支持任意类型
  /// [tag] 日志标签，用于区分不同模块的日志
  static void trace(dynamic message, {String? tag}) {
    if (kDebugMode) {
      inputLog(Level.trace, '[${tag ?? _defaultTag}] $message');
    }
  }

  /// 打印调试级别日志（仅Debug模式下输出）：Debug
  /// [message] 日志信息，支持任意类型
  /// [tag] 日志标签，用于区分不同模块的日志
  static void debug(dynamic message, {String? tag}) {
    if (kDebugMode) {
      inputLog(Level.debug, '[${tag ?? _defaultTag}] $message');
    }
  }

  /// 打印信息级别日志：Info
  /// [message] 日志信息，支持任意类型
  /// [tag] 日志标签，用于区分不同模块的日志
  static void info(dynamic message, {String? tag}) {
    inputLog(Level.info, '[${tag ?? _defaultTag}] $message');
  }

  /// 打印警告级别日志：Warning
  /// [message] 日志信息，支持任意类型
  /// [tag] 日志标签，用于区分不同模块的日志
  static void warning(dynamic message, {String? tag}) {
    inputLog(Level.warning, '[${tag ?? _defaultTag}] $message');
  }

  /// 打印错误级别日志：Error
  /// [message] 日志信息，支持任意类型
  /// [tag] 日志标签，用于区分不同模块的日志
  static void error(dynamic message, {String? tag}) {
    inputLog(Level.error, '[${tag ?? _defaultTag}] $message');
  }

  /// 打印致命错误级别日志（最严重，Debug下中断程序抛出异常）：Fatal
  /// [message] 日志信息，支持任意类型
  /// [tag] 日志标签，用于区分不同模块的日志
  static void fatal(dynamic message, {String? tag}) {
    final logMessage = '[${tag ?? _defaultTag}] $message';
    inputLog(Level.trace, logMessage);
    if (kDebugMode) {
      throw Exception('Fatal Error: $logMessage');
    }
  }

  static final FFLogPrinter _printer = FFLogPrinter();
  static final FFLogOutput _output = FFLogOutput();

  /// Log a message with [level].
  static void inputLog(
    Level level,
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    var logEvent = LogEvent(
      level,
      message,
      time: time,
      error: error,
      stackTrace: stackTrace,
    );
    var output = _printer.log(logEvent);

    if (output.isNotEmpty) {
      var outputEvent = OutputEvent(logEvent, output);
      try {
        _output.output(outputEvent);
      } catch (e, s) {
        if (kDebugMode) {
          print(e);
          print(s);
        }
      }
    }
  }

  // 使用MethodChannel向Android端发送日志信息
  static Future<void> printToAndroidLogcat(
      String tag, String message, String level) async {
    if (kDebugMode && Platform.isAndroid) {
      try {
        const platform = MethodChannel('com.flareflow.android/log');
        await platform.invokeMethod('logToLogcat', {
          'tag': tag,
          'message': message,
          'level': level,
        });
      } catch (e) {
        FFLog.error('Failed to print to Android logcat: $e', tag: 'ff_log');
      }
    }
  }
}

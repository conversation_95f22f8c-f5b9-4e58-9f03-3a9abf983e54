import 'package:get/get.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:lexo_ttplayer_decryption/vod_player_typedef.dart';
import 'package:playlet/common/event/event_key.dart';
import 'package:playlet/common/event/event_value.dart';
import 'package:playlet/common/event/track_event.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/utils/screen_utils.dart';
import 'package:playlet/utils/track_event.dart';

const String tag = "PlayerViewCoreTrackEvent";

/// 播放器埋点事件处理类
class PlayerViewCoreTrackEvent {
  /// 播放场景：shorts/immersion
  final String scene;
  
  /// 短剧ID -> reel_id
  final int? shortPlayCode;
  
  /// 剧集ID  -> episode_id
  final String? episodeId;

  /// 剧集编号(第几集) -> episode
  final int? episodeNum;
  
  
  /// 前向来源
  final String? from;
  
  /// 是否免费：1 付费，0 免费
  final String? isFree;
  
  /// 首页模块名称
  final String? moduleName;
  
  /// 传入后台的模块id
  final String? moduleId;
  
  /// 位置ID
  final String? positionId;
  
  /// 播放速度
  final String? speedLevel;
  
  /// 解锁卡点
  final String? lockBegin;
  
  /// 资源位标识
  final String? resourceBitId;
  
  /// 进剧逻辑：nature/random
  final String logic;
  
  /// 剧集名称
  final String? reelName;
  
  /// 视频时长
  final int? videoDuration;
  
  /// 记录播放器初始化成功时间戳
  int? playerInitTimestamp;
    
  /// 首帧加载完毕时间戳
  int? firstFrameReadyTimestamp;
  final Map<String, String> _extra = {};

  PlayerViewCoreTrackEvent({
    required this.scene,
    this.shortPlayCode,
    this.episodeNum, 
    this.episodeId,
    this.from,
    this.isFree,
    this.moduleName,
    this.moduleId,
    this.positionId,
    this.speedLevel,
    this.lockBegin,
    this.resourceBitId,
    this.logic = 'nature',
    this.reelName,
    this.videoDuration,
  }) {
    FFLog.info('PlayerViewCoreTrackEvent -> 初始化埋点事件, scene=$scene, shortPlayCode=$shortPlayCode episodeId=$episodeId episodeNum=$episodeNum', tag: tag);
  }
  
  /// 记录播放器初始化成功时间
  void recordPlayerInitTime() {
    playerInitTimestamp = DateTime.now().millisecondsSinceEpoch;
  }

  /// 添加有效参数
  void _addParamIfValid<T>(String key, T? value) {
    if (value == null) return;
    if (value is String && value.isEmpty) return;
    _extra[key] = value.toString();
  }
  
  /// 填充参数并上报事件
  void _inflateParamsAndTrackEvent(String event) {
    _addParamIfValid(EventKey.reelId, shortPlayCode);
    _addParamIfValid(EventKey.episodeId, episodeId);
    _addParamIfValid(EventKey.episode, episodeNum);
    _addParamIfValid(EventKey.scene, scene);
    _addParamIfValid(EventKey.from, from);
    _addParamIfValid(EventKey.isFree, isFree);
    _addParamIfValid(EventKey.moduleName, moduleName);
    _addParamIfValid(EventKey.moduleId, moduleId);
    _addParamIfValid(EventKey.positionId, positionId);
    _addParamIfValid(EventKey.speedLevel, speedLevel);
    _addParamIfValid(EventKey.lockBegin, lockBegin);
    _addParamIfValid(EventKey.resourceBitId, resourceBitId);
    _addParamIfValid(EventKey.reelName, reelName);
    _addParamIfValid(EventKey.logic, logic);
    
    final context = Get.context;
    if (context != null) {
      _addParamIfValid(EventKey.playDirection, ScreenUtils.isLandscape(context)
          ? EventValue.horizontal
          : EventValue.vertical);
    }

    useTrackEvent(event, extra: _extra);
  }

  /// 上报播放事件
  void trackReelPlay() {
    FFLog.info('trackReelPlay -> 上报播放事件', tag: tag);
    _inflateParamsAndTrackEvent(TrackEvent.reelPlay);
  }

  /// 上报首帧加载时长
  void trackFirstFrameReadyTime() {
    firstFrameReadyTimestamp = DateTime.now().millisecondsSinceEpoch;
   
    if (playerInitTimestamp == null) {
      FFLog.info('trackInitialLoadingTime -> 未记录播放器初始化时间，无法上报首帧加载时长', tag: tag);
      return;
    }

    // 计算首帧加载时长
    int firstFrameLoadingTime = DateTime.now().millisecondsSinceEpoch - playerInitTimestamp!;
    FFLog.info('trackInitialLoadingTime -> 上报首帧加载时长, firstFrameLoadingTime=$firstFrameLoadingTime', tag: tag);
    _addParamIfValid(EventKey.time, firstFrameLoadingTime);
    _inflateParamsAndTrackEvent(TrackEvent.initialLoadingTime);
  }

  /// 上报播放失败
  void trackReelPlayFail(TTError error) {
    FFLog.info('trackReelPlayFail -> 上报播放失败, errorCode=${error.errorCode}', tag: tag);
    _addParamIfValid(EventKey.errorCode, error.errorCode);
    _inflateParamsAndTrackEvent(TrackEvent.reelPlayFail);
  }

  /// 上报播放缓冲
  void trackReelPlayBuffering() {
    FFLog.info('trackReelPlayBuffering -> 上报播放缓冲', tag: tag);
    _inflateParamsAndTrackEvent(TrackEvent.reelPlayBuffering);
  }

  /// 上报播放进度
  void trackPlaybackProgress(int playPosition) {
    if (firstFrameReadyTimestamp == null) {
      FFLog.info('trackPlaybackProgress -> 未记录首帧加载完成时间，无法上报播放进度, firstFrameReadyTimestamp=$firstFrameReadyTimestamp, playerInitTimestamp=$playerInitTimestamp', tag: tag);
      return;
    }
    
    // 播放进度百分比计算
    if (videoDuration != null && videoDuration! > 0) {
      _addParamIfValid(EventKey.watchProgressPercent, (playPosition / videoDuration! * 100).toStringAsFixed(2));
    } else {
      _addParamIfValid(EventKey.watchProgressPercent, '');
    }

    FFLog.info('trackPlaybackProgress -> 上报播放进度, playPosition=$playPosition, percent=${_extra[EventKey.watchProgressPercent]}', tag: tag);
    _inflateParamsAndTrackEvent(TrackEvent.playbackProgressTrack);
  }
  
  /// 上报实际播放时长
  void trackPlayTimeReal(int playPosition) {
    if (firstFrameReadyTimestamp == null) {
      FFLog.info('trackPlayTimeReal -> 未记录首帧加载完成时间，无法上报实际播放时长', tag: tag);
      return;
    }
    
    final endTimestamp = DateTime.now().millisecondsSinceEpoch;
    final playDurationMs = endTimestamp - firstFrameReadyTimestamp!;
    final realPlaySeconds = (playDurationMs / 1000).ceil();
    FFLog.info('trackPlayTimeReal -> 用户观看剧的时长；传上一个视频的观看时长，不足1s算1s, playDuration=$realPlaySeconds', tag: tag);

    _addParamIfValid(EventKey.seconds, realPlaySeconds);
    _inflateParamsAndTrackEvent(TrackEvent.playTimeReal);
  }
  
  /// 上报切换剧集播放（reelCut）
  void trackReelCut(int playPosition) {
    if (firstFrameReadyTimestamp == null) {
      FFLog.info('trackReelCut -> 未记录首帧加载完成时间，无法上报切换剧集播放', tag: tag);
      return;
    }

    FFLog.info('trackReelCut -> 上报切换剧集播放, playPosition=$playPosition', tag: tag);
    _addParamIfValid(EventKey.seconds, playPosition);
    _inflateParamsAndTrackEvent(TrackEvent.reel_cut);
  }
} 
import 'package:flutter/cupertino.dart';

class FrameCallBack {

  static CancellationToken afterFrames(int frameCount, void Function() callback) {
    final token = CancellationToken();

    void handleFrame(Duration timestamp) {
      if (token.isCancelled) return;

      if (--frameCount <= 0) {
        if (!token.isCancelled) callback();
      } else {
        WidgetsBinding.instance.addPostFrameCallback(handleFrame);
      }
    }

    WidgetsBinding.instance.addPostFrameCallback(handleFrame);
    return token;
  }
}

class CancellationToken {
  bool isCancelled = false;

  void cancel() => isCancelled = true;
}

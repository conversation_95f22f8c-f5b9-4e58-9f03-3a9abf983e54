import 'dart:async';
import 'dart:math' as math;
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lexo_ttplayer_decryption/vod_player_typedef.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/player/buffering_indicator_view.dart';
import 'package:playlet/common/player/cover_info.dart';
import 'package:playlet/common/player/player.dart';
import 'package:playlet/common/player/player_state_manager.dart';
import 'package:playlet/common/player/player_view_core_track_event.dart';
import 'package:playlet/common/player/thumbnail_view.dart';
import 'package:playlet/components/water_mark/water_mark_view.dart';
import 'package:playlet/service/player_service.dart';
import 'package:playlet/common/player/frame_call_back.dart';

// ignore: constant_identifier_names
const String _TAG = "PlayerViewCore";

/// 播放器类型枚举
enum PlayerViewType {
  /// 短视频播放器
  reels,

  /// 详情页播放器
  episode,

  /// 通用播放器
  common,

  // 挽留播放器
  retention
}

/// 播放器核心组件，包含基本的播放器功能
///
/// 封装了播放器的创建、初始化和控制逻辑，提供统一的接口
/// 外部组件可以通过GlobalKey<PlayerViewCoreState>访问播放器实例和控制方法
class PlayerViewCore extends StatefulWidget {
  const PlayerViewCore({
    super.key,
    required this.videoUrl,
    required this.onTimeChange,
    required this.aspectRatio, // 视频宽高比
    this.coverInfo,
    this.onEnded,
    this.onMoveStart,
    this.onMoveEnd,
    this.onPlayerInitialized,
    this.onReadyToDisplay,
    this.onPlayStatusChanged,
    this.onBufferingChanged,
    this.onError,
    this.onBuffering,
    this.scalingMode =
        TTVideoEngineScalingMode.TTVideoEngineScalingModeAspectFit,
    this.enableLogging = true,
    this.playerViewType = PlayerViewType.common,
    this.index = 0,
    this.shortPlayCode,
    this.hideLoading = false,
    this.additionalData,
    this.shortPlayType,
    this.containerSize,
    this.isWaterMark = true,
    this.trackEvent,
    this.isDailyShowLoading = false,
  });

  /// 视频真实宽高比
  final double aspectRatio;

  /// 视频容器尺寸
  final Size? containerSize;

  /// 视频URL
  final String videoUrl;

  /// 封面信息，包含缩略图URL和缩放模式
  final CoverInfo? coverInfo;

  /// 播放时间变化回调
  final Function(Duration time) onTimeChange;

  /// 播放结束回调
  final Function()? onEnded;

  /// 移动开始回调 todo 进度条逻辑统一封装
  final Function? onMoveStart;

  /// 移动结束回调 todo 进度条逻辑统一封装
  final Function? onMoveEnd;

  /// 播放器初始化回调
  final Function(bool isInitialized)? onPlayerInitialized;

  /// 播放器首帧加载回调 video first frame rendered
  final Function()? onReadyToDisplay;

  /// 播放状态变更回调
  final Function(bool isPlaying)? onPlayStatusChanged;

  /// 缓冲状态变更回调
  final Function(bool isBuffering)? onBufferingChanged;

  /// 错误回调
  final Function(TTError error)? onError;

  /// 缓冲回调
  final Function()? onBuffering;

  /// 缩放模式
  final TTVideoEngineScalingMode scalingMode;

  /// 是否启用日志
  final bool enableLogging;

  /// 播放器类型
  final PlayerViewType playerViewType;

  /// 索引，用于在列表中标识位置
  final int index;

  /// 短视频代码
  final int? shortPlayCode;

  /// 是否在准备就绪前显示loading
  final bool hideLoading;

  /// 附加数据，可用于传递特定业务逻辑所需的数据
  final Map<String, dynamic>? additionalData;

  /// 短视频类型
  final int? shortPlayType;

  /// 是否显示水印 true 显示水印, false 不显示水印, 默认显示
  final bool isWaterMark;

  /// 埋点事件处理类
  final PlayerViewCoreTrackEvent? trackEvent;

  /// 是否延迟显示加载动画
  final bool isDailyShowLoading;

  @override
  State<PlayerViewCore> createState() => PlayerViewCoreState();
}

class PlayerViewCoreState extends State<PlayerViewCore> {
  late Player player;

  final PlayerService playerService = Get.find<PlayerService>();

  // 播放器状态管理器
  late PlayerStateManager stateManager;

  bool isPlaying = false;
  bool isInitialized = false;
  bool isCompleted = false;
  // 使用ValueNotifier管理isBuffering状态，这样可以避免触发整个PlayerViewCore的重组
  final ValueNotifier<bool> bufferingNotifier = ValueNotifier<bool>(false);
  // 使用ValueNotifier管理isReadyToDisplay状态，这样可以避免触发整个PlayerViewCore的重组
  final ValueNotifier<bool> readyToDisplayNotifier = ValueNotifier<bool>(false);
  // 记录当前使用的缩放模式
  TTVideoEngineScalingMode currentScaleMode =
      TTVideoEngineScalingMode.TTVideoEngineScalingModeAspectFit;
  bool needPlayOnInitialized = false;

  // 设备最大边长
  double maxSide = 720.0;

  // 缓存构建的视图，确保只构建一次
  Widget? _cachedView;

  /// 获取播放速度
  double get playbackSpeed => player.value.playbackSpeed;

  // 标记组件是否已被销毁
  bool _isDisposed = false;

  String widgetInfo() {
    return 'episodeNum=${widget.trackEvent?.episodeNum},reelName=${widget.trackEvent?.reelName} shortPlayCode=${widget.trackEvent?.episodeId}, type=${widget.shortPlayType}';
  }

  @override
  void initState() {
    if (_isDisposed) {
      FFLog.warning('initState -> 组件已被销毁，忽略initState回调, ${widgetInfo()}',
          tag: _TAG);
      return;
    }

    // 初始化播放器状态管理器
    stateManager = PlayerStateManager(widgetInfoCallback: widgetInfo);

    // 获取设备尺寸的最大边
    final deviceSize = MediaQuery.of(Get.context!).size;
    if (widget.playerViewType == PlayerViewType.episode) {
      maxSide = math.max(deviceSize.width, deviceSize.height);
    } else {
      maxSide = math.min(deviceSize.width, deviceSize.height);
    }

    FFLog.info('initState -> 初始化开始, maxSide=$maxSide, ${widgetInfo()}',
        tag: _TAG);
    initVideo();
    super.initState();
  }

  @override
  void dispose() {
    FFLog.info('dispose -> 开始销毁视频, ${widgetInfo()}', tag: _TAG);
    _isDisposed = true;

    // 清理缓存的视图
    _cachedView = null;

    // 如果有埋点对象，则上报真实播放时长和播放进度
    widget.trackEvent?.trackPlayTimeReal(player.value.position.inSeconds);
    widget.trackEvent?.trackPlaybackProgress(player.value.position.inSeconds);
    widget.trackEvent?.trackReelCut(player.value.position.inSeconds);

    try {
      // 同步调用 pause 和 dispose
      player.pause();
      player.removeListener(listener);
      player.dispose();

      // 释放ValueNotifier资源
      readyToDisplayNotifier.dispose();
      bufferingNotifier.dispose();
    } catch (e) {
      FFLog.error('dispose -> 销毁播放器时出错, error=$e, ${widgetInfo()}', tag: _TAG);
    }
    super.dispose();
    FFLog.info('dispose -> 播放器销毁完成, ${widgetInfo()}', tag: _TAG);
  }

  void initVideo() async {
    widget.trackEvent?.recordPlayerInitTime();
    FFLog.info('initVideo -> 开始初始化视频, ${widgetInfo()}', tag: _TAG);
    player = Player(
      widget.videoUrl,
      onReadyToDisplay: () {
        widget.trackEvent?.trackFirstFrameReadyTime();

        if (_isDisposed) {
          FFLog.warning(
              'initVideo_onReadyToDisplay -> 组件已被销毁，忽略回调, ${widgetInfo()}',
              tag: _TAG);
          return;
        }
        if (widget.enableLogging) {
          FFLog.info('initVideo_onReadyToDisplay -> 首帧加载完成, ${widgetInfo()}',
              tag: _TAG);
        }

        // 直接更新isReadyToDisplay状态，不触发整个视图的重组
        if (mounted) {
          FFLog.info('直接设置 -> onReadyToDisplay, ${widgetInfo()}', tag: _TAG);
          if (!_isDisposed && mounted) {
            if (Platform.isAndroid) {
              // 等待原生播放器渲染完成, 否则会闪黑色
              FrameCallBack.afterFrames(6, () {
                if (!_isDisposed && mounted) {
                  player.forceDraw();
                  readyToDisplayNotifier.value = true;
                  widget.trackEvent?.trackReelPlay();
                }
              });
            } else {
              // ios 无需设置
              readyToDisplayNotifier.value = true;
              widget.trackEvent?.trackReelPlay();
            }
          }
        }
        widget.onReadyToDisplay?.call();
      },
      onBuffer: () {
        if (_isDisposed) {
          FFLog.warning('initVideo_onBuffer -> 组件已被销毁，忽略回调, ${widgetInfo()}',
              tag: _TAG);
          return;
        }
        handleBuffering();
      },
      onError: (TTError error) {
        if (_isDisposed) {
          FFLog.warning('initVideo_onError -> 组件已被销毁，忽略回调, ${widgetInfo()}',
              tag: _TAG);
          return;
        }
        handleError(error);
      },
    );

    if (_isDisposed) {
      FFLog.warning('initVideo -> 组件已被销毁，取消后续初始化步骤, ${widgetInfo()}',
          tag: _TAG);
      return;
    }

    player.addListener(listener);

    if (_isDisposed) {
      FFLog.warning('initVideo -> 组件已被销毁，取消初始化播放器, ${widgetInfo()}', tag: _TAG);
      return;
    }

    await player.initialize();

    if (_isDisposed) {
      FFLog.warning('initVideo -> 组件已被销毁，不设置缩放模式, ${widgetInfo()}', tag: _TAG);
      return;
    }
  }

  /// 播放视频
  Future<void> onPlay() async {
    if (_isDisposed) {
      FFLog.warning('onPlay -> 组件已被销毁，忽略播放请求, ${widgetInfo()}', tag: _TAG);
      return;
    }

    if (!isInitialized) {
      FFLog.info('onPlay -> 视频未初始化，忽略播放请求, ${widgetInfo()}', tag: _TAG);
      needPlayOnInitialized = true;
      return;
    }

    // 如果用户主动暂停，则不执行播放操作
    if (stateManager.isPausedByUserState()) {
      FFLog.info('onPlay -> 用户已主动暂停，忽略播放请求, ${widgetInfo()}', tag: _TAG);
      player.forceDraw();
      return;
    }

    try {
      FFLog.info('onPlay -> 开始播放, ${widgetInfo()}', tag: _TAG);
      // 触发新的播放事件
      stateManager.triggerPlayEvent();
      await player.play();
    } catch (e) {
      FFLog.error('onPlay -> 播放失败, error=$e, ${widgetInfo()}', tag: _TAG);
    }
  }

  /// 暂停视频
  Future<void> onPause() async {
    if (_isDisposed) {
      FFLog.warning('onPause -> 组件已被销毁，忽略暂停请求, ${widgetInfo()}', tag: _TAG);
      return;
    }

    try {
      FFLog.info('onPause -> 开始暂停, ${widgetInfo()}', tag: _TAG);
      await player.pause();
    } catch (e) {
      FFLog.error('onPause -> 暂停失败, error=$e, ${widgetInfo()}', tag: _TAG);
    }
  }

  Future<void> onSetMuted(bool muted) async {
    if (_isDisposed) {
      FFLog.warning('onSetMuted -> 组件已被销毁，忽略静音请求, ${widgetInfo()}', tag: _TAG);
      return;
    }
    try {
      FFLog.info('onSetMuted -> 开始静音, muted=$muted, ${widgetInfo()}',
          tag: _TAG);
      await player.setMuted(muted);
    } catch (e) {
      FFLog.error('onSetMuted -> 静音失败, error=$e, ${widgetInfo()}', tag: _TAG);
    }
  }

  /// 定位到指定位置
  Future<void> onSeekTo(Duration position) async {
    if (_isDisposed) {
      FFLog.warning('onSeekTo -> 组件已被销毁，忽略定位请求, ${widgetInfo()}', tag: _TAG);
      return;
    }

    try {
      // 用户拖动进度条时，重置暂停状态
      if (stateManager.isPausedByUserState()) {
        // 防止后台到前台自动播放
        FFLog.info(
            'onSeekTo -> 暂停状态中, 无法 seekto, pausedByUser=${stateManager.isPausedByUserState()}, ${widgetInfo()}',
            tag: _TAG);
        player.forceDraw();
        return;
      }

      FFLog.info(
          'onSeekTo -> 开始定位, position=${position.inMilliseconds}ms, ${widgetInfo()}',
          tag: _TAG);
      // 触发新的播放事件
      stateManager.triggerPlayEvent();
      await player.seekTo(position);
    } catch (e) {
      FFLog.error('onSeekTo -> 定位失败, error=$e, ${widgetInfo()}', tag: _TAG);
    }
  }

  /// 播放或暂停
  Future<void> onPlayOrPause() async {
    if (_isDisposed) {
      FFLog.warning('onPlayOrPause -> 组件已被销毁，忽略播放/暂停请求, ${widgetInfo()}',
          tag: _TAG);
      return;
    }

    try {
      FFLog.info(
          'onPlayOrPause -> 切换播放状态, isPlaying=$isPlaying, pausedByUser=${stateManager.isPausedByUserState()}, ${widgetInfo()}',
          tag: _TAG);
      if (isPlaying) {
        await player.pause();
      } else {
        await player.play();
      }
    } catch (e) {
      FFLog.error('onPlayOrPause -> 切换播放状态失败, error=$e, ${widgetInfo()}',
          tag: _TAG);
    }
  }

  bool getIsInCriticalTimeRange(){
    return getPlayer().isInCriticalTimeRange();
  }

  /// 获取当前播放器实例
  Player getPlayer() {
    return player;
  }

  /// 获取初始化状态
  bool getInitializedState() {
    return isInitialized;
  }

  /// 获取开始播放状态
  bool getStartPlayingState() {
    return player.value.isPlaying;
  }

  /// 获取用户暂停状态
  bool getPausedByUserState() {
    return stateManager.isPausedByUserState();
  }

  /// 设置用户暂停状态
  void setPausedByUserState(bool value) {
    if (mounted) {
      FFLog.info('直接设置 -> setPausedByUserState, value=$value, ${widgetInfo()}',
          tag: _TAG);
      stateManager.setPausedByUserState(value);
    }
  }

  /// 获取播放器类型
  PlayerViewType getPlayerViewType() {
    return widget.playerViewType;
  }

  /// 获取索引
  int getIndex() {
    return widget.index;
  }

  /// 获取短视频代码
  int? getShortPlayCode() {
    return widget.shortPlayCode;
  }

  /// 获取附加数据
  Map<String, dynamic>? getAdditionalData() {
    return widget.additionalData;
  }

  /// 检查播放器是否已初始化并可用
  ///
  /// 当播放器状态已初始化时返回true，可用于判断是否可以显示播放器相关UI
  bool isPlayerInitialized() {
    return isInitialized;
  }

  /// 当播放器已准备好显示时返回true，可用于判断是否显示播放器视图
  bool isPlayerReadyToDisplay() {
    return readyToDisplayNotifier.value;
  }

  /// 设置播放器缓冲状态
  ///
  /// 这个方法直接修改bufferingNotifier的值，不会触发整个视图的重组
  /// 只有BufferingIndicatorView组件会响应这个变化
  void setBufferingState(bool value) {
    if (mounted) {
      // 直接修改ValueNotifier的值，不调用setState以避免触发重组
      if (bufferingNotifier.value != value) {
        FFLog.info('直接设置 -> setBufferingState, value=$value, ${widgetInfo()}',
            tag: _TAG);
        bufferingNotifier.value = value;
        widget.onBufferingChanged?.call(bufferingNotifier.value);
      }
    }
  }

  /// 检查播放器是否可用于显示UI
  ///
  /// 当播放器状态已初始化时返回true，可用于判断是否可以显示播放器相关UI
  /// 这是一个便捷方法，用于在UI中判断是否显示播放器相关组件
  bool isPlayerAvailable() {
    return isInitialized;
  }

  /// 检查播放器是否可用于显示控制UI
  ///
  /// 当播放器状态已初始化且已开始播放时返回true，可用于判断是否显示播放控制UI
  /// 这是一个便捷方法，用于在UI中判断是否显示播放控制组件
  bool isPlayerControlAvailable() {
    return isInitialized && isPlayerReadyToDisplay();
  }

  /// 获取有效的播放器实例
  ///
  /// 如果播放器已初始化，则返回播放器实例，否则返回null
  /// 这是一个便捷方法，用于在UI中获取有效的播放器实例
  Player? getValidPlayer() {
    return isInitialized ? player : null;
  }

  void handleError(TTError error) {
    if (_isDisposed) {
      FFLog.warning('handleError -> 组件已被销毁，忽略调用, ${widgetInfo()}', tag: _TAG);
      return;
    }
    FFLog.error(
        'handleError -> 视频初始化失败, message=${error.message}, ${widgetInfo()}',
        tag: _TAG);

    // 埋点：播放失败
    widget.trackEvent?.trackReelPlayFail(error);

    widget.onError?.call(error);
  }

  /// 处理播放器缓冲
  ///
  /// 这个方法会记录缓冲日志，并调用外部提供的缓冲回调
  void handleBuffering() {
    if (_isDisposed) {
      FFLog.warning('handleBuffering -> 组件已被销毁，忽略调用, ${widgetInfo()}',
          tag: _TAG);
      return;
    }
    if (widget.enableLogging) {
      FFLog.info('handleBuffering -> 开始缓冲, ${widgetInfo()}', tag: _TAG);
    }

    // 埋点：播放缓冲
    widget.trackEvent?.trackReelPlayBuffering();

    widget.onBuffering?.call();
  }

  void listener() {
    if (_isDisposed || !mounted) {
      return;
    }

    try {
      final positions = player.value.position;
      if (positions.inMilliseconds > 0) {
        //播放器触发了end回调后,不可以再触发ontimechange回调, 否则会导致进度被重写"
        if (!player.value.isCompleted) {
          FFLog.debug(
              'onTimeChange ${widgetInfo()} isPlaying=${player.value.isPlaying},isCompleted=${player.value.isCompleted}',
              tag: _TAG);
          widget.onTimeChange(positions);
        }
      }
      if (player.value.isInitialized != isInitialized) {
        isInitialized = player.value.isInitialized;
        if (mounted) {
          FFLog.info('setState -> isInitialized变化, ${widgetInfo()}', tag: _TAG);
          widget.onPlayerInitialized?.call(isInitialized);
        }
      }

      setBufferingState(player.value.isBuffering);

      if (player.value.isPlaying != isPlaying) {
        if (mounted) {
          FFLog.info('直接设置 -> isPlaying变化, ${widgetInfo()}', tag: _TAG);
          // 直接修改变量，不调用setState以避免触发重组
          isPlaying = player.value.isPlaying;
          widget.onPlayStatusChanged?.call(isPlaying);
        }
      }

      if (isCompleted != player.value.isCompleted) {
        isCompleted = player.value.isCompleted;
        if (isCompleted && mounted) {
          widget.onEnded?.call();
        }
      }
    } catch (e) {
      FFLog.error('listener -> 播放器监听器异常, error=$e, ${widgetInfo()}', tag: _TAG);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        _cachedView ?? _buildView(),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            // 用户点击时，切换pausedByUser状态
            if (isPlaying) {
              setPausedByUserState(true);
              FFLog.info(
                  'GestureDetector_onTap -> 用户主动暂停, pausedByUser=${stateManager.isPausedByUserState()}, ${widgetInfo()}',
                  tag: _TAG);
            } else {
              setPausedByUserState(false);
              FFLog.info(
                  'GestureDetector_onTap -> 用户主动播放, pausedByUser=${stateManager.isPausedByUserState()}, ${widgetInfo()}',
                  tag: _TAG);
            }
            onPlayOrPause();
          },
        ),
        // 使用ValueListenableBuilder监听缓冲状态变化
        // 这样当bufferingNotifier值变化时，只会触发BufferingIndicatorView的重组，而不是整个播放器视图
        ValueListenableBuilder<bool>(
          valueListenable: bufferingNotifier,
          builder: (context, isBuffering, child) {
            return BufferingIndicatorView(
              isBuffering: isBuffering,
              widgetInfo: widgetInfo,
            );
          },
        )
      ],
    );
  }

  Widget _buildView() {
    FFLog.info(
        "构建视图, isInitialized=$isInitialized, isStartPlaying=${isPlayerReadyToDisplay()}, ${widgetInfo()}",
        tag: _TAG);

    if (isInitialized && needPlayOnInitialized) {
      needPlayOnInitialized = false;
      FFLog.info('_buildView -> 初始化成功后重新触发播放, ${widgetInfo()}', tag: _TAG);
      onPlay();
    }

    // 根据屏幕方向和播放器类型设置不同的缩放模式
    if (widget.playerViewType == PlayerViewType.retention ||
        widget.playerViewType == PlayerViewType.reels) {
      currentScaleMode = getAspectRatio() < 1
          ? TTVideoEngineScalingMode.TTVideoEngineScalingModeAspectFill
          : TTVideoEngineScalingMode.TTVideoEngineScalingModeAspectFit;
    } else {
      currentScaleMode = widget.scalingMode;
    }
    FFLog.info(
        '_buildView -> 设置缩放模式 scaleMode=${currentScaleMode == TTVideoEngineScalingMode.TTVideoEngineScalingModeAspectFill ? "fill" : "fit"}, ${widgetInfo()}',
        tag: _TAG);

    // 构建视图并缓存，确保只构建一次
    _cachedView = Stack(
      alignment: Alignment.center,
      children: [
        Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.black,
          child: Platform.isAndroid
              ? FittedBox(
                  fit: currentScaleMode ==
                          TTVideoEngineScalingMode
                              .TTVideoEngineScalingModeAspectFit
                      ? BoxFit.contain
                      : BoxFit.cover,
                  child: SizedBox(
                    width: maxSide,
                    height: maxSide / getAspectRatio(),
                    child: player.getPlayerView(currentScaleMode),
                  ))
              : player.getPlayerView(
                  currentScaleMode), // ios 横竖屏切换很流畅, 无需使用FittedBox
        ),
        if (widget.isWaterMark) _buildWaterMark(),
        _buildThumbnail(),
      ],
    );

    return _cachedView!;
  }

  Widget _buildThumbnail() {
    return ValueListenableBuilder<bool>(
      valueListenable: readyToDisplayNotifier,
      builder: (context, isReadyToDisplay, child) {
        // 如果已准备好显示视频，则不显示缩略图
        if (isReadyToDisplay) {
          return const SizedBox.shrink();
        }

        return ThumbnailView(
          coverInfo: getCoverInfo(),
          getAspectRatio: getAspectRatio,
          showLoading: !widget.hideLoading,
          isDailyShowLoading: widget.isDailyShowLoading,
        );
      },
    );
  }

  /// 构建水印组件
  /// 使用ValueListenableBuilder优化性能，只在readyToDisplay状态变化时重新构建
  Widget _buildWaterMark() {
    return ValueListenableBuilder<bool>(
      valueListenable: readyToDisplayNotifier,
      builder: (context, isReadyToDisplay, child) {
        // 使用LayoutBuilder获取实际的容器尺寸
        return LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            // 获取容器实际尺寸
            final containerWidth = constraints.maxWidth;
            final containerHeight = constraints.maxHeight;

            // 获取视频宽高比
            final aspectRatio = getAspectRatio();

            // 计算视频实际显示尺寸
            double videoWidth, videoHeight;

            // 根据容器和视频宽高比计算视频实际显示尺寸
            if (aspectRatio >= containerWidth / containerHeight) {
              // 视频比例比容器宽，以容器宽为基准
              videoWidth = containerWidth;
              videoHeight = containerWidth / aspectRatio;
            } else {
              // 视频比例比容器窄，以容器高为基准
              videoHeight = containerHeight;
              videoWidth = containerHeight * aspectRatio;
            }

            // 计算视频在容器中的位置（视频居中显示）
            double videoLeft = (containerWidth - videoWidth) / 2; // 视频左上角X坐标
            double videoTop = (containerHeight - videoHeight) / 2; // 视频左上角Y坐标

            // 确保坐标不小于0
            videoLeft = videoLeft < 0 ? 0 : videoLeft;
            videoTop = videoTop < 0 ? 0 : videoTop;

            // 计算视频右上角坐标
            double videoRightX = videoLeft + videoWidth;
            double videoRightY = videoTop;

            // 计算视频右上角在屏幕上的位置
            double videoRightTopPosition, videoRightRightPosition;

            // 根据scaleMode确定视频右上角位置
            if (currentScaleMode ==
                TTVideoEngineScalingMode.TTVideoEngineScalingModeAspectFill) {
              // 当scaleMode为TTVideoEngineScalingModeAspectFill时，视频填充整个容器
              // 此时视频右上角就是容器右上角
              videoRightTopPosition = 0;
              videoRightRightPosition = 0;
            } else {
              // 其他情况，视频居中显示
              videoRightTopPosition = videoRightY;
              videoRightRightPosition = containerWidth - videoRightX;
            }

            double waterMarkRightTopPosition = videoRightTopPosition;
            double waterMarkRightRightPosition = videoRightRightPosition;

            // 对于reels类型且竖屏视频，水印需要向下移动状态栏高度
            if (widget.playerViewType == PlayerViewType.reels &&
                aspectRatio < 1) {
              waterMarkRightTopPosition += Get.mediaQuery.padding.top;
            }

            // 添加两个元素：1. 水印 2. 视频右上角的红色圆圈
            return Stack(
              children: [
                // 1. 水印
                Positioned(
                  top: waterMarkRightTopPosition + 10, // 添加一些垂直偏移
                  right: waterMarkRightRightPosition + 20, // 添加一些水平偏移
                  child: WaterMarkView(
                    shortPlayType: widget.shortPlayType,
                  ),
                )
              ],
            );
          },
        );
      },
    );
  }

  /// 获取视频的宽高比
  ///
  /// 优先从 widget.aspectRatio 获取
  /// 如果 widget.aspectRatio < 0.1，则尝试从 _player?.videoWidth 和 _player?.videoHeight 获取
  /// 如果 _player 无法获取到，则使用默认值 9/16
  double getAspectRatio() {
    // 如果视频宽高比正常，直接返回
    if (widget.aspectRatio >= 0.1) {
      return widget.aspectRatio;
    }

    // 尝试从播放器获取宽高
    Player? player = getValidPlayer();
    if (player != null &&
        player.value.size.width > 0 &&
        player.value.size.height > 0) {
      double width = player.value.size.width;
      double height = player.value.size.height;
      double aspectRatio = width / height;

      FFLog.info(
          'getAspectRatio -> 从播放器获取宽高比, width=$width, height=$height, aspectRatio=$aspectRatio, ${widgetInfo()}',
          tag: _TAG);

      // 确保获取到的宽高比有效
      if (aspectRatio >= 0.1) {
        return aspectRatio;
      }
    }

    // 默认返回 9/16
    FFLog.info('getAspectRatio -> 使用默认宽高比 9/16, ${widgetInfo()}', tag: _TAG);
    return 9 / 16;
  }

  /// 获取封面信息
  ///
  /// 如果widget.coverInfo为空，则创建一个新的CoverInfo对象
  /// 根据播放器类型和视频宽高比设置合适的fit
  CoverInfo getCoverInfo() {
    String thumbUrl;
    BoxFit fit;
    bool isNewCoverInfo = false;

    // 如果coverInfo不为空，使用其中的值
    if (widget.coverInfo != null) {
      thumbUrl = widget.coverInfo!.thumbUrl ?? widget.videoUrl;
      fit = widget.coverInfo!.fit;
    } else {
      // 创建新的CoverInfo对象
      thumbUrl = widget.videoUrl;
      fit = BoxFit.contain;
      isNewCoverInfo = true;
    }

    // 原始fit值
    BoxFit originalFit = fit;

    // 根据播放器类型和视频宽高比设置fit
    if (widget.playerViewType == PlayerViewType.reels && getAspectRatio() < 1) {
      fit = BoxFit.cover;
    }

    if (widget.playerViewType == PlayerViewType.retention) {
      fit = BoxFit.cover;
    }

    // 如果fit值发生了变化，记录日志
    if (fit != originalFit || isNewCoverInfo) {
      FFLog.info(
          'getCoverInfo -> ${isNewCoverInfo ? "创建新的" : "修改"}CoverInfo, thumbUrl=$thumbUrl, originalFit=$originalFit, newFit=$fit, playerViewType=${widget.playerViewType}, aspectRatio=${getAspectRatio()}, ${widgetInfo()}',
          tag: _TAG);
    }

    return CoverInfo(
      thumbUrl: thumbUrl,
      fit: fit,
    );
  }
}

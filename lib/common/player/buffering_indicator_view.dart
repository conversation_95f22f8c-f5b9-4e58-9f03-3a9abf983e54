import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/gen/assets.gen.dart';

const String _TAG = "BufferingIndicatorView";

/// 播放器缓冲指示器组件
///
/// 这个组件负责显示播放器的缓冲状态，根据isBuffering状态决定是否显示
/// 将其提取为单独的StatefulWidget可以避免主播放器组件因缓冲状态变化而重组
class BufferingIndicatorView extends StatefulWidget {
  const BufferingIndicatorView({
    super.key,
    required this.isBuffering,
    this.widgetInfo,
  });

  /// 是否正在缓冲
  final bool isBuffering;

  /// 组件信息，用于日志
  final String? Function()? widgetInfo;

  @override
  State<BufferingIndicatorView> createState() => _BufferingIndicatorViewState();
}

class _BufferingIndicatorViewState extends State<BufferingIndicatorView> {
  @override
  Widget build(BuildContext context) {
    // 当isBuffering为true时，显示缓冲指示器
    if (widget.isBuffering) {
      if (widget.widgetInfo != null) {
        FFLog.info('显示缓冲指示器, ${widget.widgetInfo!()}', tag: _TAG);
      }
      return Center(
        child: Assets.loading.dotLoading.image(
          width: 60.sp,
          height: 60.sp,
        ),
      );
    }

    // 当isBuffering为false时，不显示任何内容
    return const SizedBox.shrink();
  }
}

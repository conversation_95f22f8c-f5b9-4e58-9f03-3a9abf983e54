import 'package:lexo_ttplayer_decryption/vod_player_mediaSource.dart';
import 'package:playlet/common/log/ff_log.dart';

class VideoSourceRepo {
  static const String tag = "VideoSourceRepo";

  // 封装 TTVideoEngineUrlSource(urls: [url], cacheKey: url);
  static TTVideoEngineUrlSource createVideoSource(String url) {
    String cacheKey = _createVideoUrlCacheKey(url);
    FFLog.debug("$tag url: $url, cacheKey: $cacheKey");
    return TTVideoEngineUrlSource(urls: [url], cacheKey: cacheKey);
  }


  /// 创建视频源
  static TTVideoEngineUrlSource initWithURL(String url) {
    String cacheKey = _createVideoUrlCacheKey(url);
    FFLog.debug("$tag url: $url, cacheKey: $cacheKey");
    return TTVideoEngineUrlSource.initWithURL(url, cacheKey);
  }

  /// 将URL列表转换为媒体源列表
  static List<TTVideoEngineMediaSource> createVideoSources(List<String> urls) {
    List<TTVideoEngineMediaSource> sources = [];
    for (String url in urls) {
      try {
        if (url.isNotEmpty) {
          TTVideoEngineMediaSource source = createVideoSource(url);
          sources.add(source);
        }
      } catch (e) {
        FFLog.error("$tag 创建视频源失败: $e");
      }
    }
    return sources;
  }

  /// 创建视频URL缓存键
  static String _createVideoUrlCacheKey(String url) {
    try {
      // 获取URL路径部分
      final pathStart = url.indexOf("/", url.indexOf("://") + 3);
      if (pathStart == -1) return url;
      
      // 移除auth_key参数
      final authKeyIndex = url.indexOf("auth_key=");
      if (authKeyIndex == -1) return url.substring(pathStart);
      
      return url.substring(pathStart, authKeyIndex);
    } catch (e) {
      return url;
    }
  }
}

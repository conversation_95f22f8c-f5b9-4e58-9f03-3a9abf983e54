import 'package:playlet/common/log/ff_log.dart';

const String _TAG = "PlayerStateManager";

/// 播放器状态管理器
/// 
/// 负责管理全局播放事件计数和用户暂停状态
class PlayerStateManager {
  // 全局播放事件计数器，每次有视频播放时递增
  static int playerEventIndex = 0;
  
  // 记录用户暂停时的全局播放事件计数器值
  int pauseByUserIndex = 0;
  
  // 调试用信息
  final String Function() widgetInfoCallback;
  
  PlayerStateManager({required this.widgetInfoCallback});
  
  /// 触发新的播放事件
  /// 
  /// 返回当前的播放事件索引
  int triggerPlayEvent() {
    playerEventIndex++;
    FFLog.info('triggerPlayEvent -> 播放事件递增: playerEventIndex=$playerEventIndex, ${widgetInfoCallback()}', tag: _TAG);
    return playerEventIndex;
  }
  
  /// 获取用户暂停状态
  /// 
  /// 如果全局播放事件计数器大于用户暂停时的计数器值，表示已有新的播放事件，暂停状态失效
  bool isPausedByUserState() {
    if (playerEventIndex > pauseByUserIndex) {
      return false;
    }
    // 返回暂停状态（如果pauseByUserIndex等于playerEventIndex且不为0，表示用户主动暂停）
    return pauseByUserIndex > 0;
  }

  /// 设置用户暂停状态
  /// 
  /// @param value true表示暂停，false表示取消暂停
  void setPausedByUserState(bool value) {
    FFLog.info('setPausedByUserState -> value=$value, ${widgetInfoCallback()}', tag: _TAG);
    if (value) {
      // 设置为暂停状态时，记录当前的全局播放事件计数器值
      pauseByUserIndex = playerEventIndex;
      FFLog.info('setPausedByUserState -> 设置暂停索引: pauseByUserIndex=$pauseByUserIndex, ${widgetInfoCallback()}', tag: _TAG);
    } else {
      // 取消暂停状态时，重置索引为0
      pauseByUserIndex = 0;
    }
  }
} 
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/player/cover_info.dart';
import 'package:playlet/components/loading/ffloading.dart';
import 'package:playlet/components/widget/cover_widget.dart';

const String _TAG = "ThumbnailView";

/// 播放器缩略图视图组件
///
/// 这个组件负责显示播放器的封面图，根据isReadyToDisplay状态决定是否显示
/// 将其提取为单独的StatefulWidget可以避免主播放器组件因封面状态变化而重组
class ThumbnailView extends StatefulWidget {
  const ThumbnailView({
    super.key,
    required this.coverInfo,
    required this.getAspectRatio,
    this.showLoading = true,
    this.isDailyShowLoading = false,
  });

  /// 封面信息
  final CoverInfo coverInfo;

  /// 获取视频宽高比的函数
  final double Function() getAspectRatio;

  /// 是否显示加载动画
  final bool showLoading;

  /// 是否延迟显示加载动画
  final bool isDailyShowLoading;

  @override
  State<ThumbnailView> createState() => _ThumbnailViewState();
}

class _ThumbnailViewState extends State<ThumbnailView> {
  bool _show = false;
  final Duration _delayDuration = const Duration(milliseconds: 500);

  @override
  void initState() {
    if (widget.isDailyShowLoading == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showLoading();
      });
    }
    super.initState();
  }

  void _showLoading() {
    if (_show == false) {
      Future.delayed(_delayDuration, () {
        if (mounted) {
          setState(() {
            _show = true;
            FFLog.debug('延迟显示加载动画');
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildThumbnail();
  }

  Widget _buildThumbnail() {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        fit: StackFit.expand,
        alignment: Alignment.center,
        children: [
          FittedBox(
            fit: widget.coverInfo.fit,
            child: CoverWidget(
              imageUrl: widget.coverInfo.thumbUrl ?? "",
              width: Get.width,
              height: Get.width / widget.getAspectRatio(),
              placeholder: (p0, p1) => const SizedBox.shrink(),
              errorWidget: (p0, p1, p2) => const SizedBox.shrink(),
            ),
          ),
          if (widget.showLoading)
            Positioned.fill(
              child: widget.isDailyShowLoading == false
                  ? const FFLoadingWidget()
                  : _show == false
                      ? const SizedBox.shrink()
                      : const FFLoadingWidget(),
            ),
        ],
      ),
    );
  }
}

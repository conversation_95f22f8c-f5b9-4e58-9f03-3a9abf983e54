import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lexo_ttplayer_decryption/vod_player_flutter.dart';
import 'package:lexo_ttplayer_decryption/vod_player_typedef.dart';
import 'package:lexo_ttplayer_decryption/vod_player_view.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:playlet/common/player/video_source_repo.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/utils/get_extension.dart';

import 'video_value.dart';

String tag = "Player";

class Player extends ValueNotifier<VideoPlayerValue> {
  final VoidCallback? onReady;
  final VoidCallback? onBuffer;
  final VoidCallback? onReadyToDisplay;
  final Function(TTError error)? onError;

  VodPlayerFlutter? _player;

  bool _isDisposed = false;

  String url = '';

  Timer? _timer;

  Completer<void>? _creatingCompleter;
  RxBool isInCriticalTimeRange = false.obs;
  Player(
    this.url, {
    this.onReady,
    this.onBuffer,
    this.onReadyToDisplay,
    this.onError,
  }) : super(const VideoPlayerValue(
          duration: Duration.zero,
        ));

  Future<void> initialize() async {
    FFLog.debug("$tag 创建了 $url");
    _creatingCompleter = Completer<void>();
    _player = VodPlayerFlutter();
    _player?.onPrepared = onPrepared;
    _player?.loadStateDidChanged = loadStateDidChanged;
    _player?.playbackStateDidChanged = playbackStateChanged;
    _player?.didFinish = onEnded;
    _player?.readyToDisplay = _readyToDisplay;
    // sdk内部未实现
    // if (Platform.isAndroid) {
    //   _player.setScalingMode(
    //       TTVideoEngineScalingMode.TTVideoEngineScalingModeAspectFit);
    //   _player.setClipToBounds(true);
    // }
    await _player?.createPlayer(
      encryptKey: Config.playerEncryptKey,
      encryptIV: Config.playerEncryptIv,
    );
    _creatingCompleter!.complete(null);
    await _player?.setUrlSource(VideoSourceRepo.createVideoSource(url));
    Future.delayed(Duration.zero, () {
      value = value.copyWith(isInitialized: true);
      FFLog.debug("$tag 初始化完成 ${value.isInitialized}");
    });
  }

  void loadStateDidChanged(
      TTVideoEngineLoadState loadState, Map<Object?, Object?>? extraInfo) {
    FFLog.debug("$tag loadStateDidChanged $loadState $extraInfo");
    switch (loadState) {
      case TTVideoEngineLoadState.playable:
        if (onReady != null) onReady!();
        Get.dismiss();
        break;
      case TTVideoEngineLoadState.stalled:
        if (onBuffer != null) onBuffer!();
        break;
      case TTVideoEngineLoadState.error:
        Get.dismiss();
        break;
      default:
    }
  }

  void playbackStateChanged(TTVideoEnginePlaybackState state) {
    switch (state) {
      case TTVideoEnginePlaybackState.stopped:
        pause().then((void pauseResult) => seekTo(value.duration));
        value = value.copyWith(isPlaying: false);
        break;
      case TTVideoEnginePlaybackState.paused:
        value = value.copyWith(isPlaying: false);
        break;
      case TTVideoEnginePlaybackState.error:
        value = value.copyWith(isPlaying: false);
        break;
      case TTVideoEnginePlaybackState.playing:
        value = value.copyWith(isPlaying: true);
        break;
    }
  }

  Widget getPlayerView(TTVideoEngineScalingMode scalingMode) {
    NativeViewType nativeViewType = NativeViewType.TextureView;
    TTVideoPlayerView playerView = TTVideoPlayerView(
      nativeViewType: nativeViewType,
      onPlatformViewCreated: (int viewId) {
        _player?.setPlayerContainerView(viewId);
        // 确保在原生层面设置了缩放模式
        _player?.setScalingMode(scalingMode);
        // 设置裁剪超出视图的部分
        _player?.setClipToBounds(true);
      },
    );
    return playerView;
  }

  void onPrepared() async {
    FFLog.debug("$tag onPrepared");
    if (_isDisposedOrNotInitialized) {
      return;
    }
    int videoWidth = await _player?.videoWidth ?? 0;
    int videoHeight = await _player?.videoHeight ?? 0;
    Duration duration = await getDuration();
    value = value.copyWith(
      size: Size(
        videoWidth.toDouble(),
        videoHeight.toDouble(),
      ),
      duration: duration,
    );
  }

  void _readyToDisplay() async {
    FFLog.debug("$tag readyToDisplay");
    onReadyToDisplay?.call();
    Duration duration = await getDuration();
    value = value.copyWith(isBuffering: false, duration: duration);
  }

  Future<Duration> getDuration() async {
    Duration duration = Duration.zero;
    try {
      duration = await _player?.duration ?? Duration.zero;
    } catch (e) {
      FFLog.error("$tag duration error $e");
    }
    return duration;
  }

  void onEnded(TTError? error) {
    if (_isDisposedOrNotInitialized) {
      return;
    }
    if (error != null) {
      onError?.call(error);
    }
    FFLog.debug("$tag onEnded isCompleted true");
    value = value.copyWith(isCompleted: true);
  }

  void forceDraw() {
    if (Platform.isAndroid) {
      _player?.forceDraw();
    }
  }

  Future<void> setMuted(bool muted) async {
    _player?.setMuted(muted);
  }

  /// Starts playing the video.
  ///
  /// If the video is at the end, this method starts playing from the beginning.
  ///
  /// This method returns a future that completes as soon as the "play" command
  /// has been sent to the platform, not when playback itself is totally
  /// finished.
  Future<void> play() async {
    FFLog.info('play -> 开始播放, url=$url', tag: tag);
    value = value.copyWith(isPlaying: true, isBuffering: false);
    await _applyPlayPause();
  }

  /// Pauses the video.
  Future<void> pause() async {
    FFLog.info('pause -> 开始暂停, url=$url', tag: tag);
    value = value.copyWith(isPlaying: false);
    await _applyPlayPause();
  }

  Future<void> setLooping(bool looping) async {
    await _player?.setLooping(looping);
  }

  Future<void> _applyPlayPause() async {
    if (_isDisposedOrNotInitialized) {
      FFLog.info('_applyPlayPause -> 播放器已销毁或未初始化，忽略操作, url=$url', tag: tag);
      return;
    }
    if (value.isPlaying) {
      await _player?.play();
      _timer?.cancel();
      FFLog.info('_applyPlayPause -> 启动定时器, url=$url', tag: tag);
      _timer = Timer.periodic(
        const Duration(milliseconds: 500),
        (Timer timer) async {
          if (_isDisposed) {
            return;
          }
          final Duration? newPosition = await position;
          if (newPosition == null) {
            return;
          }
          _updatePosition(newPosition);
        },
      );
      forceDraw();
      await _applyPlaybackSpeed();
    } else {
      FFLog.info('_applyPlayPause -> 取消定时器, url=$url', tag: tag);
      _timer?.cancel();
      await _player?.pause();
    }
  }

  /// The position in the current video.
  Future<Duration?> get position async {
    if (_isDisposed) {
      return null;
    }
    return _player?.position;
  }

  /// Sets the video's current timestamp to be at [moment]. The next
  /// time the video is played it will resume from the given [moment].
  ///
  /// If [moment] is outside of the video's full range it will be automatically
  /// and silently clamped.
  Future<void> seekTo(Duration position) async {
    if (_isDisposedOrNotInitialized) {
      FFLog.info('seekTo -> 播放器已销毁或未初始化，忽略定位请求, url=$url', tag: tag);
      return;
    }
    if (position > value.duration) {
      position = value.duration;
    } else if (position < Duration.zero) {
      position = Duration.zero;
    }
    double pos = position.inMilliseconds.toDouble();

    value = value.copyWith(isBuffering: true);
    FFLog.info('seekTo -> 开始定位, position=${pos}ms, url=$url', tag: tag);

    await _player?.seekToTimeMs(
      time: pos,
      seekRenderCompleted: () {
        value = value.copyWith(isBuffering: false);
        FFLog.info('seekTo_seekRenderCompleted -> 定位渲染完成, url=$url', tag: tag);
        // Get.dismiss();
        _applyPlayPause();
      },
      seekCompleted: (p0) {
        if (p0 == true) {
          FFLog.info('seekTo_seekCompleted -> 定位完成，开始播放, url=$url', tag: tag);
          value = value.copyWith(isBuffering: false);
        }
      },
    );
    _updatePosition(position);
  }

  void _updatePosition(Duration position) {
    // The underlying native implementation on some platforms sometimes reports
    // a position slightly past the reported max duration. Clamp to the duration
    // to insulate clients from this behavior.
    if (position > value.duration) {
      position = value.duration;
    }
    value = value.copyWith(
      position: position,
    );
    _uodateProgress();
  }

  // 更新进度
  void _uodateProgress() {
    if (_isDisposedOrNotInitialized) {
      return;
    }
    final int duration = value.duration.inMilliseconds;
    final int position = value.position.inMilliseconds;
    final double progress = duration > 0 ? position / duration : 0.0;
    value = value.copyWith(
      playbackProgress: progress,
    );
  }

  Future<void> _applyPlaybackSpeed() async {
    if (_isDisposedOrNotInitialized) {
      FFLog.info('_applyPlaybackSpeed -> 播放器已销毁或未初始化，忽略速度设置, url=$url',
          tag: tag);
      return;
    }
    if (!value.isPlaying) {
      FFLog.info('_applyPlaybackSpeed -> 播放器未播放，忽略速度设置, url=$url', tag: tag);
      return;
    }
    FFLog.info(
        '_applyPlaybackSpeed -> 设置播放速度, speed=${value.playbackSpeed}, url=$url',
        tag: tag);
    await _player?.setPlaybackSpeed(
      value.playbackSpeed,
    );
  }

  Future<void> setPlaybackSpeed(double speed) async {
    FFLog.info('setPlaybackSpeed -> 更新播放速度, speed=$speed, url=$url', tag: tag);
    value = value.copyWith(playbackSpeed: speed);
    await _applyPlaybackSpeed();
  }

  @override
  Future<void> dispose() async {
    if (_isDisposed) {
      return;
    }
    if (_creatingCompleter != null) {
      await _creatingCompleter!.future;
      if (!_isDisposed) {
        _isDisposed = true;
        _timer?.cancel();
        await _player?.stop();
        await _player?.closeAsync();
        _player = null;
      }
    }
    _isDisposed = true;
    FFLog.debug("$tag 销毁了 $url");
    super.dispose();
  }

  bool get _isDisposedOrNotInitialized => _isDisposed || !value.isInitialized;
}

import 'dart:async';
import 'package:get_storage/get_storage.dart';
import 'package:playlet/common/log/ff_log.dart';
import 'package:intl/intl.dart';
import 'package:playlet/utils/safe_storage.dart';

/// API调用次数限制管理器
class ApiLimitManager {
  static const String tag = "ApiLimitManager";
  static const int LIMIT_COUNT = 30; // 最大调用次数限制
  static const int CHECK_UI_SHOW_TIME = 2000; // 延迟检查时间(毫秒)
  
  static Timer? _checkUIShowedTimer;
  static bool _coldBoot = true; // 冷启动标志
  
  static const String _keyPrefix = "api_call_count_";
  
  /// 检查是否超过限制
  static bool isLimited(String apiKey) {
    return getProcessLaunchCount(apiKey) >= LIMIT_COUNT;
  }
  
  /// 获取当天API调用次数
  static int getProcessLaunchCount(String apiKey) {
    final String date = _getTodayDate();
    final String storageKey = "$_keyPrefix${apiKey}_$date";
    return SafeStorage().read<int>(storageKey) ?? 0;
  }
  
  /// 检查是否应该限制，并记录调用次数
  static void checkShouldLimit(String apiKey) {
    _checkUIShowedTimer?.cancel();
    _checkUIShowedTimer = Timer(Duration(milliseconds: CHECK_UI_SHOW_TIME), () {
      if (_coldBoot) {
        final String date = _getTodayDate();
        final String storageKey = "$_keyPrefix${apiKey}_$date";
        final int count = getProcessLaunchCount(apiKey) + 1;
        
        SafeStorage().write(storageKey, count);
        FFLog.info("API调用计数: $apiKey, 日期: $date, 次数: $count", tag: tag);
        
        // 首次调用后重置冷启动标志
        _coldBoot = false;
      }
    });
  }
  
  /// 重置当天计数
  static void resetCount(String apiKey) {
    final String date = _getTodayDate();
    final String storageKey = "$_keyPrefix${apiKey}_$date";
    SafeStorage().write(storageKey, 0);
    FFLog.info("重置API调用计数: $apiKey, 日期: $date", tag: tag);
  }
  
  /// 获取当天日期，格式：yyyy-MM-dd
  static String _getTodayDate() {
    final now = DateTime.now();
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(now);
  }
  
  /// 设置冷启动状态
  static void setColdBoot(bool value) {
    _coldBoot = value;
  }
} 
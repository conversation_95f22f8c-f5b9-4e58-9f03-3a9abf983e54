import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:playlet/gen/assets.gen.dart';

enum PackageSource {
  googlePlay('googlePlay'),
  appStore('appStore'),
  unknown('');

  final String value;
  const PackageSource(this.value);

  @override
  String toString() => value;
}

class Config {
  // TODO: 正式包须替换为 true
  static const bool isProduction = false;

  static const String appName = "FlareFlow";
  //====1 TODO: 正式包须替换！！！ Api接口地址
  static String baseUrl = kDebugMode == true ? "https://api-dev.ffff.team/ffff" : "https://api-test.ffff.team/ffff";
  static const String contactEmailDebug = "<EMAIL>";
  static const String contactEmail = "<EMAIL>";
  static const String userAgreement = "https://flareflow.tv/terms.html";
  static const String privacyPolicy = "https://flareflow.tv/policy.html";
  static const String rechargeAgreement = "https://flareflow.tv/recharge.html";

  //====2 TODO: 正式包须替换！！！ 升级使用
  static const String appStoreId = "";
  static const String androidAppBundleId = "";

  //====3 TODO: 正式包须替换！！！ 埋点上报服务地址
  static const String analyticsServerUrl = 
    "https://event-test.ffff.team/ffff/eventController/appEventReport";
  // TODO: 正式包须替换！！！ 日志上报服务地址
  static const String logServerUrl = 
    "https://event-test.ffff.team/ffff/log/report";
  // TODO: 正式包须替换！！！埋点上报账号ID
  static const String analyticsAccountId = "111";
  // TODO: 正式包须替换！！！埋点上报开发者Token
  static const String analyticsDevToken = "111";
  // TODO: 正式包须替换！！！埋点上报加密密钥
  static const String analyticsCryptKey = "111";

  //====4 TODO: 正式包须替换！！！DLink归因SDK
  static const String dLinkAccountId = "不能为空";
  // TODO: mateAppId
  static const String dLinkMetaAppId = "";
  // TODO: AppsFlyer appId（android only）
  static const String dLinkAppsFlyerAppIdAndroid = "";
  // TODO: AppsFlyer appId（iOS only）
  static const String dLinkAppsFlyerAppIdIOS = "";
  // TODO: AppsFlyer afDevKey
  static const String dLinkAppsFlyerDevKey = "";

  //====5 TODO: 正式包须替换！！！播放器 AppID
  static String playerAppId = Platform.isAndroid ? "726104" : "749983";
  // TODO: 正式包须替换！！！播放器 解密 key
  static const String playerEncryptKey = "e05asD3fac4e4s9k";
  // TODO: 正式包须替换！！！播放器 解密 iv
  static const String playerEncryptIv = "525400db1e2ebede";
  // TODO: 正式包须替换！！！播放器 License 路径
  static String playerLicPath =
      Platform.isAndroid ? Assets.playerAndroid : Assets.playerIos;

  // iOS app group id
  static const String appGroupId = "group.hqz.main";

  // 代理设置，仅测试环境可用
  static String? proxyIp;
  static String? proxyPort = "8888";

  static String getPackageSource() {
    // 如果是 android 则返回 googlePlay, 如果是ios 则返回 appStore
    if (Platform.isAndroid) {
      // 打渠道包时, 需要更改, 例如打华为的渠道包时, 需要返回 huawei
      return PackageSource.googlePlay.value;
    } else if (Platform.isIOS) {
      return PackageSource.appStore.value;
    }
    return PackageSource.unknown.value;
  }
}
